#!/bin/bash

# SmartBoat Container Startup Script
set -e  # Exit on any error initially

echo "Starting SmartBoat Application..."

# Configure the application with environment variables
echo "Configuring application..."
if ! /app/configure-app.sh; then
    echo "ERROR: Configuration failed"
    exit 1
fi

# Test nginx configuration
echo "Testing nginx configuration..."
if ! nginx -t; then
    echo "ERROR: Nginx configuration test failed"
    exit 1
fi

# Start nginx in the background
echo "Starting nginx..."
nginx -g "daemon off;" &
NGINX_PID=$!

# Wait for nginx to start
sleep 3

# Verify nginx is running
if ! kill -0 $NGINX_PID 2>/dev/null; then
    echo "ERROR: Nginx failed to start"
    exit 1
fi

echo "✓ Nginx started successfully (PID: $NGINX_PID)"

# Test if nginx is responding
echo "Testing nginx response..."
if curl -f http://localhost/health >/dev/null 2>&1; then
    echo "✓ Nginx health check passed"
else
    echo "⚠ Nginx health check failed, but nginx is running"
fi

# From here on, don't exit on .NET API errors - keep nginx running
set +e

# Function to start .NET API with retry logic
start_dotnet_api() {
    local attempt=1
    local max_attempts=3
    
    while [ $attempt -le $max_attempts ]; do
        echo "Starting .NET API (attempt $attempt/$max_attempts)..."
        cd /app/api
        
        # Check if the DLL exists
        if [ ! -f "SmartBoat.API.dll" ]; then
            echo "ERROR: SmartBoat.API.dll not found"
            return 1
        fi
        
        echo "Starting .NET API on http://0.0.0.0:5000"
        dotnet SmartBoat.API.dll --urls="http://0.0.0.0:5000" &
        DOTNET_PID=$!
        
        # Wait a bit to see if it starts successfully
        sleep 5
        
        if kill -0 $DOTNET_PID 2>/dev/null; then
            echo "✓ .NET API started successfully (PID: $DOTNET_PID)"
            wait $DOTNET_PID  # Wait for the process to complete
            local exit_code=$?
            echo "⚠ .NET API exited with code: $exit_code"
        else
            echo "⚠ .NET API failed to start"
        fi
        
        attempt=$((attempt + 1))
        if [ $attempt -le $max_attempts ]; then
            echo "Retrying in 10 seconds..."
            sleep 10
        fi
    done
    
    echo "⚠ .NET API failed to start after $max_attempts attempts"
    return 1
}

# Set up signal handlers to gracefully shut down
cleanup() {
    echo "Shutting down..."
    kill $NGINX_PID 2>/dev/null || true
    kill $DOTNET_PID 2>/dev/null || true
    exit 0
}

trap cleanup SIGTERM SIGINT

# Start the .NET API (with retries)
start_dotnet_api &

# Keep nginx running indefinitely (main container process)
echo "Container ready - nginx serving on port 80"
echo "Monitoring processes..."

# Monitor both processes and restart if needed
while true; do
    if ! kill -0 $NGINX_PID 2>/dev/null; then
        echo "ERROR: Nginx died, restarting container"
        exit 1
    fi
    
    sleep 30
done