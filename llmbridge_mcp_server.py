
from fastmcp import FastMCP
import http.client
import json

# Create an MCP server
mcp = FastMCP("LLMBridge", request_timeout = 30000)


@mcp.tool()
def make_llmbridge_request(flow_to_run: str, TDD:str, ssl: bool = True, url: str = "devportal-backend-v3.westeurope.azurecontainer.io:443", code: str = None) -> str:
    """Generate code for creating a .NET application, based on a given Technical Design Document. 

       Args:
        TDD: document that describes all the technical requirements for the application to be created. 
        ssl: True if the server is using https.
        url: the url of the server to make the request to.
        code: code to be changed if needed.

    """

    try:
        conn = http.client.HTTPConnection(url) if not ssl else http.client.HTTPSConnection(url)
    except Exception as e:
        print(f"Error establishing connection: {e}")
        return "Connection failed. Please check the server status."
    
    if code is not None:
     TDD = f"{TDD}\n\n\n\n**Code Section**\n\n{code}"
    
    
    data = {
        "value": flow_to_run,
        "text": TDD
    }
    
    # Define headers
    headers = {
        'Content-Type': 'application/json',
    }
    
    # Define the payload
    payload = json.dumps(data)
    
    # Make the POST request
    try:
        conn.request("POST", "/start_flow", payload, headers)
    except Exception as e:
        print(f"Error making request: {e}")
        return "Request failed. Please check the server status."

    # Get the response
    response = conn.getresponse()
    result = response.read()

    # Print the response
    print(response.status, response.reason)
    result_text = result.decode("utf-8")
    response_json = json.loads(result_text)


    # Close the connection
    conn.close()
    summaries = [response_json[key] for key in response_json.keys() if key.startswith("output_")]
    return summaries




def main():
    mcp.run(transport='stdio')


if __name__ == "__main__":
    main()



