---
**Document Title**: API Technical Design  
**Domain**: Subscription  
**Document Version**: 1.0

---
Section Headers **
Subsection Headers*
End of Section - - -

1. **Overview**
   - Purpose of the documentation
   - Key objectives and functionalities

2. **Web API Ground Rules Section**

   *Requests*  
   Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

   Example Request:
   ```json
   {
       "header": {
           "ID": "{{$guid}}",
           "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
           "bank": "NBG",
           "UserId": "{{$user_guid}}"
       },
       "payload": {}
   }
   ```

   request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.  
   request.Header.application is a GUID for each application that invokes our web API.  
   request.Header.bank always has the value “BANK”  
   request.Header.UserId is the GUID Id for each user.

   *Responses*  
   Each API response is wrapped in a Response object.  
   All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null  
   In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

   Example Response:
   ```json
   {
       "payload": {},
       "exception": {
           "id": "guid",
           "code": "string",
           "description": "string"
       }
   }
   ```

3. **Endpoint Execution Logic**  
   All endpoints are asynchronous.  
   No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.  
   SafeExecutor is a static class.  
   SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.  
   Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:  
   Code: string  
   Description: string  
   When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.  
   Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateUser”, there should be a method “CreateUser” defined in the interface and implemented in the service layer. Types, Interfaces, and Services are defined in separate files.

4. **Database Layer Rules**  
   Dapper ORM is used to access, add, update, or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,  
   `Task<Entity> SelectEntityAsync(Guid entityId)`

   The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.  
   Also, in terms of database structure, we never use foreign keys.

5. **Common Types Section**

   **Request**  
   Field Name | Type  
   --- | ---  
   Header | RequestHeader  
   Payload | T  

   **RequestHeader**  
   Field Name | Type  
   --- | ---  
   Id | guid (Always new guid)  
   Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is 03FC0B90-DFAD-11EE-8D86-0800200C9A66  
   Bank | string  
   UserId | guid  

   **Response**  
   Field Name | Type  
   --- | ---  
   Payload | T  
   Exception | ResponseException  

   **ResponseException**  
   Field Name | Type  
   --- | ---  
   Id | guid  
   Code | string  
   Description | string  
   Category | string  

6. **Database Layer Section**
   - **Environments**
   - **DB Tables** (This section will be omitted as per your request)

## *DB Tables*

### *Subscription*

| **Name**           | **Data Type**      | **Nullable** | **Unique** | **Description**                                 |
|--------------------|-------------------|--------------|------------|-------------------------------------------------|
| Id                 | uniqueidentifier  | false        | true       | Unique identifier for the subscription          |
| Name               | nvarchar(200)     | false        | false      | Name of the subscription plan                   |
| Type               | nvarchar(100)     | false        | false      | Type/category of subscription                   |
| CustomerId         | uniqueidentifier  | false        | false      | Associated customer ID                          |
| StartDate          | datetime2(7)      | false        | false      | Subscription start date                         |
| EndDate            | datetime2(7)      | true         | false      | Subscription end date                           |
| Price              | decimal(18,2)     | false        | false      | Price of the subscription                       |
| BillingFrequency   | nvarchar(50)      | false        | false      | Billing frequency (e.g., Monthly, Yearly)       |
| Status             | nvarchar(50)      | false        | false      | Current status (e.g., Active, Cancelled)        |
| SensorLimit        | int               | true         | false      | Maximum number of sensors allowed               |
| LastUpdated        | datetime2(7)      | true         | false      | Date/time of last update                        |
| Created            | datetime2(7)      | false        | false      | Timestamp when the subscription was created     |
| Changed            | datetime2(7)      | true         | false      | Timestamp when the subscription was last updated|

### *SubscriptionFeature*

| **Name**          | **Data Type**      | **Nullable** | **Unique** | **Description**                |
|-------------------|-------------------|--------------|------------|--------------------------------|
| SubscriptionId    | uniqueidentifier  | false        | false      | Subscription ID                |
| Feature           | nvarchar(100)     | false        | false      | Feature name                   |

### *UsageRecord*

| **Name**          | **Data Type**      | **Nullable** | **Unique** | **Description**                        |
|-------------------|-------------------|--------------|------------|----------------------------------------|
| Id                | uniqueidentifier  | false        | true       | Unique identifier for the usage record |
| SubscriptionId    | uniqueidentifier  | false        | false      | Associated subscription ID             |
| CustomerId        | uniqueidentifier  | false        | false      | Associated customer ID                 |
| UserId            | uniqueidentifier  | false        | false      | Associated user ID                     |
| Feature           | nvarchar(100)     | false        | false      | Feature or resource being used         |
| UsageValue        | int               | false        | false      | Amount of usage                        |
| Timestamp         | datetime2(7)      | false        | false      | Date/time of usage record              |
| Created           | datetime2(7)      | false        | false      | Timestamp when the record was created  |
| Changed           | datetime2(7)      | true         | false      | Timestamp when the record was last updated |

---
7. **Types Layer Section**

###

### *Subscription*

Table Annotation: This entity maps to the database table Subscriptions.

| **Name**         | **Data Type** | **Description**                                      |
|------------------|--------------|------------------------------------------------------|
| Id               | guid         | Unique identifier for the subscription (PK)          |
| Name             | string       | Name of the subscription plan                        |
| Type             | string       | Type/category of subscription                        |
| CustomerId       | guid         | Associated customer ID (foreign key)                 |
| StartDate        | datetime     | Subscription start date                              |
| EndDate          | datetime     | Subscription end date                                |
| Price            | decimal      | Price of the subscription                            |
| BillingFrequency | string       | Billing frequency (e.g., Monthly, Yearly)            |
| Status           | string       | Current status (e.g., Active, Cancelled)             |
| SensorLimit      | int          | Maximum number of sensors allowed                    |
| LastUpdated      | datetime     | Date/time of last update                             |
| Created          | datetime     | Timestamp when the subscription was created          |
| Changed          | datetime     | Timestamp when the subscription was last updated     |

---

### *SubscriptionFeature*

Table Annotation: This entity maps to the database table SubscriptionFeatures.

| **Name**         | **Data Type** | **Description**                                      |
|------------------|--------------|------------------------------------------------------|
| SubscriptionId   | guid         | Subscription ID                                      |
| Feature          | string       | Feature name                                         |

---

### *SubscriptionDto*

| **Name**         | **Data Type**   | **Description**                                      |
|------------------|----------------|------------------------------------------------------|
| Id               | guid           | Unique identifier for the subscription               |
| Name             | string         | Name of the subscription plan                        |
| Type             | string         | Type/category of subscription                        |
| CustomerId       | guid           | Associated customer ID                               |
| CustomerName     | string         | Name of the customer                                 |
| StartDate        | datetime       | Subscription start date                              |
| EndDate          | datetime       | Subscription end date                                |
| Price            | decimal        | Price of the subscription                            |
| BillingFrequency | string         | Billing frequency (e.g., Monthly, Yearly)            |
| Status           | string         | Current status (e.g., Active, Cancelled)             |
| SensorLimit      | int            | Maximum number of sensors allowed                    |
| Features         | List<string>   | List of enabled features                             |
| LastUpdated      | datetime       | Date/time of last update                             |
| Created          | datetime       | Timestamp when the subscription was created          |
| Changed          | datetime       | Timestamp when the subscription was last updated     |

---

### *CreateSubscriptionDto*

| **Name**         | **Data Type**   | **Description**                                      |
|------------------|----------------|------------------------------------------------------|
| Name             | string         | Name of the subscription plan                        |
| Type             | string         | Type/category of subscription                        |
| CustomerId       | guid           | Associated customer ID                               |
| StartDate        | datetime       | Subscription start date                              |
| EndDate          | datetime       | Subscription end date                                |
| Price            | decimal        | Price of the subscription                            |
| BillingFrequency | string         | Billing frequency (e.g., Monthly, Yearly)            |
| Status           | string         | Current status (e.g., Active, Cancelled)             |
| SensorLimit      | int            | Maximum number of sensors allowed                    |
| Features         | List<string>   | List of enabled features                             |

---

### *UpdateSubscriptionDto*

| **Name**         | **Data Type**   | **Description**                                      |
|------------------|----------------|------------------------------------------------------|
| Id               | guid           | Unique identifier for the subscription               |
| Name             | string         | Name of the subscription plan                        |
| Type             | string         | Type/category of subscription                        |
| CustomerId       | guid           | Associated customer ID                               |
| StartDate        | datetime       | Subscription start date                              |
| EndDate          | datetime       | Subscription end date                                |
| Price            | decimal        | Price of the subscription                            |
| BillingFrequency | string         | Billing frequency (e.g., Monthly, Yearly)            |
| Status           | string         | Current status (e.g., Active, Cancelled)             |
| SensorLimit      | int            | Maximum number of sensors allowed                    |
| Features         | List<string>   | List of enabled features                             |

---

### *DeleteSubscriptionDto*

| **Name**         | **Data Type**   | **Description**                                      |
|------------------|----------------|------------------------------------------------------|
| Id               | guid           | Unique identifier for the subscription               |

---

### *SubscriptionRequestDto*

| **Name**         | **Data Type**   | **Description**                                      |
|------------------|----------------|------------------------------------------------------|
| Id               | guid?          | Subscription Id. It can be null.                     |
| Name             | string         | Subscription name. It can be null.                   |

---

### *ListSubscriptionRequestDto*

| **Name**         | **Data Type**   | **Description**                                      |
|------------------|----------------|------------------------------------------------------|
| PageLimit        | int            | Page limit                                           |
| PageOffset       | int            | Page offset                                          |
| SortField        | string         | Sort field                                           |
| SortOrder        | string         | Sort order                                           |
| SearchTerm       | string         | Search                                               |
| Name             | string         | Name of the subscription                             |
| CustomerId       | guid?          | Customer Id                                          |
| Status           | string         | Filter by status                                     |

---

### *MetadataDto*

| **Name**         | **Data Type**   | **Description**                                      |
|------------------|----------------|------------------------------------------------------|
| PageLimit        | int            | Page limit                                           |
| PageOffset       | int            | Page offset                                          |
| Total            | int            | Total number of pages                                |

---

### *ReturnListSubscriptionDto*

| **Name**         | **Data Type**           | **Description**                                      |
|------------------|------------------------|------------------------------------------------------|
| Data             | List<SubscriptionDto>  | List of SubscriptionDto objects                      |
| Metadata         | MetadataDto            | Pagination parameters                                |

---

### *UsageRecordDto*

| **Name**         | **Data Type**   | **Description**                                      |
|------------------|----------------|------------------------------------------------------|
| Id               | guid           | Unique identifier for the usage record               |
| SubscriptionId   | guid           | Associated subscription ID                           |
| CustomerId       | guid           | Associated customer ID                               |
| UserId           | guid           | Associated user ID                                   |
| Feature          | string         | Feature or resource being used                       |
| UsageValue       | int            | Amount of usage (e.g., number of sensors active)     |
| Timestamp        | datetime       | Date/time of usage record                            |
| Created          | datetime       | Timestamp when the record was created                |
| Changed          | datetime       | Timestamp when the record was last updated           |

---

### *CustomerDto* (Reference)

| **Name**         | **Data Type**   | **Description**                                      |
|------------------|----------------|------------------------------------------------------|
| Id               | guid           | Unique identifier for the customer                   |
| Name             | string         | Name of the customer                                 |
| ContactPerson    | string         | Main contact person for the customer                 |
| Email            | string         | Contact email address                                |
| Phone            | string         | Contact phone number                                 |
| Status           | string         | Current status (e.g., Active, Inactive)              |

---

8. **Mapping Definitions Section**

### CreateSubscriptionDto to Subscription

Source: CreateSubscriptionDto
Target: Subscription
Map: CreateSubscriptionDto to Subscription

| **Source**      | **Target**        | **Mapping Details**                                      |
|-----------------|------------------|----------------------------------------------------------|
| -               | Id               | Guid.NewGuid()                                           |
| Name            | Name             | Direct Mapping                                           |
| Type            | Type             | Direct Mapping                                           |
| CustomerId      | CustomerId       | Direct Mapping                                           |
| StartDate       | StartDate        | Direct Mapping                                           |
| EndDate         | EndDate          | Direct Mapping                                           |
| Price           | Price            | Direct Mapping                                           |
| BillingFrequency| BillingFrequency | Direct Mapping                                           |
| Status          | Status           | Direct Mapping (default: "Active" if not provided)       |
| SensorLimit     | SensorLimit      | Direct Mapping                                           |
| -               | LastUpdated      | DateTime.Now                                             |
| -               | Created          | DateTime.Now                                             |
| -               | Changed          | null                                                     |

### CreateSubscriptionDto to SubscriptionDto

Source: CreateSubscriptionDto
Target: SubscriptionDto
Map: CreateSubscriptionDto to SubscriptionDto

| **Source**      | **Target**        | **Mapping Details**                                      |
|-----------------|------------------|----------------------------------------------------------|
| Name            | Name             | Direct Mapping                                           |
| Type            | Type             | Direct Mapping                                           |
| CustomerId      | CustomerId       | Direct Mapping                                           |
| StartDate       | StartDate        | Direct Mapping                                           |
| EndDate         | EndDate          | Direct Mapping                                           |
| Price           | Price            | Direct Mapping                                           |
| BillingFrequency| BillingFrequency | Direct Mapping                                           |
| Status          | Status           | Direct Mapping (default: "Active" if not provided)       |
| SensorLimit     | SensorLimit      | Direct Mapping                                           |
| Features        | Features         | Direct Mapping                                           |

### Subscription to SubscriptionDto

Source: Subscription
Target: SubscriptionDto
Map: Subscription to SubscriptionDto

| **Source**      | **Target**        | **Mapping Details**                                      |
|-----------------|------------------|----------------------------------------------------------|
| Id              | Id               | Direct Mapping                                           |
| Name            | Name             | Direct Mapping                                           |
| Type            | Type             | Direct Mapping                                           |
| CustomerId      | CustomerId       | Direct Mapping                                           |
| CustomerName    | CustomerName     | Direct Mapping (fetch from Customer if not present)      |
| StartDate       | StartDate        | Direct Mapping                                           |
| EndDate         | EndDate          | Direct Mapping                                           |
| Price           | Price            | Direct Mapping                                           |
| BillingFrequency| BillingFrequency | Direct Mapping                                           |
| Status          | Status           | Direct Mapping                                           |
| SensorLimit     | SensorLimit      | Direct Mapping                                           |
| Features        | Features         | Fetch from SubscriptionFeature table (List<string>)      |
| LastUpdated     | LastUpdated      | Direct Mapping                                           |
| Created         | Created          | Direct Mapping                                           |
| Changed         | Changed          | Direct Mapping                                           |

### UpdateSubscriptionDto to Subscription

Source: UpdateSubscriptionDto
Target: Subscription
Map: UpdateSubscriptionDto to Subscription

| **Source**      | **Target**        | **Mapping Details**                                      |
|-----------------|------------------|----------------------------------------------------------|
| Id              | Id               | Direct Mapping                                           |
| Name            | Name             | Conditional Mapping (Direct Mapping or No Change)        |
| Type            | Type             | Conditional Mapping (Direct Mapping or No Change)        |
| CustomerId      | CustomerId       | Conditional Mapping (Direct Mapping or No Change)        |
| StartDate       | StartDate        | Conditional Mapping (Direct Mapping or No Change)        |
| EndDate         | EndDate          | Conditional Mapping (Direct Mapping or No Change)        |
| Price           | Price            | Conditional Mapping (Direct Mapping or No Change)        |
| BillingFrequency| BillingFrequency | Conditional Mapping (Direct Mapping or No Change)        |
| Status          | Status           | Conditional Mapping (Direct Mapping or No Change)        |
| SensorLimit     | SensorLimit      | Conditional Mapping (Direct Mapping or No Change)        |
| -               | LastUpdated      | DateTime.Now                                             |
| -               | Changed          | DateTime.Now                                             |

### UpdateSubscriptionDto to SubscriptionDto

Source: UpdateSubscriptionDto
Target: SubscriptionDto
Map: UpdateSubscriptionDto to SubscriptionDto

| **Source**      | **Target**        | **Mapping Details**                                      |
|-----------------|------------------|----------------------------------------------------------|
| Id              | Id               | Direct Mapping                                           |
| Name            | Name             | Direct Mapping                                           |
| Type            | Type             | Direct Mapping                                           |
| CustomerId      | CustomerId       | Direct Mapping                                           |
| StartDate       | StartDate        | Direct Mapping                                           |
| EndDate         | EndDate          | Direct Mapping                                           |
| Price           | Price            | Direct Mapping                                           |
| BillingFrequency| BillingFrequency | Direct Mapping                                           |
| Status          | Status           | Direct Mapping                                           |
| SensorLimit     | SensorLimit      | Direct Mapping                                           |
| Features        | Features         | Direct Mapping                                           |

### ListSubscriptionRequestDto to ReturnListSubscriptionDto

Source: ListSubscriptionRequestDto
Target: ReturnListSubscriptionDto
Map: ListSubscriptionRequestDto to ReturnListSubscriptionDto

| **Source**      | **Target**            | **Mapping Details**                                      |
|-----------------|----------------------|----------------------------------------------------------|
| PageLimit       | Metadata.PageLimit   | Provided pageLimit value                                 |
| PageOffset      | Metadata.PageOffset  | Provided pageOffset value                                |

### PagedResult to ReturnListSubscriptionDto

Source: pagedResult
Target: ReturnListSubscriptionDto
Map: pagedResult to ReturnListSubscriptionDto

| **Source**      | **Target**            | **Mapping Details**                                      |
|-----------------|----------------------|----------------------------------------------------------|
| Records         | Data (List<SubscriptionDto>) | ToList()                                         |
| TotalRecords    | Metadata.Total       | pagedResult.TotalRecords                                 |

---

9. **Implementation Layer Section**

## *SubscriptionService*

---

### *Create*

Creates a new subscription plan for a customer.

| **Arguments** | CreateSubscriptionDto request, Guid userId |
| --- | --- |
| **Return value** | SubscriptionDto |

**Implementation**

1. **Validate Input Parameters**:
   - Ensure the request contains all required properties: name, type, customerId, startDate, endDate, price, billingFrequency, sensorLimit, features.
   - If any required property is missing or invalid (e.g., invalid dates, negative price, sensorLimit < 1), return an error response (DP-422).

2. **Check Customer Existence**:
   - Fetch the customer by customerId from the Customers table.
   - If not found, return a not found error (DP-404).

3. **Check Subscription Name Uniqueness**:
   - Query Subscriptions for the same customerId and name.
   - If a subscription with the same name exists for the customer, return a conflict error (DP-422).

4. **Create Subscription Record**:
   - Map the request to a new Subscription entity.
   - Set Id to a new Guid.
   - Set status to "Active".
   - Set created and lastUpdated to the current timestamp.

5. **Create Features**:
   - For each feature in the features list, create a SubscriptionFeature record linked to the subscription.

6. **Insert into Database**:
   - Insert the Subscription and SubscriptionFeature records into the database.
   - If any error occurs, return a technical error (DP-500).

7. **Log Event**:
   - Log the subscription creation event with userId and timestamp.

8. **Return**:
   - Return the created subscription profile in the response payload.

---

### *Get*

Retrieves the details of a specific subscription by subscription ID.

| **Arguments** | SubscriptionRequestDto request, Guid userId |
| --- | --- |
| **Return value** | SubscriptionDto |

**Implementation**

1. **Validate Input**:
   - Ensure the request contains the required property: id.
   - If id is missing, return an error (DP-422).

2. **Fetch Subscription**:
   - Fetch the subscription by id from the Subscriptions table.
   - If not found, return a not found error (DP-404).

3. **Fetch Related Data**:
   - Fetch the customer by customerId and include the customer name.
   - Fetch all features for the subscription from the SubscriptionFeature table.

4. **Authorization Check**:
   - Validate that the user has permission to view the subscription.

5. **Map to DTO**:
   - Map the subscription and related data to a SubscriptionDto.

6. **Return**:
   - Return the SubscriptionDto.

---

### *List*

Retrieves a paginated list of subscriptions, optionally filtered by criteria.

| **Arguments** | ListSubscriptionRequestDto request, Guid userId |
| --- | --- |
| **Return value** | List<SubscriptionDto> |

**Implementation**

1. **Validate Input**:
   - Ensure the request contains paging information (page, size).
   - If missing or invalid, return an error (DP-422).

2. **Apply Filters**:
   - Apply filters for name, customerId, status, and searchTerm as provided.

3. **Fetch Subscriptions**:
   - Fetch the list of subscriptions from the Subscriptions table according to the filters and paging parameters.

4. **Fetch Related Data**:
   - For each subscription, fetch the customer name and features.

5. **Authorization Check**:
   - Validate that the user has permission to list subscriptions.

6. **Map to DTOs**:
   - Map the list of subscriptions and related data to SubscriptionDto objects.

7. **Return**:
   - Return the list of SubscriptionDto objects.

---

### *Update*

Updates the details of an existing subscription.

| **Arguments** | UpdateSubscriptionDto request, Guid userId |
| --- | --- |
| **Return value** | SubscriptionDto |

**Implementation**

1. **Validate Input**:
   - Ensure the request contains the required property: id, and at least one updatable field.
   - If id is missing, return an error (DP-422).

2. **Fetch Subscription**:
   - Fetch the subscription by id from the Subscriptions table.
   - If not found, return a not found error (DP-404).

3. **Validate Updated Fields**:
   - If updating the name, check for uniqueness for the customer.
   - If not unique, return a conflict error (DP-422).
   - Validate other updated fields for correct format and business rules.

4. **Authorization Check**:
   - Validate that the user has permission to update the subscription.

5. **Update Subscription Record**:
   - Update the subscription record with the provided fields.
   - Set lastUpdated to the current timestamp.

6. **Update Features**:
   - If features are updated, update the SubscriptionFeature records accordingly.

7. **Database Operation**:
   - Update the subscription and features in the database.
   - If any error occurs, return a technical error (DP-500).

8. **Log Event**:
   - Log the subscription update event with userId and timestamp.

9. **Return**:
   - Return the updated subscription profile in the response payload.

---

### *Deactivate*

Deactivates a subscription record, marking the subscription as inactive.

| **Arguments** | DeleteSubscriptionDto request, Guid userId |
| --- | --- |
| **Return value** | SubscriptionDto |

**Implementation**

1. **Validate Input**:
   - Ensure the request contains the required property: id.
   - If id is missing, return an error (DP-422).

2. **Fetch Subscription**:
   - Fetch the subscription by id from the Subscriptions table.
   - If not found, return a not found error (DP-404).

3. **Authorization Check**:
   - Validate that the user has permission to deactivate the subscription.

4. **Update Status**:
   - Update the subscription status to "Inactive".
   - Set lastUpdated to the current timestamp.

5. **Database Operation**:
   - Update the subscription in the database.
   - If any error occurs, return a technical error (DP-500).

6. **Log Event**:
   - Log the deactivation event with userId and timestamp.

7. **Return**:
   - Return the updated subscription profile in the response payload.

---

11. **Interface Layer Section**

## *ISubscriptionService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | CreateSubscriptionDto request, Guid userId | SubscriptionDto |
| Get | SubscriptionRequestDto request, Guid userId | SubscriptionDto |
| Update | UpdateSubscriptionDto request, Guid userId | SubscriptionDto |
| Deactivate | DeleteSubscriptionDto request, Guid userId | SubscriptionDto |
| GetList | ListSubscriptionRequestDto request, Guid userId | ReturnListSubscriptionDto |

12. **Controller Layer Section**

## *SubscriptionController*

### /subscription/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | [Request](#request)<[CreateSubscriptionDto](#createsubscriptiondto)> |
| **Response** | [Response](#response)<string> |

- Handles the creation of a new subscription.
- Receives a request object containing a payload of type `CreateSubscriptionDto`.
- Returns a response object containing the created subscription's identifier as a string.
- The controller method injects the `ISubscriptionService` interface and invokes the `Create` method using SafeExecutor to ensure exception handling and consistent response structure.

---

### /subscription/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | [Request](#request)<[SubscriptionRequestDto](#subscriptionrequestdto)> |
| **Response** | [Response](#response)<[SubscriptionDto](#subscriptiondto)> |

- Retrieves a specific subscription by its identifier or other unique criteria.
- Receives a request object containing a payload of type `SubscriptionRequestDto`.
- Returns a response object containing the subscription details as a `SubscriptionDto`.
- The controller method injects the `ISubscriptionService` interface and invokes the `Get` method using SafeExecutor.

---

### /subscription/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | [Request](#request)<[UpdateSubscriptionDto](#updatesubscriptiondto)> |
| **Response** | [Response](#response)<string> |

- Updates an existing subscription with new details.
- Receives a request object containing a payload of type `UpdateSubscriptionDto`.
- Returns a response object containing the updated subscription's identifier as a string.
- The controller method injects the `ISubscriptionService` interface and invokes the `Update` method using SafeExecutor.

---

### /subscription/deactivate

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Deactivate |
| **Request** | [Request](#request)<[DeleteSubscriptionDto](#deletesubscriptiondto)> |
| **Response** | [Response](#response)<[SubscriptionDto](#subscriptiondto)> |

- Deactivates a subscription (sets its status to "Inactive").
- Receives a request object containing a payload of type `DeleteSubscriptionDto`.
- Returns a response object containing the updated subscription as a `SubscriptionDto`.
- The controller method injects the `ISubscriptionService` interface and invokes the `Deactivate` method using SafeExecutor.

---

### /subscription/list

| **HTTP Request Method** | GET |
| --- | --- |
| **Method** | GetList |
| **Request** | [Request](#request)<[ListSubscriptionRequestDto](#listsubscriptionrequestdto)> |
| **Response** | [Response](#response)<[ReturnListSubscriptionDto](#returnlistsubscriptiondto)> |

- Retrieves a paginated list of subscriptions based on filtering and sorting criteria.
- Receives a request object containing a payload of type `ListSubscriptionRequestDto`.
- Returns a response object containing a list of `SubscriptionDto` objects and pagination metadata in a `ReturnListSubscriptionDto`.
- The controller method injects the `ISubscriptionService` interface and invokes the `GetList` method using SafeExecutor.

---

**Controller Implementation Notes:**

- All controller methods are asynchronous and return HTTP 200 OK regardless of execution outcome. The actual result or exception is encapsulated in the response object.
- Each controller method:
  - Accepts a `Request<T>` object as input, where `T` is the specific DTO for the operation.
  - Returns a `Response<T>` object, where `T` is the expected result type.
  - Uses dependency injection to obtain the `ISubscriptionService` interface.
  - Wraps service calls in the `SafeExecutor` to ensure consistent error handling and response formatting.
- The controller is responsible only for request/response wrapping, dependency injection, and safe execution. All business logic resides in the service layer.

---