---
**Document Title**: API Technical Design
**Domain**: Customer
**Document Version**: [Version Number]

---
Section Headers **
Subsection Headers*
End of Section - - -

1. **Overview**
   - Purpose of the documentation
   - Key objectives and functionalities

2. **Web API Ground Rules Section**

   *Requests*
   Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

   Example Request:
   ```json
   {
       "header": {
           "ID": "{{$guid}}",
           "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
           "bank": "NBG",
           "UserId": "{{$user_guid}}"
       },
       "payload": {}
   }
   ```

   request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
   request.Header.application is a GUID for each application that invokes our web API.
   request.Header.bank always has the value “BANK”
   request.Header.UserId is the GUID Id for each user.

   *Responses*
   Each API response is wrapped in a Response object.
   All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
   In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

   Example Response:
   ```json
   {
       "payload": {},
       "exception": {
           "id": "guid",
           "code": "string",
           "description": "string"
       }
   }
   ```

3. **Endpoint Execution Logic**
   All endpoints are asynchronous.
   No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.
   SafeExecutor is a static class.
   SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.
   Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:
   Code: string
   Description: string
   When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.
   Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateUser”, there should be a method “CreateUser” defined in the interface and implemented in the service layer. Types, Interfaces, and Services are defined in separate files.

4. **Database Layer Rules**
   Dapper ORM is used to access, add, update, or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,
   `Task<Entity> SelectEntityAsync(Guid entityId)`

   The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.
   Also, in terms of database structure, we never use foreign keys.

5. **Common Types Section**

   **Request**
   Field Name | Type
   --- | ---
   Header | RequestHeader
   Payload | T

   **RequestHeader**
   Field Name | Type
   --- | ---
   Id | guid (Always new guid)
   Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is 03FC0B90-DFAD-11EE-8D86-0800200C9A66
   Bank | string
   UserId | guid

   **Response**
   Field Name | Type
   --- | ---
   Payload | T
   Exception | ResponseException

   **ResponseException**
   Field Name | Type
   --- | ---
   Id | guid
   Code | string
   Description | string
   Category | string

6. **Database Layer Section**
   - **Environments**
   - **DB Tables**

---

## **DB Tables**

### *Customer*

| **Name**        | **Data Type**     | **Nullable** | **Unique** | **Description**                          |
|-----------------|------------------|--------------|------------|------------------------------------------|
| Id              | uniqueidentifier | false        | true       | Unique identifier for the customer       |
| Name            | nvarchar(200)    | false        | false      | Name of the customer                     |
| ContactPerson   | nvarchar(200)    | true         | false      | Main contact person for the customer     |
| Email           | nvarchar(200)    | true         | false      | Contact email address                    |
| Phone           | nvarchar(50)     | true         | false      | Contact phone number                     |
| Status          | nvarchar(50)     | false        | false      | Current status (e.g., Active, Inactive)  |
| LastActive      | datetime2(7)     | true         | false      | Date/time of last activity               |
| Created         | datetime2(7)     | false        | false      | Timestamp when the customer was created  |
| Changed         | datetime2(7)     | true         | false      | Timestamp when the customer was updated  |

---

### *Company*

| **Name**     | **Data Type**     | **Nullable** | **Unique** | **Description**                          |
|--------------|------------------|--------------|------------|------------------------------------------|
| Id           | uniqueidentifier | false        | true       | Unique identifier for the company        |
| Name         | nvarchar(200)    | false        | false      | Name of the company                      |
| Location     | nvarchar(200)    | true         | false      | Physical location or address             |
| Industry     | nvarchar(100)    | true         | false      | Industry sector                          |
| Status       | nvarchar(50)     | false        | false      | Current status (e.g., Active, Inactive)  |
| CustomerId   | uniqueidentifier | false        | false      | Associated customer ID                   |
| LastUpdated  | datetime2(7)     | true         | false      | Date/time of last update                 |
| Created      | datetime2(7)     | false        | false      | Timestamp when the company was created   |
| Changed      | datetime2(7)     | true         | false      | Timestamp when the company was updated   |

---

### *Subscription*

| **Name**           | **Data Type**     | **Nullable** | **Unique** | **Description**                          |
|--------------------|------------------|--------------|------------|------------------------------------------|
| Id                 | uniqueidentifier | false        | true       | Unique identifier for the subscription   |
| Name               | nvarchar(200)    | false        | false      | Name of the subscription plan            |
| Type               | nvarchar(100)    | false        | false      | Type/category of subscription            |
| CustomerId         | uniqueidentifier | false        | false      | Associated customer ID                   |
| StartDate          | datetime2(7)     | false        | false      | Subscription start date                  |
| EndDate            | datetime2(7)     | true         | false      | Subscription end date                    |
| Price              | decimal(18,2)    | false        | false      | Price of the subscription                |
| BillingFrequency   | nvarchar(50)     | false        | false      | Billing frequency (e.g., Monthly, Yearly)|
| Status             | nvarchar(50)     | false        | false      | Current status (e.g., Active, Cancelled) |
| SensorLimit        | int              | true         | false      | Maximum number of sensors allowed        |
| LastUpdated        | datetime2(7)     | true         | false      | Date/time of last update                 |
| Created            | datetime2(7)     | false        | false      | Timestamp when the subscription was created |
| Changed            | datetime2(7)     | true         | false      | Timestamp when the subscription was updated |

---

### *UsageRecord*

| **Name**        | **Data Type**     | **Nullable** | **Unique** | **Description**                          |
|-----------------|------------------|--------------|------------|------------------------------------------|
| Id              | uniqueidentifier | false        | true       | Unique identifier for the usage record   |
| SubscriptionId  | uniqueidentifier | false        | false      | Associated subscription ID               |
| CustomerId      | uniqueidentifier | false        | false      | Associated customer ID                   |
| UserId          | uniqueidentifier | false        | false      | Associated user ID                       |
| Feature         | nvarchar(100)    | false        | false      | Feature or resource being used           |
| UsageValue      | int              | false        | false      | Amount of usage                          |
| Timestamp       | datetime2(7)     | false        | false      | Date/time of usage record                |
| Created         | datetime2(7)     | false        | false      | Timestamp when the record was created    |
| Changed         | datetime2(7)     | true         | false      | Timestamp when the record was updated    |

---
7. **Types Layer Section**

###

### *Customer*

Table Annotation: This entity maps to the database table Customer.

| **Name**        | **Data Type** | **Description**                                   |
|-----------------|---------------|---------------------------------------------------|
| Id              | guid          | Unique identifier for the customer                |
| Name            | string        | Name of the customer                              |
| ContactPerson   | string        | Main contact person for the customer              |
| Email           | string        | Contact email address                             |
| Phone           | string        | Contact phone number                              |
| Status          | string        | Current status (e.g., Active, Inactive)           |
| LastActive      | datetime      | Date/time of last activity                        |
| Created         | datetime      | Timestamp when the customer was created           |
| Changed         | datetime      | Timestamp when the customer was updated           |

### *CustomerDto*

| **Name**        | **Data Type**         | **Description**                                   |
|-----------------|----------------------|---------------------------------------------------|
| Id              | guid                 | Unique identifier for the customer                |
| Name            | string               | Name of the customer                              |
| ContactPerson   | string               | Main contact person for the customer              |
| Email           | string               | Contact email address                             |
| Phone           | string               | Contact phone number                              |
| Status          | string               | Current status (e.g., Active, Inactive)           |
| LastActive      | datetime             | Date/time of last activity                        |
| Created         | datetime             | Timestamp when the customer was created           |
| Changed         | datetime             | Timestamp when the customer was updated           |
| Companies       | List<CompanyDto>     | List of companies owned by the customer           |
| Subscriptions   | List<SubscriptionDto>| List of subscriptions for the customer            |

### *CreateCustomerDto*

| **Name**        | **Data Type** | **Description**                                   |
|-----------------|---------------|---------------------------------------------------|
| Name            | string        | Name of the customer                              |
| ContactPerson   | string        | Main contact person for the customer              |
| Email           | string        | Contact email address                             |
| Phone           | string        | Contact phone number                              |
| Status          | string        | Current status (e.g., Active, Inactive)           |

### *UpdateCustomerDto*

| **Name**        | **Data Type** | **Description**                                   |
|-----------------|---------------|---------------------------------------------------|
| Id              | guid          | Unique identifier for the customer                |
| Name            | string        | Name of the customer                              |
| ContactPerson   | string        | Main contact person for the customer              |
| Email           | string        | Contact email address                             |
| Phone           | string        | Contact phone number                              |
| Status          | string        | Current status (e.g., Active, Inactive)           |

### *DeleteCustomerDto*

| **Name**        | **Data Type** | **Description**                                   |
|-----------------|---------------|---------------------------------------------------|
| Id              | guid          | Unique identifier for the customer                |

### *CustomerRequestDto*

| **Name**        | **Data Type** | **Description**                                   |
|-----------------|---------------|---------------------------------------------------|
| Id              | guid          | Customer Id. It can be null.                      |
| Name            | string        | Name of the customer. It can be null.             |

### *ListCustomerRequestDto*

| **Name**        | **Data Type** | **Description**                                   |
|-----------------|---------------|---------------------------------------------------|
| PageLimit       | int           | Page limit                                        |
| PageOffset      | int           | Page offset                                       |
| SortField       | string        | Sort field                                        |
| SortOrder       | string        | Sort order                                        |
| SearchTerm      | string        | Search                                            |
| Name            | string        | Name of the customer                              |
| Status          | string        | Filter by status                                  |

### *MetadataDto*

| **Name**        | **Data Type** | **Description**                                   |
|-----------------|---------------|---------------------------------------------------|
| PageLimit       | int           | Page limit                                        |
| PageOffset      | int           | Page offset                                       |
| Total           | int           | Total number of pages                             |

### *ReturnListCustomerDto*

| **Name**        | **Data Type**         | **Description**                                   |
|-----------------|----------------------|---------------------------------------------------|
| Data            | List<CustomerDto>    | List of CustomerDto objects                       |
| Metadata        | MetadataDto          | Pagination parameters                             |

###

8. **Mapping Definitions Section**

### CreateCustomerDto to Customer

Source: CreateCustomerDto
Target: Customer
Map: CreateCustomerDto to Customer

| **Source**      | **Target**    | **Mapping Details**                                      |
|-----------------|--------------|----------------------------------------------------------|
| Name            | Name         | Direct mapping                                           |
| ContactPerson   | ContactPerson| Direct mapping                                           |
| Email           | Email        | Direct mapping                                           |
| Phone           | Phone        | Direct mapping                                           |
| Status          | Status       | If provided, use value; else set to "Active"             |
|                 | Id           | Guid.NewGuid()                                           |
|                 | Created      | DateTime.Now                                             |
|                 | LastActive   | DateTime.Now                                             |
|                 | Changed      | null                                                     |

### UpdateCustomerDto to Customer

Source: UpdateCustomerDto
Target: Customer
Map: UpdateCustomerDto to Customer

| **Source**      | **Target**    | **Mapping Details**                                      |
|-----------------|--------------|----------------------------------------------------------|
| Id              | Id           | Direct mapping                                           |
| Name            | Name         | Conditional mapping (update if provided, else no change) |
| ContactPerson   | ContactPerson| Conditional mapping (update if provided, else no change) |
| Email           | Email        | Conditional mapping (update if provided, else no change) |
| Phone           | Phone        | Conditional mapping (update if provided, else no change) |
| Status          | Status       | Conditional mapping (update if provided, else no change) |
|                 | Changed      | DateTime.Now                                             |
|                 | LastActive   | DateTime.Now                                             |

### Customer to CustomerDto

Source: Customer
Target: CustomerDto
Map: Customer to CustomerDto

| **Source**      | **Target**      | **Mapping Details**                                      |
|-----------------|-----------------|----------------------------------------------------------|
| Id              | Id              | Direct mapping                                           |
| Name            | Name            | Direct mapping                                           |
| ContactPerson   | ContactPerson   | Direct mapping                                           |
| Email           | Email           | Direct mapping                                           |
| Phone           | Phone           | Direct mapping                                           |
| Status          | Status          | Direct mapping                                           |
| LastActive      | LastActive      | Direct mapping                                           |
| Created         | Created         | Direct mapping                                           |
| Changed         | Changed         | Direct mapping                                           |
|                 | Companies       | Fetch related companies (List<CompanyDto>)               |
|                 | Subscriptions   | Fetch related subscriptions (List<SubscriptionDto>)      |

### ListCustomerRequestDto to ReturnListCustomerDto

Source: ListCustomerRequestDto
Target: ReturnListCustomerDto
Map: ListCustomerRequestDto to ReturnListCustomerDto

| **Source**      | **Target**          | **Mapping Details**                                      |
|-----------------|---------------------|----------------------------------------------------------|
| PageLimit       | Metadata.PageLimit  | Provided pageLimit value                                 |
| PageOffset      | Metadata.PageOffset | Provided pageOffset value                                |

### PagedResult to ReturnListCustomerDto

Source: PagedResult
Target: ReturnListCustomerDto
Map: PagedResult to ReturnListCustomerDto

| **Source**      | **Target**          | **Mapping Details**                                      |
|-----------------|---------------------|----------------------------------------------------------|
| Records         | Data (List<CustomerDto>) | ToList()                                            |
| TotalRecords    | Metadata.Total      | PagedResult.TotalRecords                                 |

---

9. **Implementation Layer Section**

## *CustomerService*

---

### *Create*

Creates a new customer record in the system.

| **Arguments** | [CreateCustomerDto](#createcustomerdto) request, Guid userId |
| --- | --- |
| **Return value** | [CustomerDto](#customerdto) |

**Implementation:**

1. **Validate** the request and its parameters:
   1. Ensure "Name", "ContactPerson", "Email", and "Phone" are present and not empty.
   2. Validate the email format.
   3. If any required parameter is missing or invalid, throw a DP-422 exception.
2. **Authorization Check:**
   1. Validate that the user has permission to create a customer.
3. **Check Uniqueness:**
   1. Ensure that no existing customer has the same "Email" or "Name".
   2. If a duplicate is found, throw a DP-409 (conflict) exception.
4. **Map Input Data to Entity:**
   1. Map the request to a new Customer entity using the CreateCustomerDto to Customer mapping.
   2. Set "Status" to "Active" if not provided.
   3. Set "Created" and "LastActive" to the current timestamp.
5. **Perform Database Operations:**
   1. Insert the new Customer entity into the Customers table.
   2. Handle errors during insertion by throwing a DP-500 exception.
6. **Log Event:**
   1. Log the customer creation event with user ID and timestamp.
7. **Return the Result:**
   1. Map the created Customer entity to CustomerDto.
   2. Return the CustomerDto in the response payload.

---

### *Get*

Retrieves the details of a specific customer by customer ID.

| **Arguments** | [CustomerRequestDto](#customerrequestdto) request, Guid userId |
| --- | --- |
| **Return value** | [CustomerDto](#customerdto) |

**Implementation:**

1. **Validate** the request and its parameters:
   1. Ensure "Id" is present and not null.
   2. If missing, throw a DP-422 exception.
2. **Fetch Customer:**
   1. Retrieve the customer record from the Customers table by "Id".
   2. If not found, throw a DP-404 exception.
   3. Handle errors during retrieval by throwing a DP-500 exception.
3. **Authorization Check:**
   1. Validate that the user has permission to view customer details.
4. **Map Entity to DTO:**
   1. Map the Customer entity to CustomerDto, including related Companies and Subscriptions.
5. **Return the Result:**
   1. Return the CustomerDto in the response payload.

---

### *GetList*

Retrieves a paginated list of customers, optionally filtered by criteria.

| **Arguments** | [ListCustomerRequestDto](#listcustomerrequestdto) request, Guid userId |
| --- | --- |
| **Return value** | [ReturnListCustomerDto](#returnlistcustomerdto) |

**Implementation:**

1. **Validate** the request and its parameters:
   1. Ensure "PageLimit" and "PageOffset" are present and valid (PageLimit > 0, PageOffset ≥ 0).
   2. If missing or invalid, throw a DP-422 exception.
2. **Authorization Check:**
   1. Validate that the user has permission to list customers.
3. **Apply Filters:**
   1. Apply any provided filter criteria (e.g., Name, Status, SearchTerm) to the Customers table query.
4. **Retrieve Paged Customers:**
   1. Fetch the list of customers according to the filter and paging parameters.
   2. Handle errors during retrieval by throwing a DP-500 exception.
5. **Map Entities to DTOs:**
   1. For each customer record, map to CustomerDto, including related Companies and Subscriptions.
6. **Map to ReturnListCustomerDto:**
   1. Map the paged result to ReturnListCustomerDto, including metadata (PageLimit, PageOffset, Total).
7. **Return the Result:**
   1. Return the ReturnListCustomerDto in the response payload.

---

### *Update*

Updates the details of an existing customer.

| **Arguments** | [UpdateCustomerDto](#updatecustomerdto) request, Guid userId |
| --- | --- |
| **Return value** | [CustomerDto](#customerdto) |

**Implementation:**

1. **Validate** the request and its parameters:
   1. Ensure "Id" is present and not null.
   2. Ensure at least one updatable field ("Name", "ContactPerson", "Email", "Phone", "Status") is provided.
   3. If missing, throw a DP-422 exception.
2. **Fetch Customer:**
   1. Retrieve the customer record from the Customers table by "Id".
   2. If not found, throw a DP-404 exception.
   3. Handle errors during retrieval by throwing a DP-500 exception.
3. **Authorization Check:**
   1. Validate that the user has permission to update customer details.
4. **Validate Updated Fields:**
   1. Validate the updated fields for correct format and business rules.
   2. If updating "Email" or "Name", check for uniqueness.
   3. If a duplicate is found, throw a DP-409 (conflict) exception.
5. **Map Input Data to Entity:**
   1. Map the UpdateCustomerDto to the Customer entity, updating only provided fields.
   2. Set "Changed" and "LastActive" to the current timestamp.
6. **Perform Database Operations:**
   1. Update the Customer entity in the Customers table.
   2. Handle errors during update by throwing a DP-500 exception.
7. **Log Event:**
   1. Log the customer update event with user ID and timestamp.
8. **Return the Result:**
   1. Map the updated Customer entity to CustomerDto.
   2. Return the CustomerDto in the response payload.

---

### *Delete*

Deletes a customer record from the system.

| **Arguments** | [DeleteCustomerDto](#deletecustomerdto) request, Guid userId |
| --- | --- |
| **Return value** | [CustomerDto](#customerdto) |

**Implementation:**

1. **Validate** the request and its parameters:
   1. Ensure "Id" is present and not null.
   2. If missing, throw a DP-422 exception.
2. **Fetch Customer:**
   1. Retrieve the customer record from the Customers table by "Id".
   2. If not found, throw a DP-404 exception.
   3. Handle errors during retrieval by throwing a DP-500 exception.
3. **Authorization Check:**
   1. Validate that the user has permission to delete the customer.
4. **Delete Operation:**
   1. Remove the customer record from the Customers table.
   2. Handle errors during deletion by throwing a DP-500 exception.
5. **Log Event:**
   1. Log the deletion event with user ID and timestamp.
6. **Return the Result:**
   1. Return the deleted CustomerDto in the response payload.
---

10. **Core Service Dependencies Section**
    - None

11. **API Exceptions Section**

| Error Code | Exception Name         | Description                                      |
|------------|-----------------------|--------------------------------------------------|
| DP-422     | ValidationException   | One or more required fields are missing or invalid. |
| DP-404     | NotFoundException     | The requested customer was not found.            |
| DP-409     | ConflictException     | Duplicate customer (e.g., same email or name).   |
| DP-500     | InternalException     | An internal server error occurred.               |


11. **Interface Layer Section**

## *ICustomerService*

| **Method**   | **Arguments**                                                        | **Return value**           |
|--------------|-----------------------------------------------------------------------|----------------------------|
| Create       | [CreateCustomerDto](#createcustomerdto) request, Guid userId          | [CustomerDto](#customerdto)|
| Get          | [CustomerRequestDto](#customerrequestdto) request, Guid userId        | [CustomerDto](#customerdto)|
| GetList      | [ListCustomerRequestDto](#listcustomerrequestdto) request, Guid userId| [ReturnListCustomerDto](#returnlistcustomerdto)|
| Update       | [UpdateCustomerDto](#updatecustomerdto) request, Guid userId          | [CustomerDto](#customerdto)|
| Delete       | [DeleteCustomerDto](#deletecustomerdto) request, Guid userId          | [CustomerDto](#customerdto)|

- - -

12. **Controller Layer Section**

## *CustomerController*

### /customer/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | Request<CreateCustomerDto> |
| **Response** | Response<CustomerDto> |

---

### /customer/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | Request<CustomerRequestDto> |
| **Response** | Response<CustomerDto> |

---

### /customer/list

| **HTTP Request Method** | GET |
| --- | --- |
| **Method** | GetList |
| **Request** | Request<ListCustomerRequestDto> |
| **Response** | Response<ReturnListCustomerDto> |

---

### /customer/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | Request<UpdateCustomerDto> |
| **Response** | Response<CustomerDto> |

---

### /customer/deactivate

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Delete |
| **Request** | Request<DeleteCustomerDto> |
| **Response** | Response<CustomerDto> |

---

---