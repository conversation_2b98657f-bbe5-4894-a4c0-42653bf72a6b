---
**Document Title**: API Technical Design
**Domain**: Notification
**Document Version**: 1.0

---
Section Headers **
Subsection Headers*
End of Section - - -

1. **Overview**
   - Purpose of the documentation
   - Key objectives and functionalities

2. **Web API Ground Rules Section**

   *Requests*
   Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

   Example Request:
   ```json
   {
       "header": {
           "ID": "{{$guid}}",
           "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
           "bank": "NBG",
           "UserId": "{{$user_guid}}"
       },
       "payload": {}
   }
   ```

   request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
   request.Header.application is a GUID for each application that invokes our web API.
   request.Header.bank always has the value “BANK”
   request.Header.UserId is the GUID Id for each user.

   *Responses*
   Each API response is wrapped in a Response object.
   All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
   In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

   Example Response:
   ```json
   {
       "payload": {},
       "exception": {
           "id": "guid",
           "code": "string",
           "description": "string"
       }
   }
   ```

3. **Endpoint Execution Logic**
   All endpoints are asynchronous.
   No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.
   SafeExecutor is a static class.
   SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.
   Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:
   Code: string
   Description: string
   When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.
   Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateUser”, there should be a method “CreateUser” defined in the interface and implemented in the service layer. Types, Interfaces, and Services are defined in separate files.

4. **Database Layer Rules**
   Dapper ORM is used to access, add, update, or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,
   `Task<Entity> SelectEntityAsync(Guid entityId)`

   The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.
   Also, in terms of database structure, we never use foreign keys.

5. **Common Types Section**

   **Request**
   Field Name | Type
   --- | ---
   Header | RequestHeader
   Payload | T

   **RequestHeader**
   Field Name | Type
   --- | ---
   Id | guid (Always new guid)
   Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is 03FC0B90-DFAD-11EE-8D86-0800200C9A66
   Bank | string
   UserId | guid

   **Response**
   Field Name | Type
   --- | ---
   Payload | T
   Exception | ResponseException

   **ResponseException**
   Field Name | Type
   --- | ---
   Id | guid
   Code | string
   Description | string
   Category | string

6. **Database Layer Section**

| **Database** | **Description** |
| --- | --- |
| SmartBoat | Provides a detailed structure of SmartBoat Notification tables including field names, data types, and constraints. |

## *DB Tables*

### *Notification*

| **Name**      | **Data Type**       | **Nullable** | **Unique** | **Description**                                 |
|---------------|--------------------|--------------|------------|-------------------------------------------------|
| Id            | uniqueidentifier   | false        | true       | Unique identifier for the notification          |
| UserId        | uniqueidentifier   | false        | false      | User to whom the notification is sent           |
| EventType     | nvarchar(50)       | false        | false      | Type of event triggering the notification       |
| Content       | nvarchar(1000)     | false        | false      | Notification content/message                    |
| Channel       | nvarchar(50)       | false        | false      | Delivery channel                                |
| Status        | nvarchar(50)       | false        | false      | Status (e.g., Sent, Read, Failed)               |
| Timestamp     | datetime2(7)       | false        | false      | Date/time notification was sent                 |
| PreferenceId  | uniqueidentifier   | true         | false      | Associated notification preference (nullable)   |
| Created       | datetime2(7)       | false        | false      | Timestamp when the notification was created     |
| Changed       | datetime2(7)       | true         | false      | Timestamp when the notification was last updated|

### *NotificationPreference*

| **Name**      | **Data Type**       | **Nullable** | **Unique** | **Description**                                 |
|---------------|--------------------|--------------|------------|-------------------------------------------------|
| Id            | uniqueidentifier   | false        | true       | Unique identifier for the preference            |
| UserId        | uniqueidentifier   | false        | false      | Associated user ID                              |
| EventType     | nvarchar(50)       | false        | false      | Event type for which preference applies         |
| Channel       | nvarchar(50)       | false        | false      | Preferred channel                               |
| Enabled       | bit                | false        | false      | Whether notifications are enabled               |
| UpdatedAt     | datetime2(7)       | true         | false      | Date/time preference was last updated           |
| Created       | datetime2(7)       | false        | false      | Timestamp when the preference was created       |
| Changed       | datetime2(7)       | true         | false      | Timestamp when the preference was last updated  |

---

7. **Types Layer Section**

###

### *Notification*

Table Annotation: This entity maps to the database table Notifications.

| **Name**      | **Data Type**     | **Description**                                 |
|---------------|------------------|-------------------------------------------------|
| Id            | guid             | Unique identifier for the notification          |
| UserId        | guid             | User to whom the notification is sent           |
| EventType     | string           | Type of event triggering the notification       |
| Content       | string           | Notification content/message                    |
| Channel       | string           | Delivery channel (e.g., Email, In-App)          |
| Status        | string           | Status (e.g., Sent, Read, Failed)               |
| Timestamp     | datetime         | Date/time notification was sent                 |
| PreferenceId  | guid             | Associated notification preference (nullable)   |
| Created       | datetime         | Timestamp when the notification was created     |
| Changed       | datetime         | Timestamp when the notification was last updated|

### *NotificationPreference*

Table Annotation: This entity maps to the database table NotificationPreferences.

| **Name**      | **Data Type**     | **Description**                                 |
|---------------|------------------|-------------------------------------------------|
| Id            | guid             | Unique identifier for the preference            |
| UserId        | guid             | Associated user ID                              |
| EventType     | string           | Event type for which preference applies         |
| Channel       | string           | Preferred channel (e.g., Email, In-App)         |
| Enabled       | bool             | Whether notifications are enabled               |
| UpdatedAt     | datetime         | Date/time preference was last updated           |
| Created       | datetime         | Timestamp when the preference was created       |
| Changed       | datetime         | Timestamp when the preference was last updated  |

### *NotificationDto*

| **Name**      | **Data Type**                | **Description**                                 |
|---------------|-----------------------------|-------------------------------------------------|
| Id            | guid                        | Unique identifier for the notification          |
| User          | UserDto                     | User entity                                     |
| EventType     | string                      | Type of event triggering the notification       |
| Content       | string                      | Notification content/message                    |
| Channel       | string                      | Delivery channel (e.g., Email, In-App)          |
| Status        | string                      | Status (e.g., Sent, Read, Failed)               |
| Timestamp     | datetime                    | Date/time notification was sent                 |
| Preference    | NotificationPreferenceDto   | Associated notification preference (nullable)   |
| Created       | datetime                    | Timestamp when the notification was created     |
| Changed       | datetime                    | Timestamp when the notification was last updated|

### *NotificationPreferenceDto*

| **Name**      | **Data Type**     | **Description**                                 |
|---------------|------------------|-------------------------------------------------|
| Id            | guid             | Unique identifier for the preference            |
| User          | UserDto          | Associated user                                 |
| EventType     | string           | Event type for which preference applies         |
| Channel       | string           | Preferred channel (e.g., Email, In-App)         |
| Enabled       | bool             | Whether notifications are enabled               |
| UpdatedAt     | datetime         | Date/time preference was last updated           |
| Created       | datetime         | Timestamp when the preference was created       |
| Changed       | datetime         | Timestamp when the preference was last updated  |

### *CreateNotificationDto*

| **Name**      | **Data Type**     | **Description**                                 |
|---------------|------------------|-------------------------------------------------|
| UserId        | guid             | User to whom the notification is sent           |
| EventType     | string           | Type of event triggering the notification       |
| Content       | string           | Notification content/message                    |
| Channel       | string           | Delivery channel (e.g., Email, In-App)          |
| PreferenceId  | guid             | Associated notification preference (nullable)   |

### *UpdateNotificationDto*

| **Name**      | **Data Type**     | **Description**                                 |
|---------------|------------------|-------------------------------------------------|
| Id            | guid             | Unique identifier for the notification          |
| UserId        | guid             | User to whom the notification is sent           |
| EventType     | string           | Type of event triggering the notification       |
| Content       | string           | Notification content/message                    |
| Channel       | string           | Delivery channel (e.g., Email, In-App)          |
| Status        | string           | Status (e.g., Sent, Read, Failed)               |
| PreferenceId  | guid             | Associated notification preference (nullable)   |

### *DeleteNotificationDto*

| **Name**      | **Data Type**     | **Description**                                 |
|---------------|------------------|-------------------------------------------------|
| Id            | guid             | Unique identifier for the notification          |
| FieldsToDelete| List<string>     | List of fields to be deleted                    |

### *NotificationRequestDto*

| **Name**      | **Data Type**     | **Description**                                 |
|---------------|------------------|-------------------------------------------------|
| Id            | guid             | Notification Id. It can be null.                |
| UserId        | guid             | User Id. It can be null.                        |

### *MetadataDto*

| **Name**      | **Data Type**     | **Description**                                 |
|---------------|------------------|-------------------------------------------------|
| PageLimit     | int              | Page limit                                      |
| PageOffset    | int              | Page offset                                     |
| Total         | int              | Total number of pages                           |

### *ReturnListNotificationDto*

| **Name**      | **Data Type**             | **Description**                                |
|---------------|--------------------------|------------------------------------------------|
| Data          | List<NotificationDto>     | List of NotificationDto objects                |
| Metadata      | MetadataDto               | Pagination parameters                          |

8. **Mapping Definitions Section**

### CreateNotificationDto to Notification

Source: CreateNotificationDto

Target: Notification

Map: CreateNotificationDto to Notification

| **Source**      | **Target**      | **Mapping Details**                                      |
|-----------------|-----------------|----------------------------------------------------------|
| -               | Id              | Guid.NewGuid()                                           |
| UserId          | UserId          | Direct Mapping                                           |
| EventType       | EventType       | Direct Mapping                                           |
| Content         | Content         | Direct Mapping                                           |
| Channel         | Channel         | Direct Mapping                                           |
| PreferenceId    | PreferenceId    | Conditional mapping (Create or mapping null)             |
| -               | Status          | Default: "Sent"                                          |
| -               | Timestamp       | DateTime.Now                                             |
| -               | Created         | DateTime.Now                                             |
| -               | Changed         | null                                                     |

### CreateNotificationDto to NotificationDto

Source: CreateNotificationDto

Target: NotificationDto

Map: CreateNotificationDto to NotificationDto

| **Source**      | **Target**      | **Mapping Details**                                      |
|-----------------|-----------------|----------------------------------------------------------|
| UserId          | User             | Fetch UserDto object if available. Otherwise null.       |
| EventType       | EventType        | Direct Mapping                                           |
| Content         | Content          | Direct Mapping                                           |
| Channel         | Channel          | Direct Mapping                                           |
| PreferenceId    | Preference       | Fetch NotificationPreferenceDto if available. Otherwise null. |
| -               | Status           | Default: "Sent"                                          |
| -               | Timestamp        | DateTime.Now                                             |
| -               | Created          | DateTime.Now                                             |
| -               | Changed          | null                                                     |

### Notification to NotificationDto

Source: Notification

Target: NotificationDto

Map: Notification to NotificationDto

| **Source**      | **Target**      | **Mapping Details**                                      |
|-----------------|-----------------|----------------------------------------------------------|
| Id              | Id              | Direct Mapping                                           |
| UserId          | User            | Fetch UserDto object if available. Otherwise null.       |
| EventType       | EventType       | Direct Mapping                                           |
| Content         | Content         | Direct Mapping                                           |
| Channel         | Channel         | Direct Mapping                                           |
| Status          | Status          | Direct Mapping                                           |
| Timestamp       | Timestamp       | Direct Mapping                                           |
| PreferenceId    | Preference      | Fetch NotificationPreferenceDto if available. Otherwise null. |
| Created         | Created         | Direct Mapping                                           |
| Changed         | Changed         | Direct Mapping                                           |

### UpdateNotificationDto to Notification

Source: UpdateNotificationDto

Target: Notification

Map: UpdateNotificationDto to Notification

| **Source**      | **Target**      | **Mapping Details**                                      |
|-----------------|-----------------|----------------------------------------------------------|
| Id              | Id              | Direct Mapping                                           |
| UserId          | UserId          | Conditional Mapping (Direct Mapping or No Change)        |
| EventType       | EventType       | Conditional Mapping (Direct Mapping or No Change)        |
| Content         | Content         | Conditional Mapping (Direct Mapping or No Change)        |
| Channel         | Channel         | Conditional Mapping (Direct Mapping or No Change)        |
| Status          | Status          | Conditional Mapping (Direct Mapping or No Change)        |
| PreferenceId    | PreferenceId    | Conditional Mapping (Direct Mapping or No Change)        |
| -               | Changed         | DateTime.Now                                             |

### UpdateNotificationDto to NotificationDto

Source: UpdateNotificationDto

Target: NotificationDto

Map: UpdateNotificationDto to NotificationDto

| **Source**      | **Target**      | **Mapping Details**                                      |
|-----------------|-----------------|----------------------------------------------------------|
| Id              | Id              | Direct Mapping                                           |
| UserId          | User            | Fetch UserDto object if available. Otherwise null.       |
| EventType       | EventType       | Direct Mapping                                           |
| Content         | Content         | Direct Mapping                                           |
| Channel         | Channel         | Direct Mapping                                           |
| Status          | Status          | Direct Mapping                                           |
| PreferenceId    | Preference      | Fetch NotificationPreferenceDto if available. Otherwise null. |
| -               | Changed         | DateTime.Now                                             |

### ListNotificationRequestDto to ReturnListNotificationDto

Source: ListNotificationRequestDto

Target: ReturnListNotificationDto

Map: ListNotificationRequestDto to ReturnListNotificationDto

| **Source**      | **Target**           | **Mapping Details**                                    |
|-----------------|---------------------|--------------------------------------------------------|
| PageLimit       | Metadata.PageLimit  | Provided pageLimit value.                              |
| PageOffset      | Metadata.PageOffset | Provided pageOffset value.                             |

### PagedResult to ReturnListNotificationDto

Source: pagedResult

Target: ReturnListNotificationDto

Map: pagedResult to ReturnListNotificationDto

| **Source**      | **Target**           | **Mapping Details**                                    |
|-----------------|---------------------|--------------------------------------------------------|
| Records         | Data                | ToList()                                               |
| TotalRecords    | Metadata.Total      | pagedResult.TotalRecords                               |

### DeleteNotificationDto to Notification

Source: DeleteNotificationDto

Target: Notification

Map: DeleteNotificationDto to Notification

| **Source**      | **Target**           | **Mapping Details**                                    |
|-----------------|---------------------|--------------------------------------------------------|
| Id              | Id                  | Direct Mapping                                         |
| FieldsToDelete  | -                   | Used to determine which fields to null or remove       |

9. **Implementation Layer Section**
# **Implementation Layer Section**

## *NotificationService*

---

### *Create*

Creates a notification with the specified details

| **Arguments** | [CreateNotificationDto](#createnotificationdto) request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**

1. **Validate** the request and its parameters:
   1. “UserId”, “EventType”, “Content”, and “Channel” must not be null or empty.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Authorization Check:**
   1. Validate that the user has the permission to perform the Create operation.
3. **Fetch and Validate User**:
   1. Retrieve the User by UserId.
   2. If not found, throw the [DP-404](#_dp-404) exception.
4. **Fetch and Validate NotificationPreference** (if PreferenceId is provided):
   1. Retrieve the NotificationPreference by PreferenceId.
   2. If not found, throw the [DP-404](#_dp-404) exception.
5. **Map** the Notification based on the CreateNotificationDto to Notification from the Mapping Definition Section.
6. **Perform Database Operations**:
   1. Insert the Notification into the database.
   2. Retrieve the Notification by Id.
   3. Handle errors during insertions or retrievals by throwing the [DP-500](#_dp-500) exception.
   4. If not found, throw the [DP-404](#_dp-404) exception.
   5. Return the Notification’s Id.

---

### *Get*

Get the specified notification

| **Arguments** | [NotificationRequestDto](#notificationrequestdto) request, Guid userId |
| --- | --- |
| **Return value** | [NotificationDto](#notificationdto) |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Fetch Notification:**
   1. Retrieve Notification by Id.
   2. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
   3. If not found, throw the [DP-404](#_dp-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
4. **Fetch and Validate User**:
   1. Try to fetch the User by UserId.
   2. Handle errors during fetching by logging the error and continue.
5. **Fetch NotificationPreference**:
   1. If notification.PreferenceId is not null,
      1. Try to fetch the NotificationPreference by PreferenceId.
      2. Handle errors during fetching by logging the error and continue.
6. **Map** the NotificationDto based on the Notification to NotificationDto from the Mapping Definition Section.
7. **Return** the notificationDto.

---

### *Update*

Updates a notification with the specified details

| **Arguments** | [UpdateNotificationDto](#updatenotificationdto) request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**:

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. At least one updatable field (“EventType”, “Content”, “Channel”, “Status”, “PreferenceId”) must be provided.
   3. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Fetch Notification**:
   1. Retrieve Notification by Id.
   2. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
   3. If not found, throw the [DP-404](#_dp-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Update operation.
4. **Fetch and Validate User:**
   1. If request.UserId is not null,
      1. Fetch the User by UserId.
      2. If not found, throw the [DP-404](#_dp-404) exception.
5. **Fetch and Validate NotificationPreference:**
   1. If request.PreferenceId is not null,
      1. Fetch the NotificationPreference by PreferenceId.
      2. If not found, throw the [DP-404](#_dp-404) exception.
6. **Map** the Notification based on the UpdateNotificationDto to Notification from the Mapping Definition Section.
7. **Perform Database Operations**:
   1. Update the Notification by Id.
   2. Retrieve the Notification by Id.
   3. Handle errors during updates or retrievals by throwing the [DP-500](#_dp-500) exception.
   4. If not found, throw the [DP-404](#_dp-404) exception.
   5. Return the Notification’s Id.

---

### *Delete*

Deletes a notification with the specified details

| **Arguments** | [DeleteNotificationDto](#deletenotificationdto) request, Guid userId |
| --- | --- |
| **Return value** | bool |

**Implementation**:

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Fetch Notification:**
   1. Retrieve the Notification by Id.
   2. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
   3. If not found, throw the [DP-404](#_dp-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Delete operation.
4. **Perform Database Operations:**
   1. If request.FieldsToDelete is null:
      1. Perform a complete deletion:
         1. Delete the Notification from the database.
      2. Return true.
   2. Else If request.FieldsToDelete is not null:
      1. Perform a partial deletion:
         1. For each field in request.FieldsToDelete:
            1. Nullify the specified field for the notification (excluding "Id", "UserId").
      2. Update the Notification in the database.
      3. Return true.
   3. Handle errors during deletions by throwing the [DP-500](#_dp-500) exception.
5. Return false.

---

### *GetList*

Get a notification list with the specified details

| **Arguments** | [ListNotificationRequestDto](#listnotificationrequestdto) request, Guid userId |
| --- | --- |
| **Return value** | [ReturnListNotificationDto](#returnlistnotificationdto) |

**Implementation**:

1. **Validate** the request and its parameters:
   1. "PageLimit” must not be null or <= 0.
   2. “PageOffset” must not be null or < 0.
   3. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
3. **Retrieve Paged Notifications:**
   1. Fetch paged Notifications with filters using the database service and the following parameters:
      1. Pagination: Use request.PageLimit and request.PageOffset for PageSize and Offset.
      2. Sorting: Use request.SortField and request.SortOrder if provided.
      3. If request.SearchTerm is not null, then Search = request.SearchTerm.
      4. Filters:
         1. If request.UserId is not null, add “UserId” to the filters.
         2. If request.Status is not null, add “Status” to the filters.
         3. If request.EventType is not null, add “EventType” to the filters.
         4. If request.Channel is not null, add “Channel” to the filters.
      5. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
4. **Map** the ReturnListNotificationDto based on the ListNotificationRequestDto and paged result to ReturnListNotificationDto from the Mapping Definition Section.
5. Return the ReturnListNotificationDto object.

---

## *Core Service Dependencies*

| **Service** | **Method** | **Arguments** | **Return value** | **Argument Details** |
| --- | --- | --- | --- | --- |
| IUserService | Get | UserRequestDto request, Guid userId | UserDto | UserRequestDto:  - Id (guid): Unique identifier for the user |
| INotificationPreferenceService | Get | NotificationPreferenceRequestDto request, Guid userId | NotificationPreferenceDto | NotificationPreferenceRequestDto: - Id (guid): Unique identifier for the NotificationPreference |

---

## **API Exceptions**

| **Code** | **Description** | **Category** |
| --- | --- | --- |
| **DP-500** | Technical Error | Technical |
| **DP-422** | Client Error | Business |
| **DP-404** | Technical Error | Technical |
| **DP-400** | Technical Error | Technical |

---

10. **API Exceptions Section**
    - Error codes and descriptions

11. **Interface Layer Section**

## *INotificationService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | [CreateNotificationDto](#createnotificationdto) request, Guid userId | string |
| Get | [NotificationRequestDto](#notificationdto) request, Guid userId | [NotificationDto](#notificationdto) |
| Update | [UpdateNotificationDto](#updatenotificationdto) request, Guid userId | string |
| GetList | [ListNotificationRequestDto](#listnotificationrequestdto) request, Guid userId | [ReturnListNotificationDto](#returnlistnotificationdto) |

    - Service interfaces and method definitions

12. **Controller Layer Section**

## *NotificationController*

### /notification/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | [Request](#request)<[CreateNotificationDto](#createnotificationdto)> |
| **Response** | [Response](#response)<string> |

###

### /notification/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | [Request](#request)<[NotificationRequestDto](#notificationdto)> |
| **Response** | [Response](#response)<[NotificationDto](#notificationdto)> |

###

### /notification/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | [Request](#request)<[UpdateNotificationDto](#updatenotificationdto)> |
| **Response** | [Response](#response)<string> |

###

### /notification/list

| **HTTP Request Method** | GET |
| --- | --- |
| **Method** | GetList |
| **Request** | [Request](#request)<[ListNotificationRequestDto](#listnotificationrequestdto)> |
| **Response** | [Response](#response)<[ReturnListNotificationDto](#returnlistnotificationdto)> |

---