**API Technical Design
Developers Portal**
Domain: Image

Document Version: 1.1
Last Updated: 2025-07-29
Testing Status: ✅ Validated

#

#

Section Headers **

Subsection Headers*

End of Section - - -

# **Overview**

The purpose of this documentation is to describe in detail the functionality of the Core Developers Portal backend API for the Image domain.

The Image system provides a generic, reusable image management solution that can attach images to any entity in the SmartBoat platform (vessels, sensors, companies, customers, etc.) using a universal EntityId approach. Images are stored as binary data (varbinary) in the database with proper content type handling and metadata.

Key features:
- Generic entity association via EntityId
- Binary storage (varbinary) for efficient data handling
- Multipart form data upload support
- JWT-based authentication and user tracking
- Complete CRUD operations
- Secure file handling with content type validation

- - -

# **Web API Ground Rules Section**

## *Requests*

The Image API uses a hybrid approach due to file upload requirements:

**Standard JSON Requests** (for listing and deletion):
Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint's technical description.

**Example Request**

{
  "header": {
    "ID": "{{$guid}}",
    "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
    "bank": "NBG",
    "UserId": "{{$user_guid}}"
  },
  "payload": {}
}

**Multipart Form Requests** (for file uploads):
File upload endpoints use multipart/form-data with direct parameter binding due to IFormFile requirements. User authentication is handled via JWT token extraction.

* request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
* request.Header.application is a GUID for each application that invokes our web API.
* request.Header.bank always has the value "BANK"
* request.Header.UserId is the GUID Id for each user.

## *Responses*

Each API response is wrapped in a Response object.

* All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
* In case of success (no exception thrown), the response.payload is described in each endpoint's technical description. The response.exception = null
* Binary image data endpoints return FileContentResult directly with proper content type headers

**Example Response**

{
  "payload": {},
  "exception": {
    "id": "guid",
    "code": "string",
    "description": "string"
  }
}

## *Endpoint Execution Logic*

All endpoints are asynchronous.

No matter what happens during the execution of an endpoint's logic, the HTTP response code is always 200-OK (except for binary file downloads). To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.

SafeExecutor is a static class.

SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.

Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:

* Code: string
* Description: string

When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of "1001" and description "A technical exception has occurred, please contact your system administrator".

Each endpoint's logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named "Create", there should be a method "Create" defined in the interface and implemented in the service layer. Types, Interfaces and Services are defined in separate files.

## *Authentication*

Image endpoints use JWT token authentication. For multipart form uploads, the user ID is extracted directly from the JWT token in the Authorization header, as the standard middleware only processes JSON request bodies.

## *Database Layer Rules*

Dapper ORM is used to access, add, update or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as "Repositories". Method signatures must describe what the method is about. For example,

| Task<Image> SelectByIdAsync(Guid imageId) |
| --- |

The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.

Also, in terms of database structure, we never use foreign keys. The Image system uses EntityId for generic entity association without enforcing referential integrity.

- - -

# **Common Types Section**

| **Request** | |
| --- | --- |
| Field Name | Type |
| Header | [RequestHeader](#_requestheader) |
| Payload | T |

| **RequestHeader** | |
| --- | --- |
| Field Name | Type |
| Id | guid (Always new guid) |
| Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is **03FC0B90-DFAD-11EE-8D86-0800200C9A66** |
| Bank | string |
| UserId | guid |

| **Response** | |
| --- | --- |
| Field Name | Type |
| Payload | T |
| Exception | [ResponseException](#_responseexception) |

| **ResponseException** | |
| --- | --- |
| Field Name | Type |
| Id | guid |
| Code | string |
| Description | string |
| Category | string |

#

- - -

# **Database Layer Section**

| **Database** | **Description** |
| --- | --- |
| Smartboat | Provides a detailed structure of SmartBoat platform tables including field names, data types, and constraints. |

## *Environments*

| **Environment** | **Database Server** | **Database** |
| --- | --- | --- |
| Development | Docker SQL Server | Smartboat |
| QA |  |  |
| Production |  |  |

#

## *DB Tables*

### *Images*

| **Name** | **Data Type** | **Nullable** | **Unique** | **Description** |
| --- | --- | --- | --- | --- |
| Id | uniqueidentifier | false | true | Unique entry's identifier to the table |
| EntityId | uniqueidentifier | false | false | Generic entity identifier (vessel, sensor, company, etc.) |
| ImageData | varbinary(max) | false | false | Binary image data |
| FileName | nvarchar(255) | false | false | Original filename from upload |
| ContentType | nvarchar(100) | false | false | MIME type (image/jpeg, image/png, etc.) |
| UploadedDate | datetime2(7) | false | false | Date/time when image was uploaded |
| UploadedBy | uniqueidentifier | false | false | User ID who uploaded the image |

- - -

# **Types Layer Section**

###

### *Image*

Table Annotation: This entity maps to the database table Images.

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry's identifier |
| EntityId | guid | Generic entity identifier (vessel, sensor, company, etc.) |
| ImageData | byte[] | Binary image data |
| FileName | string | Original filename from upload |
| ContentType | string | MIME type (image/jpeg, image/png, etc.) |
| UploadedDate | datetime | Date/time when image was uploaded |
| UploadedBy | guid | User ID who uploaded the image |

### *ImageDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry's identifier |
| EntityId | guid | Generic entity identifier |
| FileName | string | Original filename from upload |
| ContentType | string | MIME type (image/jpeg, image/png, etc.) |
| UploadedDate | datetime | Date/time when image was uploaded |
| UploadedBy | guid | User ID who uploaded the image |

### *CreateImageDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| EntityId | guid | Generic entity identifier |
| ImageFile | IFormFile | Uploaded image file |
| UploadedBy | guid | User ID who uploaded the image |

###

- - -

#

#

# **Mapping Definitions Section**

### CreateImageDto to Image

Source: CreateImageDto

Target: Image

Map: CreateImageDto to Image

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| - | Id | Guid.NewGuid() |
| EntityId | EntityId | Direct Mapping |
| ImageFile.ContentStream | ImageData | Convert IFormFile stream to byte array |
| ImageFile.FileName | FileName | Direct Mapping |
| ImageFile.ContentType | ContentType | Direct Mapping |
| UploadedBy | UploadedBy | Direct Mapping |
| - | UploadedDate | DateTime.UtcNow |

### Image to ImageDto

Source: Image

Target: ImageDto

Map: Image to ImageDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct Mapping |
| EntityId | EntityId | Direct Mapping |
| FileName | FileName | Direct Mapping |
| ContentType | ContentType | Direct Mapping |
| UploadedDate | UploadedDate | Direct Mapping |
| UploadedBy | UploadedBy | Direct Mapping |

###

- - -

#

#

# **Implementation Layer Section**

## *ImageService*

###

### *Create*

Creates an image record with binary data for the specified entity

| **Arguments** | [CreateImageDto](#_createimagedto) createImageDto |
| --- | --- |
| **Return value** | string |

**Implementation**

1. **Validate** the request and its parameters:
   1. "EntityId" must not be null or empty.
   2. "ImageFile" must not be null and must be a valid image file.
   3. "UploadedBy" must not be null.
   4. If the request or the necessary parameters are null or invalid, throw the appropriate exception.
2. Generate a new GUID for the image Id.
3. **Process File Upload:**
   1. Read the IFormFile stream into a byte array using MemoryStream.
   2. Validate file size and content type (image/* only).
4. **Map** the Image based on the CreateImageDto to Image from the Mapping Definition Section.
5. **Perform Database Operations**:
   1. Insert the Image using _databaseService.InsertAsync.
   2. Retrieve Image by Id to confirm creation.
   3. Handle errors during insertions or retrievals by throwing exceptions.
   4. If not found after insertion, throw exception.
   5. Return the Image's Id as string.

###

### *GetByEntityId*

Get all images associated with a specific entity

| **Arguments** | Guid entityId |
| --- | --- |
| **Return value** | List<[ImageDto](#_imagedto)> |

**Implementation**

1. **Validate** the entityId parameter:
   1. "EntityId" must not be null or empty.
   2. If invalid, throw the appropriate exception.
2. **Fetch Images:**
   1. Retrieve all Images by EntityId using _databaseService.SelectAsync.
   2. Handle errors during retrievals by throwing exceptions.
   3. If no images found, return empty list.
3. **Map Results:**
   1. For each Image, map to ImageDto using Image to ImageDto mapping.
   2. Order results by UploadedDate.
4. **Return** the List<ImageDto>.

###

### *GetImageData*

Get the binary image data for serving/download

| **Arguments** | Guid imageId |
| --- | --- |
| **Return value** | [Image](#_image) |

**Implementation**

1. **Validate** the imageId parameter:
   1. "ImageId" must not be null.
   2. If invalid, throw the appropriate exception.
2. **Fetch Image:**
   1. Retrieve Image by Id using _databaseService.SelectByIdAsync.
   2. Handle errors during retrievals by throwing exceptions.
   3. If not found, throw not found exception.
3. **Return** the complete Image object with binary data.

###

### *Delete*

Deletes an image record and its binary data

| **Arguments** | Guid imageId, Guid userId |
| --- | --- |
| **Return value** | bool |

**Implementation**:

1. **Validate** the parameters:
   1. "ImageId" must not be null.
   2. "UserId" must not be null.
   3. If invalid, throw the appropriate exception.
2. **Verify Image Exists:**
   1. Retrieve the Image by Id using _databaseService.SelectByIdAsync.
   2. Handle errors during retrievals by throwing exceptions.
   3. If not found, throw not found exception.
3. **Perform Database Operations:**
   1. Delete the Image using _databaseService.DeleteAsync.
   2. Handle errors during deletion by throwing exceptions.
   3. Log the deletion with userId for audit purposes.
4. Return true on successful deletion.

## *Core Service Dependencies*

| **Service** | **Method** | **Arguments** | **Return value** | **Argument Details** |
| --- | --- | --- | --- | --- |
| IDatabaseService | InsertAsync<Image> | Image image | Task | Generic database insert operation |
| IDatabaseService | SelectAsync<Image> | object filter | Task<IEnumerable<Image>> | Generic database select with filters |
| IDatabaseService | SelectByIdAsync<Image, Guid> | Guid id | Task<Image> | Generic database select by ID |
| IDatabaseService | DeleteAsync<Image> | object filter | Task | Generic database delete with filters |

- - -

#

#

#

# **API Exceptions**

| **Code** | **Description** | **Category** |
| --- | --- | --- |
| **DP-500** | Technical Error | Technical |
| **DP-422** | Client Error | Business |
| **DP-404** | Technical Error | Technical |
| **DP-400** | Technical Error | Technical |

- - -

#

#

#

#

#

# **Interface Layer Section**

## *IImageService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | [CreateImageDto](#_createimagedto) createImageDto | string |
| GetByEntityId | Guid entityId | List<[ImageDto](#_imagedto)> |
| GetImageData | Guid imageId | [Image](#_image) |
| Delete | Guid imageId, Guid userId | bool |

- - -

#

#

# **Controller Layer Section**

## *ImageController*

### /api/image/upload/{entityId}

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Upload |
| **Request** | Multipart Form Data: entityId (path), imageFile (form), userId (extracted from JWT) |
| **Response** | [Response](#_response)<string> |

### /api/image/entity/{entityId}

| **HTTP Request Method** | GET |
| --- | --- |
| **Method** | GetByEntityId |
| **Request** | entityId (path parameter) |
| **Response** | [Response](#_response)<List<[ImageDto](#_imagedto)>> |

### /api/image/{imageId}

| **HTTP Request Method** | GET |
| --- | --- |
| **Method** | GetImage |
| **Request** | imageId (path parameter) |
| **Response** | FileContentResult (binary image data with proper content type headers) |

### /api/image/{imageId}

| **HTTP Request Method** | DELETE |
| --- | --- |
| **Method** | Delete |
| **Request** | imageId (path parameter), userId (extracted from JWT) |
| **Response** | [Response](#_response)<bool> |

- - -

# **API Testing & Validation Section**

## *Testing Environment*

| **Environment** | **API Base URL** | **Authentication** | **Status** |
| --- | --- | --- | --- |
| Development | https://localhost:7001 | JWT Bearer Token | ✅ Tested |

## *Endpoint Validation Results*

### *POST /api/image/upload/{entityId}*

**Test Status**: ✅ **PASS**

**Test Details**:
- **Endpoint**: `POST https://localhost:7001/api/image/upload/123e4567-e89b-12d3-a456-426614174000`
- **Authentication**: JWT Bearer token successfully validated
- **Request Format**: Multipart form data with imageFile parameter
- **Response**: `{"payload":"f337a68d-a050-43ba-83d6-67cd3b796a67","exception":null}`
- **Validation**: Successfully uploaded image and returned generated image ID

### *GET /api/image/entity/{entityId}*

**Test Status**: ✅ **PASS**

**Test Details**:
- **Endpoint**: `GET https://localhost:7001/api/image/entity/123e4567-e89b-12d3-a456-426614174000`
- **Authentication**: JWT Bearer token successfully validated
- **Empty Entity Response**: `{"payload":[],"exception":null}`
- **With Images Response**: 
```json
{
  "payload": [
    {
      "id": "f337a68d-a050-43ba-83d6-67cd3b796a67",
      "entityId": "123e4567-e89b-12d3-a456-426614174000",
      "fileName": "test_image.jpg",
      "contentType": "image/jpeg",
      "uploadedDate": "2025-07-29T22:38:56.8566667",
      "uploadedBy": "66779540-2295-4deb-b26a-e697d471c202"
    }
  ],
  "exception": null
}
```
- **Validation**: Successfully retrieved image metadata with proper ImageDto mapping

### *GET /api/image/{imageId}*

**Test Status**: ✅ **PASS**

**Test Details**:
- **Endpoint**: `GET https://localhost:7001/api/image/f337a68d-a050-43ba-83d6-67cd3b796a67`
- **Authentication**: JWT Bearer token successfully validated
- **Response**: Binary image data returned correctly
- **Content Type**: Proper content type headers applied
- **Validation**: Successfully downloaded binary image data matching uploaded content

### *DELETE /api/image/{imageId}*

**Test Status**: ✅ **PASS**

**Test Details**:
- **Endpoint**: `DELETE https://localhost:7001/api/image/f337a68d-a050-43ba-83d6-67cd3b796a67`
- **Authentication**: JWT Bearer token successfully validated
- **Response**: `{"payload":true,"exception":null}`
- **Validation**: Successfully deleted image, confirmed by subsequent entity query returning empty array

## *Authentication Validation*

**JWT Token Extraction**: ✅ **PASS**
- User ID successfully extracted from JWT token: `66779540-2295-4deb-b26a-e697d471c202`
- No manual userId parameter required for upload and delete operations
- Token validation working correctly across all endpoints

## *Data Flow Validation*

**Complete CRUD Cycle**: ✅ **PASS**
1. **Create**: Image uploaded and stored with binary data
2. **Read**: Image metadata retrieved via entity association
3. **Read**: Binary image data downloaded successfully
4. **Delete**: Image removed and confirmed via entity query

**Generic Entity Association**: ✅ **PASS**
- EntityId approach allows attachment to any platform entity
- No foreign key constraints enforced as per design specifications

## *Error Handling Validation*

**Response Format**: ✅ **PASS**
- All responses follow consistent `{"payload": ..., "exception": null}` format
- SafeExecutor pattern successfully implemented
- HTTP 200 status codes returned as specified

## *Testing Summary*

- **Total Endpoints Tested**: 4/4
- **Success Rate**: 100%
- **Authentication**: Fully functional
- **Data Integrity**: Validated
- **API Design Compliance**: Full compliance with technical specifications

**Test Date**: 2025-07-29  
**Tested By**: Automated API testing  
**Environment**: Development (Docker SQL Server)

- - -