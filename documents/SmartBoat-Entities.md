# SmartBoat Fleet Management API – Entities

This document defines the logical data entities used by the SmartBoat Fleet Management .NET API System. Each entity represents a data contract for API requests and responses, including all key attributes and relationships.

---

## User

| Name               | Data Type | Description                                      |
|--------------------|-----------|--------------------------------------------------|
| `id`               | number    | Unique identifier for the user (primary key)     |
| `name`             | string    | Full name of the user                            |
| `email`            | string    | Email address of the user                        |
| `roleId`           | number    | Role assigned to the user (foreign key)          |
| `avatar`           | string    | URL or path to the user's avatar image           |
| `joined`           | string    | Date/time when the user joined (ISO 8601)        |
| `status`           | string    | Current status (e.g., Active, Inactive)          |
| `lastLogin`        | string    | Date/time of last login (ISO 8601)               |
| `twoFactorEnabled` | boolean   | Whether two-factor authentication is enabled     |
| `company`          | string    | Name of the company the user belongs to          |
| `department`       | string    | Department within the company                    |
| `phone`            | string    | Contact phone number                             |
| `timezone`         | string    | User's preferred timezone                        |
| `language`         | string    | Preferred language code (e.g., 'en', 'el')       |
| `bio`              | string    | Short biography or description                   |

---

## Role

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the role (primary key)     |
| `name`         | string    | Name of the role (e.g., Admin, Manager)          |
| `permissions`  | array     | List of permissions assigned to the role         |
| `description`  | string    | Description of the role                          |
| `createdAt`    | string    | Date/time role was created (ISO 8601)            |
| `updatedAt`    | string    | Date/time role was last updated (ISO 8601)       |

---

## Customer

| Name            | Data Type | Description                                      |
|-----------------|-----------|--------------------------------------------------|
| `id`            | number    | Unique identifier for the customer (primary key) |
| `name`          | string    | Name of the customer                             |
| `contactPerson` | string    | Main contact person for the customer             |
| `email`         | string    | Contact email address                            |
| `phone`         | string    | Contact phone number                             |
| `companies`     | number    | Number of companies owned by the customer        |
| `vessels`       | number    | Number of vessels associated                     |
| `sensors`       | number    | Number of sensors associated                     |
| `status`        | string    | Current status (e.g., Active, Inactive)          |
| `lastActive`    | string    | Date/time of last activity (ISO 8601)            |

---

## Company

| Name        | Data Type | Description                                      |
|-------------|-----------|--------------------------------------------------|
| `id`        | number    | Unique identifier for the company (primary key)  |
| `name`      | string    | Name of the company                              |
| `location`  | string    | Physical location or address                     |
| `industry`  | string    | Industry sector                                  |
| `vessels`   | number    | Number of vessels operated by the company        |
| `sensors`   | number    | Number of sensors managed by the company         |
| `status`    | string    | Current status (e.g., Active, Inactive)          |
| `customerId`| number    | Associated customer ID (foreign key)             |
| `lastUpdated`| string   | Date/time of last update (ISO 8601)              |

---

## Vessel

| Name         | Data Type | Description                                      |
|--------------|-----------|--------------------------------------------------|
| `id`         | number    | Unique identifier for the vessel (primary key)   |
| `name`       | string    | Name of the vessel                               |
| `number`     | string    | Vessel registration or identification number     |
| `type`       | string    | Type/class of vessel                             |
| `location`   | string    | Current or home location                         |
| `status`     | string    | Operational status (e.g., Active, Inactive)      |
| `startDate`  | string    | Date the vessel became operational (ISO 8601)    |
| `endDate`    | string    | Date the vessel was decommissioned (ISO 8601)    |
| `image`      | string    | URL or path to vessel image                      |
| `onsigners`  | number    | Number of crew currently onboard                 |
| `offsigners` | number    | Number of crew offboarded                        |
| `sensors`    | number    | Number of sensors equipped                       |
| `lastUpdated`| string    | Date/time of last update (ISO 8601)              |
| `companyId`  | number    | Associated company ID (foreign key)              |

---

## Sensor

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the sensor (primary key)   |
| `name`         | string    | Name or label of the sensor                      |
| `type`         | string    | Type/category of the sensor                      |
| `vessel`       | string    | Name or ID of the vessel the sensor is on        |
| `location`     | string    | Physical location on the vessel                  |
| `status`       | string    | Operational status (e.g., Active, Faulty)        |
| `lastReading`  | string    | Timestamp of the last reading (ISO 8601)         |
| `lastUpdated`  | string    | Date/time of last update (ISO 8601)              |
| `alertThreshold`| string   | Configured alert threshold value(s)              |

---

## Subscription

| Name            | Data Type | Description                                      |
|-----------------|-----------|--------------------------------------------------|
| `id`            | number    | Unique identifier for the subscription (PK)      |
| `name`          | string    | Name of the subscription plan                    |
| `type`          | string    | Type/category of subscription                    |
| `customerId`    | number    | Associated customer ID (foreign key)             |
| `customerName`  | string    | Name of the customer                             |
| `startDate`     | string    | Subscription start date (ISO 8601)               |
| `endDate`       | string    | Subscription end date (ISO 8601)                 |
| `price`         | number    | Price of the subscription                        |
| `billingFrequency`| string  | Billing frequency (e.g., Monthly, Yearly)        |
| `status`        | string    | Current status (e.g., Active, Cancelled)         |
| `sensorLimit`   | number    | Maximum number of sensors allowed                |
| `features`      | array     | List of enabled features                         |
| `lastUpdated`   | string    | Date/time of last update (ISO 8601)              |

---

## UsageRecord

| Name            | Data Type | Description                                      |
|-----------------|-----------|--------------------------------------------------|
| `id`            | number    | Unique identifier for the usage record (PK)      |
| `subscriptionId`| number    | Associated subscription ID (foreign key)         |
| `customerId`    | number    | Associated customer ID (foreign key)             |
| `userId`        | number    | Associated user ID (foreign key)                 |
| `feature`       | string    | Feature or resource being used                   |
| `usageValue`    | number    | Amount of usage (e.g., number of sensors active) |
| `timestamp`     | string    | Date/time of usage record (ISO 8601)             |

---

## Alert

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the alert (PK)             |
| `type`         | string    | Type of alert (e.g., Sensor, System, Subscription)|
| `entityId`     | number    | ID of the related entity (sensor, vessel, etc.)  |
| `entityType`   | string    | Type of the related entity                       |
| `value`        | string    | Value that triggered the alert                   |
| `threshold`    | string    | Threshold that was crossed                       |
| `status`       | string    | Status of the alert (e.g., Active, Resolved)     |
| `message`      | string    | Alert message                                    |
| `timestamp`    | string    | Date/time alert was generated (ISO 8601)         |
| `deliveredTo`  | array     | List of user IDs or contacts notified            |
| `deliveryStatus`| string   | Delivery status (e.g., Sent, Failed)             |

---

## Notification

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the notification (PK)      |
| `userId`       | number    | User to whom the notification is sent            |
| `eventType`    | string    | Type of event triggering the notification        |
| `content`      | string    | Notification content/message                     |
| `channel`      | string    | Delivery channel (e.g., Email, In-App)           |
| `status`       | string    | Status (e.g., Sent, Read, Failed)                |
| `timestamp`    | string    | Date/time notification was sent (ISO 8601)       |
| `preferenceId` | number    | Associated notification preference (FK)          |

---

## NotificationPreference

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the preference (PK)        |
| `userId`       | number    | Associated user ID (foreign key)                 |
| `eventType`    | string    | Event type for which preference applies          |
| `channel`      | string    | Preferred channel (e.g., Email, In-App)          |
| `enabled`      | boolean   | Whether notifications are enabled                |
| `updatedAt`    | string    | Date/time preference was last updated (ISO 8601) |

---

## AuditLog

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the audit log (PK)         |
| `userId`       | number    | User who performed the action                    |
| `entityType`   | string    | Type of entity affected                          |
| `entityId`     | number    | ID of entity affected                            |
| `action`       | string    | Action performed (e.g., Create, Update, Delete)  |
| `details`      | string    | Details of the change                            |
| `timestamp`    | string    | Date/time of the action (ISO 8601)               |

---

## AssignmentEvent

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the event (PK)             |
| `entityType`   | string    | Type of entity assigned/reassigned               |
| `entityId`     | number    | ID of entity assigned/reassigned                 |
| `fromId`       | number    | Previous parent/entity ID                        |
| `toId`         | number    | New parent/entity ID                             |
| `eventType`    | string    | Type of event (Assignment, Reassignment, Removal)|
| `userId`       | number    | User who performed the action                    |
| `timestamp`    | string    | Date/time of the event (ISO 8601)                |

---

## StatusHistory

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the status history (PK)    |
| `entityType`   | string    | Type of entity (Vessel, Sensor, Subscription, etc.)|
| `entityId`     | number    | ID of the entity                                 |
| `oldStatus`    | string    | Previous status                                  |
| `newStatus`    | string    | New status                                       |
| `userId`       | number    | User who changed the status                      |
| `timestamp`    | string    | Date/time of the status change (ISO 8601)        |

---

## Report

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the report (PK)            |
| `type`         | string    | Type of report (e.g., Usage, Activity, Support)  |
| `criteria`     | string    | Criteria/filters used to generate the report     |
| `generatedBy`  | number    | User who generated the report                    |
| `generatedAt`  | string    | Date/time report was generated (ISO 8601)        |
| `status`       | string    | Status (e.g., Completed, Failed)                 |
| `deliveryChannel`| string  | Channel used for delivery (e.g., Email, Download)|
| `deliveryStatus`| string   | Delivery status (e.g., Sent, Failed)             |

---

## SupportRequest

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the support request (PK)   |
| `userId`       | number    | User who submitted the request                   |
| `subject`      | string    | Subject of the support request                   |
| `description`  | string    | Detailed description of the issue                |
| `status`       | string    | Status (e.g., Open, In Progress, Resolved)       |
| `createdAt`    | string    | Date/time request was created (ISO 8601)         |
| `resolvedAt`   | string    | Date/time request was resolved (ISO 8601)        |
| `resolution`   | string    | Resolution details                               |

---

## Feedback

| Name           | Data Type | Description                                      |
|----------------|-----------|--------------------------------------------------|
| `id`           | number    | Unique identifier for the feedback (PK)          |
| `userId`       | number    | User who submitted the feedback                  |
| `type`         | string    | Type (e.g., Usability, Satisfaction, Bug)        |
| `content`      | string    | Feedback content                                 |
| `submittedAt`  | string    | Date/time feedback was submitted (ISO 8601)      |
| `status`       | string    | Status (e.g., New, Reviewed, Actioned)           |

---

## SensorDataPoint

| Name         | Data Type | Description                                      |
|--------------|-----------|--------------------------------------------------|
| `time`       | string    | ISO 8601 timestamp of the data point             |
| `timestamp`  | number    | Unix timestamp (seconds or ms since epoch)       |
| `temperature`| number    | Temperature reading                              |
| `humidity`   | number    | Humidity reading                                 |

---

## VesselPathPoint

| Name      | Data Type | Description                                      |
|-----------|-----------|--------------------------------------------------|
| `lat`     | number    | Latitude of the vessel at the point              |
| `lng`     | number    | Longitude of the vessel at the point             |
| `timestamp`| string   | ISO 8601 timestamp of the position               |
| `location`| string    | Human-readable location or description           |

---

## Entity Relationships

- **User** has access to **Subscription**
- **User** may be associated with **Customer**
- **User** has one **Role**
- **User** has many **NotificationPreference**
- **User** receives many **Notification**
- **User** submits many **SupportRequest**
- **User** submits many **Feedback**
- **Customer** owns one or more **Company**
- **Customer** purchases one or more **Subscription**
- **Company** operates one or more **Vessel**
- **Vessel** is equipped with one or more **Sensor**
- **Sensor** generates multiple **SensorDataPoint**
- **Vessel** records position as multiple **VesselPathPoint**
- **Alert** is generated for **Sensor**, **Vessel**, **Subscription**, etc.
- **AuditLog** records actions on all entities
- **AssignmentEvent** and **StatusHistory** track changes and assignments for all major entities
- **Report** can be generated for any entity or activity
- **SupportRequest** and **Feedback** are linked to **User**

---

*All data types and relationships are defined for API data contracts and may not directly reflect the underlying database schema.*