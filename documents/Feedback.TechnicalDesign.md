**API Technical Design
Domain: Feedback**
Document Version: 1.1

---

# **Overview**

The purpose of this documentation is to describe in detail the functionality of the Feedback backend API.

---

# **Web API Ground Rules Section**

## *Requests*

Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

**Example Request**

```json
{
  "header": {
    "ID": "{{$guid}}",
    "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
    "bank": "NBG",
    "UserId": "{{$user_guid}}"
  },
  "payload": {}
}
```

- request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
- request.Header.application is a GUID for each application that invokes our web API.
- request.Header.bank always has the value “BANK”
- request.Header.UserId is the GUID Id for each user.

## *Responses*

Each API response is wrapped in a Response object.

- All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
- In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

**Example Response**

```json
{
  "payload": {},
  "exception": {
    "id": "guid",
    "code": "string",
    "description": "string"
  }
}
```

## *Endpoint Execution Logic*

All endpoints are asynchronous.

No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.

SafeExecutor is a static class.

SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.

Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:

- Code: string
- Description: string

When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.

Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateFeedback”, there should be a method “CreateFeedback” defined in the interface and implemented in the service layer. Types, Interfaces and Services are defined in separate files.

## *Database Layer Rules*

Dapper ORM is used to access, add, update or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,

| Task<Feedback> SelectFeedbackAsync(Guid feedbackId) |
| --- |

The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.

Also, in terms of database structure, we never use foreign keys.

---

# **Common Types Section**

| **Request** | |
| --- | --- |
| Field Name | Type |
| Header | RequestHeader |
| Payload | T |

| **RequestHeader** | |
| --- | --- |
| Field Name | Type |
| Id | guid (Always new guid) |
| Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is **03FC0B90-DFAD-11EE-8D86-0800200C9A66** |
| Bank | string |
| UserId | guid |

| **Response** | |
| --- | --- |
| Field Name | Type |
| Payload | T |
| Exception | ResponseException |

| **ResponseException** | |
| --- | --- |
| Field Name | Type |
| Id | guid |
| Code | string |
| Description | string |
| Category | string |

---

# **Database Layer Section**

| **Database** | **Description** |
| --- | --- |
| SmartBoat | Provides a detailed structure of SmartBoat Feedback tables including field names, data types, and constraints. |

## *Environments*

| **Environment** | **Database Server** | **Database** |
| --- | --- | --- |
| Development | (see deployment) | SmartBoat |
| QA |  |  |
| Production |  |  |

## *DB Tables*

### *Feedback*

| **Name**    | **Data Type**     | **Nullable** | **Unique** | **Description**                          |
|-------------|------------------|--------------|------------|------------------------------------------|
| Id          | uniqueidentifier | false        | true       | Unique identifier for the feedback       |
| UserId      | uniqueidentifier | false        | false      | User who submitted the feedback          |
| Type        | nvarchar(50)     | false        | false      | Type (e.g., Usability, Satisfaction, Bug)|
| Content     | nvarchar(1000)   | false        | false      | Feedback content                         |
| SubmittedAt | datetime2(7)     | false        | false      | Date/time feedback was submitted         |
| Status      | nvarchar(50)     | false        | false      | Status (e.g., New, Reviewed, Actioned)   |
| Created     | datetime2(7)     | false        | false      | Timestamp when the feedback was created  |
| Changed     | datetime2(7)     | true         | false      | Timestamp when the feedback was last updated |

---

# **Types Layer Section**

### *Feedback*

Table Annotation: This entity maps to the database table Feedback.

| **Name**    | **Data Type** | **Description**                                   |
|-------------|--------------|---------------------------------------------------|
| Id          | guid         | Unique identifier for the feedback                |
| UserId      | guid         | User who submitted the feedback                   |
| Type        | string       | Type (e.g., Usability, Satisfaction, Bug)         |
| Content     | string       | Feedback content                                  |
| SubmittedAt | datetime     | Date/time feedback was submitted                  |
| Status      | string       | Status (e.g., New, Reviewed, Actioned)            |
| Created     | datetime     | Timestamp when the feedback was created           |
| Changed     | datetime     | Timestamp when the feedback was last updated      |

### *FeedbackDto*

| **Name**    | **Data Type** | **Description**                                   |
|-------------|--------------|---------------------------------------------------|
| Id          | guid         | Unique identifier for the feedback                |
| User        | UserDto      | User who submitted the feedback                   |
| Type        | string       | Type (e.g., Usability, Satisfaction, Bug)         |
| Content     | string       | Feedback content                                  |
| SubmittedAt | datetime     | Date/time feedback was submitted                  |
| Status      | string       | Status (e.g., New, Reviewed, Actioned)            |
| Created     | datetime     | Timestamp when the feedback was created           |
| Changed     | datetime     | Timestamp when the feedback was last updated      |

### *CreateFeedbackDto*

| **Name**    | **Data Type** | **Description**                                   |
|-------------|--------------|---------------------------------------------------|
| UserId      | guid         | User who submits the feedback                     |
| Type        | string       | Type (e.g., Usability, Satisfaction, Bug)         |
| Content     | string       | Feedback content                                  |

### *UpdateFeedbackDto*

| **Name**    | **Data Type** | **Description**                                   |
|-------------|--------------|---------------------------------------------------|
| Id          | guid         | Unique identifier for the feedback                |
| Type        | string       | Type (e.g., Usability, Satisfaction, Bug)         |
| Content     | string       | Feedback content                                  |
| Status      | string       | Status (e.g., New, Reviewed, Actioned)            |

### *DeleteFeedbackDto*

| **Name**    | **Data Type** | **Description**                                   |
|-------------|--------------|---------------------------------------------------|
| Id          | guid         | Unique identifier for the feedback                |

### *FeedbackRequestDto*

| **Name**    | **Data Type** | **Description**                                   |
|-------------|--------------|---------------------------------------------------|
| Id          | guid         | Unique identifier for the feedback (optional)     |
| UserId      | guid         | User who submitted the feedback (optional)        |

### *ListFeedbackRequestDto*

| **Name**    | **Data Type** | **Description**                                   |
|-------------|--------------|---------------------------------------------------|
| PageLimit   | int          | Page limit                                        |
| PageOffset  | int          | Page offset                                       |
| SortField   | string       | Sort field                                        |
| SortOrder   | string       | Sort order                                        |
| Type        | string       | Type filter (optional)                            |
| Status      | string       | Status filter (optional)                          |
| UserId      | guid         | User who submitted the feedback (optional)        |
| SearchTerm  | string       | Search term (optional)                            |

### *MetadataDto*

| **Name**    | **Data Type** | **Description**                                   |
|-------------|--------------|---------------------------------------------------|
| PageLimit   | int          | Page limit                                        |
| PageOffset  | int          | Page offset                                       |
| Total       | int          | Total number of records                           |

### *ReturnListFeedbackDto*

| **Name**    | **Data Type**         | **Description**                              |
|-------------|----------------------|----------------------------------------------|
| Data        | List<FeedbackDto>    | List of FeedbackDto objects                  |
| Metadata    | MetadataDto          | Pagination parameters                        |

---

# **Mapping Definitions Section**

### CreateFeedbackDto to Feedback

Source: CreateFeedbackDto
Target: Feedback
Map: CreateFeedbackDto to Feedback

| **Source**   | **Target**   | **Mapping Details**                                      |
|--------------|--------------|----------------------------------------------------------|
| -            | Id           | Guid.NewGuid()                                           |
| UserId       | UserId       | Direct Mapping                                           |
| Type         | Type         | Direct Mapping                                           |
| Content      | Content      | Direct Mapping                                           |
| -            | SubmittedAt  | DateTime.Now                                             |
| -            | Status       | "New" (Initial status)                                   |
| -            | Created      | DateTime.Now                                             |
| -            | Changed      | null                                                     |

### Feedback to FeedbackDto

Source: Feedback
Target: FeedbackDto
Map: Feedback to FeedbackDto

| **Source**   | **Target**   | **Mapping Details**                                      |
|--------------|--------------|----------------------------------------------------------|
| Id           | Id           | Direct Mapping                                           |
| UserId       | User         | Fetch UserDto by UserId                                  |
| Type         | Type         | Direct Mapping                                           |
| Content      | Content      | Direct Mapping                                           |
| SubmittedAt  | SubmittedAt  | Direct Mapping                                           |
| Status       | Status       | Direct Mapping                                           |
| Created      | Created      | Direct Mapping                                           |
| Changed      | Changed      | Direct Mapping                                           |

### UpdateFeedbackDto to Feedback

Source: UpdateFeedbackDto
Target: Feedback
Map: UpdateFeedbackDto to Feedback

| **Source**   | **Target**   | **Mapping Details**                                      |
|--------------|--------------|----------------------------------------------------------|
| Id           | Id           | Direct Mapping                                           |
| Type         | Type         | Conditional Mapping (Direct Mapping or No Change)        |
| Content      | Content      | Conditional Mapping (Direct Mapping or No Change)        |
| Status       | Status       | Conditional Mapping (Direct Mapping or No Change)        |
| -            | Changed      | DateTime.Now                                             |

### ListFeedbackRequestDto to ReturnListFeedbackDto

Source: ListFeedbackRequestDto
Target: ReturnListFeedbackDto
Map: ListFeedbackRequestDto to ReturnListFeedbackDto

| **Source**   | **Target**         | **Mapping Details**                                  |
|--------------|--------------------|------------------------------------------------------|
| PageLimit    | Metadata.PageLimit | Provided pageLimit value                             |
| PageOffset   | Metadata.PageOffset| Provided pageOffset value                            |

### PagedResult/List<Feedback> to ReturnListFeedbackDto

Source: PagedResult/List<Feedback>
Target: ReturnListFeedbackDto
Map: PagedResult/List<Feedback> to ReturnListFeedbackDto

| **Source**   | **Target**         | **Mapping Details**                                  |
|--------------|--------------------|------------------------------------------------------|
| Records      | Data (List<FeedbackDto>) | Map each Feedback to FeedbackDto using above mapping |
| TotalRecords | Metadata.Total     | Total number of records                              |

---

# **Implementation Layer Section**

## *FeedbackService*

### *Create*

Creates a new feedback entry from a user

| **Arguments** | CreateFeedbackDto request, Guid userId |
| --- | --- |
| **Return value** | FeedbackDto |

**Implementation**

1. **Validate** the request and its parameters:
   1. Ensure `UserId`, `Type`, and `Content` are present and not null or empty.
   2. If any required parameter is missing or invalid, throw the [DP-422](#_feedback_dp422) exception.
2. **Authorization Check:**
   1. Validate that the user has permission to submit feedback.
3. **Fetch and Validate User:**
   1. Retrieve the user by `UserId` using the User service.
   2. If the user does not exist, throw the [DP-404](#_feedback_dp404) exception.
4. **Map Input Data to Entity:**
   1. Map the request to a new Feedback entity:
      - Generate a new Id (Guid).
      - Set `UserId`, `Type`, and `Content` from the request.
      - Set `SubmittedAt` and `Created` to the current timestamp.
      - Set `Status` to "New".
      - Set `Changed` to null.
5. **Perform Database Operations:**
   1. Insert the new Feedback entity into the Feedback table.
   2. Handle errors during insertion by throwing a [DP-500](#_feedback_dp500) exception.
6. **Map to FeedbackDto:**
   1. Fetch the created Feedback entity by Id.
   2. Map the Feedback entity to FeedbackDto, including fetching the UserDto.
7. **Return** the FeedbackDto.

### *Get*

Get the specified feedback entry

| **Arguments** | FeedbackRequestDto request, Guid userId |
| --- | --- |
| **Return value** | FeedbackDto |

**Implementation**

1. **Validate** the request and its parameters:
   1. Ensure `Id` is present and not null.
   2. If missing or invalid, throw the [DP-422](#_feedback_dp422) exception.
2. **Fetch Feedback:**
   1. Retrieve the Feedback entity by Id.
   2. If not found, throw the [DP-404](#_feedback_dp404) exception.
   3. Handle errors during retrieval by throwing a [DP-500](#_feedback_dp500) exception.
3. **Authorization Check:**
   1. Validate that the user has permission to view the feedback entry.
4. **Map to FeedbackDto:**
   1. Map the Feedback entity to FeedbackDto, including fetching the UserDto.
5. **Return** the FeedbackDto.

### *GetList*

Get a paginated list of feedback entries

| **Arguments** | ListFeedbackRequestDto request, Guid userId |
| --- | --- |
| **Return value** | ReturnListFeedbackDto |

**Implementation**

1. **Validate** the request and its parameters:
   1. Ensure `PageLimit` is present and > 0, and `PageOffset` is present and ≥ 0.
   2. If missing or invalid, throw the [DP-422](#_feedback_dp422) exception.
2. **Authorization Check:**
   1. Validate that the user has permission to list feedback entries.
3. **Retrieve Paged Feedback:**
   1. Fetch paged Feedback entities from the Feedback table using the provided filters:
      - Pagination: `PageLimit`, `PageOffset`
      - Sorting: Use `SortField` and `SortOrder` if provided, otherwise default to "SubmittedAt" descending.
      - Filtering: Apply `Type`, `Status`, `UserId`, and `SearchTerm` if provided.
   2. Handle errors during retrieval by throwing a [DP-500](#_feedback_dp500) exception.
4. **Map to FeedbackDto List:**
   1. For each Feedback entity in the result, map to FeedbackDto (including UserDto).
5. **Map to ReturnListFeedbackDto:**
   1. Set `Data` to the list of FeedbackDto.
   2. Set `Metadata` with pagination info and total count.
6. **Return** the ReturnListFeedbackDto.

### *Update*

Updates an existing feedback entry

| **Arguments** | UpdateFeedbackDto request, Guid userId |
| --- | --- |
| **Return value** | FeedbackDto |

**Implementation**

1. **Validate** the request and its parameters:
   1. Ensure `Id` is present and not null.
   2. At least one updatable field (`Type`, `Content`, `Status`) must be provided.
   3. If missing or invalid, throw the [DP-422](#_feedback_dp422) exception.
2. **Fetch Feedback:**
   1. Retrieve the Feedback entity by Id.
   2. If not found, throw the [DP-404](#_feedback_dp404) exception.
   3. Handle errors during retrieval by throwing a [DP-500](#_feedback_dp500) exception.
3. **Authorization Check:**
   1. Validate that the user has permission to update the feedback entry.
4. **Validate Updated Fields:**
   1. Ensure updated fields meet format and business rules.
   2. If invalid, throw the [DP-422](#_feedback_dp422) exception.
5. **Map Input Data to Entity:**
   1. Update the Feedback entity fields with provided values (Type, Content, Status) if present.
   2. Set `Changed` to the current timestamp.
6. **Perform Database Operations:**
   1. Update the Feedback entity in the Feedback table.
   2. Handle errors during update by throwing a [DP-500](#_feedback_dp500) exception.
7. **Map to FeedbackDto:**
   1. Fetch the updated Feedback entity by Id.
   2. Map the Feedback entity to FeedbackDto, including fetching the UserDto.
8. **Return** the FeedbackDto.

---

# **Core Service Dependencies**

| **Service** | **Method** | **Arguments** | **Return value** | **Argument Details** |
| --- | --- | --- | --- | --- |
| IUserService | Get | UserRequestDto request, Guid userId | UserDto | UserRequestDto: - Id (guid): Unique identifier for the user |

---

# **API Exceptions**

| **Code** | **Description** | **Category** |
| --- | --- | --- |
| **DP-500** <a name="_feedback_dp500"></a> | Technical Error | Technical |
| **DP-422** <a name="_feedback_dp422"></a> | Client Error | Business |
| **DP-404** <a name="_feedback_dp404"></a> | Not Found | Technical |

---

# **Interface Layer Section**

## *IFeedbackService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | CreateFeedbackDto request, Guid userId | FeedbackDto |
| Get | FeedbackRequestDto request, Guid userId | FeedbackDto |
| GetList | ListFeedbackRequestDto request, Guid userId | ReturnListFeedbackDto |
| Update | UpdateFeedbackDto request, Guid userId | FeedbackDto |

---

# **Controller Layer Section**

## *FeedbackController*

### /feedback/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | Request<CreateFeedbackDto> |
| **Response** | Response<FeedbackDto> |

### /feedback/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | Request<FeedbackRequestDto> |
| **Response** | Response<FeedbackDto> |

### /feedback/list

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | GetList |
| **Request** | Request<ListFeedbackRequestDto> |
| **Response** | Response<ReturnListFeedbackDto> |

### /feedback/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | Request<UpdateFeedbackDto> |
| **Response** | Response<FeedbackDto> |

---