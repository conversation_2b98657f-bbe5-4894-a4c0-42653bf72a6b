---
**Document Title**: API Technical Design
**Domain**: Company
**Document Version**: 1.0

---
Section Headers **
Subsection Headers*
End of Section - - -

1. **Overview**
   - Purpose of the documentation
   - Key objectives and functionalities

2. **Web API Ground Rules Section**

   *Requests*
   Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

   Example Request:
   ```json
   {
       "header": {
           "ID": "{{$guid}}",
           "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
           "bank": "NBG",
           "UserId": "{{$user_guid}}"
       },
       "payload": {}
   }
   ```

   request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
   request.Header.application is a GUID for each application that invokes our web API.
   request.Header.bank always has the value “BANK”
   request.Header.UserId is the GUID Id for each user.

   *Responses*
   Each API response is wrapped in a Response object.
   All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
   In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

   Example Response:
   ```json
   {
       "payload": {},
       "exception": {
           "id": "guid",
           "code": "string",
           "description": "string"
       }
   }
   ```

3. **Endpoint Execution Logic**
   All endpoints are asynchronous.
   No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.
   SafeExecutor is a static class.
   SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.
   Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:
   Code: string
   Description: string
   When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.
   Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateUser”, there should be a method “CreateUser” defined in the interface and implemented in the service layer. Types, Interfaces, and Services are defined in separate files.

4. **Database Layer Rules**
   Dapper ORM is used to access, add, update, or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,
   `Task<Entity> SelectEntityAsync(Guid entityId)`

   The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.
   Also, in terms of database structure, we never use foreign keys.

5. **Common Types Section**

   **Request**
   Field Name | Type
   --- | ---
   Header | RequestHeader
   Payload | T

   **RequestHeader**
   Field Name | Type
   --- | ---
   Id | guid (Always new guid)
   Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is 03FC0B90-DFAD-11EE-8D86-0800200C9A66
   Bank | string
   UserId | guid

   **Response**
   Field Name | Type
   --- | ---
   Payload | T
   Exception | ResponseException

   **ResponseException**
   Field Name | Type
   --- | ---
   Id | guid
   Code | string
   Description | string
   Category | string

6. **Database Layer Section**
   - **Environments**

## *DB Tables*

### *Company*

| **Name**    | **Data Type**     | **Nullable** | **Unique** | **Description**                          |
|-------------|------------------|--------------|------------|------------------------------------------|
| Id          | uniqueidentifier | false        | true       | Unique identifier for the company        |
| Name        | nvarchar(200)    | false        | false      | Name of the company                      |
| Location    | nvarchar(200)    | true         | false      | Physical location or address             |
| Industry    | nvarchar(100)    | true         | false      | Industry sector                          |
| Status      | nvarchar(50)     | false        | false      | Current status (e.g., Active, Inactive)  |
| CustomerId  | uniqueidentifier | false        | false      | Associated customer ID                   |
| LastUpdated | datetime2(7)     | true         | false      | Date/time of last update                 |
| Created     | datetime2(7)     | false        | false      | Timestamp when the company was created   |
| Changed     | datetime2(7)     | true         | false      | Timestamp when the company was last updated |


7. **Types Layer Section**

###

### *Company*

Table Annotation: This entity maps to the database table Company.

| **Name**     | **Data Type**     | **Description**                                 |
|--------------|------------------|-------------------------------------------------|
| Id           | guid             | Unique identifier for the company               |
| Name         | string           | Name of the company                             |
| Location     | string           | Physical location or address                    |
| Industry     | string           | Industry sector                                 |
| Status       | string           | Current status (e.g., Active, Inactive)         |
| CustomerId   | guid             | Associated customer ID                          |
| LastUpdated  | datetime         | Date/time of last update                        |
| Created      | datetime         | Timestamp when the company was created          |
| Changed      | datetime         | Timestamp when the company was last updated     |

### *CompanyDto*

| **Name**     | **Data Type**     | **Description**                                 |
|--------------|------------------|-------------------------------------------------|
| Id           | guid             | Unique identifier for the company               |
| Name         | string           | Name of the company                             |
| Location     | string           | Physical location or address                    |
| Industry     | string           | Industry sector                                 |
| Status       | string           | Current status (e.g., Active, Inactive)         |
| CustomerId   | guid             | Associated customer ID                          |
| LastUpdated  | datetime         | Date/time of last update                        |
| Created      | datetime         | Timestamp when the company was created          |
| Changed      | datetime         | Timestamp when the company was last updated     |

### *CreateCompanyDto*

| **Name**     | **Data Type**     | **Description**                                 |
|--------------|------------------|-------------------------------------------------|
| Name         | string           | Name of the company                             |
| Location     | string           | Physical location or address                    |
| Industry     | string           | Industry sector                                 |
| Status       | string           | Current status (e.g., Active, Inactive)         |
| CustomerId   | guid             | Associated customer ID                          |

### *UpdateCompanyDto*

| **Name**     | **Data Type**     | **Description**                                 |
|--------------|------------------|-------------------------------------------------|
| Id           | guid             | Unique identifier for the company               |
| Name         | string           | Name of the company                             |
| Location     | string           | Physical location or address                    |
| Industry     | string           | Industry sector                                 |
| Status       | string           | Current status (e.g., Active, Inactive)         |
| CustomerId   | guid             | Associated customer ID                          |

### *DeleteCompanyDto*

| **Name**     | **Data Type**     | **Description**                                 |
|--------------|------------------|-------------------------------------------------|
| Id           | guid             | Unique identifier for the company               |
| FieldsToDelete | List<string>    | List of fields to be deleted                    |

### *CompanyRequestDto*

| **Name**     | **Data Type**     | **Description**                                 |
|--------------|------------------|-------------------------------------------------|
| Id           | guid             | Company Id. It can be null.                     |
| Name         | string           | Company name. It can be null.                   |

### *ListCompanyRequestDto*

| **Name**     | **Data Type**     | **Description**                                 |
|--------------|------------------|-------------------------------------------------|
| PageLimit    | int              | Page limit                                      |
| PageOffset   | int              | Page offset                                     |
| SortField    | string           | Sort field                                      |
| SortOrder    | string           | Sort order                                      |
| SearchTerm   | string           | Search                                          |
| Name         | string           | Name of the company                             |
| Status       | string           | Status filter                                   |
| CustomerId   | guid             | Associated customer ID                          |

### *MetadataDto*

| **Name**     | **Data Type**     | **Description**                                 |
|--------------|------------------|-------------------------------------------------|
| PageLimit    | int              | Page limit                                      |
| PageOffset   | int              | Page offset                                     |
| Total        | int              | Total number of pages.                          |

### *ReturnListCompanyDto*

| **Name**     | **Data Type**     | **Description**                                 |
|--------------|------------------|-------------------------------------------------|
| Data         | List<CompanyDto> | List of CompanyDto objects.                     |
| Metadata     | MetadataDto      | Pagination parameters.                          |

###

8. **Mapping Definitions Section**
### CreateCompanyDto to Company

Source: CreateCompanyDto

Target: Company

Map: CreateCompanyDto to Company

| **Source**   | **Target**   | **Mapping Details**                                 |
|--------------|--------------|-----------------------------------------------------|
| Name         | Name         | Direct Mapping                                      |
| Location     | Location     | Direct Mapping                                      |
| Industry     | Industry     | Direct Mapping                                      |
| Status       | Status       | Direct Mapping (default "Active" if not provided)   |
| CustomerId   | CustomerId   | Direct Mapping                                      |
|              | Id           | Guid.NewGuid()                                      |
|              | Created      | DateTime.Now                                        |
|              | LastUpdated  | DateTime.Now                                        |
|              | Changed      | null                                                |

### Company to CompanyDto

Source: Company

Target: CompanyDto

Map: Company to CompanyDto

| **Source**   | **Target**   | **Mapping Details**                                 |
|--------------|--------------|-----------------------------------------------------|
| Id           | Id           | Direct Mapping                                      |
| Name         | Name         | Direct Mapping                                      |
| Location     | Location     | Direct Mapping                                      |
| Industry     | Industry     | Direct Mapping                                      |
| Status       | Status       | Direct Mapping                                      |
| CustomerId   | CustomerId   | Direct Mapping                                      |
| LastUpdated  | LastUpdated  | Direct Mapping                                      |
| Created      | Created      | Direct Mapping                                      |
| Changed      | Changed      | Direct Mapping                                      |

### UpdateCompanyDto to Company

Source: UpdateCompanyDto

Target: Company

Map: UpdateCompanyDto to Company

| **Source**   | **Target**   | **Mapping Details**                                 |
|--------------|--------------|-----------------------------------------------------|
| Id           | Id           | Direct Mapping                                      |
| Name         | Name         | Conditional Mapping (Direct or No Change)           |
| Location     | Location     | Conditional Mapping (Direct or No Change)           |
| Industry     | Industry     | Conditional Mapping (Direct or No Change)           |
| Status       | Status       | Conditional Mapping (Direct or No Change)           |
| CustomerId   | CustomerId   | Conditional Mapping (Direct or No Change)           |
|              | Changed      | DateTime.Now                                        |
|              | LastUpdated  | DateTime.Now                                        |

### ListCompanyRequestDto to ReturnListCompanyDto

Source: ListCompanyRequestDto

Target: ReturnListCompanyDto

Map: ListCompanyRequestDto to ReturnListCompanyDto

| **Source**   | **Target**           | **Mapping Details**                             |
|--------------|----------------------|-------------------------------------------------|
| PageLimit    | Metadata.PageLimit   | Provided pageLimit value                        |
| PageOffset   | Metadata.PageOffset  | Provided pageOffset value                       |

### PagedResult to ReturnListCompanyDto

Source: pagedResult

Target: ReturnListCompanyDto

Map: pagedResult to ReturnListCompanyDto

| **Source**   | **Target**           | **Mapping Details**                             |
|--------------|----------------------|-------------------------------------------------|
| Records      | Data (List<CompanyDto>) | ToList()                                      |
| TotalRecords | Metadata.Total        | pagedResult.TotalRecords                        |

9. **Implementation Layer Section**

## *CompanyService*

---

### *Create*

Creates a new company with the specified details.

| **Arguments** | CreateCompanyDto request, Guid userId |
| --- | --- |
| **Return value** | CompanyDto |

**Implementation:**

1. **Validate Input Parameters:**
   - Ensure `request` is not null.
   - Ensure `Name`, `Location`, `Industry`, and `CustomerId` are present and not empty/null.
   - If any required parameter is missing or invalid, throw a business exception (e.g., "Invalid or missing fields").
2. **Authorization Check:**
   - Validate that the user has permission to create a company.
3. **Check Uniqueness:**
   - Check if a company with the same `Name` already exists in the Companies table.
   - If found, throw a business exception ("Company already exists").
4. **Fetch and Validate Dependencies:**
   - Check that the provided `CustomerId` exists in the Customers table.
   - If not found, throw a not found exception ("Customer not found").
5. **Map Input Data to Entity:**
   - Map `CreateCompanyDto` to a new `Company` entity.
   - Set `Id` to a new Guid, `Created` and `LastUpdated` to current timestamp, `Status` to "Active" (if not provided), and `Changed` to null.
6. **Perform Database Operations:**
   - Insert the new Company entity into the Companies table.
   - Retrieve the inserted company by Id.
   - If insertion or retrieval fails, throw a technical exception.
7. **Return the Result:**
   - Map the Company entity to `CompanyDto` and return.

---

### *Get*

Retrieves the details of a specific company.

| **Arguments** | CompanyRequestDto request, Guid userId |
| --- | --- |
| **Return value** | CompanyDto |

**Implementation:**

1. **Validate Input Parameters:**
   - Ensure `request` is not null.
   - Ensure at least one of `Id` or `Name` is provided.
   - If both are missing, throw a business exception ("Missing id or name").
2. **Fetch and Validate Company:**
   - Retrieve the company by `Id` if provided, otherwise by `Name`.
   - If not found, throw a not found exception ("Company not found").
3. **Authorization Check:**
   - Validate that the user has permission to view company details.
4. **Return the Result:**
   - Map the Company entity to `CompanyDto` and return.

---

### *GetList*

Retrieves a paginated list of companies, optionally filtered by criteria.

| **Arguments** | ListCompanyRequestDto request, Guid userId |
| --- | --- |
| **Return value** | ReturnListCompanyDto |

**Implementation:**

1. **Validate Input Parameters:**
   - Ensure `request` is not null.
   - Ensure `PageLimit` > 0 and `PageOffset` ≥ 0.
   - If invalid, throw a business exception ("Invalid paging").
2. **Authorization Check:**
   - Validate that the user has permission to list companies.
3. **Apply Filters and Fetch Companies:**
   - Apply filters from the request (e.g., `Name`, `Status`, `CustomerId`, `SearchTerm`).
   - Fetch paginated companies from the Companies table using the provided paging and sorting parameters.
   - If retrieval fails, throw a technical exception.
4. **Return the Result:**
   - Map the paged result to `ReturnListCompanyDto` (including `Data` as a list of `CompanyDto` and `Metadata` for pagination).
   - Return the result.

---

### *Update*

Updates the details of an existing company.

| **Arguments** | UpdateCompanyDto request, Guid userId |
| --- | --- |
| **Return value** | CompanyDto |

**Implementation:**

1. **Validate Input Parameters:**
   - Ensure `request` is not null.
   - Ensure `Id` is provided.
   - Ensure at least one updatable field (`Name`, `Location`, `Industry`, `Status`, `CustomerId`) is present.
   - If invalid, throw a business exception ("Missing id or fields").
2. **Fetch and Validate Company:**
   - Retrieve the company by `Id`.
   - If not found, throw a not found exception ("Company not found").
3. **Authorization Check:**
   - Validate that the user has permission to update company details.
4. **Validate Updated Fields:**
   - If updating `Name`, check for uniqueness (no other company with the same name).
   - If updating `CustomerId`, check that the new customer exists.
   - If any validation fails, throw a business or not found exception as appropriate.
5. **Map Input Data to Entity:**
   - Map the updated fields from `UpdateCompanyDto` to the Company entity.
   - Set `Changed` and `LastUpdated` to current timestamp.
6. **Perform Database Operations:**
   - Update the Company entity in the Companies table.
   - Retrieve the updated company by Id.
   - If update or retrieval fails, throw a technical exception.
7. **Return the Result:**
   - Map the Company entity to `CompanyDto` and return.

---

### *Deactivate*

Deactivates a company record, marking the company as inactive.

| **Arguments** | CompanyRequestDto request, Guid userId |
| --- | --- |
| **Return value** | CompanyDto |

**Implementation:**

1. **Validate Input Parameters:**
   - Ensure `request` is not null.
   - Ensure `Id` is provided.
   - If missing, throw a business exception ("Missing id").
2. **Fetch and Validate Company:**
   - Retrieve the company by `Id`.
   - If not found, throw a not found exception ("Company not found").
3. **Authorization Check:**
   - Validate that the user has permission to deactivate the company.
4. **Perform Database Operations:**
   - Update the company's `Status` to "Inactive".
   - Set `Changed` and `LastUpdated` to current timestamp.
   - Save the changes to the Companies table.
   - Retrieve the updated company by Id.
   - If update or retrieval fails, throw a technical exception.
5. **Return the Result:**
   - Map the Company entity to `CompanyDto` and return.

---

10. **API Exceptions Section**
    - Error codes and descriptions

11. **Interface Layer Section**

## *ICompanyService*

| **Method**   | **Arguments**                                   | **Return value**         |
|--------------|-------------------------------------------------|--------------------------|
| Create       | CreateCompanyDto request, Guid userId           | CompanyDto               |
| Get          | CompanyRequestDto request, Guid userId          | CompanyDto               |
| GetList      | ListCompanyRequestDto request, Guid userId      | ReturnListCompanyDto     |
| Update       | UpdateCompanyDto request, Guid userId           | CompanyDto               |
| Deactivate   | CompanyRequestDto request, Guid userId          | CompanyDto               |

---

12. **Controller Layer Section**

## *CompanyController*

### /company/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | Request<CreateCompanyDto> |
| **Response** | Response<CompanyDto> |

---

### /company/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | Request<CompanyRequestDto> |
| **Response** | Response<CompanyDto> |

---

### /company/list

| **HTTP Request Method** | GET |
| --- | --- |
| **Method** | GetList |
| **Request** | Request<ListCompanyRequestDto> |
| **Response** | Response<ReturnListCompanyDto> |

---

### /company/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | Request<UpdateCompanyDto> |
| **Response** | Response<CompanyDto> |

---

### /company/deactivate

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Deactivate |
| **Request** | Request<CompanyRequestDto> |
| **Response** | Response<CompanyDto> |

---