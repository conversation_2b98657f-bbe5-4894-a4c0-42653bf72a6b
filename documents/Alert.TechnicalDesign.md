**API Technical Design
Developers Portal**
Domain: Alert

Document Version: 4.1

#

#

Section Headers **

Subsection Headers*

End of Section - - -

# **Overview**

The purpose of this documentation is to describe in detail the functionality of the Core Developers Portal backend API for the Alert domain.

- - -

# **Web API Ground Rules Section**

## *Requests*

Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

**Example Request**

{
  "header": {
    "ID": "{{$guid}}",
    "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
    "bank": "NBG",
    "UserId": "{{$user_guid}}"
  },
  "payload": {}
}

* request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
* request.Header.application is a GUID for each application that invokes our web API.
* request.Header.bank always has the value “BANK”
* request.Header.UserId is the GUID Id for each user.

## *Responses*

Each API response is wrapped in a Response object.

* All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
* In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

**Example Response**

{
  "payload": {},
  "exception": {
    "id": "guid",
    "code": "string",
    "description": "string"
  }
}

## *Endpoint Execution Logic*

All endpoints are asynchronous.

No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.

SafeExecutor is a static class.

SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.

Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:

* Code: string
* Description: string

When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.

Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateAlert”, there should be a method “CreateAlert” defined in the interface and implemented in the service layer. Types, Interfaces and Services are defined in separate files.

## *Database Layer Rules*

Dapper ORM is used to access, add, update or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,

| Task<Alert> SelectAlertAsync(Guid alertId) |
| --- |

The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.

Also, in terms of database structure, we never use foreign keys.

- - -

# **Common Types Section**

| **Request** | |
| --- | --- |
| Field Name | Type |
| Header | [RequestHeader](#_requestheader) |
| Payload | T |

| **RequestHeader** | |
| --- | --- |
| Field Name | Type |
| Id | guid (Always new guid) |
| Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is **03FC0B90-DFAD-11EE-8D86-0800200C9A66** |
| Bank | string |
| UserId | guid |

| **Response** | |
| --- | --- |
| Field Name | Type |
| Payload | T |
| Exception | [ResponseException](#_responseexception) |

| **ResponseException** | |
| --- | --- |
| Field Name | Type |
| Id | guid |
| Code | string |
| Description | string |
| Category | string |

#

- - -

# **Database Layer Section**

| **Database** | **Description** |
| --- | --- |
| DevelopersPortal | Provides a detailed structure of Developers Portal tables including field names, data types, and constraints. |

## *Environments*

| **Environment** | **Database Server** | **Database** |
| --- | --- | --- |
| Development | V00008065 | DevPortal |
| QA |  |  |
| Production |  |  |

#

## *DB Tables*

### *Alert*

| **Name** | **Data Type** | **Nullable** | **Unique** | **Description** |
| --- | --- | --- | --- | --- |
| Id | uniqueidentifier | false | true | Unique identifier for the alert |
| Type | nvarchar(50) | false | false | Type of alert |
| EntityId | uniqueidentifier | false | false | ID of the related entity |
| EntityType | nvarchar(50) | false | false | Type of the related entity |
| Value | nvarchar(200) | true | false | Value that triggered the alert |
| Threshold | nvarchar(200) | true | false | Threshold that was crossed |
| Status | nvarchar(50) | false | false | Status of the alert |
| Message | nvarchar(500) | true | false | Alert message |
| Timestamp | datetime2(7) | false | false | Date/time alert was generated |
| DeliveryStatus | nvarchar(50) | true | false | Delivery status |
| Created | datetime2(7) | false | false | Timestamp when the alert was created |
| Changed | datetime2(7) | true | false | Timestamp when the alert was updated |

### *AlertDelivery*

| **Name** | **Data Type** | **Nullable** | **Unique** | **Description** |
| --- | --- | --- | --- | --- |
| Id | uniqueidentifier | false | true | Unique entry’s identifier to the table |
| AlertId | uniqueidentifier | false | false | Alert id |
| UserId | uniqueidentifier | false | false | User notified |

- - -

# **Types Layer Section**

### *Alert*

Table Annotation: This entity maps to the database table Alert.

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique identifier for the alert |
| Type | string | Type of alert |
| EntityId | guid | ID of the related entity |
| EntityType | string | Type of the related entity |
| Value | string | Value that triggered the alert |
| Threshold | string | Threshold that was crossed |
| Status | string | Status of the alert |
| Message | string | Alert message |
| Timestamp | datetime | Date/time alert was generated |
| DeliveryStatus | string | Delivery status |
| Created | datetime | Timestamp when the alert was created |
| Changed | datetime | Timestamp when the alert was updated |

### *AlertDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique identifier for the alert |
| Type | string | Type of alert |
| EntityId | guid | ID of the related entity |
| EntityType | string | Type of the related entity |
| Value | string | Value that triggered the alert |
| Threshold | string | Threshold that was crossed |
| Status | string | Status of the alert |
| Message | string | Alert message |
| Timestamp | datetime | Date/time alert was generated |
| DeliveryStatus | string | Delivery status |
| Created | datetime | Timestamp when the alert was created |
| Changed | datetime | Timestamp when the alert was updated |

### *CreateAlertDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Type | string | Type of alert |
| EntityId | guid | ID of the related entity |
| EntityType | string | Type of the related entity |
| Value | string | Value that triggered the alert |
| Threshold | string | Threshold that was crossed |
| Status | string | Status of the alert |
| Message | string | Alert message |
| Timestamp | datetime | Date/time alert was generated |
| DeliveryStatus | string | Delivery status |

### *UpdateAlertDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique identifier for the alert |
| Type | string | Type of alert |
| EntityId | guid | ID of the related entity |
| EntityType | string | Type of the related entity |
| Value | string | Value that triggered the alert |
| Threshold | string | Threshold that was crossed |
| Status | string | Status of the alert |
| Message | string | Alert message |
| Timestamp | datetime | Date/time alert was generated |
| DeliveryStatus | string | Delivery status |

### *DeleteAlertDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique identifier for the alert. |
| FieldsToDelete | List<string> | List of fields to be deleted. |

### *ListAlertRequestDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| PageLimit | int | Page limit |
| PageOffset | int | Page offset |
| SortField | string | Sort field |
| SortOrder | string | Sort order |
| SearchTerm | string | Search |
| Type | string | Type of alert |
| EntityId | guid | ID of the related entity |
| Status | string | Status of the alert |
| DeliveryStatus | string | Delivery status |
| DateFrom | datetime | Start date for filtering |
| DateTo | datetime | End date for filtering |

### *MetadataDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| PageLimit | int | Page limit |
| PageOffset | int | Page offset |
| Total | int | Total number of pages. |

### *ReturnListAlertDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Data | List<AlertDto> | List of AlertDto objects. |
| Metadata | MetadataDto | Pagination parameters. |

###

- - -

#

#

# **Mapping Definitions Section**

### CreateAlertDto to Alert

Source: CreateAlertDto

Target: Alert

Map: CreateAlertDto to Alert

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| - | Id | Guid.NewGuid() |
| Type | Type | Direct Mapping |
| EntityId | EntityId | Direct Mapping |
| EntityType | EntityType | Direct Mapping |
| Value | Value | Direct Mapping |
| Threshold | Threshold | Direct Mapping |
| Status | Status | Direct Mapping |
| Message | Message | Direct Mapping |
| Timestamp | Timestamp | DateTime.Now |
| DeliveryStatus | DeliveryStatus | Direct Mapping |
|  | Created | DateTime.Now |
|  | Changed | null |

### Alert to AlertDto

Source: Alert

Target: AlertDto

Map: Alert to AlertDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct mapping |
| Type | Type | Direct mapping |
| EntityId | EntityId | Direct mapping |
| EntityType | EntityType | Direct mapping |
| Value | Value | Direct mapping |
| Threshold | Threshold | Direct mapping |
| Status | Status | Direct mapping |
| Message | Message | Direct mapping |
| Timestamp | Timestamp | Direct mapping |
| DeliveryStatus | DeliveryStatus | Direct mapping |
| Created | Created | Direct mapping |
| Changed | Changed | Direct mapping |

### UpdateAlertDto to Alert

Source: UpdateAlertDto

Target: Alert

Map: UpdateAlertDto to Alert

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct Mapping |
| Type | Type | Conditional Mapping (Direct Mapping or No Change) |
| EntityId | EntityId | Conditional Mapping (Direct Mapping or No Change) |
| EntityType | EntityType | Conditional Mapping (Direct Mapping or No Change) |
| Value | Value | Conditional Mapping (Direct Mapping or No Change) |
| Threshold | Threshold | Conditional Mapping (Direct Mapping or No Change) |
| Status | Status | Conditional Mapping (Direct Mapping or No Change) |
| Message | Message | Conditional Mapping (Direct Mapping or No Change) |
| Timestamp | Timestamp | Conditional Mapping (Direct Mapping or No Change) |
| DeliveryStatus | DeliveryStatus | Conditional Mapping (Direct Mapping or No Change) |
|  | Changed | DateTime.Now |

### ListAlertRequestDto to ReturnListAlertDto

Source: ListAlertRequestDto

Target: ReturnListAlertDto

Map: ListAlertRequestDto to ReturnListAlertDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| PageLimit | Metadata.PageLimit | Provided pageLimit value. |
| PageOffset | Metadata.PageOffset | Provided pageOffset value. |

### PagedResult to ReturnListAlertDto

Source: pagedResult

Target: ReturnListAlertDto

Map: pagedResult to ReturnListAlertDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Records | List<AlertDto> | ToList() |
| TotalRecords | Metadata.Total | pagedResult.TotalRecords |

###

- - -

#

#

# **Implementation Layer Section**

## *AlertService*

###

### *Create*

Creates an alert with the specified details

| **Arguments** | [CreateAlertDto](#_createalertdto) request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Type”, “EntityId”, “EntityType”, “Status”, and “Message” must not be null or empty.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. Initialize a null object of type Alert, named alert.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Create operation.
4. **Map** the Alert based on the CreateAlertDto to Alert from the Mapping Definition Section.
5. **Perform Database Operations**:
   1. Insert the Alert.
   2. Retrieve Alert by Id.
   3. Assign the first of the retrieved alerts to the alert.
   4. Handle errors during insertions or retrievals by throwing the [DP-500](#_dp-500) exception.
   5. If not found, throw the [DP-404](#_dp-404) exception.
   6. Return the Alert’s Id.

###

### *Get*

Get the specified alert

| **Arguments** | guid id, Guid userId |
| --- | --- |
| **Return value** | [AlertDto](#_alertdto) |

**Implementation**

1. **Validate** the id parameter:
   1. “Id” must not be null.
   2. If the id is null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Fetch Alert:**
   1. Retrieve Alert by Id.
   2. Assign the first of the retrieved alerts to the alert.
   3. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
   4. If not found, throw the [DP-404](#_dp-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
4. **Map** the AlertDto based on the Alert to AlertDto from the Mapping Definition Section.
5. **Return** the alertDto.

###

###

### *Update*

Updates an alert with the specified details

| **Arguments** | [UpdateAlertDto](#_updatealertdto) request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**:

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Fetch Alert**:
   1. Retrieve Alert by Id.
   2. Assign the first of the retrieved alerts to the alert object.
   3. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
   4. If not found, throw the [DP-404](#_dp-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Update operation.
4. **Map** the Alert based on the UpdateAlertDto to Alert from the Mapping Definition Section.
5. **Perform Database Operations**:
   1. Update the Alert by Id.
   2. Retrieve Alert by Id.
   3. Assign the first of the retrieved alerts to the alert.
   4. Handle errors during insertions or retrievals by throwing the [DP-500](#_dp-500) exception.
   5. If not found, throw the [DP-404](#_dp-404) exception.
   6. Return the Alert’s Id.

### *Delete*

Deletes an alert with the specified details

| **Arguments** | [DeleteAlertDto](#_deletealertdto) request, Guid userId |
| --- | --- |
| **Return value** | bool |

**Implementation**:

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Fetch Alert:**
   1. Retrieve the Alert by Id.
   2. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
   3. If not found, throw the [DP-404](#_dp-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Delete operation.
4. **Perform Database Operations:**
   1. If request.FieldsToDelete is null:
      1. Perform a complete deletion:
         1. Delete the Alert.
      2. Return true.
   2. Else If request.FieldsToDelete is not null:
      1. Perform a partial deletion:
         1. For each field in request.FieldsToDelete:
            1. Nullify the specified field for the alert.
      2. Return true.
   3. Handle errors during deletions by throwing the [DP-500](#_dp-500) exception.
5. Return false

### *GetList*

Get an alert list with the specified details

| **Arguments** | [ListAlertRequestDto](#_listalertrequestdto) request, Guid userId |
| --- | --- |
| **Return value** | [ReturnListAlertDto](#_returnlistalertdto) |

**Implementation**:

1. **Validate** the request and its parameters:
   1. "PageLimit” must not be null or > 0.
   2. “PageOffset” must not be null or ≥ 0.
   3. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. Initialize a null object of type Alert, named alert.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
4. **Retrieve Paged Alerts:**
   1. Fetch paged Alerts with filters using the AutoCodeDbOperations Service and the following parameters:
      1. Pagination: Use request.PageLimit and request.PageOffset for PageSize and Offset.
      2. Sorting: Default to SortField = "Created" and SortOrder = "desc" if not provided.
      3. If request.SearchTerm is not null, then Search = request.SearchTerm.
      4. Filters:
         1. If request.Type is not null, add “Type” to the filters Dictionary.
         2. If request.EntityId is not null, add “EntityId” to the filters Dictionary.
         3. if request.Status is not null, add “Status” to the filters Dictionary.
         4. if request.DeliveryStatus is not null, add “DeliveryStatus” to the filters Dictionary.
         5. if request.DateFrom is not null, add “DateFrom” to the filters Dictionary.
         6. if request.DateTo is not null, add “DateTo” to the filters Dictionary.
      5. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
5. Create a List of AlertDtos type
6. **For each record in pagedResults:**
7. Create an empty object of type AlertDto, named alertDto.
8. **Map** the AlertDto based on the Alert to AlertDto from the Mapping Definition Section.
9. Add it to the alertDtos list.
10. **Map** the ReturnListAlertDto based on the ListAlertRequestDto to List<AlertDto> and PagedResult to List<AlertDto> from the Mapping Definition Section.
11. Return the ReturnListAlertDto object.

## *Core Service Dependencies*

| **Service** | **Method** | **Arguments** | **Return value** | **Argument Details** |
| --- | --- | --- | --- | --- |
| IAlertRepository | Insert | Alert alert | void | Inserts a new alert record |
| IAlertRepository | Update | Alert alert | void | Updates an existing alert record |
| IAlertRepository | GetById | Guid id | Alert | Retrieves an alert by Id |
| IAlertRepository | List | ListAlertRequestDto filter | List<Alert> | Retrieves alerts matching the filter |

- - -

#

#

#

# **API Exceptions**

| **Code** | **Description** | **Category** |
| --- | --- | --- |
| **DP-500** | Technical Error | Technical |
| **DP-422** | Client Error | Business |
| **DP-404** | Technical Error | Technical |
| **DP-400** | Technical Error | Technical |

- - -

#

#

#

#

#

# **Interface Layer Section**

## *IAlertService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | [CreateAlertDto](#_createalertdto) request, Guid userId | string |
| Get | guid id, Guid userId | [AlertDto](#_alertdto) |
| Update | [UpdateAlertDto](#_updatealertdto) request, Guid userId | string |
| Delete | [DeleteAlertDto](#_deletealertdto) request, Guid userId | bool |
| GetList | [ListAlertRequestDto](#_listalertrequestdto) request, Guid userId | [ReturnListAlertDto](#_returnlistalertdto) |

- - -

#

#

# **Controller Layer Section**

## *AlertController*

### /alert/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | [Request](#_request)<[CreateAlertDto](#_createalertdto)> |
| **Response** | [Response](#_response)<string> |

###

### /alert/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | [Request](#_request)<guid> |
| **Response** | [Response](#_response)<[AlertDto](#_alertdto)> |

### /alert/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | [Request](#_request)<[UpdateAlertDto](#_updatealertdto)> |
| **Response** | [Response](#_response)<string> |

### /alert/delete

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Delete |
| **Request** | [Request](#_request)<[DeleteAlertDto](#_deletealertdto)> |
| **Response** | [Response](#_response)<bool> |

### /alert/list

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | GetList |
| **Request** | [Request](#_request)<[ListAlertRequestDto](#_listalertrequestdto)> |
| **Response** | [Response](#_response)<[ReturnListAlertDto](#_returnlistalertdto)> |

- - -