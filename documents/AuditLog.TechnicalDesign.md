**API Technical Design**
**Domain:** AuditLog
**Document Version:** 1.1

---

# **Overview**

The purpose of this documentation is to describe in detail the functionality of the Core Developers Portal backend API for the AuditLog domain.

---

# **Web API Ground Rules Section**

## _Requests_

Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

**Example Request**

```json
{
  "header": {
    "ID": "{{$guid}}",
    "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
    "bank": "NBG",
    "UserId": "{{$user_guid}}"
  },
  "payload": {}
}
```

- request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
- request.Header.application is a GUID for each application that invokes our web API.
- request.Header.bank always has the value “BANK”
- request.Header.UserId is the GUID Id for each user.

## _Responses_

Each API response is wrapped in a Response object.

- All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
- In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

**Example Response**

```json
{
  "payload": {},
  "exception": {
    "id": "guid",
    "code": "string",
    "description": "string"
  }
}
```

## _Endpoint Execution Logic_

All endpoints are asynchronous.

No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.

SafeExecutor is a static class.

SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.

Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:

- Code: string
- Description: string

When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.

Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateAuditLog”, there should be a method “CreateAuditLog” defined in the interface and implemented in the service layer. Types, Interfaces and Services are defined in separate files.

## _Database Layer Rules_

Dapper ORM is used to access, add, update or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,

| Task<AuditLog> SelectAuditLogAsync(Guid auditLogId) |
| --------------------------------------------------- |

The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.

Also, in terms of database structure, we never use foreign keys.

---

# **Common Types Section**

| **Request** |               |
| ----------- | ------------- |
| Field Name  | Type          |
| Header      | RequestHeader |
| Payload     | T             |

| **RequestHeader** |                                                                                                                                                     |
| ----------------- | --------------------------------------------------------------------------------------------------------------------------------------------------- |
| Field Name        | Type                                                                                                                                                |
| Id                | guid (Always new guid)                                                                                                                              |
| Application       | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is **03FC0B90-DFAD-11EE-8D86-0800200C9A66** |
| Bank              | string                                                                                                                                              |
| UserId            | guid                                                                                                                                                |

| **Response** |                   |
| ------------ | ----------------- |
| Field Name   | Type              |
| Payload      | T                 |
| Exception    | ResponseException |

| **ResponseException** |        |
| --------------------- | ------ |
| Field Name            | Type   |
| Id                    | guid   |
| Code                  | string |
| Description           | string |
| Category              | string |

---

# **Database Layer Section**

| **Database**     | **Description**                                                                                               |
| ---------------- | ------------------------------------------------------------------------------------------------------------- |
| DevelopersPortal | Provides a detailed structure of Developers Portal tables including field names, data types, and constraints. |

## _Environments_

| **Environment** | **Database Server** | **Database** |
| --------------- | ------------------- | ------------ |
| Development     | V00008065           | DevPortal    |
| QA              |                     |              |
| Production      |                     |              |

## _DB Tables_

### _AuditLog_

| **Name**   | **Data Type**    | **Nullable** | **Unique** | **Description**                         |
| ---------- | ---------------- | ------------ | ---------- | --------------------------------------- |
| Id         | uniqueidentifier | false        | true       | Unique identifier for the audit log     |
| UserId     | uniqueidentifier | false        | false      | User who performed the action           |
| EntityType | nvarchar(50)     | false        | false      | Type of entity affected                 |
| EntityId   | uniqueidentifier | false        | false      | ID of entity affected                   |
| Action     | nvarchar(50)     | false        | false      | Action performed                        |
| Details    | nvarchar(1000)   | true         | false      | Details of the change                   |
| Timestamp  | datetime2(7)     | false        | false      | Date/time of the action                 |
| Created    | datetime2(7)     | false        | false      | Timestamp when the log was created      |
| Changed    | datetime2(7)     | true         | false      | Timestamp when the log was last updated |

---

# **Types Layer Section**

### _AuditLog_

Table Annotation: This entity maps to the database table AuditLog.

| **Name**   | **Data Type** | **Description**                                 |
| ---------- | ------------- | ----------------------------------------------- |
| Id         | guid          | Unique identifier for the audit log             |
| UserId     | guid          | User who performed the action                   |
| EntityType | string        | Type of entity affected                         |
| EntityId   | guid          | ID of entity affected                           |
| Action     | string        | Action performed (e.g., Create, Update, Delete) |
| Details    | string        | Details of the change                           |
| Timestamp  | datetime      | Date/time of the action                         |
| Created    | datetime      | Timestamp when the log was created              |
| Changed    | datetime      | Timestamp when the log was last updated         |

### _AuditLogDto_

| **Name**   | **Data Type** | **Description**                                 |
| ---------- | ------------- | ----------------------------------------------- |
| Id         | guid          | Unique identifier for the audit log             |
| UserId     | guid          | User who performed the action                   |
| EntityType | string        | Type of entity affected                         |
| EntityId   | guid          | ID of entity affected                           |
| Action     | string        | Action performed (e.g., Create, Update, Delete) |
| Details    | string        | Details of the change                           |
| Timestamp  | datetime      | Date/time of the action                         |
| Created    | datetime      | Timestamp when the log was created              |
| Changed    | datetime      | Timestamp when the log was last updated         |

### _CreateAuditLogDto_

| **Name**   | **Data Type** | **Description**                                 |
| ---------- | ------------- | ----------------------------------------------- |
| UserId     | guid          | User who performed the action                   |
| EntityType | string        | Type of entity affected                         |
| EntityId   | guid          | ID of entity affected                           |
| Action     | string        | Action performed (e.g., Create, Update, Delete) |
| Details    | string        | Details of the change                           |

### _UpdateAuditLogDto_

| **Name**   | **Data Type** | **Description**                                 |
| ---------- | ------------- | ----------------------------------------------- |
| Id         | guid          | Unique identifier for the audit log             |
| UserId     | guid          | User who performed the action                   |
| EntityType | string        | Type of entity affected                         |
| EntityId   | guid          | ID of entity affected                           |
| Action     | string        | Action performed (e.g., Create, Update, Delete) |
| Details    | string        | Details of the change                           |

### _DeleteAuditLogDto_

| **Name** | **Data Type** | **Description**                     |
| -------- | ------------- | ----------------------------------- |
| Id       | guid          | Unique identifier for the audit log |

### _ListAuditLogRequestDto_

| **Name**   | **Data Type** | **Description**                                    |
| ---------- | ------------- | -------------------------------------------------- |
| PageLimit  | int           | Page limit                                         |
| PageOffset | int           | Page offset                                        |
| SortField  | string        | Sort field                                         |
| SortOrder  | string        | Sort order                                         |
| UserId     | guid          | Filter by user who performed the action (optional) |
| EntityType | string        | Filter by entity type (optional)                   |
| EntityId   | guid          | Filter by entity id (optional)                     |
| Action     | string        | Filter by action (optional)                        |
| FromDate   | datetime      | Filter logs from this date (optional)              |
| ToDate     | datetime      | Filter logs up to this date (optional)             |

### _MetadataDto_

| **Name**   | **Data Type** | **Description**         |
| ---------- | ------------- | ----------------------- |
| PageLimit  | int           | Page limit              |
| PageOffset | int           | Page offset             |
| Total      | int           | Total number of records |

### _ReturnListAuditLogDto_

| **Name** | **Data Type**     | **Description**             |
| -------- | ----------------- | --------------------------- |
| Data     | List<AuditLogDto> | List of AuditLogDto objects |
| Metadata | MetadataDto       | Pagination parameters       |

---

# **Mapping Definitions Section**

### CreateAuditLogDto to AuditLog

Source: CreateAuditLogDto
Target: AuditLog
Map: CreateAuditLogDto to AuditLog

| **Source** | **Target** | **Mapping Details** |
| ---------- | ---------- | ------------------- |
| -          | Id         | Guid.NewGuid()      |
| UserId     | UserId     | Direct Mapping      |
| EntityType | EntityType | Direct Mapping      |
| EntityId   | EntityId   | Direct Mapping      |
| Action     | Action     | Direct Mapping      |
| Details    | Details    | Direct Mapping      |
| -          | Timestamp  | DateTime.Now        |
| -          | Created    | DateTime.Now        |
| -          | Changed    | null                |

### AuditLog to AuditLogDto

Source: AuditLog
Target: AuditLogDto
Map: AuditLog to AuditLogDto

| **Source** | **Target** | **Mapping Details** |
| ---------- | ---------- | ------------------- |
| Id         | Id         | Direct Mapping      |
| UserId     | UserId     | Direct Mapping      |
| EntityType | EntityType | Direct Mapping      |
| EntityId   | EntityId   | Direct Mapping      |
| Action     | Action     | Direct Mapping      |
| Details    | Details    | Direct Mapping      |
| Timestamp  | Timestamp  | Direct Mapping      |
| Created    | Created    | Direct Mapping      |
| Changed    | Changed    | Direct Mapping      |

### UpdateAuditLogDto to AuditLog

Source: UpdateAuditLogDto
Target: AuditLog
Map: UpdateAuditLogDto to AuditLog

| **Source** | **Target** | **Mapping Details** |
| ---------- | ---------- | ------------------- |
| Id         | Id         | Direct Mapping      |
| UserId     | UserId     | Direct Mapping      |
| EntityType | EntityType | Direct Mapping      |
| EntityId   | EntityId   | Direct Mapping      |
| Action     | Action     | Direct Mapping      |
| Details    | Details    | Direct Mapping      |
| -          | Changed    | DateTime.Now        |

### ListAuditLogRequestDto to ReturnListAuditLogDto

Source: ListAuditLogRequestDto
Target: ReturnListAuditLogDto
Map: ListAuditLogRequestDto to ReturnListAuditLogDto

| **Source** | **Target**          | **Mapping Details**       |
| ---------- | ------------------- | ------------------------- |
| PageLimit  | Metadata.PageLimit  | Provided pageLimit value  |
| PageOffset | Metadata.PageOffset | Provided pageOffset value |

### PagedResult to ReturnListAuditLogDto

Source: pagedResult
Target: ReturnListAuditLogDto
Map: pagedResult to ReturnListAuditLogDto

| **Source**   | **Target**               | **Mapping Details**      |
| ------------ | ------------------------ | ------------------------ |
| Records      | Data (List<AuditLogDto>) | ToList()                 |
| TotalRecords | Metadata.Total           | pagedResult.TotalRecords |

---

# **Implementation Layer Section**

## _AuditLogService_

### _Create_

Creates an audit log entry with the specified details

| **Arguments**    | CreateAuditLogDto request, Guid userId |
| ---------------- | -------------------------------------- |
| **Return value** | string                                 |

**Implementation**

1. **Validate** the request and its parameters:
   1. “UserId”, “EntityType”, “EntityId”, and “Action” must not be null or empty.
   2. If the request or the necessary parameters are null or invalid, throw the DP-422 exception.
2. **Authorization Check:**
   1. Validate that the user has the permission to perform the Create operation.
3. **Map** the AuditLog based on the CreateAuditLogDto to AuditLog from the Mapping Definition Section.
4. **Perform Database Operation:**
   1. Insert the AuditLog entity into the AuditLog table.
   2. If insertion fails, throw the DP-500 exception.
5. **Return** the Id of the created audit log as a string.

### _Get_

Get the specified audit log entry

| **Arguments**    | Guid id, Guid userId |
| ---------------- | -------------------- |
| **Return value** | AuditLogDto          |

**Implementation**

1. **Validate** the id parameter:
   1. “id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the DP-422 exception.
2. **Fetch AuditLog:**
   1. Retrieve the AuditLog entity by Id from the database.
   2. If not found, throw the DP-404 exception.
   3. If retrieval fails, throw the DP-500 exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
4. **Map** the AuditLogDto based on the AuditLog to AuditLogDto from the Mapping Definition Section.
5. **Return** the AuditLogDto.

### _Update_

Updates an audit log entry with the specified details

| **Arguments**    | UpdateAuditLogDto request, Guid userId |
| ---------------- | -------------------------------------- |
| **Return value** | string                                 |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the DP-422 exception.
2. **Fetch AuditLog:**
   1. Retrieve the AuditLog entity by Id from the database.
   2. If not found, throw the DP-404 exception.
   3. If retrieval fails, throw the DP-500 exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Update operation.
4. **Map** the AuditLog based on the UpdateAuditLogDto to AuditLog from the Mapping Definition Section.
5. **Perform Database Operation:**
   1. Update the AuditLog entity in the database.
   2. If update fails, throw the DP-500 exception.
6. **Return** the Id of the updated audit log as a string.

### _Delete_

Deletes an audit log entry with the specified details

| **Arguments**    | DeleteAuditLogDto request, Guid userId |
| ---------------- | -------------------------------------- |
| **Return value** | bool                                   |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the DP-422 exception.
2. **Fetch AuditLog:**
   1. Retrieve the AuditLog entity by Id from the database.
   2. If not found, throw the DP-404 exception.
   3. If retrieval fails, throw the DP-500 exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Delete operation.
4. **Perform Database Operation:**
   1. Delete the AuditLog entity from the database.
   2. If deletion fails, throw the DP-500 exception.
5. **Return** true if deletion is successful.

### _GetList_

Get a paginated list of audit log entries with the specified filters

| **Arguments**    | ListAuditLogRequestDto request, Guid userId |
| ---------------- | ------------------------------------------- |
| **Return value** | ReturnListAuditLogDto                       |

**Implementation**

1. **Validate** the request and its parameters:
   1. "PageLimit” must not be null or > 0.
   2. “PageOffset” must not be null or ≥ 0.
   3. If the request or the necessary parameters are null or invalid, throw the DP-422 exception.
2. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
3. **Fetch Paged AuditLogs:**
   1. Retrieve audit logs from the database using the provided filters (UserId, EntityType, EntityId, Action, FromDate, ToDate), sorting, and pagination.
   2. If retrieval fails, throw the DP-500 exception.
4. **Map** the ReturnListAuditLogDto based on the ListAuditLogRequestDto to ReturnListAuditLogDto and PagedResult to ReturnListAuditLogDto from the Mapping Definition Section.
5. **Return** the ReturnListAuditLogDto object.

## _Core Service Dependencies_

None

---

# **API Exceptions**

| **Code**   | **Description** | **Category** |
| ---------- | --------------- | ------------ |
| **DP-500** | Technical Error | Technical    |
| **DP-422** | Client Error    | Business     |
| **DP-404** | Technical Error | Technical    |
| **DP-400** | Technical Error | Technical    |

---

# **Interface Layer Section**

## _IAuditLogService_

| **Method** | **Arguments**                               | **Return value**      |
| ---------- | ------------------------------------------- | --------------------- |
| Create     | CreateAuditLogDto request, Guid userId      | string                |
| Get        | Guid id, Guid userId                        | AuditLogDto           |
| Update     | UpdateAuditLogDto request, Guid userId      | string                |
| Delete     | DeleteAuditLogDto request, Guid userId      | bool                  |
| GetList    | ListAuditLogRequestDto request, Guid userId | ReturnListAuditLogDto |

---

# **Controller Layer Section**

## _AuditLogController_

### /auditlog/create

| **HTTP Request Method** | POST                       |
| ----------------------- | -------------------------- |
| **Method**              | Create                     |
| **Request**             | Request<CreateAuditLogDto> |
| **Response**            | Response<string>           |

### /auditlog/get

| **HTTP Request Method** | POST                  |
| ----------------------- | --------------------- |
| **Method**              | Get                   |
| **Request**             | Request<{ Id: guid }> |
| **Response**            | Response<AuditLogDto> |

### /auditlog/update

| **HTTP Request Method** | POST                       |
| ----------------------- | -------------------------- |
| **Method**              | Update                     |
| **Request**             | Request<UpdateAuditLogDto> |
| **Response**            | Response<string>           |

### /auditlog/delete

| **HTTP Request Method** | POST                       |
| ----------------------- | -------------------------- |
| **Method**              | Delete                     |
| **Request**             | Request<DeleteAuditLogDto> |
| **Response**            | Response<bool>             |

### /auditlog/list

| **HTTP Request Method** | POST                            |
| ----------------------- | ------------------------------- |
| **Method**              | GetList                         |
| **Request**             | Request<ListAuditLogRequestDto> |
| **Response**            | Response<ReturnListAuditLogDto> |

---
