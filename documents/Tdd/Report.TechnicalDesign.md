**Document Title**: API Technical Design
**Domain**: Report
**Document Version**: 1.0


## 1. Overview
- Purpose of the documentation
- Key objectives and functionalities

## 2. Web API Ground Rules Section

   *Requests*
   Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

   Example Request:
   ```json
   {
       "header": {
           "ID": "{{$guid}}",
           "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
           "bank": "NBG",
           "UserId": "{{$user_guid}}"
       },
       "payload": {}
   }
   ```

   request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
   request.Header.application is a GUID for each application that invokes our web API.
   request.Header.bank always has the value “BANK”
   request.Header.UserId is the GUID Id for each user.

   *Responses*
   Each API response is wrapped in a Response object.
   All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
   In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

   Example Response:
   ```json
   {
       "payload": {},
       "exception": {
           "id": "guid",
           "code": "string",
           "description": "string"
       }
   }
   ```

## 3. Endpoint Execution Logic
All endpoints are asynchronous.
No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.
SafeExecutor is a static class.
SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.
Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:
- Code: string
- Description: string
When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.
Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateUser”, there should be a method “CreateUser” defined in the interface and implemented in the service layer. Types, Interfaces, and Services are defined in separate files.

## 4. Database Layer Rules
Dapper ORM is used to access, add, update, or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,
`Task<Entity> SelectEntityAsync(Guid entityId)`

   The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.
   Also, in terms of database structure, we never use foreign keys.

## 5. Common Types Section

   **Request**
   Field Name | Type
   --- | ---
   Header | RequestHeader
   Payload | T

   **RequestHeader**
   Field Name | Type
   --- | ---
   Id | guid (Always new guid)
   Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is 03FC0B90-DFAD-11EE-8D86-0800200C9A66
   Bank | string
   UserId | guid

   **Response**
   Field Name | Type
   --- | ---
   Payload | T
   Exception | ResponseException

   **ResponseException**
   Field Name | Type
   --- | ---
   Id | guid
   Code | string
   Description | string
   Category | string

## 6. Database Layer Section
- Environments
- DB Tables

   ### *Reports*

   | **Name**            | **Data Type**     | **Nullable** | **Unique** | **Description**                                 |
   |---------------------|-------------------|--------------|------------|-------------------------------------------------|
   | Id                  | uniqueidentifier  | false        | true       | Unique identifier for the report                |
   | Type                | nvarchar(50)      | false        | false      | Type of report                                  |
   | Criteria            | nvarchar(1000)    | true         | false      | Criteria/filters used to generate the report    |
   | GeneratedBy         | uniqueidentifier  | false        | false      | User who generated the report                   |
   | GeneratedAt         | datetime2(7)      | false        | false      | Date/time report was generated                  |
   | Status              | nvarchar(50)      | false        | false      | Status (e.g., Completed, Failed)                |
   | DeliveryChannel     | nvarchar(50)      | true         | false      | Channel used for delivery                       |
   | DeliveryStatus      | nvarchar(50)      | true         | false      | Delivery status                                 |
   | Created             | datetime2(7)      | false        | false      | Timestamp when the report was created           |
   | Changed             | datetime2(7)      | true         | false      | Timestamp when the report was last updated      |

## 7. Types Layer Section

###

### *Report*

Table Annotation: This entity maps to the database table Reports.

| **Name**         | **Data Type** | **Description**                                      |
|------------------|---------------|------------------------------------------------------|
| Id               | number        | Unique identifier for the report                     |
| Type             | string        | Type of report (e.g., Usage, Activity, Support)      |
| Criteria         | string        | Criteria/filters used to generate the report         |
| GeneratedBy      | number        | User who generated the report                        |
| GeneratedAt      | string        | Date/time report was generated (ISO 8601)            |
| Status           | string        | Status (e.g., Completed, Failed)                     |
| DeliveryChannel  | string        | Channel used for delivery (e.g., Email, Download)    |
| DeliveryStatus   | string        | Delivery status (e.g., Sent, Failed)                 |

-------

###

### *ReportDto*

| **Name**         | **Data Type** | **Description**                                      |
|------------------|---------------|------------------------------------------------------|
| Id               | number        | Unique identifier for the report                     |
| Type             | string        | Type of report (e.g., Usage, Activity, Support)      |
| Criteria         | string        | Criteria/filters used to generate the report         |
| GeneratedBy      | number        | User who generated the report                        |
| GeneratedAt      | string        | Date/time report was generated (ISO 8601)            |
| Status           | string        | Status (e.g., Completed, Failed)                     |
| DeliveryChannel  | string        | Channel used for delivery (e.g., Email, Download)    |
| DeliveryStatus   | string        | Delivery status (e.g., Sent, Failed)                 |

### *GenerateReportDto*

| **Name**         | **Data Type** | **Description**                                      |
|------------------|---------------|------------------------------------------------------|
| Type             | string        | Type of report (e.g., Usage, Activity, Support)      |
| Criteria         | string        | Criteria/filters used to generate the report         |

### *GetReportRequestDto*

| **Name**         | **Data Type** | **Description**                                      |
|------------------|---------------|------------------------------------------------------|
| Id               | number        | Unique identifier for the report                     |

### *ListReportRequestDto*

| **Name**         | **Data Type** | **Description**                                      |
|------------------|---------------|------------------------------------------------------|
| PageLimit        | int           | Page limit                                           |
| PageOffset       | int           | Page offset                                          |
| SortField        | string        | Sort field                                           |
| SortOrder        | string        | Sort order                                           |
| SearchTerm       | string        | Search                                               |
| Type             | string        | Type of report (optional filter)                     |
| Status           | string        | Status (optional filter)                             |
| GeneratedBy      | number        | User who generated the report (optional filter)      |

### *MetadataDto*

| **Name**         | **Data Type** | **Description**                                      |
|------------------|---------------|------------------------------------------------------|
| PageLimit        | int           | Page limit                                           |
| PageOffset       | int           | Page offset                                          |
| Total            | int           | Total number of records                              |

### *ReturnListReportDto*

| **Name**         | **Data Type** | **Description**                                      |
|------------------|---------------|------------------------------------------------------|
| Data             | List<ReportDto> | List of ReportDto objects.                         |
| Metadata         | MetadataDto   | Pagination parameters.                              |

###

## 8. Mapping Definitions Section
- DTO-to-Entity mappings

### GenerateReportDto to Report

Source: GenerateReportDto
Target: Report
Map: GenerateReportDto to Report

| **Source**     | **Target**        | **Mapping Details**                                      |
|----------------|-------------------|----------------------------------------------------------|
| -              | Id                | Auto-generated (Guid.NewGuid() or DB identity)           |
| Type           | Type              | Direct Mapping                                           |
| Criteria       | Criteria          | Direct Mapping                                           |
| -              | GeneratedBy       | userId (from context)                                    |
| -              | GeneratedAt       | DateTime.Now                                             |
| -              | Status            | "Completed"                                              |
| -              | DeliveryChannel   | Set based on delivery logic (e.g., "Email", "Download")  |
| -              | DeliveryStatus    | "Sent"                                                   |

### GenerateReportDto to ReportDto

Source: GenerateReportDto
Target: ReportDto
Map: GenerateReportDto to ReportDto

| **Source**     | **Target**        | **Mapping Details**                                      |
|----------------|-------------------|----------------------------------------------------------|
| Type           | Type              | Direct Mapping                                           |
| Criteria       | Criteria          | Direct Mapping                                           |

### Report to ReportDto

Source: Report
Target: ReportDto
Map: Report to ReportDto

| **Source**        | **Target**        | **Mapping Details**                                      |
|-------------------|-------------------|----------------------------------------------------------|
| Id                | Id                | Direct Mapping                                           |
| Type              | Type              | Direct Mapping                                           |
| Criteria          | Criteria          | Direct Mapping                                           |
| GeneratedBy       | GeneratedBy       | Direct Mapping                                           |
| GeneratedAt       | GeneratedAt       | Direct Mapping                                           |
| Status            | Status            | Direct Mapping                                           |
| DeliveryChannel   | DeliveryChannel   | Direct Mapping                                           |
| DeliveryStatus    | DeliveryStatus    | Direct Mapping                                           |

### GetReportRequestDto to ReportDto

Source: GetReportRequestDto
Target: ReportDto
Map: GetReportRequestDto to ReportDto

| **Source**     | **Target**        | **Mapping Details**                                      |
|----------------|-------------------|----------------------------------------------------------|
| Id             | Id                | Direct Mapping                                           |

### ListReportRequestDto to ReturnListReportDto

Source: ListReportRequestDto
Target: ReturnListReportDto
Map: ListReportRequestDto to ReturnListReportDto

| **Source**     | **Target**            | **Mapping Details**                                      |
|----------------|-----------------------|----------------------------------------------------------|
| PageLimit      | Metadata.PageLimit    | Provided pageLimit value                                 |
| PageOffset     | Metadata.PageOffset   | Provided pageOffset value                                |

### PagedResult to ReturnListReportDto

Source: pagedResult
Target: ReturnListReportDto
Map: pagedResult to ReturnListReportDto

| **Source**     | **Target**            | **Mapping Details**                                      |
|----------------|-----------------------|----------------------------------------------------------|
| Records        | Data (List<ReportDto>)| ToList()                                                 |
| TotalRecords   | Metadata.Total        | pagedResult.TotalRecords                                 |

## 9. Implementation Layer Section
- Service method descriptions and implementation details

# **Implementation Layer Section**

## *ReportService*

---

### *Generate*

Generates a new report based on specified criteria.

| **Arguments** | [GenerateReportDto](#generateReportDto) request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Type” must not be null or empty.
   2. “Criteria” must not be null or empty.
   3. If the request or the necessary parameters are null or invalid, throw the [DP-422](#api-exceptions) exception.
2. **Authorization Check:**
   1. Validate that the user has the permission to perform the Generate operation.
3. **Generate Report Data:**
   1. Generate the report data based on the specified type and criteria.
   2. If report generation fails, throw the [DP-500](#api-exceptions) exception.
4. **Map** the request to a Report entity using the GenerateReportDto to Report mapping:
   1. Set Id to a new Guid.
   2. Set Type and Criteria from the request.
   3. Set GeneratedBy to userId.
   4. Set GeneratedAt to DateTime.Now.
   5. Set Status to "Completed".
   6. Set DeliveryChannel and DeliveryStatus as per delivery logic (e.g., "Download", "Sent").
5. **Perform Database Operations:**
   1. Insert the Report entity into the Reports table.
   2. If insertion fails, throw the [DP-500](#api-exceptions) exception.
6. **Return** the Id of the created report as a string.

---

### *Get*

Retrieves the details of a specific report by report ID.

| **Arguments** | [GetReportRequestDto](#getReportRequestDto) request, Guid userId |
| --- | --- |
| **Return value** | [ReportDto](#reportDto) |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#api-exceptions) exception.
2. **Fetch Report:**
   1. Retrieve the Report entity from the Reports table by Id.
   2. If retrieval fails, throw the [DP-500](#api-exceptions) exception.
   3. If not found, throw the [DP-404](#api-exceptions) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Get operation.
4. **Map** the Report entity to ReportDto using the Report to ReportDto mapping.
5. **Return** the ReportDto.

---

### *GetList*

Retrieves a paginated list of reports, optionally filtered by criteria.

| **Arguments** | [ListReportRequestDto](#listReportRequestDto) request, Guid userId |
| --- | --- |
| **Return value** | [ReturnListReportDto](#returnListReportDto) |

**Implementation**

1. **Validate** the request and its parameters:
   1. “PageLimit” must not be null or ≤ 0.
   2. “PageOffset” must not be null or < 0.
   3. If the request or the necessary parameters are null or invalid, throw the [DP-422](#api-exceptions) exception.
2. **Authorization Check:**
   1. Validate that the user has the permission to perform the GetList operation.
3. **Retrieve Paged Reports:**
   1. Fetch paged Report entities from the Reports table using the provided filters:
      - Pagination: Use request.PageLimit and request.PageOffset.
      - Sorting: Use request.SortField and request.SortOrder, defaulting to "GeneratedAt" and "desc" if not provided.
      - Filtering: Apply filters for Type, Status, GeneratedBy, and SearchTerm if provided.
   2. If retrieval fails, throw the [DP-500](#api-exceptions) exception.
4. **Map** the paged result to a list of ReportDto using the Report to ReportDto mapping.
5. **Map** the result to ReturnListReportDto using the ListReportRequestDto and PagedResult to ReturnListReportDto mappings.
6. **Return** the ReturnListReportDto.

---

## *Core Service Dependencies*

This section lists all internal services referenced in the implementation text.

| **Service** | **Method** | **Arguments** | **Return value** | **Argument Details** |
| --- | --- | --- | --- | --- |
| IReportRepository | Insert | Report entity | Guid | Inserts a new report and returns its Id |
| IReportRepository | GetById | Guid id | Report | Retrieves a report by Id |
| IReportRepository | GetPaged | Paging/filter/sort parameters | PagedResult<Report> | Retrieves paged reports with filters |
| IAuthorizationService | Authorize | Guid userId, string operation | bool | Checks if the user has permission for the operation |

---

## *Mappings*

- **GenerateReportDto to Report**: See Mapping Definitions Section.
- **Report to ReportDto**: See Mapping Definitions Section.
- **PagedResult to ReturnListReportDto**: See Mapping Definitions Section.

---

## 10. API Exceptions Section
- Error codes and descriptions

# **API Exceptions**

| **Code** | **Description** | **Category** |
| --- | --- | --- |
| **DP-500** | Technical Error | Technical |
| **DP-422** | Client Error | Business |
| **DP-404** | Not Found | Technical |
| **DP-400** | Bad Request | Technical |

---

## 11. Interface Layer Section
- Service interfaces and method definitions

# **Interface Layer Section**

## *IReportService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Generate | [GenerateReportDto] request, Guid userId | string |
| Get | [GetReportRequestDto] request, Guid userId | [ReportDto] |
| GetList | [ListReportRequestDto] request, Guid userId | [ReturnListReportDto] |

## 12. Controller Layer Section
- API endpoints and associated details
# **Controller Layer Section**

## *ReportController*

### /report/generate

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Generate |
| **Request** | [Request]<[GenerateReportDto]> |
| **Response** | [Response]<[ReportDto]> |

###

### /report/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | [Request]<[GetReportRequestDto]> |
| **Response** | [Response]<[ReportDto]> |

###

### /report/list

| **HTTP Request Method** | GET |
| --- | --- |
| **Method** | GetList |
| **Request** | [Request]<[ListReportRequestDto]> |
| **Response** | [Response]<[ReturnListReportDto]> |

---