**API Technical Design
Domain: Sensor**
Document Version: 2.0

---

Section Headers **
Subsection Headers*
End of Section - - -

# **Overview**

The purpose of this documentation is to describe in detail the functionality of the Core SmartBoat backend API for the Sensor domain.

- - -

# **Web API Ground Rules Section**

## *Requests*

Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

**Example Request**

{
  "header": {
    "ID": "{{$guid}}",
    "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
    "bank": "NBG",
    "UserId": "{{$user_guid}}"
  },
  "payload": {}
}

* request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
* request.Header.application is a GUID for each application that invokes our web API.
* request.Header.bank always has the value “BANK”
* request.Header.UserId is the GUID Id for each user.

## *Responses*

Each API response is wrapped in a Response object.

* All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
* In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

**Example Response**

{
  "payload": {},
  "exception": {
    "id": "guid",
    "code": "string",
    "description": "string",
    "category": "string"
  }
}

## *Endpoint Execution Logic*

All endpoints are asynchronous.

No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.

SafeExecutor is a static class.

SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.

Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:

* Code: string
* Description: string

When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.

Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateSensor”, there should be a method “CreateSensor” defined in the interface and implemented in the service layer. Types, Interfaces and Services are defined in separate files.

## *Database Layer Rules*

Dapper ORM is used to access, add, update or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,

| Task<Sensor> SelectSensorAsync(Guid sensorId) |
| --- |

The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.

Also, in terms of database structure, we never use foreign keys.

- - -

# **Common Types Section**

| **Request** | |
| --- | --- |
| Field Name | Type |
| Header | [RequestHeader](#_requestheader) |
| Payload | T |

| **RequestHeader** | |
| --- | --- |
| Field Name | Type |
| Id | guid (Always new guid) |
| Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is **03FC0B90-DFAD-11EE-8D86-0800200C9A66** |
| Bank | string |
| UserId | guid |

| **Response** | |
| --- | --- |
| Field Name | Type |
| Payload | T |
| Exception | [ResponseException](#_responseexception) |

| **ResponseException** | |
| --- | --- |
| Field Name | Type |
| Id | guid |
| Code | string |
| Description | string |
| Category | string |

- - -

# **Database Layer Section**

| **Database** | **Description** |
| --- | --- |
| SmartBoat | Provides a detailed structure of SmartBoat tables including field names, data types, and constraints. |

## *Environments*

| **Environment** | **Database Server** | **Database** |
| --- | --- | --- |
| Development | V00008065 | SmartBoat |
| QA |  |  |
| Production |  |  |

## *DB Tables*

### *Sensors*

| **Name** | **Data Type** | **Nullable** | **Unique** | **Description** |
| --- | --- | --- | --- | --- |
| Id | uniqueidentifier | false | true | Unique entry’s identifier to the table |
| Name | nvarchar(200) | false | false | Sensor name |
| Type | nvarchar(100) | false | false | Sensor type/category |
| VesselId | uniqueidentifier | false | false | Vessel Id |
| Location | nvarchar(200) | true | false | Physical location on the vessel |
| Status | nvarchar(50) | false | false | Operational status (e.g., Active, Faulty) |
| LastReading | datetime2(7) | true | false | Timestamp of the last reading |
| LastUpdated | datetime2(7) | true | false | Date/time of last update |
| AlertThreshold | nvarchar(200) | true | false | Configured alert threshold value(s) |
| Created | datetime2(7) | false | false | Timestamp when the sensor was created |
| Changed | datetime2(7) | true | false | Timestamp when the sensor was last updated |

### *SensorDataPoints*

| **Name** | **Data Type** | **Nullable** | **Unique** | **Description** |
| --- | --- | --- | --- | --- |
| Id | uniqueidentifier | false | true | Unique entry’s identifier to the table |
| SensorId | uniqueidentifier | false | false | Sensor Id |
| Time | datetime2(7) | false | false | ISO 8601 timestamp of the data point |
| Timestamp | bigint | false | false | Unix timestamp |
| Temperature | float | true | false | Temperature reading |
| Humidity | float | true | false | Humidity reading |
| Created | datetime2(7) | false | false | Timestamp when the data point was created |
| Changed | datetime2(7) | true | false | Timestamp when the data point was last updated |

- - -

# **Types Layer Section**

### *Sensor*

Table Annotation: This entity maps to the database table Sensors.

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| Name | string | Sensor name |
| Type | string | Sensor type/category |
| VesselId | guid | Vessel Id |
| Location | string | Physical location on the vessel |
| Status | string | Operational status (e.g., Active, Faulty) |
| LastReading | datetime | Timestamp of the last reading |
| LastUpdated | datetime | Date/time of last update |
| AlertThreshold | string | Configured alert threshold value(s) |
| Created | datetime | Timestamp when the sensor was created |
| Changed | datetime | Timestamp when the sensor was last updated |

### *SensorDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| Name | string | Sensor name |
| Type | string | Sensor type/category |
| Vessel | VesselDto | Vessel entity |
| Location | string | Physical location on the vessel |
| Status | string | Operational status (e.g., Active, Faulty) |
| LastReading | datetime | Timestamp of the last reading |
| LastUpdated | datetime | Date/time of last update |
| AlertThreshold | string | Configured alert threshold value(s) |
| DataPoints | List<SensorDataPoint> | List of recent data points |
| Created | datetime | Timestamp when the sensor was created |
| Changed | datetime | Timestamp when the sensor was last updated |

### *CreateSensorDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Name | string | Sensor name |
| Type | string | Sensor type/category |
| VesselId | guid | Vessel Id |
| Location | string | Physical location on the vessel |
| Status | string | Operational status (e.g., Active, Faulty) |
| AlertThreshold | string | Configured alert threshold value(s) |

### *UpdateSensorDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| Name | string | Sensor name |
| Type | string | Sensor type/category |
| VesselId | guid | Vessel Id |
| Location | string | Physical location on the vessel |
| Status | string | Operational status (e.g., Active, Faulty) |
| AlertThreshold | string | Configured alert threshold value(s) |

### *DeleteSensorDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier. |
| FieldsToDelete | List<string> | List of fields to be deleted. |

### *SensorRequestDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Sensor Id. It can be null. |
| Name | string | Sensor name. It can be null. |

### *ListSensorRequestDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| PageLimit | int | Page limit |
| PageOffset | int | Page offset |
| SortField | string | Sort field |
| SortOrder | string | Sort order |
| SearchTerm | string | Search |
| Name | string | Name of the sensor |
| Type | string | Sensor type/category |
| VesselId | guid | Vessel Id |
| Status | string | Operational status (e.g., Active, Faulty) |

### *MetadataDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| PageLimit | int | Page limit |
| PageOffset | int | Page offset |
| Total | int | Total number of pages. |

### *ReturnListSensorDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Data | List<SensorDto> | List of SensorDto objects. |
| Metadata | MetadataDto | Pagination parameters. |

### *SensorDataPoint*

Table Annotation: This entity maps to the database table SensorDataPoints.

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| SensorId | guid | Sensor Id |
| Time | datetime | ISO 8601 timestamp of the data point |
| Timestamp | long | Unix timestamp |
| Temperature | float | Temperature reading |
| Humidity | float | Humidity reading |
| Created | datetime | Timestamp when the data point was created |
| Changed | datetime | Timestamp when the data point was last updated |

- - -

# **Mapping Definitions Section**

### CreateSensorDto to Sensor

Source: CreateSensorDto
Target: Sensor
Map: CreateSensorDto to Sensor

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| - | Id | Guid.NewGuid() |
| Name | Name | Direct Mapping |
| Type | Type | Direct Mapping |
| VesselId | VesselId | Direct Mapping |
| Location | Location | Direct Mapping |
| Status | Status | Direct Mapping (default "Active" if not provided) |
| AlertThreshold | AlertThreshold | Direct Mapping |
|  | Created | DateTime.Now |
|  | LastUpdated | DateTime.Now |

### CreateSensorDto to SensorDto

Source: CreateSensorDto
Target: SensorDto
Map: CreateSensorDto to SensorDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Name | Name | Direct Mapping |
| Type | Type | Direct Mapping |
| VesselId | Vessel | Fetch VesselDto by VesselId |
| Location | Location | Direct Mapping |
| Status | Status | Direct Mapping (default "Active" if not provided) |
| AlertThreshold | AlertThreshold | Direct Mapping |

### Sensor to SensorDto

Source: Sensor
Target: SensorDto
Map: Sensor to SensorDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct mapping |
| Name | Name | Direct mapping |
| Type | Type | Direct mapping |
| VesselId | Vessel | Fetch the VesselDto object if available. Otherwise it remains null. |
| Location | Location | Direct mapping |
| Status | Status | Direct mapping |
| LastReading | LastReading | Direct mapping |
| LastUpdated | LastUpdated | Direct mapping |
| AlertThreshold | AlertThreshold | Direct mapping |
| Created | Created | Direct mapping |
| Changed | Changed | Direct mapping |

### UpdateSensorDto to Sensor

Source: UpdateSensorDto
Target: Sensor
Map: UpdateSensorDto to Sensor

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct Mapping |
| Name | Name | Conditional Mapping (Direct Mapping or No Change) |
| Type | Type | Conditional Mapping (Direct Mapping or No Change) |
| VesselId | VesselId | Conditional Mapping (Direct Mapping or No Change) |
| Location | Location | Conditional Mapping (Direct Mapping or No Change) |
| Status | Status | Conditional Mapping (Direct Mapping or No Change) |
| AlertThreshold | AlertThreshold | Conditional Mapping (Direct Mapping or No Change) |
|  | LastUpdated | DateTime.Now |
|  | Changed | DateTime.Now |

### UpdateSensorDto to SensorDto

Source: UpdateSensorDto
Target: SensorDto
Map: UpdateSensorDto to SensorDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct Mapping |
| Name | Name | Direct Mapping |
| Type | Type | Direct Mapping |
| VesselId | Vessel | Fetch VesselDto by VesselId |
| Location | Location | Direct Mapping |
| Status | Status | Direct Mapping |
| AlertThreshold | AlertThreshold | Direct Mapping |

### ListSensorRequestDto to ReturnListSensorDto

Source: ListSensorRequestDto
Target: ReturnListSensorDto
Map: ListSensorRequestDto to ReturnListSensorDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| PageLimit | Metadata.PageLimit | Provided pageLimit value. |
| PageOffset | Metadata.PageOffset | Provided pageOffset value. |

### PagedResult to ReturnListSensorDto

Source: pagedResult
Target: ReturnListSensorDto
Map: pagedResult to ReturnListSensorDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Records | Data (List<SensorDto>) | ToList() |
| TotalRecords | Metadata.Total | pagedResult.TotalRecords |

- - -

# **Implementation Layer Section**

## *SensorService*

### *Create*

Creates a sensor with the specified details

| **Arguments** | [CreateSensorDto](#createsensordto) request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**

1. **Validate** the request and its parameters:
   1. “VesselId” must not be null.
   2. “Name” and “Type” must not be null or empty.
   3. If the request or the necessary parameters are null or invalid, throw the [SB-422](#_sb-422) exception.
2. Initialize a null object of type Sensor, named sensor.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Create operation.
4. **Fetch and Validate Vessel** using IVesselServiceFactory.CreateVesselService().Get from the Core Service Dependencies Section.
   1. If not found, throw the [SB-404](#_sb-404) exception.
5. **Map** the Sensor based on the CreateSensorDto to Sensor from the Mapping Definition Section.
6. **Perform Database Operations**:
   1. Insert the Sensor entity into the database.
   2. Retrieve the Sensor by Id.
   3. Assign the first of the retrieved sensors to the sensor object.
   4. Handle errors during insertions or retrievals by throwing the [SB-500](#_sb-500) exception.
   5. If not found, throw the [SB-404](#_sb-404) exception.
   6. Return the Sensor’s Id.

### *Get*

Get the specified sensor

| **Arguments** | [SensorRequestDto](#sensorrequestdto) request, Guid userId |
| --- | --- |
| **Return value** | [SensorDto](#sensordto) |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [SB-422](#_sb-422) exception.
2. **Fetch Sensor:**
   1. Retrieve Sensor by Id.
   2. Assign the first of the retrieved sensors to the sensor object.
   3. Handle errors during retrievals by throwing the [SB-500](#_sb-500) exception.
   4. If not found, throw the [SB-404](#_sb-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
4. **Fetch and Validate Vessel**:
   1. Try to fetch the Vessel using IVesselServiceFactory.CreateVesselService().Get from the Core Service Dependencies Section.
   2. Handle errors during fetching by logging the error and continue.
   3. Otherwise, the vessel remains null.
5. **Map** the SensorDto based on the Sensor to SensorDto from the Mapping Definition Section.
   1. Include the related Vessel.
6. **Return** the sensorDto.

### *Update*

Updates a sensor with the specified details

| **Arguments** | [UpdateSensorDto](#updatesensordto) request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**:

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. "Name" and "Type" must not be empty strings (""), but they can be null.
   3. If the request or the necessary parameters are null or invalid, throw the [SB-422](#_sb-422) exception.
2. **Validate** that mandatory parameters are not empty.
   1. If request.Name or request.Type is not null but WhiteSpace, throw the [SB-422](#_sb-422) exception.
3. **Fetch Sensor**:
   1. Retrieve all Sensors.
   2. Assign the sensor where sensor.Id = request.Id from the retrieved sensors to the sensor object.
   3. For each item in sensors:
      1. If sensor.Name is not equal to request.Name and item.Name equals request.Name, throw the [SB-422](#_sb-422) exception.
   4. Handle errors during retrievals by throwing the [SB-500](#_sb-500) exception.
   5. If not found, throw the [SB-404](#_sb-404) exception.
4. **Authorization Check:**
   1. Validate that the user has the permission to perform the Update operation.
5. **Fetch and Validate Vessel:**
   1. If request.VesselId is not null,
      1. Fetch and Validate Vessel using IVesselServiceFactory.CreateVesselService().Get from the Core Service Dependencies Section.
      2. If not found, throw the [SB-404](#_sb-404) exception.
   2. Otherwise, keep the same Vessel.
6. **Map** the Sensor based on the UpdateSensorDto to Sensor from the Mapping Definition Section.
7. **Perform Database Operations**:
   1. Update the Sensor entity by Id.
   2. Retrieve the Sensor by Id.
   3. Assign the first of the retrieved sensors to the sensor object.
   4. Handle errors during updates or retrievals by throwing the [SB-500](#_sb-500) exception.
   5. If not found, throw the [SB-404](#_sb-404) exception.
   6. Return the Sensor’s Id.

### *Delete*

Deletes a sensor with the specified details

| **Arguments** | [DeleteSensorDto](#deletesensordto) request, Guid userId |
| --- | --- |
| **Return value** | bool |

**Implementation**:

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [SB-422](#_sb-422) exception.
2. **Fetch Sensor:**
   1. Retrieve the Sensor by Id.
   2. Handle errors during retrievals by throwing the [SB-500](#_sb-500) exception.
   3. If not found, throw the [SB-404](#_sb-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Delete operation.
4. **Perform Database Operations:**
   1. If request.FieldsToDelete is null:
      1. Perform a complete deletion:
         1. Delete the Sensor entity by Id.
      2. Return true.
   2. Else If request.FieldsToDelete is not null:
      1. Perform a partial deletion:
         1. For each field in request.FieldsToDelete:
            1. Nullify the specified field for the sensor.
      2. Return true.
   3. Handle errors during deletions by throwing the [SB-500](#_sb-500) exception.
5. Return false

### *GetList*

Get a sensor list with the specified details

| **Arguments** | [ListSensorRequestDto](#listsensorrequestdto) request, Guid userId |
| --- | --- |
| **Return value** | [ReturnListSensorDto](#returnlistsensordto) |

**Implementation**:

1. **Validate** the request and its parameters:
   1. "PageLimit” must not be null or > 0.
   2. “PageOffset” must not be null or ≥ 0.
   3. If the request or the necessary parameters are null or invalid, throw the [SB-422](#_sb-422) exception.
2. Initialize a null object of type Sensor, named sensor.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
4. **Retrieve Paged Sensors:**
   1. Fetch paged Sensors with filters using the database service and the following parameters:
      1. Pagination: Use request.PageLimit and request.PageOffset for PageSize and Offset.
      2. Sorting: Default to SortField = "Created" and SortOrder = "desc" if not provided.
      3. If request.SearchTerm is not null, then Search = request.SearchTerm.
      4. Filters:
         1. If request.Name is not null, add “Name” to the filters Dictionary.
         2. If request.Type is not null, add “Type” to the filters Dictionary.
         3. If request.VesselId is not null, add “VesselId” to the filters Dictionary.
      5. Handle errors during retrievals by throwing the [SB-500](#_sb-500) exception.
5. Create a List of SensorDtos type.
6. **For each record in pagedResults:**
   1. Create an empty object of type SensorDto, named sensorDto.
   2. **Fetch and Validate Vessel**:
      1. Try to fetch the Vessel using IVesselServiceFactory.CreateVesselService().Get from the Core Service Dependencies Section.
      2. Handle errors during fetching by logging the error and continue.
      3. Otherwise, the vessel remains null.
   3. **Map** the SensorDto based on the Sensor to SensorDto from the Mapping Definition Section.
      1. Include the related Vessel.
   4. Add it to the sensorDtos list.
7. **Map** the ReturnListSensorDto based on the ListSensorRequestDto to List<SensorDto> and PagedResult to List<SensorDto> from the Mapping Definition Section.
8. Return the ReturnListSensorDto object.

## *Core Service Dependencies*

**Note**: To avoid circular dependency between SensorService and VesselService, SensorService uses a factory pattern to create VesselService instances when needed.

| **Factory/Service** | **Method** | **Arguments** | **Return value** | **Argument Details** |
| --- | --- | --- | --- | --- |
| IVesselServiceFactory | CreateVesselService | None | IVesselService | Factory creates VesselService instance |
| IVesselService | Get | VesselRequestDto request, Guid userId | VesselDto | VesselRequestDto:  - Id (guid): Unique identifier for the vessel |

- - -

# **API Exceptions**

| **Code** | **Description** | **Category** |
| --- | --- | --- |
| **SB-500** | Technical Error | Technical |
| **SB-422** | Client Error | Business |
| **SB-404** | Technical Error | Technical |
| **SB-400** | Technical Error | Technical |

- - -

# **Interface Layer Section**

## *ISensorService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | [CreateSensorDto](#createsensordto) request, Guid userId | string |
| Get | [SensorRequestDto](#sensorrequestdto) request, Guid userId | [SensorDto](#sensordto) |
| Update | [UpdateSensorDto](#updatesensordto) request, Guid userId | string |
| Delete | [DeleteSensorDto](#deletesensordto) request, Guid userId | bool |
| GetList | [ListSensorRequestDto](#listsensorrequestdto) request, Guid userId | [ReturnListSensorDto](#returnlistsensordto) |

- - -

# **Controller Layer Section**

## *SensorController*

### /sensor/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | [Request](#request)<[CreateSensorDto](#createsensordto)> |
| **Response** | [Response](#response)<string> |

### /sensor/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | [Request](#request)<[SensorRequestDto](#sensorrequestdto)> |
| **Response** | [Response](#response)<[SensorDto](#sensordto)> |

### /sensor/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | [Request](#request)<[UpdateSensorDto](#updatesensordto)> |
| **Response** | [Response](#response)<string> |

### /sensor/delete

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Delete |
| **Request** | [Request](#request)<[DeleteSensorDto](#deletesensordto)> |
| **Response** | [Response](#response)<bool> |

### /sensor/list

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | GetList |
| **Request** | [Request](#request)<[ListSensorRequestDto](#listsensorrequestdto)> |
| **Response** | [Response](#response)<[ReturnListSensorDto](#returnlistsensordto)> |

- - -
