**API Technical Design
Developers Portal**
Domain: Vessel

Document Version: 4.1

#

#

Section Headers **

Subsection Headers*

End of Section - - -

# **Overview**

The purpose of this documentation is to describe in detail the functionality of the Core Developers Portal backend API for the Vessel domain.

- - -

# **Web API Ground Rules Section**

## *Requests*

Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

**Example Request**

{
  "header": {
    "ID": "{{$guid}}",
    "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
    "bank": "NBG",
    "UserId": "{{$user_guid}}"
  },
  "payload": {}
}

* request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
* request.Header.application is a GUID for each application that invokes our web API.
* request.Header.bank always has the value “BANK”
* request.Header.UserId is the GUID Id for each user.

## *Responses*

Each API response is wrapped in a Response object.

* All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
* In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

**Example Response**

{
  "payload": {},
  "exception": {
    "id": "guid",
    "code": "string",
    "description": "string"
  }
}

## *Endpoint Execution Logic*

All endpoints are asynchronous.

No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.

SafeExecutor is a static class.

SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.

Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:

* Code: string
* Description: string

When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.

Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateVessel”, there should be a method “CreateVessel” defined in the interface and implemented in the service layer. Types, Interfaces and Services are defined in separate files.

## *Database Layer Rules*

Dapper ORM is used to access, add, update or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,

| Task<Vessel> SelectVesselAsync(Guid vesselId) |
| --- |

The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.

Also, in terms of database structure, we never use foreign keys.

- - -

# **Common Types Section**

| **Request** | |
| --- | --- |
| Field Name | Type |
| Header | [RequestHeader](#_requestheader) |
| Payload | T |

| **RequestHeader** | |
| --- | --- |
| Field Name | Type |
| Id | guid (Always new guid) |
| Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is **03FC0B90-DFAD-11EE-8D86-0800200C9A66** |
| Bank | string |
| UserId | guid |

| **Response** | |
| --- | --- |
| Field Name | Type |
| Payload | T |
| Exception | [ResponseException](#_responseexception) |

| **ResponseException** | |
| --- | --- |
| Field Name | Type |
| Id | guid |
| Code | string |
| Description | string |
| Category | string |

#

- - -

# **Database Layer Section**

| **Database** | **Description** |
| --- | --- |
| DevelopersPortal | Provides a detailed structure of Developers Portal tables including field names, data types, and constraints. |

## *Environments*

| **Environment** | **Database Server** | **Database** |
| --- | --- | --- |
| Development | V00008065 | DevPortal |
| QA |  |  |
| Production |  |  |

#

## *DB Tables*

### *Vessels*

| **Name** | **Data Type** | **Nullable** | **Unique** | **Description** |
| --- | --- | --- | --- | --- |
| Id | uniqueidentifier | false | true | Unique entry’s identifier to the table |
| Name | nvarchar(200) | false | false | Vessel name |
| Number | nvarchar(100) | false | true | Vessel registration or identification number |
| Type | nvarchar(100) | true | false | Type/class of vessel |
| Location | nvarchar(200) | true | false | Current or home location |
| Status | nvarchar(50) | false | false | Operational status (e.g., Active, Inactive) |
| StartDate | datetime2(7) | true | false | Date the vessel became operational |
| EndDate | datetime2(7) | true | false | Date the vessel was decommissioned |
| Image | nvarchar(500) | true | false | URL or path to vessel image |
| Onsigners | int | true | false | Number of crew currently onboard |
| Offsigners | int | true | false | Number of crew offboarded |
| CompanyId | uniqueidentifier | false | false | Associated company ID |
| LastUpdated | datetime2(7) | true | false | Date/time of last update |
| Created | datetime2(7) | false | false | Show when the entity is created |
| Changed | datetime2(7) | true | false | Show when the entity is updated |

### *VesselPathPoints*

| **Name** | **Data Type** | **Nullable** | **Unique** | **Description** |
| --- | --- | --- | --- | --- |
| Id | uniqueidentifier | false | true | Unique entry’s identifier to the table |
| VesselId | uniqueidentifier | false | false | Vessel id |
| Lat | float | false | false | Latitude of the vessel at the point |
| Lng | float | false | false | Longitude of the vessel at the point |
| Timestamp | datetime2(7) | false | false | ISO 8601 timestamp of the position |
| Location | nvarchar(200) | true | false | Human-readable location or description |
| Created | datetime2(7) | false | false | Show when the entity is created |
| Changed | datetime2(7) | true | false | Show when the entity is updated |

- - -

# **Types Layer Section**

###

### *Vessel*

Table Annotation: This entity maps to the database table Vessels.

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| Name | string | Vessel name |
| Number | string | Vessel registration or identification number |
| Type | string | Type/class of vessel |
| Location | string | Current or home location |
| Status | string | Operational status (e.g., Active, Inactive) |
| StartDate | datetime | Date the vessel became operational |
| EndDate | datetime | Date the vessel was decommissioned |
| Image | string | URL or path to vessel image |
| Onsigners | int | Number of crew currently onboard |
| Offsigners | int | Number of crew offboarded |
| CompanyId | guid | Associated company ID |
| LastUpdated | datetime | Date/time of last update |
| Created | datetime | Show when the entity is created |
| Changed | datetime | Show when the entity is updated |

### *VesselDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| Name | string | Vessel name |
| Number | string | Vessel registration or identification number |
| Type | string | Type/class of vessel |
| Location | string | Current or home location |
| Status | string | Operational status (e.g., Active, Inactive) |
| StartDate | datetime | Date the vessel became operational |
| EndDate | datetime | Date the vessel was decommissioned |
| Image | string | URL or path to vessel image |
| Onsigners | int | Number of crew currently onboard |
| Offsigners | int | Number of crew offboarded |
| Company | CompanyDto | Associated company object |
| Sensors | List<SensorDto> | List of sensors equipped on the vessel |
| LastUpdated | datetime | Date/time of last update |
| Created | datetime | Show when the entity is created |
| Changed | datetime | Show when the entity is updated |

### *CreateVesselDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Name | string | Vessel name |
| Number | string | Vessel registration or identification number |
| Type | string | Type/class of vessel |
| Location | string | Current or home location |
| Status | string | Operational status (e.g., Active, Inactive) |
| StartDate | datetime | Date the vessel became operational |
| EndDate | datetime | Date the vessel was decommissioned |
| Image | string | URL or path to vessel image |
| Onsigners | int | Number of crew currently onboard |
| Offsigners | int | Number of crew offboarded |
| CompanyId | guid | Associated company ID |

### *UpdateVesselDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| Name | string | Vessel name |
| Number | string | Vessel registration or identification number |
| Type | string | Type/class of vessel |
| Location | string | Current or home location |
| Status | string | Operational status (e.g., Active, Inactive) |
| StartDate | datetime | Date the vessel became operational |
| EndDate | datetime | Date the vessel was decommissioned |
| Image | string | URL or path to vessel image |
| Onsigners | int | Number of crew currently onboard |
| Offsigners | int | Number of crew offboarded |
| CompanyId | guid | Associated company ID |

### *DeleteVesselDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier. |
| FieldsToDelete | List<string> | List of fields to be deleted. |

### *ListVesselRequestDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| PageLimit | int | Page limit |
| PageOffset | int | Page offset |
| SortField | string | Sort field |
| SortOrder | string | Sort order |
| SearchTerm | string | Search |
| Name | string | Vessel name |
| Number | string | Vessel registration or identification number |
| Type | string | Type/class of vessel |
| Status | string | Operational status (e.g., Active, Inactive) |
| CompanyId | guid | Associated company ID |

### *MetadataDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| PageLimit | int | Page limit |
| PageOffset | int | Page offset |
| Total | int | Total number of pages. |

### *ReturnListVesselDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Data | List<VesselDto> | List of VesselDto objects. |
| Metadata | MetadataDto | Pagination parameters. |

### *VesselPathPoint*

Table Annotation: This entity maps to the database table VesselPathPoints.

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier to the table |
| VesselId | guid | Vessel id |
| Lat | float | Latitude of the vessel at the point |
| Lng | float | Longitude of the vessel at the point |
| Timestamp | datetime | ISO 8601 timestamp of the position |
| Location | string | Human-readable location or description |
| Created | datetime | Show when the entity is created |
| Changed | datetime | Show when the entity is updated |

###

- - -

#

#

# **Mapping Definitions Section**

### CreateVesselDto to Vessel

Source: CreateVesselDto

Target: Vessel

Map: CreateVesselDto to Vessel

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| - | Id | Guid.NewGuid() |
| Name | Name | Direct Mapping |
| Number | Number | Direct Mapping |
| Type | Type | Direct Mapping |
| Location | Location | Direct Mapping |
| Status | Status | Direct Mapping |
| StartDate | StartDate | Direct Mapping |
| EndDate | EndDate | Direct Mapping |
| Image | Image | Direct Mapping |
| Onsigners | Onsigners | Direct Mapping |
| Offsigners | Offsigners | Direct Mapping |
| CompanyId | CompanyId | Direct Mapping |
|  | LastUpdated | DateTime.Now |
|  | Created | DateTime.Now |
|  | Changed | null |

### CreateVesselDto to VesselDto

Source: CreateVesselDto

Target: VesselDto

Map: CreateVesselDto to VesselDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Name | Name | Direct Mapping |
| Number | Number | Direct Mapping |
| Type | Type | Direct Mapping |
| Location | Location | Direct Mapping |
| Status | Status | Direct Mapping |
| StartDate | StartDate | Direct Mapping |
| EndDate | EndDate | Direct Mapping |
| Image | Image | Direct Mapping |
| Onsigners | Onsigners | Direct Mapping |
| Offsigners | Offsigners | Direct Mapping |
| CompanyId | Company | Fetch CompanyDto by CompanyId |
|  | Sensors | Fetch List<SensorDto> for Vessel |
|  | Id | Guid.NewGuid() |
|  | LastUpdated | DateTime.Now |
|  | Created | DateTime.Now |
|  | Changed | null |

### Vessel to VesselDto

Source: Vessel

Target: VesselDto

Map: Vessel to VesselDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct Mapping |
| Name | Name | Direct Mapping |
| Number | Number | Direct Mapping |
| Type | Type | Direct Mapping |
| Location | Location | Direct Mapping |
| Status | Status | Direct Mapping |
| StartDate | StartDate | Direct Mapping |
| EndDate | EndDate | Direct Mapping |
| Image | Image | Direct Mapping |
| Onsigners | Onsigners | Direct Mapping |
| Offsigners | Offsigners | Direct Mapping |
| CompanyId | Company | Fetch CompanyDto by CompanyId |
|  | Sensors | Fetch List<SensorDto> for Vessel |
| LastUpdated | LastUpdated | Direct Mapping |
| Created | Created | Direct Mapping |
| Changed | Changed | Direct Mapping |

### UpdateVesselDto to Vessel

Source: UpdateVesselDto

Target: Vessel

Map: UpdateVesselDto to Vessel

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct Mapping |
| Name | Name | Conditional Mapping (Direct Mapping or No Change) |
| Number | Number | Conditional Mapping (Direct Mapping or No Change) |
| Type | Type | Conditional Mapping (Direct Mapping or No Change) |
| Location | Location | Conditional Mapping (Direct Mapping or No Change) |
| Status | Status | Conditional Mapping (Direct Mapping or No Change) |
| StartDate | StartDate | Conditional Mapping (Direct Mapping or No Change) |
| EndDate | EndDate | Conditional Mapping (Direct Mapping or No Change) |
| Image | Image | Conditional Mapping (Direct Mapping or No Change) |
| Onsigners | Onsigners | Conditional Mapping (Direct Mapping or No Change) |
| Offsigners | Offsigners | Conditional Mapping (Direct Mapping or No Change) |
| CompanyId | CompanyId | Conditional Mapping (Direct Mapping or No Change) |
|  | LastUpdated | DateTime.Now |
|  | Changed | DateTime.Now |

### UpdateVesselDto to VesselDto

Source: UpdateVesselDto

Target: VesselDto

Map: UpdateVesselDto to VesselDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct Mapping |
| Name | Name | Conditional Mapping (Direct Mapping or No Change) |
| Number | Number | Conditional Mapping (Direct Mapping or No Change) |
| Type | Type | Conditional Mapping (Direct Mapping or No Change) |
| Location | Location | Conditional Mapping (Direct Mapping or No Change) |
| Status | Status | Conditional Mapping (Direct Mapping or No Change) |
| StartDate | StartDate | Conditional Mapping (Direct Mapping or No Change) |
| EndDate | EndDate | Conditional Mapping (Direct Mapping or No Change) |
| Image | Image | Conditional Mapping (Direct Mapping or No Change) |
| Onsigners | Onsigners | Conditional Mapping (Direct Mapping or No Change) |
| Offsigners | Offsigners | Conditional Mapping (Direct Mapping or No Change) |
| CompanyId | Company | Fetch CompanyDto by CompanyId |
|  | Sensors | Fetch List<SensorDto> for Vessel |
|  | LastUpdated | DateTime.Now |
|  | Changed | DateTime.Now |

### ListVesselRequestDto to ReturnListVesselDto

Source: ListVesselRequestDto

Target: ReturnListVesselDto

Map: ListVesselRequestDto to ReturnListVesselDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| PageLimit | Metadata.PageLimit | Provided pageLimit value. |
| PageOffset | Metadata.PageOffset | Provided pageOffset value. |

### PagedResult to ReturnListVesselDto

Source: pagedResult

Target: ReturnListVesselDto

Map: pagedResult to ReturnListVesselDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Records | Data (List<VesselDto>) | ToList() |
| TotalRecords | Metadata.Total | pagedResult.TotalRecords |

###

- - -

#

#

# **Implementation Layer Section**

## *VesselService*

###

### *Create*

Creates a vessel with the specified details

| **Arguments** | [CreateVesselDto](#_createvesseldto) request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Name”, “Number”, “Type”, “Location”, and “CompanyId” must not be null or empty.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. Initialize a null object of type Vessel, named vessel.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Create operation.
4. **Fetch and Validate Company** using ICompanyService.Get from the Core Service Dependencies Section.
   1. If not found, throw the [DP-404](#_dp-404) exception.
5. **Check Uniqueness:**
   1. Ensure the “Number” is unique in the Vessels table. If a vessel with the same number exists, throw the [DP-422](#_dp-422) exception.
6. **Map** the Vessel based on the CreateVesselDto to Vessel from the Mapping Definition Section.
7. **Perform Database Operations**:
   1. Insert the Vessel.
   2. Retrieve Vessel by Id.
   3. Assign the first of the retrieved vessels to the vessel.
   4. Handle errors during insertions or retrievals by throwing the [DP-500](#_dp-500) exception.
   5. If not found, throw the [DP-404](#_dp-404) exception.
   6. Return the Vessel’s Id.

###

### *Get*

Get the specified vessel

| **Arguments** | [DeleteVesselDto](#_deletevesseldto) request, Guid userId |
| --- | --- |
| **Return value** | [VesselDto](#_vesseldto) |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Fetch Vessel:**
   1. Retrieve Vessel by Id.
   2. Assign the first of the retrieved vessels to the vessel.
   3. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
   4. If not found, throw the [DP-404](#_dp-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
4. **Fetch and Validate Company**:
   1. Try to fetch the Company using ICompanyService.Get from the Core Service Dependencies Section.
   2. Handle errors during fetching by logging the error and continue.
   3. Otherwise, the company remains null.
5. **Fetch Associated Sensors**:
   1. Create an empty list of type SensorDto, named sensors.
   2. Retrieve all sensors by VesselId.
   3. For each sensor, add to the sensors list.
   4. Handle exceptions during fetching operation, log error and continue without throwing an error.
6. **Map** the VesselDto based on the Vessel to VesselDto from the Mapping Definition Section.
   1. Include the related Company.
   2. Include the related Sensors.
7. **Return** the vesselDto.

###

### *Update*

Updates a vessel with the specified details

| **Arguments** | [UpdateVesselDto](#_updatevesseldto) request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**:

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. At least one updatable field (Name, Number, Type, Location, CompanyId, etc.) must be provided.
   3. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Fetch Vessel**:
   1. Retrieve Vessel by Id.
   2. Assign the first of the retrieved vessels to the vessel.
   3. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
   4. If not found, throw the [DP-404](#_dp-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Update operation.
4. **Check Uniqueness:**
   1. If updating “Number”, ensure the new number is unique in the Vessels table. If not unique, throw the [DP-422](#_dp-422) exception.
5. **Fetch and Validate Company:**
   1. If updating “CompanyId”, fetch the Company using ICompanyService.Get from the Core Service Dependencies Section.
   2. If not found, throw the [DP-404](#_dp-404) exception.
6. **Map** the Vessel based on the UpdateVesselDto to Vessel from the Mapping Definition Section.
7. **Perform Database Operations**:
   1. Update the Vessel.
   2. Retrieve Vessel by Id.
   3. Assign the first of the retrieved vessels to the vessel.
   4. Handle errors during updates or retrievals by throwing the [DP-500](#_dp-500) exception.
   5. If not found, throw the [DP-404](#_dp-404) exception.
   6. Return the Vessel’s Id.

###

### *Delete*

Deletes a vessel with the specified details

| **Arguments** | [DeleteVesselDto](#_deletevesseldto) request, Guid userId |
| --- | --- |
| **Return value** | bool |

**Implementation**:

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. **Fetch Vessel:**
   1. Retrieve the Vessel by Id.
   2. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
   3. If not found, throw the [DP-404](#_dp-404) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Delete operation.
4. **Perform Database Operations:**
   1. If request.FieldsToDelete is null:
      1. Perform a complete deletion:
         1. Delete the Vessel along with its related entities (VesselPathPoints, Sensors, etc.).
      2. Return true.
   2. Else If request.FieldsToDelete is not null:
      1. Perform a partial deletion:
         1. For each field in request.FieldsToDelete:
            1. Nullify the specified field for the vessel (excluding "Name", "Number", "CompanyId").
      2. Return true.
   3. Handle errors during deletions by throwing the [DP-500](#_dp-500) exception.
5. Return false

### *GetList*

Get a vessel list with the specified details

| **Arguments** | [ListVesselRequestDto](#_listvesselrequestdto) request, Guid userId |
| --- | --- |
| **Return value** | [ReturnListVesselDto](#_returnlistvesseldto) |

**Implementation**:

1. **Validate** the request and its parameters:
   1. "PageLimit” must not be null or > 0.
   2. “PageOffset” must not be null or ≥ 0.
   3. If the request or the necessary parameters are null or invalid, throw the [DP-422](#_dp-422) exception.
2. Initialize a null object of type Vessel, named vessel.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
4. **Retrieve Paged Vessels:**
   1. Fetch paged Vessels with filters using the AutoCodeDbOperations Service and the following parameters:
      1. Pagination: Use request.PageLimit and request.PageOffset for PageSize and Offset.
      2. Sorting: Default to SortField = "Created" and SortOrder = "desc" if not provided.
      3. If request.SearchTerm is not null, then Search = request.SearchTerm.
      4. Filters:
         1. If request.Name is not null, add “Name” to the filters Dictionary.
         2. If request.Number is not null, add “Number” to the filters Dictionary.
         3. If request.Type is not null, add “Type” to the filters Dictionary.
         4. if request.Status is not null, add “Status” to the filters Dictionary.
         5. if request.CompanyId is not null, add “CompanyId” to the filters Dictionary.
      5. Handle errors during retrievals by throwing the [DP-500](#_dp-500) exception.
5. Create a List of VesselDtos type
6. **For each record in pagedResults:**
7. Create an empty object of type VesselDto, named vesselDto.
8. **Fetch and Validate Company**:
   1. Try to fetch the Company using ICompanyService.Get from the Core Service Dependencies Section.
   2. Handle errors during fetching by logging the error and continue.
   3. Otherwise, the company remains null.
9. **Fetch Associated Sensors**:
   1. Create an empty list of type SensorDto, named sensors.
   2. Retrieve all sensors by VesselId.
   3. For each sensor, add to the sensors list.
   4. Handle exceptions during fetching operation, log error and continue without throwing an error.
10. **Map** the VesselDto based on the Vessel to VesselDto from the Mapping Definition Section.
    1. Include the related Company.
    2. Include the related Sensors.
11. Add it to the vesselDtos list.
12. **Map** the ReturnListVesselDto based on the ListVesselRequestDto to List<VesselDto> and PagedResult to List<VesselDto> from the Mapping Definition Section.
13. Return the ReturnListVesselDto object.

## *Core Service Dependencies*

**Note**: VesselService depends on ISensorService directly. To avoid circular dependency, SensorService uses IVesselServiceFactory instead of directly injecting IVesselService.

| **Service** | **Method** | **Arguments** | **Return value** | **Argument Details** |
| --- | --- | --- | --- | --- |
| ICompanyService | Get | CompanyRequestDto request, Guid userId | CompanyDto | CompanyRequestDto:  - Id (guid): Unique identifier for the company |
| ISensorService | GetListByVessel | Guid vesselId | List<SensorDto> | vesselId: Unique identifier for the vessel |

- - -

#

#

#

# **API Exceptions**

| **Code** | **Description** | **Category** |
| --- | --- | --- |
| **DP-500** | Technical Error | Technical |
| **DP-422** | Client Error | Business |
| **DP-404** | Technical Error | Technical |
| **DP-400** | Technical Error | Technical |

- - -

#

#

#

#

#

# **Interface Layer Section**

## *IVesselService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | [CreateVesselDto](#_createvesseldto) request, Guid userId | string |
| Get | [DeleteVesselDto](#_deletevesseldto) request, Guid userId | [VesselDto](#_vesseldto) |
| Update | [UpdateVesselDto](#_updatevesseldto) request, Guid userId | string |
| Delete | [DeleteVesselDto](#_deletevesseldto) request, Guid userId | bool |
| GetList | [ListVesselRequestDto](#_listvesselrequestdto) request, Guid userId | [ReturnListVesselDto](#_returnlistvesseldto) |

- - -

#

#

# **Controller Layer Section**

## *VesselController*

### /vessel/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | [Request](#_request)<[CreateVesselDto](#_createvesseldto)> |
| **Response** | [Response](#_response)<string> |

###

### /vessel/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | [Request](#_request)<[DeleteVesselDto](#_deletevesseldto)> |
| **Response** | [Response](#_response)<[VesselDto](#_vesseldto)> |

### /vessel/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | [Request](#_request)<[UpdateVesselDto](#_updatevesseldto)> |
| **Response** | [Response](#_response)<string> |

### /vessel/delete

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Delete |
| **Request** | [Request](#_request)<[DeleteVesselDto](#_deletevesseldto)> |
| **Response** | [Response](#_response)<bool> |

### /vessel/list

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | GetList |
| **Request** | [Request](#_request)<[ListVesselRequestDto](#_listvesselrequestdto)> |
| **Response** | [Response](#_response)<[ReturnListVesselDto](#_returnlistvesseldto)> |

- - -