# SmartBoat Fleet Management API – Database Diagram

This diagram visualizes the logical data entities and their relationships for the SmartBoat Fleet Management .NET API System.

```mermaid
erDiagram

    User {
        number id PK
        string name
        string email
        number roleId FK
        string avatar
        string joined
        string status
        string lastLogin
        boolean twoFactorEnabled
        string company
        string department
        string phone
        string timezone
        string language
        string bio
    }

    Role {
        number id PK
        string name
        array permissions
        string description
        string createdAt
        string updatedAt
    }

    Customer {
        number id PK
        string name
        string contactPerson
        string email
        string phone
        number companies
        number vessels
        number sensors
        string status
        string lastActive
    }

    Company {
        number id PK
        string name
        string location
        string industry
        number vessels
        number sensors
        string status
        number customerId FK
        string lastUpdated
    }

    Vessel {
        number id PK
        string name
        string number
        string type
        string location
        string status
        string startDate
        string endDate
        string image
        number onsigners
        number offsigners
        number sensors
        string lastUpdated
        number companyId FK
    }

    Sensor {
        number id PK
        string name
        string type
        string vessel
        string location
        string status
        string lastReading
        string lastUpdated
        string alertThreshold
    }

    Subscription {
        number id PK
        string name
        string type
        number customerId FK
        string customerName
        string startDate
        string endDate
        number price
        string billingFrequency
        string status
        number sensorLimit
        array features
        string lastUpdated
    }

    UsageRecord {
        number id PK
        number subscriptionId FK
        number customerId FK
        number userId FK
        string feature
        number usageValue
        string timestamp
    }

    Alert {
        number id PK
        string type
        number entityId
        string entityType
        string value
        string threshold
        string status
        string message
        string timestamp
        array deliveredTo
        string deliveryStatus
    }

    Notification {
        number id PK
        number userId FK
        string eventType
        string content
        string channel
        string status
        string timestamp
        number preferenceId FK
    }

    NotificationPreference {
        number id PK
        number userId FK
        string eventType
        string channel
        boolean enabled
        string updatedAt
    }

    AuditLog {
        number id PK
        number userId FK
        string entityType
        number entityId
        string action
        string details
        string timestamp
    }

    AssignmentEvent {
        number id PK
        string entityType
        number entityId
        number fromId
        number toId
        string eventType
        number userId FK
        string timestamp
    }

    StatusHistory {
        number id PK
        string entityType
        number entityId
        string oldStatus
        string newStatus
        number userId FK
        string timestamp
    }

    Report {
        number id PK
        string type
        string criteria
        number generatedBy FK
        string generatedAt
        string status
        string deliveryChannel
        string deliveryStatus
    }

    SupportRequest {
        number id PK
        number userId FK
        string subject
        string description
        string status
        string createdAt
        string resolvedAt
        string resolution
    }

    Feedback {
        number id PK
        number userId FK
        string type
        string content
        string submittedAt
        string status
    }

    SensorDataPoint {
        string time
        number timestamp
        number temperature
        number humidity
    }

    VesselPathPoint {
        number lat
        number lng
        string timestamp
        string location
    }

    %% Relationships

    User ||--o{ NotificationPreference : has
    User ||--o{ Notification : receives
    User ||--o{ SupportRequest : submits
    User ||--o{ Feedback : submits
    User }o--|| Role : "has"
    User }o--o{ Subscription : "accesses"
    User }o--o{ Customer : "associated with"
    Customer ||--o{ Company : owns
    Customer ||--o{ Subscription : purchases
    Company ||--o{ Vessel : operates
    Vessel ||--o{ Sensor : equipped_with
    Sensor ||--o{ SensorDataPoint : generates
    Vessel ||--o{ VesselPathPoint : records
    Subscription ||--o{ UsageRecord : has
    Subscription ||--o{ Alert : triggers
    Sensor ||--o{ Alert : triggers
    Vessel ||--o{ Alert : triggers
    Subscription ||--o{ Alert : triggers
    User ||--o{ AuditLog : "performs"
    AuditLog }o--|| User : "performed_by"
    AssignmentEvent }o--|| User : "performed_by"
    StatusHistory }o--|| User : "changed_by"
    Report }o--|| User : "generated_by"
    Notification }o--|| NotificationPreference : "uses"
```

**Legend:**
- `PK` = Primary Key
- `FK` = Foreign Key
- `||` = one, `o{` = many