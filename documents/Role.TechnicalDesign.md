---
**Document Title**: API Technical Design  
**Domain**: Role  
**Document Version**: 1.0

---
Section Headers **
Subsection Headers*
End of Section - - -

1. **Overview**
   - Purpose of the documentation
   - Key objectives and functionalities

2. **Web API Ground Rules Section**

   *Requests*  
   Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

   Example Request:
   ```json
   {
       "header": {
           "ID": "{{$guid}}",
           "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
           "bank": "NBG",
           "UserId": "{{$user_guid}}"
       },
       "payload": {}
   }
   ```

   request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.  
   request.Header.application is a GUID for each application that invokes our web API.  
   request.Header.bank always has the value “BANK”  
   request.Header.UserId is the GUID Id for each user.

   *Responses*  
   Each API response is wrapped in a Response object.  
   All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null  
   In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

   Example Response:
   ```json
   {
       "payload": {},
       "exception": {
           "id": "guid",
           "code": "string",
           "description": "string"
       }
   }
   ```

3. **Endpoint Execution Logic**  
   All endpoints are asynchronous.  
   No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.  
   SafeExecutor is a static class.  
   SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.  
   Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:  
   Code: string  
   Description: string  
   When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.  
   Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateUser”, there should be a method “CreateUser” defined in the interface and implemented in the service layer. Types, Interfaces, and Services are defined in separate files.

4. **Database Layer Rules**  
   Dapper ORM is used to access, add, update, or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,  
   `Task<Entity> SelectEntityAsync(Guid entityId)`

   The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.  
   Also, in terms of database structure, we never use foreign keys.

5. **Common Types Section**

   **Request**  
   Field Name | Type  
   --- | ---  
   Header | RequestHeader  
   Payload | T  

   **RequestHeader**  
   Field Name | Type  
   --- | ---  
   Id | guid (Always new guid)  
   Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is 03FC0B90-DFAD-11EE-8D86-0800200C9A66  
   Bank | string  
   UserId | guid  

   **Response**  
   Field Name | Type  
   --- | ---  
   Payload | T  
   Exception | ResponseException  

   **ResponseException**  
   Field Name | Type  
   --- | ---  
   Id | guid  
   Code | string  
   Description | string  
   Category | string  

6. **Database Layer Section**
   - **Environments**
   - **DB Tables** (This section will be omitted as per your request)

## *DB Tables*

### *Role*

| **Name**     | **Data Type**     | **Nullable** | **Unique** | **Description**                        |
|--------------|------------------|--------------|------------|----------------------------------------|
| Id           | uniqueidentifier | false        | true       | Unique identifier for the role         |
| Name         | nvarchar(100)    | false        | true       | Name of the role (e.g., Admin, Manager)|
| Description  | nvarchar(500)    | true         | false      | Description of the role                |
| Created      | datetime2(7)     | false        | false      | Timestamp when the role was created    |
| Changed      | datetime2(7)     | true         | false      | Timestamp when the role was updated    |

### *RolePermission*

| **Name**     | **Data Type**     | **Nullable** | **Unique** | **Description**            |
|--------------|------------------|--------------|------------|----------------------------|
| RoleId       | uniqueidentifier | false        | false      | Role (foreign key)         |
| Permission   | nvarchar(100)    | false        | false      | Permission name            |

---

7. **Types Layer Section**

###

### *Role*

Table Annotation: This entity maps to the database table Role.

| **Name**      | **Data Type** | **Description**                                 |
|---------------|--------------|-------------------------------------------------|
| Id            | guid         | Unique identifier for the role                  |
| Name          | string       | Name of the role (e.g., Admin, Manager)         |
| Description   | string       | Description of the role                         |
| Created       | datetime     | Timestamp when the role was created             |
| Changed       | datetime     | Timestamp when the role was last updated        |

### *RolePermission*

Table Annotation: This entity maps to the database table RolePermission.

| **Name**    | **Data Type** | **Description**                |
|-------------|--------------|--------------------------------|
| RoleId      | guid         | Role identifier (foreign key)  |
| Permission  | string       | Permission name                |

### *RoleDto*

| **Name**      | **Data Type**   | **Description**                                 |
|---------------|----------------|-------------------------------------------------|
| Id            | guid           | Unique identifier for the role                  |
| Name          | string         | Name of the role (e.g., Admin, Manager)         |
| Description   | string         | Description of the role                         |
| Permissions   | List<string>   | List of permissions assigned to the role        |
| Created       | datetime       | Timestamp when the role was created             |
| Changed       | datetime       | Timestamp when the role was last updated        |

### *CreateRoleDto*

| **Name**      | **Data Type**   | **Description**                                 |
|---------------|----------------|-------------------------------------------------|
| Name          | string         | Name of the role                                |
| Description   | string         | Description of the role                         |
| Permissions   | List<string>   | List of permissions assigned to the role        |

### *UpdateRoleDto*

| **Name**      | **Data Type**   | **Description**                                 |
|---------------|----------------|-------------------------------------------------|
| Id            | guid           | Unique identifier for the role                  |
| Name          | string         | Name of the role                                |
| Description   | string         | Description of the role                         |
| Permissions   | List<string>   | List of permissions assigned to the role        |

### *DeleteRoleDto*

| **Name**      | **Data Type**   | **Description**                                 |
|---------------|----------------|-------------------------------------------------|
| Id            | guid           | Unique identifier for the role                  |

### *ListRoleRequestDto*

| **Name**      | **Data Type**   | **Description**                                 |
|---------------|----------------|-------------------------------------------------|
| PageLimit     | int            | Page limit                                      |
| PageOffset    | int            | Page offset                                     |
| SortField     | string         | Sort field                                      |
| SortOrder     | string         | Sort order                                      |
| SearchTerm    | string         | Search term                                     |
| Name          | string         | Name of the role                                |

### *MetadataDto*

| **Name**      | **Data Type**   | **Description**                                 |
|---------------|----------------|-------------------------------------------------|
| PageLimit     | int            | Page limit                                      |
| PageOffset    | int            | Page offset                                     |
| Total         | int            | Total number of pages                           |

### *ReturnListRoleDto*

| **Name**      | **Data Type**           | **Description**                              |
|---------------|------------------------|----------------------------------------------|
| Data          | List<RoleDto>          | List of RoleDto objects                      |
| Metadata      | MetadataDto            | Pagination parameters                        |

###

8. **Mapping Definitions Section**

### CreateRoleDto to Role

Source: CreateRoleDto
Target: Role
Map: CreateRoleDto to Role

| **Source**    | **Target**   | **Mapping Details**                                 |
|---------------|--------------|-----------------------------------------------------|
| -             | Id           | Guid.NewGuid()                                      |
| Name          | Name         | Direct Mapping                                      |
| Description   | Description  | Direct Mapping                                      |
| -             | Created      | DateTime.Now                                        |
| -             | Changed      | null                                                |

### CreateRoleDto to RoleDto

Source: CreateRoleDto
Target: RoleDto
Map: CreateRoleDto to RoleDto

| **Source**    | **Target**   | **Mapping Details**                                 |
|---------------|--------------|-----------------------------------------------------|
| Name          | Name         | Direct Mapping                                      |
| Description   | Description  | Direct Mapping                                      |
| Permissions   | Permissions  | Direct Mapping                                      |

### Role to RoleDto

Source: Role
Target: RoleDto
Map: Role to RoleDto

| **Source**    | **Target**   | **Mapping Details**                                 |
|---------------|--------------|-----------------------------------------------------|
| Id            | Id           | Direct Mapping                                      |
| Name          | Name         | Direct Mapping                                      |
| Description   | Description  | Direct Mapping                                      |
| -             | Permissions  | Fetch all permissions from RolePermission table     |
| Created       | Created      | Direct Mapping                                      |
| Changed       | Changed      | Direct Mapping                                      |

### UpdateRoleDto to Role

Source: UpdateRoleDto
Target: Role
Map: UpdateRoleDto to Role

| **Source**    | **Target**   | **Mapping Details**                                 |
|---------------|--------------|-----------------------------------------------------|
| Id            | Id           | Direct Mapping                                      |
| Name          | Name         | Conditional Mapping (Direct Mapping or No Change)   |
| Description   | Description  | Conditional Mapping (Direct Mapping or No Change)   |
| -             | Changed      | DateTime.Now                                        |

### UpdateRoleDto to RoleDto

Source: UpdateRoleDto
Target: RoleDto
Map: UpdateRoleDto to RoleDto

| **Source**    | **Target**   | **Mapping Details**                                 |
|---------------|--------------|-----------------------------------------------------|
| Id            | Id           | Direct Mapping                                      |
| Name          | Name         | Direct Mapping                                      |
| Description   | Description  | Direct Mapping                                      |
| Permissions   | Permissions  | Direct Mapping                                      |
| -             | Changed      | DateTime.Now                                        |

### ListRoleRequestDto to ReturnListRoleDto

Source: ListRoleRequestDto
Target: ReturnListRoleDto
Map: ListRoleRequestDto to ReturnListRoleDto

| **Source**    | **Target**           | **Mapping Details**                             |
|---------------|----------------------|-------------------------------------------------|
| PageLimit     | Metadata.PageLimit   | Provided pageLimit value                        |
| PageOffset    | Metadata.PageOffset  | Provided pageOffset value                       |

### PagedResult to ReturnListRoleDto

Source: pagedResult
Target: ReturnListRoleDto
Map: pagedResult to ReturnListRoleDto

| **Source**    | **Target**           | **Mapping Details**                             |
|---------------|----------------------|-------------------------------------------------|
| Records       | Data (List<RoleDto>) | ToList()                                        |
| TotalRecords  | Metadata.Total       | pagedResult.TotalRecords                        |

---

9. **Implementation Layer Section**

## *RoleService*

---

### *Create*

Creates a new role with the specified details.

| **Arguments** | CreateRoleDto request, Guid userId |
| --- | --- |
| **Return value** | RoleDto |

**Implementation**

1. **Validate** the request and its parameters:
   - Ensure "Name" and "Permissions" are not null or empty.
   - If missing or invalid, throw a validation exception.
2. **Authorization Check:**
   - Validate that the user has permission to create roles.
3. **Check Role Name Uniqueness:**
   - Query the Roles table for an existing role with the same name.
   - If found, throw a conflict exception.
4. **Map Input Data to Entity:**
   - Map CreateRoleDto to a new Role entity (Id = new Guid, Name, Description, Created = DateTime.Now).
5. **Create RolePermissions:**
   - For each permission in request.Permissions, create a RolePermission entity with RoleId and Permission.
6. **Perform Database Operations:**
   - Insert the Role and associated RolePermission records.
   - Handle errors by throwing a technical exception.
7. **Log the creation event** with userId and timestamp.
8. **Return the created RoleDto** (including assigned permissions).

---

### *Get*

Retrieves the details of a specific role by role ID.

| **Arguments** | Guid id, Guid userId |
| --- | --- |
| **Return value** | RoleDto |

**Implementation**

1. **Validate** the input:
   - Ensure "id" is not null.
   - If missing, throw a validation exception.
2. **Fetch Role:**
   - Retrieve the Role by id from the Roles table.
   - If not found, throw a not found exception.
3. **Authorization Check:**
   - Validate that the user has permission to view roles.
4. **Fetch Permissions:**
   - Retrieve all RolePermission records for the RoleId.
5. **Map to RoleDto:**
   - Map Role and permissions to RoleDto.
6. **Return** the RoleDto.

---

### *GetList*

Retrieves a paginated list of roles, optionally filtered by criteria.

| **Arguments** | ListRoleRequestDto request, Guid userId |
| --- | --- |
| **Return value** | ReturnListRoleDto |

**Implementation**

1. **Validate** the request and its parameters:
   - Ensure "PageLimit" and "PageOffset" are valid (PageLimit > 0, PageOffset >= 0).
   - If invalid, throw a validation exception.
2. **Authorization Check:**
   - Validate that the user has permission to list roles.
3. **Apply Filters:**
   - Apply any provided filter criteria (e.g., Name, SearchTerm) to the Roles query.
4. **Fetch Paged Roles:**
   - Retrieve roles from the Roles table using paging and sorting parameters.
   - For each role, fetch associated permissions from RolePermission.
5. **Map to RoleDto List:**
   - Map each Role and its permissions to RoleDto.
6. **Map to ReturnListRoleDto:**
   - Include pagination metadata.
7. **Return** the ReturnListRoleDto.

---

### *Update*

Updates the details of an existing role.

| **Arguments** | UpdateRoleDto request, Guid userId |
| --- | --- |
| **Return value** | RoleDto |

**Implementation**

1. **Validate** the request and its parameters:
   - Ensure "Id" is not null and at least one updatable field (Name, Description, Permissions) is provided.
   - If invalid, throw a validation exception.
2. **Fetch Role:**
   - Retrieve the Role by id from the Roles table.
   - If not found, throw a not found exception.
3. **Authorization Check:**
   - Validate that the user has permission to update roles.
4. **Validate Updated Fields:**
   - If updating "Name", check for uniqueness.
   - If not unique, throw a conflict exception.
5. **Update Role Entity:**
   - Update fields (Name, Description) as provided.
   - Set Changed = DateTime.Now.
6. **Update Permissions:**
   - If Permissions are provided:
     - Remove existing RolePermission records for the RoleId.
     - Insert new RolePermission records for each permission in request.Permissions.
7. **Perform Database Operations:**
   - Update the Role and RolePermission records.
   - Handle errors by throwing a technical exception.
8. **Log the update event** with userId and timestamp.
9. **Return the updated RoleDto** (including assigned permissions).

---

### *Delete*

Deletes a role by role ID.

| **Arguments** | Guid id, Guid userId |
| --- | --- |
| **Return value** | bool |

**Implementation**

1. **Validate** the input:
   - Ensure "id" is not null.
   - If missing, throw a validation exception.
2. **Fetch Role:**
   - Retrieve the Role by id from the Roles table.
   - If not found, throw a not found exception.
3. **Authorization Check:**
   - Validate that the user has permission to delete roles.
4. **Check for Dependencies:**
   - Check if any users are assigned to this role.
   - If dependencies exist, throw a conflict exception.
5. **Perform Database Operations:**
   - Delete RolePermission records for the RoleId.
   - Delete the Role record.
   - Handle errors by throwing a technical exception.
6. **Log the deletion event** with userId and timestamp.
7. **Return** true if deletion is successful.

---
10. **API Exceptions Section**
    - Error codes and descriptions

11. **Interface Layer Section**
    - Service interfaces and method definitions

## *IRoleService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | CreateRoleDto request, Guid userId | RoleDto |
| Get | Guid id, Guid userId | RoleDto |
| GetList | ListRoleRequestDto request, Guid userId | ReturnListRoleDto |
| Update | UpdateRoleDto request, Guid userId | RoleDto |
| Delete | Guid id, Guid userId | bool |

12. **Controller Layer Section**

## *RoleController*

### /role/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | Request<CreateRoleDto> |
| **Response** | Response<RoleDto> |

### /role/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | Request<{ id: guid }> |
| **Response** | Response<RoleDto> |

### /role/list

| **HTTP Request Method** | GET |
| --- | --- |
| **Method** | GetList |
| **Request** | Request<ListRoleRequestDto> |
| **Response** | Response<List<RoleDto>> |

### /role/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | Request<UpdateRoleDto> |
| **Response** | Response<RoleDto> |

### /role/delete

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Delete |
| **Request** | Request<{ id: guid }> |
| **Response** | Response<{ status: string }> |

---