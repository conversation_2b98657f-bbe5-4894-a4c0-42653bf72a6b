---
**Document Title**: API Technical Design
**Domain**: SupportRequest
**Document Version**: 1.0

---
Section Headers **
Subsection Headers*
End of Section - - -

1. **Overview**
   - Purpose of the documentation
   - Key objectives and functionalities

2. **Web API Ground Rules Section**

   *Requests*
   Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

   Example Request:
   ```json
   {
       "header": {
           "ID": "{{$guid}}",
           "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
           "bank": "NBG",
           "UserId": "{{$user_guid}}"
       },
       "payload": {}
   }
   ```

   request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
   request.Header.application is a GUID for each application that invokes our web API.
   request.Header.bank always has the value “BANK”
   request.Header.UserId is the GUID Id for each user.

   *Responses*
   Each API response is wrapped in a Response object.
   All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
   In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

   Example Response:
   ```json
   {
       "payload": {},
       "exception": {
           "id": "guid",
           "code": "string",
           "description": "string"
       }
   }
   ```

3. **Endpoint Execution Logic**
   All endpoints are asynchronous.
   No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.
   SafeExecutor is a static class.
   SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.
   Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:
   Code: string
   Description: string
   When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.
   Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateUser”, there should be a method “CreateUser” defined in the interface and implemented in the service layer. Types, Interfaces, and Services are defined in separate files.

4. **Database Layer Rules**
   Dapper ORM is used to access, add, update, or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,
   `Task<Entity> SelectEntityAsync(Guid entityId)`

   The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.
   Also, in terms of database structure, we never use foreign keys.

5. **Common Types Section**

   **Request**
   Field Name | Type
   --- | ---
   Header | RequestHeader
   Payload | T

   **RequestHeader**
   Field Name | Type
   --- | ---
   Id | guid (Always new guid)
   Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is 03FC0B90-DFAD-11EE-8D86-0800200C9A66
   Bank | string
   UserId | guid

   **Response**
   Field Name | Type
   --- | ---
   Payload | T
   Exception | ResponseException

   **ResponseException**
   Field Name | Type
   --- | ---
   Id | guid
   Code | string
   Description | string
   Category | string

6. **Database Layer Section**
   - **Environments**
   ## *DB Tables*

   ### *SupportRequest*

   | **Name**    | **Data Type**     | **Nullable** | **Unique** | **Description**                                 |
   |-------------|------------------|--------------|------------|-------------------------------------------------|
   | Id          | uniqueidentifier | false        | true       | Unique identifier for the support request        |
   | UserId      | uniqueidentifier | false        | false      | User who submitted the request                   |
   | Subject     | nvarchar(200)    | false        | false      | Subject of the support request                   |
   | Description | nvarchar(1000)   | false        | false      | Detailed description of the issue                |
   | Status      | nvarchar(50)     | false        | false      | Status (e.g., Open, In Progress, Resolved)       |
   | CreatedAt   | datetime2(7)     | false        | false      | Date/time request was created                    |
   | ResolvedAt  | datetime2(7)     | true         | false      | Date/time request was resolved                   |
   | Resolution  | nvarchar(1000)   | true         | false      | Resolution details                               |
   | Created     | datetime2(7)     | false        | false      | Timestamp when the request was created           |
   | Changed     | datetime2(7)     | true         | false      | Timestamp when the request was last updated      |

   7. **Types Layer Section**

   ###

   ### *SupportRequest*

   Table Annotation: This entity maps to the database table SupportRequest.

   | **Name**      | **Data Type** | **Description**                                 |
   |---------------|---------------|-------------------------------------------------|
   | Id            | guid          | Unique identifier for the support request       |
   | UserId        | guid          | User who submitted the request                  |
   | Subject       | string        | Subject of the support request                  |
   | Description   | string        | Detailed description of the issue               |
   | Status        | string        | Status (e.g., Open, In Progress, Resolved)      |
   | CreatedAt     | datetime      | Date/time request was created                   |
   | ResolvedAt    | datetime      | Date/time request was resolved                  |
   | Resolution    | string        | Resolution details                              |
   | Created       | datetime      | Timestamp when the request was created          |
   | Changed       | datetime      | Timestamp when the request was last updated     |

   ### *SupportRequestDto*

   | **Name**      | **Data Type** | **Description**                                 |
   |---------------|---------------|-------------------------------------------------|
   | Id            | guid          | Unique identifier for the support request       |
   | User          | UserDto       | User who submitted the request                  |
   | Subject       | string        | Subject of the support request                  |
   | Description   | string        | Detailed description of the issue               |
   | Status        | string        | Status (e.g., Open, In Progress, Resolved)      |
   | CreatedAt     | datetime      | Date/time request was created                   |
   | ResolvedAt    | datetime      | Date/time request was resolved                  |
   | Resolution    | string        | Resolution details                              |
   | Created       | datetime      | Timestamp when the request was created          |
   | Changed       | datetime      | Timestamp when the request was last updated     |

   ### *CreateSupportRequestDto*

   | **Name**      | **Data Type** | **Description**                                 |
   |---------------|---------------|-------------------------------------------------|
   | UserId        | guid          | User who submitted the request                  |
   | Subject       | string        | Subject of the support request                  |
   | Description   | string        | Detailed description of the issue               |

   ### *UpdateSupportRequestDto*

   | **Name**      | **Data Type** | **Description**                                 |
   |---------------|---------------|-------------------------------------------------|
   | Id            | guid          | Unique identifier for the support request       |
   | Subject       | string        | Subject of the support request                  |
   | Description   | string        | Detailed description of the issue               |
   | Status        | string        | Status (e.g., Open, In Progress, Resolved)      |
   | ResolvedAt    | datetime      | Date/time request was resolved                  |
   | Resolution    | string        | Resolution details                              |

   ### *DeleteSupportRequestDto*

   | **Name**      | **Data Type** | **Description**                                 |
   |---------------|---------------|-------------------------------------------------|
   | Id            | guid          | Unique identifier for the support request       |

   ### *SupportRequestRequestDto*

   | **Name**      | **Data Type** | **Description**                                 |
   |---------------|---------------|-------------------------------------------------|
   | Id            | guid          | SupportRequest Id. It can be null.              |
   | Subject       | string        | Subject of the support request. It can be null. |

   ### *ListSupportRequestRequestDto*

   | **Name**      | **Data Type** | **Description**                                 |
   |---------------|---------------|-------------------------------------------------|
   | PageLimit     | int           | Page limit                                      |
   | PageOffset    | int           | Page offset                                     |
   | SortField     | string        | Sort field                                      |
   | SortOrder     | string        | Sort order                                      |
   | SearchTerm    | string        | Search                                          |
   | Subject       | string        | Subject of the support request                  |
   | UserId        | guid          | User who submitted the request                  |
   | Status        | string        | Status filter                                   |

   ### *MetadataDto*

   | **Name**      | **Data Type** | **Description**                                 |
   |---------------|---------------|-------------------------------------------------|
   | PageLimit     | int           | Page limit                                      |
   | PageOffset    | int           | Page offset                                     |
   | Total         | int           | Total number of pages.                          |

   ### *ReturnListSupportRequestDto*

   | **Name**      | **Data Type**         | **Description**                              |
   |---------------|----------------------|----------------------------------------------|
   | Data          | List<SupportRequestDto> | List of SupportRequestDto objects.         |
   | Metadata      | MetadataDto           | Pagination parameters.                      |

   ###

   8. **Mapping Definitions Section**
      - DTO-to-Entity mappings

   ### CreateSupportRequestDto to SupportRequest

   Source: CreateSupportRequestDto
   Target: SupportRequest
   Map: CreateSupportRequestDto to SupportRequest

   | **Source**   | **Target**   | **Mapping Details**                                      |
   |--------------|--------------|----------------------------------------------------------|
   | UserId       | UserId       | Direct Mapping                                           |
   | Subject      | Subject      | Direct Mapping                                           |
   | Description  | Description  | Direct Mapping                                           |
   | -            | Status       | "Open" (Initial status)                                  |
   | -            | CreatedAt    | DateTime.Now                                             |
   | -            | ResolvedAt   | null                                                     |
   | -            | Resolution   | null                                                     |
   | -            | Id           | Guid.NewGuid()                                           |
   | -            | Created      | DateTime.Now                                             |
   | -            | Changed      | null                                                     |

   ### SupportRequest to SupportRequestDto

   Source: SupportRequest
   Target: SupportRequestDto
   Map: SupportRequest to SupportRequestDto

   | **Source**   | **Target**   | **Mapping Details**                                      |
   |--------------|--------------|----------------------------------------------------------|
   | Id           | Id           | Direct Mapping                                           |
   | UserId       | User         | Fetch UserDto by UserId                                  |
   | Subject      | Subject      | Direct Mapping                                           |
   | Description  | Description  | Direct Mapping                                           |
   | Status       | Status       | Direct Mapping                                           |
   | CreatedAt    | CreatedAt    | Direct Mapping                                           |
   | ResolvedAt   | ResolvedAt   | Direct Mapping                                           |
   | Resolution   | Resolution   | Direct Mapping                                           |
   | Created      | Created      | Direct Mapping                                           |
   | Changed      | Changed      | Direct Mapping                                           |

   ### UpdateSupportRequestDto to SupportRequest

   Source: UpdateSupportRequestDto
   Target: SupportRequest
   Map: UpdateSupportRequestDto to SupportRequest

   | **Source**   | **Target**   | **Mapping Details**                                      |
   |--------------|--------------|----------------------------------------------------------|
   | Id           | Id           | Direct Mapping                                           |
   | Subject      | Subject      | Conditional Mapping (Direct Mapping or No Change)        |
   | Description  | Description  | Conditional Mapping (Direct Mapping or No Change)        |
   | Status       | Status       | Conditional Mapping (Direct Mapping or No Change)        |
   | Resolution   | Resolution   | Conditional Mapping (Direct Mapping or No Change)        |
   | -            | Changed      | DateTime.Now                                             |
   | -            | ResolvedAt   | If Status updated to "Resolved", set to DateTime.Now; else No Change |

   ### ListSupportRequestRequestDto to ReturnListSupportRequestDto

   Source: ListSupportRequestRequestDto
   Target: ReturnListSupportRequestDto
   Map: ListSupportRequestRequestDto to ReturnListSupportRequestDto

   | **Source**   | **Target**         | **Mapping Details**                                  |
   |--------------|--------------------|------------------------------------------------------|
   | PageLimit    | Metadata.PageLimit | Provided pageLimit value                             |
   | PageOffset   | Metadata.PageOffset| Provided pageOffset value                            |

   ### PagedResult to ReturnListSupportRequestDto

   Source: pagedResult
   Target: ReturnListSupportRequestDto
   Map: pagedResult to ReturnListSupportRequestDto

   | **Source**   | **Target**         | **Mapping Details**                                  |
   |--------------|--------------------|------------------------------------------------------|
   | Records      | Data (List<SupportRequestDto>) | ToList()                                 |
   | TotalRecords | Metadata.Total     | pagedResult.TotalRecords                             |

   9. **Implementation Layer Section**
      - Service method descriptions and implementation details

   ---

   ## *SupportRequestService*

   ### *Create*

   Creates a new support request.

   | **Arguments** | CreateSupportRequestDto request, Guid userId |
   | --- | --- |
   | **Return value** | string |

   **Implementation:**

   1. **Validate** the request and its parameters:
      - Ensure `UserId`, `Subject`, and `Description` are present and not empty.
      - If any required parameter is missing or invalid, throw a DP-422 (Client Error) exception.
   2. **Authorization Check:**
      - Validate that the user has permission to create a support request.
   3. **Fetch and Validate User:**
      - Check that the `UserId` exists in the Users table.
      - If not found, throw a DP-404 (Not Found) exception.
   4. **Map Input Data to Entity:**
      - Map the request to a new SupportRequest entity:
        - Set `Status` to "Open".
        - Set `CreatedAt` and `Created` to the current timestamp.
        - Set `ResolvedAt` and `Resolution` to null.
        - Generate a new `Id`.
      - Follow the mapping defined in the Mapping Definitions Section.
   5. **Perform Database Operations:**
      - Insert the new SupportRequest entity into the database.
      - Handle errors during insertion by throwing a DP-500 (Technical Error) exception.
   6. **Return the Result:**
      - Map the created entity to SupportRequestDto, including fetching the UserDto.
      - Return the SupportRequestDto.

   ---

   ### *Get*

   Retrieves the details of a specific support request.

   | **Arguments** | SupportRequestRequestDto request, Guid userId |
   | --- | --- |
   | **Return value** | SupportRequestDto |

   **Implementation:**

   1. **Validate** the request and its parameters:
      - Ensure `Id` is present.
      - If missing, throw a DP-422 (Client Error) exception.
   2. **Fetch SupportRequest:**
      - Retrieve the SupportRequest entity by `Id`.
      - If not found, throw a DP-404 (Not Found) exception.
      - Handle errors during retrieval by throwing a DP-500 (Technical Error) exception.
   3. **Authorization Check:**
      - Validate that the user has permission to view the support request.
   4. **Map to DTO:**
      - Map the SupportRequest entity to SupportRequestDto, including fetching the UserDto.
   5. **Return the Result:**
      - Return the SupportRequestDto.

   ---

   ### *GetList*

   Retrieves a paginated list of support requests.

   | **Arguments** | ListSupportRequestRequestDto request, Guid userId |
   | --- | --- |
   | **Return value** | ReturnListSupportRequestDto |

   **Implementation:**

   1. **Validate** the request and its parameters:
      - Ensure `PageLimit` and `PageOffset` are present and valid (PageLimit > 0, PageOffset >= 0).
      - If invalid, throw a DP-422 (Client Error) exception.
   2. **Authorization Check:**
      - Validate that the user has permission to list support requests.
   3. **Retrieve Paged SupportRequests:**
      - Fetch support requests from the database using the provided paging, sorting, and filter criteria.
      - Handle errors during retrieval by throwing a DP-500 (Technical Error) exception.
   4. **Map to DTOs:**
      - For each record, map the SupportRequest entity to SupportRequestDto, including fetching the UserDto.
   5. **Map to ReturnListSupportRequestDto:**
      - Map the paged result to ReturnListSupportRequestDto as per the Mapping Definitions Section.
   6. **Return the Result:**
      - Return the ReturnListSupportRequestDto.

   ---

   ### *Update*

   Updates the details of an existing support request.

   | **Arguments** | UpdateSupportRequestDto request, Guid userId |
   | --- | --- |
   | **Return value** | string |

   **Implementation:**

   1. **Validate** the request and its parameters:
      - Ensure `Id` is present and at least one updatable field (`Subject`, `Description`, `Status`, `Resolution`) is provided.
      - If invalid, throw a DP-422 (Client Error) exception.
   2. **Fetch SupportRequest:**
      - Retrieve the SupportRequest entity by `Id`.
      - If not found, throw a DP-404 (Not Found) exception.
      - Handle errors during retrieval by throwing a DP-500 (Technical Error) exception.
   3. **Authorization Check:**
      - Validate that the user has permission to update the support request.
   4. **Validate and Map Updates:**
      - For each provided field, validate and update the entity.
      - If updating `Status` to "Resolved", set `ResolvedAt` to the current timestamp and update `Resolution`.
      - Set `Changed` to the current timestamp.
      - Follow the mapping defined in the Mapping Definitions Section.
   5. **Perform Database Operations:**
      - Update the SupportRequest entity in the database.
      - Handle errors during update by throwing a DP-500 (Technical Error) exception.
   6. **Return the Result:**
      - Map the updated entity to SupportRequestDto, including fetching the UserDto.
      - Return the SupportRequestDto.

   ---
10. **API Exceptions Section**
    - Error codes and descriptions

11. **Interface Layer Section**

## *ISupportRequestService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | CreateSupportRequestDto request, Guid userId | string |
| Get | SupportRequestRequestDto request, Guid userId | SupportRequestDto |
| GetList | ListSupportRequestRequestDto request, Guid userId | ReturnListSupportRequestDto |
| Update | UpdateSupportRequestDto request, Guid userId | string |

- - -

12. **Controller Layer Section**

## *SupportRequestController*

### /support/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | Request<CreateSupportRequestDto> |
| **Response** | Response<SupportRequestDto> |

###

### /support/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | Request<SupportRequestRequestDto> |
| **Response** | Response<SupportRequestDto> |

###

### /support/list

| **HTTP Request Method** | GET |
| --- | --- |
| **Method** | GetList |
| **Request** | Request<ListSupportRequestRequestDto> |
| **Response** | Response<ReturnListSupportRequestDto> |

###

### /support/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | Request<UpdateSupportRequestDto> |
| **Response** | Response<SupportRequestDto> |

---
