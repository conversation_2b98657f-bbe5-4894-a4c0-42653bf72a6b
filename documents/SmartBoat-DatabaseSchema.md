# SmartBoat Fleet Management API – Database Schema

This document defines the relational database schema for the SmartBoat Fleet Management .NET API System. Each table is designed to support the logical entities and relationships described in the system's data model.

---

## User

| Name             | Data Type         | Nullable | Unique | Description                                      |
|------------------|------------------|----------|--------|--------------------------------------------------|
| Id               | uniqueidentifier | false    | true   | Unique identifier for the user                   |
| Name             | nvarchar(200)    | false    | false  | Full name of the user                            |
| Email            | nvarchar(200)    | false    | true   | Email address of the user                        |
| RoleId           | uniqueidentifier | false    | false  | Role assigned to the user (foreign key)          |
| Avatar           | nvarchar(500)    | true     | false  | URL or path to the user's avatar image           |
| Joined           | datetime2(7)     | false    | false  | Date/time when the user joined                   |
| Status           | nvarchar(50)     | false    | false  | Current status (e.g., Active, Inactive)          |
| LastLogin        | datetime2(7)     | true     | false  | Date/time of last login                          |
| TwoFactorEnabled | bit              | false    | false  | Whether two-factor authentication is enabled     |
| Company          | nvarchar(200)    | true     | false  | Name of the company the user belongs to          |
| Department       | nvarchar(100)    | true     | false  | Department within the company                    |
| Phone            | nvarchar(50)     | true     | false  | Contact phone number                             |
| Timezone         | nvarchar(50)     | true     | false  | User's preferred timezone                        |
| Language         | nvarchar(10)     | true     | false  | Preferred language code (e.g., 'en', 'el')       |
| Bio              | nvarchar(500)    | true     | false  | Short biography or description                   |
| Created          | datetime2(7)     | false    | false  | Timestamp when the user was created              |
| Changed          | datetime2(7)     | true     | false  | Timestamp when the user was last updated         |

```sql
CREATE TABLE [User] (
    Id uniqueidentifier PRIMARY KEY,
    Name nvarchar(200) NOT NULL,
    Email nvarchar(200) NOT NULL UNIQUE,
    RoleId uniqueidentifier NOT NULL,
    Avatar nvarchar(500) NULL,
    Joined datetime2(7) NOT NULL,
    Status nvarchar(50) NOT NULL,
    LastLogin datetime2(7) NULL,
    TwoFactorEnabled bit NOT NULL,
    Company nvarchar(200) NULL,
    Department nvarchar(100) NULL,
    Phone nvarchar(50) NULL,
    Timezone nvarchar(50) NULL,
    Language nvarchar(10) NULL,
    Bio nvarchar(500) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_User_Role FOREIGN KEY (RoleId) REFERENCES [Role](Id)
);
```

---

## Role

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the role                   |
| Name         | nvarchar(100)    | false    | true   | Name of the role (e.g., Admin, Manager)          |
| Description  | nvarchar(500)    | true     | false  | Description of the role                          |
| Created      | datetime2(7)     | false    | false  | Timestamp when the role was created              |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the role was last updated         |

```sql
CREATE TABLE [Role] (
    Id uniqueidentifier PRIMARY KEY,
    Name nvarchar(100) NOT NULL UNIQUE,
    Description nvarchar(500) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL
);
```

### RolePermission (Join Table)

| Name      | Data Type         | Nullable | Unique | Description                        |
|-----------|------------------|----------|--------|------------------------------------|
| RoleId    | uniqueidentifier | false    | false  | Role (foreign key)                 |
| Permission| nvarchar(100)    | false    | false  | Permission name                    |

```sql
CREATE TABLE [RolePermission] (
    RoleId uniqueidentifier NOT NULL,
    Permission nvarchar(100) NOT NULL,
    PRIMARY KEY (RoleId, Permission),
    CONSTRAINT FK_RolePermission_Role FOREIGN KEY (RoleId) REFERENCES [Role](Id)
);
```

---

## Customer

| Name            | Data Type         | Nullable | Unique | Description                                      |
|-----------------|------------------|----------|--------|--------------------------------------------------|
| Id              | uniqueidentifier | false    | true   | Unique identifier for the customer               |
| Name            | nvarchar(200)    | false    | false  | Name of the customer                             |
| ContactPerson   | nvarchar(200)    | true     | false  | Main contact person for the customer             |
| Email           | nvarchar(200)    | true     | false  | Contact email address                            |
| Phone           | nvarchar(50)     | true     | false  | Contact phone number                             |
| Status          | nvarchar(50)     | false    | false  | Current status (e.g., Active, Inactive)          |
| LastActive      | datetime2(7)     | true     | false  | Date/time of last activity                       |
| Created         | datetime2(7)     | false    | false  | Timestamp when the customer was created          |
| Changed         | datetime2(7)     | true     | false  | Timestamp when the customer was last updated     |

```sql
CREATE TABLE [Customer] (
    Id uniqueidentifier PRIMARY KEY,
    Name nvarchar(200) NOT NULL,
    ContactPerson nvarchar(200) NULL,
    Email nvarchar(200) NULL,
    Phone nvarchar(50) NULL,
    Status nvarchar(50) NOT NULL,
    LastActive datetime2(7) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL
);
```

---

## Company

| Name        | Data Type         | Nullable | Unique | Description                                      |
|-------------|------------------|----------|--------|--------------------------------------------------|
| Id          | uniqueidentifier | false    | true   | Unique identifier for the company                |
| Name        | nvarchar(200)    | false    | false  | Name of the company                              |
| Location    | nvarchar(200)    | true     | false  | Physical location or address                     |
| Industry    | nvarchar(100)    | true     | false  | Industry sector                                  |
| Status      | nvarchar(50)     | false    | false  | Current status (e.g., Active, Inactive)          |
| CustomerId  | uniqueidentifier | false    | false  | Associated customer ID (foreign key)             |
| LastUpdated | datetime2(7)     | true     | false  | Date/time of last update                         |
| Created     | datetime2(7)     | false    | false  | Timestamp when the company was created           |
| Changed     | datetime2(7)     | true     | false  | Timestamp when the company was last updated      |

```sql
CREATE TABLE [Company] (
    Id uniqueidentifier PRIMARY KEY,
    Name nvarchar(200) NOT NULL,
    Location nvarchar(200) NULL,
    Industry nvarchar(100) NULL,
    Status nvarchar(50) NOT NULL,
    CustomerId uniqueidentifier NOT NULL,
    LastUpdated datetime2(7) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_Company_Customer FOREIGN KEY (CustomerId) REFERENCES [Customer](Id)
);
```

---

## Vessel

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the vessel                 |
| Name         | nvarchar(200)    | false    | false  | Name of the vessel                               |
| Number       | nvarchar(100)    | false    | true   | Vessel registration or identification number     |
| Type         | nvarchar(100)    | true     | false  | Type/class of vessel                             |
| Location     | nvarchar(200)    | true     | false  | Current or home location                         |
| Status       | nvarchar(50)     | false    | false  | Operational status (e.g., Active, Inactive)      |
| StartDate    | datetime2(7)     | true     | false  | Date the vessel became operational               |
| EndDate      | datetime2(7)     | true     | false  | Date the vessel was decommissioned               |
| Image        | nvarchar(500)    | true     | false  | URL or path to vessel image                      |
| Onsigners    | int              | true     | false  | Number of crew currently onboard                 |
| Offsigners   | int              | true     | false  | Number of crew offboarded                        |
| CompanyId    | uniqueidentifier | false    | false  | Associated company ID (foreign key)              |
| LastUpdated  | datetime2(7)     | true     | false  | Date/time of last update                         |
| Created      | datetime2(7)     | false    | false  | Timestamp when the vessel was created            |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the vessel was last updated       |

```sql
CREATE TABLE [Vessel] (
    Id uniqueidentifier PRIMARY KEY,
    Name nvarchar(200) NOT NULL,
    Number nvarchar(100) NOT NULL UNIQUE,
    Type nvarchar(100) NULL,
    Location nvarchar(200) NULL,
    Status nvarchar(50) NOT NULL,
    StartDate datetime2(7) NULL,
    EndDate datetime2(7) NULL,
    Image nvarchar(500) NULL,
    Onsigners int NULL,
    Offsigners int NULL,
    CompanyId uniqueidentifier NOT NULL,
    LastUpdated datetime2(7) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_Vessel_Company FOREIGN KEY (CompanyId) REFERENCES [Company](Id)
);
```

---

## Sensor

| Name           | Data Type         | Nullable | Unique | Description                                      |
|----------------|------------------|----------|--------|--------------------------------------------------|
| Id             | uniqueidentifier | false    | true   | Unique identifier for the sensor                 |
| Name           | nvarchar(200)    | false    | false  | Name or label of the sensor                      |
| Type           | nvarchar(100)    | false    | false  | Type/category of the sensor                      |
| VesselId       | uniqueidentifier | false    | false  | Vessel the sensor is on (foreign key)            |
| Location       | nvarchar(200)    | true     | false  | Physical location on the vessel                  |
| Status         | nvarchar(50)     | false    | false  | Operational status (e.g., Active, Faulty)        |
| LastReading    | datetime2(7)     | true     | false  | Timestamp of the last reading                    |
| LastUpdated    | datetime2(7)     | true     | false  | Date/time of last update                         |
| AlertThreshold | nvarchar(200)    | true     | false  | Configured alert threshold value(s)              |
| Created        | datetime2(7)     | false    | false  | Timestamp when the sensor was created            |
| Changed        | datetime2(7)     | true     | false  | Timestamp when the sensor was last updated       |

```sql
CREATE TABLE [Sensor] (
    Id uniqueidentifier PRIMARY KEY,
    Name nvarchar(200) NOT NULL,
    Type nvarchar(100) NOT NULL,
    VesselId uniqueidentifier NOT NULL,
    Location nvarchar(200) NULL,
    Status nvarchar(50) NOT NULL,
    LastReading datetime2(7) NULL,
    LastUpdated datetime2(7) NULL,
    AlertThreshold nvarchar(200) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_Sensor_Vessel FOREIGN KEY (VesselId) REFERENCES [Vessel](Id)
);
```

---

## Subscription

| Name             | Data Type         | Nullable | Unique | Description                                      |
|------------------|------------------|----------|--------|--------------------------------------------------|
| Id               | uniqueidentifier | false    | true   | Unique identifier for the subscription           |
| Name             | nvarchar(200)    | false    | false  | Name of the subscription plan                    |
| Type             | nvarchar(100)    | false    | false  | Type/category of subscription                    |
| CustomerId       | uniqueidentifier | false    | false  | Associated customer ID (foreign key)             |
| StartDate        | datetime2(7)     | false    | false  | Subscription start date                          |
| EndDate          | datetime2(7)     | true     | false  | Subscription end date                            |
| Price            | decimal(18,2)    | false    | false  | Price of the subscription                        |
| BillingFrequency | nvarchar(50)     | false    | false  | Billing frequency (e.g., Monthly, Yearly)        |
| Status           | nvarchar(50)     | false    | false  | Current status (e.g., Active, Cancelled)         |
| SensorLimit      | int              | true     | false  | Maximum number of sensors allowed                |
| LastUpdated      | datetime2(7)     | true     | false  | Date/time of last update                         |
| Created          | datetime2(7)     | false    | false  | Timestamp when the subscription was created      |
| Changed          | datetime2(7)     | true     | false  | Timestamp when the subscription was last updated |

```sql
CREATE TABLE [Subscription] (
    Id uniqueidentifier PRIMARY KEY,
    Name nvarchar(200) NOT NULL,
    Type nvarchar(100) NOT NULL,
    CustomerId uniqueidentifier NOT NULL,
    StartDate datetime2(7) NOT NULL,
    EndDate datetime2(7) NULL,
    Price decimal(18,2) NOT NULL,
    BillingFrequency nvarchar(50) NOT NULL,
    Status nvarchar(50) NOT NULL,
    SensorLimit int NULL,
    LastUpdated datetime2(7) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_Subscription_Customer FOREIGN KEY (CustomerId) REFERENCES [Customer](Id)
);
```

### SubscriptionFeature (Join Table)

| Name            | Data Type         | Nullable | Unique | Description                        |
|-----------------|------------------|----------|--------|------------------------------------|
| SubscriptionId  | uniqueidentifier | false    | false  | Subscription (foreign key)         |
| Feature         | nvarchar(100)    | false    | false  | Feature name                       |

```sql
CREATE TABLE [SubscriptionFeature] (
    SubscriptionId uniqueidentifier NOT NULL,
    Feature nvarchar(100) NOT NULL,
    PRIMARY KEY (SubscriptionId, Feature),
    CONSTRAINT FK_SubscriptionFeature_Subscription FOREIGN KEY (SubscriptionId) REFERENCES [Subscription](Id)
);
```

---

## UsageRecord

| Name            | Data Type         | Nullable | Unique | Description                                      |
|-----------------|------------------|----------|--------|--------------------------------------------------|
| Id              | uniqueidentifier | false    | true   | Unique identifier for the usage record           |
| SubscriptionId  | uniqueidentifier | false    | false  | Associated subscription ID (foreign key)         |
| CustomerId      | uniqueidentifier | false    | false  | Associated customer ID (foreign key)             |
| UserId          | uniqueidentifier | false    | false  | Associated user ID (foreign key)                 |
| Feature         | nvarchar(100)    | false    | false  | Feature or resource being used                   |
| UsageValue      | int              | false    | false  | Amount of usage                                 |
| Timestamp       | datetime2(7)     | false    | false  | Date/time of usage record                        |
| Created         | datetime2(7)     | false    | false  | Timestamp when the record was created            |
| Changed         | datetime2(7)     | true     | false  | Timestamp when the record was last updated       |

```sql
CREATE TABLE [UsageRecord] (
    Id uniqueidentifier PRIMARY KEY,
    SubscriptionId uniqueidentifier NOT NULL,
    CustomerId uniqueidentifier NOT NULL,
    UserId uniqueidentifier NOT NULL,
    Feature nvarchar(100) NOT NULL,
    UsageValue int NOT NULL,
    Timestamp datetime2(7) NOT NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_UsageRecord_Subscription FOREIGN KEY (SubscriptionId) REFERENCES [Subscription](Id),
    CONSTRAINT FK_UsageRecord_Customer FOREIGN KEY (CustomerId) REFERENCES [Customer](Id),
    CONSTRAINT FK_UsageRecord_User FOREIGN KEY (UserId) REFERENCES [User](Id)
);
```

---

## Alert

| Name           | Data Type         | Nullable | Unique | Description                                      |
|----------------|------------------|----------|--------|--------------------------------------------------|
| Id             | uniqueidentifier | false    | true   | Unique identifier for the alert                  |
| Type           | nvarchar(50)     | false    | false  | Type of alert                                    |
| EntityId       | uniqueidentifier | false    | false  | ID of the related entity                         |
| EntityType     | nvarchar(50)     | false    | false  | Type of the related entity                       |
| Value          | nvarchar(200)    | true     | false  | Value that triggered the alert                   |
| Threshold      | nvarchar(200)    | true     | false  | Threshold that was crossed                       |
| Status         | nvarchar(50)     | false    | false  | Status of the alert                              |
| Message        | nvarchar(500)    | true     | false  | Alert message                                    |
| Timestamp      | datetime2(7)     | false    | false  | Date/time alert was generated                    |
| DeliveryStatus | nvarchar(50)     | true     | false  | Delivery status                                  |
| Created        | datetime2(7)     | false    | false  | Timestamp when the alert was created             |
| Changed        | datetime2(7)     | true     | false  | Timestamp when the alert was last updated        |

```sql
CREATE TABLE [Alert] (
    Id uniqueidentifier PRIMARY KEY,
    Type nvarchar(50) NOT NULL,
    EntityId uniqueidentifier NOT NULL,
    EntityType nvarchar(50) NOT NULL,
    Value nvarchar(200) NULL,
    Threshold nvarchar(200) NULL,
    Status nvarchar(50) NOT NULL,
    Message nvarchar(500) NULL,
    Timestamp datetime2(7) NOT NULL,
    DeliveryStatus nvarchar(50) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL
);
```

### AlertDelivery (Join Table)

| Name      | Data Type         | Nullable | Unique | Description                        |
|-----------|------------------|----------|--------|------------------------------------|
| AlertId   | uniqueidentifier | false    | false  | Alert (foreign key)                |
| UserId    | uniqueidentifier | false    | false  | User notified                      |

```sql
CREATE TABLE [AlertDelivery] (
    AlertId uniqueidentifier NOT NULL,
    UserId uniqueidentifier NOT NULL,
    PRIMARY KEY (AlertId, UserId),
    CONSTRAINT FK_AlertDelivery_Alert FOREIGN KEY (AlertId) REFERENCES [Alert](Id),
    CONSTRAINT FK_AlertDelivery_User FOREIGN KEY (UserId) REFERENCES [User](Id)
);
```

---

## Notification

| Name           | Data Type         | Nullable | Unique | Description                                      |
|----------------|------------------|----------|--------|--------------------------------------------------|
| Id             | uniqueidentifier | false    | true   | Unique identifier for the notification           |
| UserId         | uniqueidentifier | false    | false  | User to whom the notification is sent            |
| EventType      | nvarchar(50)     | false    | false  | Type of event triggering the notification        |
| Content        | nvarchar(1000)   | false    | false  | Notification content/message                     |
| Channel        | nvarchar(50)     | false    | false  | Delivery channel                                 |
| Status         | nvarchar(50)     | false    | false  | Status (e.g., Sent, Read, Failed)                |
| Timestamp      | datetime2(7)     | false    | false  | Date/time notification was sent                  |
| PreferenceId   | uniqueidentifier | true     | false  | Associated notification preference (FK)          |
| Created        | datetime2(7)     | false    | false  | Timestamp when the notification was created      |
| Changed        | datetime2(7)     | true     | false  | Timestamp when the notification was last updated |

```sql
CREATE TABLE [Notification] (
    Id uniqueidentifier PRIMARY KEY,
    UserId uniqueidentifier NOT NULL,
    EventType nvarchar(50) NOT NULL,
    Content nvarchar(1000) NOT NULL,
    Channel nvarchar(50) NOT NULL,
    Status nvarchar(50) NOT NULL,
    Timestamp datetime2(7) NOT NULL,
    PreferenceId uniqueidentifier NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_Notification_User FOREIGN KEY (UserId) REFERENCES [User](Id),
    CONSTRAINT FK_Notification_Preference FOREIGN KEY (PreferenceId) REFERENCES [NotificationPreference](Id)
);
```

---

## NotificationPreference

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the preference             |
| UserId       | uniqueidentifier | false    | false  | Associated user ID (foreign key)                 |
| EventType    | nvarchar(50)     | false    | false  | Event type for which preference applies          |
| Channel      | nvarchar(50)     | false    | false  | Preferred channel                                |
| Enabled      | bit              | false    | false  | Whether notifications are enabled                |
| UpdatedAt    | datetime2(7)     | true     | false  | Date/time preference was last updated            |
| Created      | datetime2(7)     | false    | false  | Timestamp when the preference was created        |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the preference was last updated   |

```sql
CREATE TABLE [NotificationPreference] (
    Id uniqueidentifier PRIMARY KEY,
    UserId uniqueidentifier NOT NULL,
    EventType nvarchar(50) NOT NULL,
    Channel nvarchar(50) NOT NULL,
    Enabled bit NOT NULL,
    UpdatedAt datetime2(7) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_NotificationPreference_User FOREIGN KEY (UserId) REFERENCES [User](Id)
);
```

---

## AuditLog

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the audit log              |
| UserId       | uniqueidentifier | false    | false  | User who performed the action                    |
| EntityType   | nvarchar(50)     | false    | false  | Type of entity affected                          |
| EntityId     | uniqueidentifier | false    | false  | ID of entity affected                            |
| Action       | nvarchar(50)     | false    | false  | Action performed                                 |
| Details      | nvarchar(1000)   | true     | false  | Details of the change                            |
| Timestamp    | datetime2(7)     | false    | false  | Date/time of the action                          |
| Created      | datetime2(7)     | false    | false  | Timestamp when the log was created               |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the log was last updated          |

```sql
CREATE TABLE [AuditLog] (
    Id uniqueidentifier PRIMARY KEY,
    UserId uniqueidentifier NOT NULL,
    EntityType nvarchar(50) NOT NULL,
    EntityId uniqueidentifier NOT NULL,
    Action nvarchar(50) NOT NULL,
    Details nvarchar(1000) NULL,
    Timestamp datetime2(7) NOT NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_AuditLog_User FOREIGN KEY (UserId) REFERENCES [User](Id)
);
```

---

## AssignmentEvent

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the event                  |
| EntityType   | nvarchar(50)     | false    | false  | Type of entity assigned/reassigned               |
| EntityId     | uniqueidentifier | false    | false  | ID of entity assigned/reassigned                 |
| FromId       | uniqueidentifier | true     | false  | Previous parent/entity ID                        |
| ToId         | uniqueidentifier | true     | false  | New parent/entity ID                             |
| EventType    | nvarchar(50)     | false    | false  | Type of event                                    |
| UserId       | uniqueidentifier | false    | false  | User who performed the action                    |
| Timestamp    | datetime2(7)     | false    | false  | Date/time of the event                           |
| Created      | datetime2(7)     | false    | false  | Timestamp when the event was created             |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the event was last updated        |

```sql
CREATE TABLE [AssignmentEvent] (
    Id uniqueidentifier PRIMARY KEY,
    EntityType nvarchar(50) NOT NULL,
    EntityId uniqueidentifier NOT NULL,
    FromId uniqueidentifier NULL,
    ToId uniqueidentifier NULL,
    EventType nvarchar(50) NOT NULL,
    UserId uniqueidentifier NOT NULL,
    Timestamp datetime2(7) NOT NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_AssignmentEvent_User FOREIGN KEY (UserId) REFERENCES [User](Id)
);
```

---

## StatusHistory

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the status history         |
| EntityType   | nvarchar(50)     | false    | false  | Type of entity                                   |
| EntityId     | uniqueidentifier | false    | false  | ID of the entity                                 |
| OldStatus    | nvarchar(50)     | false    | false  | Previous status                                  |
| NewStatus    | nvarchar(50)     | false    | false  | New status                                       |
| UserId       | uniqueidentifier | false    | false  | User who changed the status                      |
| Timestamp    | datetime2(7)     | false    | false  | Date/time of the status change                   |
| Created      | datetime2(7)     | false    | false  | Timestamp when the record was created            |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the record was last updated       |

```sql
CREATE TABLE [StatusHistory] (
    Id uniqueidentifier PRIMARY KEY,
    EntityType nvarchar(50) NOT NULL,
    EntityId uniqueidentifier NOT NULL,
    OldStatus nvarchar(50) NOT NULL,
    NewStatus nvarchar(50) NOT NULL,
    UserId uniqueidentifier NOT NULL,
    Timestamp datetime2(7) NOT NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_StatusHistory_User FOREIGN KEY (UserId) REFERENCES [User](Id)
);
```

---

## Report

| Name            | Data Type         | Nullable | Unique | Description                                      |
|-----------------|------------------|----------|--------|--------------------------------------------------|
| Id              | uniqueidentifier | false    | true   | Unique identifier for the report                 |
| Type            | nvarchar(50)     | false    | false  | Type of report                                   |
| Criteria        | nvarchar(1000)   | true     | false  | Criteria/filters used to generate the report     |
| GeneratedBy     | uniqueidentifier | false    | false  | User who generated the report                    |
| GeneratedAt     | datetime2(7)     | false    | false  | Date/time report was generated                   |
| Status          | nvarchar(50)     | false    | false  | Status (e.g., Completed, Failed)                 |
| DeliveryChannel | nvarchar(50)     | true     | false  | Channel used for delivery                        |
| DeliveryStatus  | nvarchar(50)     | true     | false  | Delivery status                                  |
| Created         | datetime2(7)     | false    | false  | Timestamp when the report was created            |
| Changed         | datetime2(7)     | true     | false  | Timestamp when the report was last updated       |

```sql
CREATE TABLE [Report] (
    Id uniqueidentifier PRIMARY KEY,
    Type nvarchar(50) NOT NULL,
    Criteria nvarchar(1000) NULL,
    GeneratedBy uniqueidentifier NOT NULL,
    GeneratedAt datetime2(7) NOT NULL,
    Status nvarchar(50) NOT NULL,
    DeliveryChannel nvarchar(50) NULL,
    DeliveryStatus nvarchar(50) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_Report_User FOREIGN KEY (GeneratedBy) REFERENCES [User](Id)
);
```

---

## SupportRequest

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the support request        |
| UserId       | uniqueidentifier | false    | false  | User who submitted the request                   |
| Subject      | nvarchar(200)    | false    | false  | Subject of the support request                   |
| Description  | nvarchar(1000)   | false    | false  | Detailed description of the issue                |
| Status       | nvarchar(50)     | false    | false  | Status (e.g., Open, In Progress, Resolved)       |
| CreatedAt    | datetime2(7)     | false    | false  | Date/time request was created                    |
| ResolvedAt   | datetime2(7)     | true     | false  | Date/time request was resolved                   |
| Resolution   | nvarchar(1000)   | true     | false  | Resolution details                               |
| Created      | datetime2(7)     | false    | false  | Timestamp when the request was created           |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the request was last updated      |

```sql
CREATE TABLE [SupportRequest] (
    Id uniqueidentifier PRIMARY KEY,
    UserId uniqueidentifier NOT NULL,
    Subject nvarchar(200) NOT NULL,
    Description nvarchar(1000) NOT NULL,
    Status nvarchar(50) NOT NULL,
    CreatedAt datetime2(7) NOT NULL,
    ResolvedAt datetime2(7) NULL,
    Resolution nvarchar(1000) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_SupportRequest_User FOREIGN KEY (UserId) REFERENCES [User](Id)
);
```

---

## Feedback

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the feedback               |
| UserId       | uniqueidentifier | false    | false  | User who submitted the feedback                  |
| Type         | nvarchar(50)     | false    | false  | Type (e.g., Usability, Satisfaction, Bug)        |
| Content      | nvarchar(1000)   | false    | false  | Feedback content                                 |
| SubmittedAt  | datetime2(7)     | false    | false  | Date/time feedback was submitted                 |
| Status       | nvarchar(50)     | false    | false  | Status (e.g., New, Reviewed, Actioned)           |
| Created      | datetime2(7)     | false    | false  | Timestamp when the feedback was created          |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the feedback was last updated     |

```sql
CREATE TABLE [Feedback] (
    Id uniqueidentifier PRIMARY KEY,
    UserId uniqueidentifier NOT NULL,
    Type nvarchar(50) NOT NULL,
    Content nvarchar(1000) NOT NULL,
    SubmittedAt datetime2(7) NOT NULL,
    Status nvarchar(50) NOT NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_Feedback_User FOREIGN KEY (UserId) REFERENCES [User](Id)
);
```

---

## SensorDataPoint

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the data point             |
| SensorId     | uniqueidentifier | false    | false  | Sensor (foreign key)                             |
| Time         | datetime2(7)     | false    | false  | ISO 8601 timestamp of the data point             |
| Timestamp    | bigint           | false    | false  | Unix timestamp                                   |
| Temperature  | float            | true     | false  | Temperature reading                              |
| Humidity     | float            | true     | false  | Humidity reading                                 |
| Created      | datetime2(7)     | false    | false  | Timestamp when the data point was created        |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the data point was last updated   |

```sql
CREATE TABLE [SensorDataPoint] (
    Id uniqueidentifier PRIMARY KEY,
    SensorId uniqueidentifier NOT NULL,
    Time datetime2(7) NOT NULL,
    Timestamp bigint NOT NULL,
    Temperature float NULL,
    Humidity float NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_SensorDataPoint_Sensor FOREIGN KEY (SensorId) REFERENCES [Sensor](Id)
);
```

---

## VesselPathPoint

| Name         | Data Type         | Nullable | Unique | Description                                      |
|--------------|------------------|----------|--------|--------------------------------------------------|
| Id           | uniqueidentifier | false    | true   | Unique identifier for the path point             |
| VesselId     | uniqueidentifier | false    | false  | Vessel (foreign key)                             |
| Lat          | float            | false    | false  | Latitude of the vessel at the point              |
| Lng          | float            | false    | false  | Longitude of the vessel at the point             |
| Timestamp    | datetime2(7)     | false    | false  | ISO 8601 timestamp of the position               |
| Location     | nvarchar(200)    | true     | false  | Human-readable location or description           |
| Created      | datetime2(7)     | false    | false  | Timestamp when the path point was created        |
| Changed      | datetime2(7)     | true     | false  | Timestamp when the path point was last updated   |

```sql
CREATE TABLE [VesselPathPoint] (
    Id uniqueidentifier PRIMARY KEY,
    VesselId uniqueidentifier NOT NULL,
    Lat float NOT NULL,
    Lng float NOT NULL,
    Timestamp datetime2(7) NOT NULL,
    Location nvarchar(200) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    CONSTRAINT FK_VesselPathPoint_Vessel FOREIGN KEY (VesselId) REFERENCES [Vessel](Id)
);
```

---

## Entity Relationship Notes

- All primary keys are `Id` columns of type `uniqueidentifier`.
- All tables include `Created` (not null) and `Changed` (nullable) timestamp columns.
- Foreign key constraints are defined for all relationships.
- Array fields (e.g., permissions, features, deliveredTo) are normalized into join tables.
- Many-to-many and one-to-many relationships are implemented via foreign keys or join tables as appropriate.

---