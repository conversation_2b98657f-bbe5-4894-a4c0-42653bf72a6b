# SmartBoat Fleet Management System – System Description

## 1. Overview

The SmartBoat Fleet Management System is a .NET API platform designed to support maritime companies in monitoring, managing, and analyzing their fleets, sensors, and subscriptions. The system provides a robust foundation for real-time operational insight, customer management, and business process automation in the maritime sector.

## 2. Purpose and Objectives

- **Primary Goal:**  
  To deliver a centralized, autonomous backend for maritime fleet management, enabling efficient oversight of vessels, sensors, companies, customers, and subscriptions.

- **Key Objectives:**  
  - Real-time monitoring of vessel and sensor data.
  - Streamlined management of customers, companies, and subscriptions.
  - Support for business processes such as onboarding, alerting, and reporting.
  - High system uptime, user satisfaction, and data accuracy.

## 3. Core Business Entities and Relationships

The system is structured around the following core entities and their relationships:

- **User:**  
  Represents an individual with access to the system. Users may be associated with a Customer and have access to one or more Subscriptions. User attributes include role, company, department, and contact details.

- **Customer:**  
  Represents a maritime client organization. Customers own Companies and purchase Subscriptions. Each Customer may have multiple Users associated.

- **Company:**  
  Represents a business entity under a Customer, operating one or more Vessels. Each Company is linked to a Customer.

- **Vessel:**  
  Represents a ship or boat operated by a Company. Each Vessel is equipped with Sensors and records its path.

- **Sensor:**  
  Represents an IoT device installed on a Vessel, generating real-time data points (e.g., temperature, humidity).

- **Subscription:**  
  Represents a service plan purchased by a Customer, defining access, features, and sensor limits.

- **SensorDataPoint:**  
  Represents a single data reading from a Sensor (e.g., temperature, humidity, timestamp).

- **VesselPathPoint:**  
  Represents a recorded position of a Vessel (latitude, longitude, timestamp).

### Entity Relationships

- Users may be associated with Customers and have access to Subscriptions.
- Customers own Companies and purchase Subscriptions.
- Companies operate Vessels.
- Vessels are equipped with Sensors and record VesselPathPoints.
- Sensors generate SensorDataPoints.

## 4. User Roles and Access

- **Admin:**  
  Full access to all system features, including management of users, customers, companies, vessels, sensors, and subscriptions.

- **Customer User:**  
  Associated with a specific Customer. Can view and manage their own companies, vessels, sensors, and subscriptions according to their permissions.

- **Other Roles (optional):**  
  The system supports extensible roles (e.g., Manager, Technician, Viewer) for more granular access control.

## 5. System Modules

### 5.1 User & Authentication Management

- User registration, authentication, and role-based access control.
- Association of Users with Customers and Subscriptions.
- User profile management and security features (e.g., two-factor authentication).

### 5.2 Customer & Company Management

- Customer onboarding and profile management.
- Management of Companies under each Customer, including company details and operational status.

### 5.3 Vessel & Sensor Management

- Vessel registration, assignment to Companies, and operational tracking.
- Sensor onboarding, assignment to Vessels, and real-time monitoring.
- Management of vessel paths and sensor data streams.

### 5.4 Subscription Management

- Creation and management of Subscription plans.
- Assignment of Subscriptions to Customers and Users.
- Enforcement of plan limits (e.g., sensor limits, features).
- Billing and usage tracking.

### 5.5 Monitoring, Analytics & Alerting

- Real-time dashboards for vessel and sensor status.
- Historical data analysis and reporting.
- Automated alerts for sensor anomalies, vessel events, and subscription changes.

## 6. Key Business Processes

- **User Onboarding:**  
  Registering new users, associating them with customers, and assigning roles.

- **Customer & Company Onboarding:**  
  Creating new customer profiles and their associated companies.

- **Vessel & Sensor Onboarding:**  
  Registering vessels and sensors, assigning them to companies and vessels, respectively.

- **Real-Time Monitoring:**  
  Ingesting and displaying live sensor data and vessel positions.

- **Subscription Lifecycle:**  
  Managing subscription purchases, renewals, upgrades/downgrades, and enforcing plan limits.

- **Alert Notification:**  
  Automated notifications for operational anomalies and business events.

- **Reporting & Analytics:**  
  Generating reports on fleet activity, sensor data, and subscription usage.

## 7. Scale and Performance

- Supports up to 100 customers, 1,000 companies, 1,000 vessels, and 10,000 sensors.
- Designed for high availability, low latency, and efficient onboarding.

## 8. Success Indicators

- High system uptime and reliability.
- User satisfaction and positive feedback.
- Accurate, timely real-time data reporting.
- Efficient onboarding of users, customers, vessels, and sensors.
- Minimal support requests due to intuitive workflows.

## 9. Integrations and Extensibility

- **Current State:**  
  The system is autonomous, with all data managed internally.

- **Future-Ready:**  
  Architecture allows for future integration with external data sources (e.g., AIS, weather APIs, billing providers).

## 10. Dependencies and Assumptions

- Built on the .NET platform.
- Assumes secure, reliable network connectivity for real-time data ingestion and user access.
- All data is managed internally at launch.

## 11. Entity-Relationship Diagram (Business View)

```mermaid
erDiagram
    User {
        number id PK
        string name
        string email
        string role
        string avatar
        string joined
        string status
        string lastLogin
        boolean twoFactorEnabled
        string company
        string department
        string phone
        string timezone
        string language
        string bio
    }
    Customer {
        number id PK
        string name
        string contactPerson
        string email
        string phone
        number companies
        number vessels
        number sensors
        string status
        string lastActive
    }
    Company {
        number id PK
        string name
        string location
        string industry
        number vessels
        number sensors
        string status
        number customerId FK
        string lastUpdated
    }
    Vessel {
        number id PK
        string name
        string number
        string type
        string location
        string status
        string startDate
        string endDate
        string image
        number onsigners
        number offsigners
        number sensors
        string lastUpdated
        number companyId FK
    }
    Sensor {
        number id PK
        string name
        string type
        string vessel
        string location
        string status
        string lastReading
        string lastUpdated
        string alertThreshold
    }
    Subscription {
        number id PK
        string name
        string type
        number customerId FK
        string customerName
        string startDate
        string endDate
        number price
        string billingFrequency
        string status
        number sensorLimit
        array features
        string lastUpdated
    }
    SensorDataPoint {
        string time
        number timestamp
        number temperature
        number humidity
    }
    VesselPathPoint {
        number lat
        number lng
        string timestamp
        string location
    }
    User }|--o{ Subscription : "has access to"
    User ||--o| Customer : "may be associated with"
    Customer ||--o{ Company : "owns"
    Customer ||--o{ Subscription : "purchases"
    Company ||--o{ Vessel : "operates"
    Vessel ||--o{ Sensor : "equipped with"
    Sensor ||--o{ SensorDataPoint : "generates"
    Vessel ||--o{ VesselPathPoint : "records position as"
```

---

*This System Description provides a comprehensive overview of the SmartBoat Fleet Management System, its business entities, processes, and operational context, serving as a foundation for further technical and functional specifications.*