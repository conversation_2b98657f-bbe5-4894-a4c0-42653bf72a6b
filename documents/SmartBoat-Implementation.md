# SmartBoat Fleet Management .NET API – Implementation

This document provides detailed step-by-step implementation instructions and pseudocode documentation for each endpoint in the SmartBoat Fleet Management .NET API System. Each section includes a description, implementation logic, and a visual flow diagram.

## Architectural Patterns

### Factory Pattern for Circular Dependency Resolution

**Problem:**  
The SensorService and VesselService had a circular dependency where:
- SensorService needed IVesselService to validate vessels when creating/updating sensors
- VesselService needed ISensorService to fetch sensors for vessels

**Solution: Factory Pattern**  
Implemented IVesselServiceFactory to break the circular dependency at construction time while maintaining the service relationship at runtime.

**Implementation Details:**
- Created `IVesselServiceFactory` interface with `CreateVesselService()` method
- Implemented `VesselServiceFactory` class that uses `IServiceProvider` to create VesselService instances
- Updated `SensorService` to inject `IVesselServiceFactory` instead of `IVesselService`
- SensorService calls `_vesselServiceFactory.CreateVesselService()` when it needs to validate vessels
- VesselService continues to inject `ISensorService` directly for fetching sensors

**Dependency Injection Registration:**
```csharp
// Register VesselService first
builder.Services.AddScoped<IVesselService, VesselService>();

// Register factory to break circular dependency
builder.Services.AddScoped<IVesselServiceFactory, VesselServiceFactory>();

// Register SensorService with factory dependency
builder.Services.AddScoped<ISensorService, SensorService>();
```

**Benefits:**
- Breaks circular dependency at construction time
- Maintains service relationships at runtime
- Preserves domain boundaries and separation of concerns
- No direct database access between domains

---

## 1. UserController

### /user/register

**Description:**  
Registers a new user in the system. Used for onboarding new users with required profile information.

**Implementation:**

- Validate that the request payload contains the following necessary properties: name, email, password, roleId.
- If any required property is missing or invalid (e.g., email format, password complexity), return an error response indicating the specific validation error.
- Check that the email is unique in the Users table. If a user with the same email exists, return a conflict error.
- Hash the password using a secure, one-way encryption algorithm.
- Create a new user record in the Users table with the provided details and default values for status ("Active"), joined date (current timestamp), and twoFactorEnabled (false).
- Assign the user to the specified role and set any additional profile fields (company, department, phone, timezone, language, bio) if provided.
- Log the registration event with user ID and timestamp.
- Return the created user profile in the response payload, excluding the password.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: name, email, password, roleId]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check email uniqueness in Users table]
    E --> F{Email already exists?}
    F -- Yes --> G[Return error: Email already registered]
    F -- No --> H[Hash password securely]
    H --> I[Create user record with status Active, joined date, twoFactorEnabled false]
    I --> J[Assign role and set profile fields]
    J --> K[Log registration event]
    K --> L[Return user profile in response]
    D --> M[End]
    G --> M
    L --> M
```

---

### /user/login

**Description:**  
Authenticates a user and returns a JWT token along with user profile details.

**Implementation:**

- Validate that the request payload contains the required properties: email and password.
- If any required property is missing, return an error response indicating the missing field.
- Fetch the user record from the Users table by email.
- If the user does not exist or is inactive, return an authentication error.
- Verify the provided password against the stored hashed password.
- If the password is incorrect, return an authentication error.
- If two-factor authentication is enabled for the user, initiate the second factor process and require verification before issuing a token.
- If authentication is successful, generate a JWT token for the user session.
- Log the login event with user ID and timestamp.
- Return the JWT token and user profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: email, password]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing fields]
    C -- Yes --> E[Fetch user by email from Users table]
    E --> F{User exists and is active?}
    F -- No --> G[Return error: Authentication failed]
    F -- Yes --> H[Verify password]
    H --> I{Password correct?}
    I -- No --> G
    I -- Yes --> J{Two-factor enabled?}
    J -- Yes --> K[Initiate two-factor process]
    K --> L{Second factor verified?}
    L -- No --> G
    L -- Yes --> M[Generate JWT token]
    J -- No --> M
    M --> N[Log login event]
    N --> O[Return token and user profile]
    D --> P[End]
    G --> P
    O --> P
```

---

### /user/get

**Description:**  
Retrieves the profile details of a specific user by user ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If the id is missing, return an error response indicating the missing field.
- Fetch the user record from the Users table by id.
- If the user does not exist, return a not found error.
- Check that the requesting user has permission to view the user profile.
- Return the user profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch user by id from Users table]
    E --> F{User exists?}
    F -- No --> G[Return error: User not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return user profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /user/list

**Description:**  
Retrieves a paginated list of users, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list users.
- Apply any provided filter criteria to the Users table query.
- Fetch the list of users according to the filter and paging parameters.
- Return the list of user profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch users]
    H --> I[Return user list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /user/update

**Description:**  
Updates the profile details of an existing user.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field.
- If id is missing, return an error response.
- Fetch the user record from the Users table by id.
- If the user does not exist, return a not found error.
- Check that the requesting user has permission to update the user profile.
- Validate the updated fields for correct format and business rules.
- If updating the email, check for uniqueness.
- Update the user record with the provided fields.
- Log the update event with user ID and timestamp.
- Return the updated user profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch user by id from Users table]
    E --> F{User exists?}
    F -- No --> G[Return error: User not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[If email updated, check uniqueness]
    N --> O{Email unique?}
    O -- No --> P[Return error: Email already registered]
    O -- Yes --> Q[Update user record]
    Q --> R[Log update event]
    R --> S[Return updated user profile]
    D --> T[End]
    G --> T
    J --> T
    M --> T
    P --> T
    S --> T
```

---

### /user/deactivate

**Description:**  
Deactivates a user account, marking the user as inactive.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the user record from the Users table by id.
- If the user does not exist, return a not found error.
- Check that the requesting user has permission to deactivate the user.
- Update the user status to "Inactive".
- Log the deactivation event with user ID and timestamp.
- Return the updated user profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch user by id from Users table]
    E --> F{User exists?}
    F -- No --> G[Return error: User not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Update user status to Inactive]
    K --> L[Log deactivation event]
    L --> M[Return updated user profile]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

### /user/enable-2fa

**Description:**  
Enables or disables two-factor authentication for a user account.

**Implementation:**

- Validate that the request payload contains the required properties: id and enable (boolean).
- If any required property is missing, return an error response.
- Fetch the user record from the Users table by id.
- If the user does not exist, return a not found error.
- Check that the requesting user has permission to update two-factor settings.
- Update the twoFactorEnabled field to the value of enable.
- Log the two-factor update event with user ID and timestamp.
- Return the updated user profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: id, enable]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing fields]
    C -- Yes --> E[Fetch user by id from Users table]
    E --> F{User exists?}
    F -- No --> G[Return error: User not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Update twoFactorEnabled field]
    K --> L[Log two-factor update event]
    L --> M[Return updated user profile]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

### /user/reset-password

**Description:**  
Initiates a password reset process for a user by email.

**Implementation:**

- Validate that the request payload contains the required property: email.
- If email is missing, return an error response.
- Fetch the user record from the Users table by email.
- If the user does not exist, return a not found error.
- Generate a password reset token and store it with an expiration timestamp.
- Send a password reset email to the user with instructions and the reset token.
- Log the password reset request event with user ID and timestamp.
- Return a status message indicating that the reset email was sent.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: email]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing email]
    C -- Yes --> E[Fetch user by email from Users table]
    E --> F{User exists?}
    F -- No --> G[Return error: User not found]
    F -- Yes --> H[Generate password reset token]
    H --> I[Store token with expiration]
    I --> J[Send reset email to user]
    J --> K[Log password reset request event]
    K --> L[Return status: Reset email sent]
    D --> M[End]
    G --> M
    L --> M
```

---

## 2. RoleController

### /role/create

**Description:**
Creates a new user role with specified permissions and description.

**Implementation:**

- Validate that the request payload contains the following necessary properties: name, permissions (array), and description.
- If any required property is missing or invalid (e.g., name not unique, permissions not a valid array), return an error response indicating the specific validation error.
- Check that the role name is unique in the Roles table. If a role with the same name exists, return a conflict error.
- Create a new role record in the Roles table with the provided details and set createdAt and updatedAt to the current timestamp.
- Log the role creation event with user ID and timestamp.
- Return the created role in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: name, permissions, description]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check role name uniqueness in Roles table]
    E --> F{Role name already exists?}
    F -- Yes --> G[Return error: Role name already exists]
    F -- No --> H[Create role record with timestamps]
    H --> I[Log role creation event]
    I --> J[Return created role in response]
    D --> K[End]
    G --> K
    J --> K
```

---

### /role/get

**Description:**
Retrieves the details of a specific role by role ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the role record from the Roles table by id.
- If the role does not exist, return a not found error.
- Check that the requesting user has permission to view roles.
- Return the role details in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch role by id from Roles table]
    E --> F{Role exists?}
    F -- No --> G[Return error: Role not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return role details in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /role/list

**Description:**
Retrieves a paginated list of roles, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list roles.
- Apply any provided filter criteria to the Roles table query.
- Fetch the list of roles according to the filter and paging parameters.
- Return the list of roles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch roles]
    H --> I[Return role list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /role/update

**Description:**
Updates the details of an existing role.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (name, permissions, description).
- If id is missing, return an error response.
- Fetch the role record from the Roles table by id.
- If the role does not exist, return a not found error.
- Check that the requesting user has permission to update roles.
- Validate the updated fields for correct format and business rules.
- If updating the name, check for uniqueness.
- Update the role record with the provided fields and set updatedAt to the current timestamp.
- Log the role update event with user ID and timestamp.
- Return the updated role in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch role by id from Roles table]
    E --> F{Role exists?}
    F -- No --> G[Return error: Role not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[If name updated, check uniqueness]
    N --> O{Name unique?}
    O -- No --> P[Return error: Role name already exists]
    O -- Yes --> Q[Update role record and set updatedAt]
    Q --> R[Log role update event]
    R --> S[Return updated role]
    D --> T[End]
    G --> T
    J --> T
    M --> T
    P --> T
    S --> T
```

---

### /role/delete

**Description:**
Deletes a role by role ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the role record from the Roles table by id.
- If the role does not exist, return a not found error.
- Check that the requesting user has permission to delete roles.
- Check for dependencies (e.g., users assigned to this role). If dependencies exist, block deletion and return a conflict error.
- Delete the role record from the Roles table.
- Log the role deletion event with user ID and timestamp.
- Return a status message indicating successful deletion.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch role by id from Roles table]
    E --> F{Role exists?}
    F -- No --> G[Return error: Role not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Check for dependencies]
    K --> L{Dependencies exist?}
    L -- Yes --> M[Return error: Role in use]
    L -- No --> N[Delete role record]
    N --> O[Log role deletion event]
    O --> P[Return status: Deleted]
    D --> Q[End]
    G --> Q
    J --> Q
    M --> Q
    P --> Q
```

---

## 3. CustomerController

### /customer/create

**Description:**
Creates a new customer record in the system.

**Implementation:**

- Validate that the request payload contains the following necessary properties: name, contactPerson, email, phone.
- If any required property is missing or invalid (e.g., email format), return an error response indicating the specific validation error.
- Check that the email and name are unique in the Customers table. If a customer with the same email or name exists, return a conflict error.
- Create a new customer record in the Customers table with the provided details, set status to "Active", and set lastActive to the current timestamp.
- Log the customer creation event with user ID and timestamp.
- Return the created customer profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: name, contactPerson, email, phone]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check email and name uniqueness in Customers table]
    E --> F{Email or name already exists?}
    F -- Yes --> G[Return error: Customer already exists]
    F -- No --> H[Create customer record with status Active, lastActive]
    H --> I[Log customer creation event]
    I --> J[Return customer profile in response]
    D --> K[End]
    G --> K
    J --> K
```

---

### /customer/get

**Description:**
Retrieves the details of a specific customer by customer ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the customer record from the Customers table by id.
- If the customer does not exist, return a not found error.
- Check that the requesting user has permission to view customer details.
- Return the customer profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch customer by id from Customers table]
    E --> F{Customer exists?}
    F -- No --> G[Return error: Customer not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return customer profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /customer/list

**Description:**
Retrieves a paginated list of customers, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list customers.
- Apply any provided filter criteria to the Customers table query.
- Fetch the list of customers according to the filter and paging parameters.
- Return the list of customer profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch customers]
    H --> I[Return customer list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /customer/update

**Description:**
Updates the details of an existing customer.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (name, contactPerson, email, phone).
- If id is missing, return an error response.
- Fetch the customer record from the Customers table by id.
- If the customer does not exist, return a not found error.
- Check that the requesting user has permission to update customer details.
- Validate the updated fields for correct format and business rules.
- If updating the email or name, check for uniqueness.
- Update the customer record with the provided fields and set lastActive to the current timestamp.
- Log the customer update event with user ID and timestamp.
- Return the updated customer profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch customer by id from Customers table]
    E --> F{Customer exists?}
    F -- No --> G[Return error: Customer not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[If email or name updated, check uniqueness]
    N --> O{Unique?}
    O -- No --> P[Return error: Customer already exists]
    O -- Yes --> Q[Update customer record and set lastActive]
    Q --> R[Log customer update event]
    R --> S[Return updated customer profile]
    D --> T[End]
    G --> T
    J --> T
    M --> T
    P --> T
    S --> T
```

---

### /customer/deactivate

**Description:**
Deactivates a customer record, marking the customer as inactive.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the customer record from the Customers table by id.
- If the customer does not exist, return a not found error.
- Check that the requesting user has permission to deactivate the customer.
- Update the customer status to "Inactive".
- Log the deactivation event with user ID and timestamp.
- Return the updated customer profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch customer by id from Customers table]
    E --> F{Customer exists?}
    F -- No --> G[Return error: Customer not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Update customer status to Inactive]
    K --> L[Log deactivation event]
    L --> M[Return updated customer profile]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

## 4. CompanyController

### /company/create

**Description:**
Creates a new company record in the system.

**Implementation:**

- Validate that the request payload contains the following necessary properties: name, location, industry, customerId.
- If any required property is missing or invalid, return an error response indicating the specific validation error.
- Check that the name is unique in the Companies table. If a company with the same name exists, return a conflict error.
- Check that the customerId exists in the Customers table. If not, return a not found error.
- Create a new company record in the Companies table with the provided details, set status to "Active", and set lastUpdated to the current timestamp.
- Log the company creation event with user ID and timestamp.
- Return the created company profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: name, location, industry, customerId]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check name uniqueness in Companies table]
    E --> F{Name already exists?}
    F -- Yes --> G[Return error: Company already exists]
    F -- No --> H[Check customerId exists in Customers table]
    H --> I{Customer exists?}
    I -- No --> J[Return error: Customer not found]
    I -- Yes --> K[Create company record with status Active, lastUpdated]
    K --> L[Log company creation event]
    L --> M[Return company profile in response]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

### /company/get

**Description:**
Retrieves the details of a specific company by company ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the company record from the Companies table by id.
- If the company does not exist, return a not found error.
- Check that the requesting user has permission to view company details.
- Return the company profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch company by id from Companies table]
    E --> F{Company exists?}
    F -- No --> G[Return error: Company not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return company profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /company/list

**Description:**
Retrieves a paginated list of companies, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list companies.
- Apply any provided filter criteria to the Companies table query.
- Fetch the list of companies according to the filter and paging parameters.
- Return the list of company profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch companies]
    H --> I[Return company list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /company/update

**Description:**
Updates the details of an existing company.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (name, location, industry, customerId).
- If id is missing, return an error response.
- Fetch the company record from the Companies table by id.
- If the company does not exist, return a not found error.
- Check that the requesting user has permission to update company details.
- Validate the updated fields for correct format and business rules.
- If updating the name, check for uniqueness.
- If updating the customerId, check that the new customerId exists in the Customers table.
- Update the company record with the provided fields and set lastUpdated to the current timestamp.
- Log the company update event with user ID and timestamp.
- Return the updated company profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch company by id from Companies table]
    E --> F{Company exists?}
    F -- No --> G[Return error: Company not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[If name updated, check uniqueness]
    N --> O{Name unique?}
    O -- No --> P[Return error: Company already exists]
    O -- Yes --> Q[If customerId updated, check existence]
    Q --> R{Customer exists?}
    R -- No --> S[Return error: Customer not found]
    R -- Yes --> T[Update company record and set lastUpdated]
    T --> U[Log company update event]
    U --> V[Return updated company profile]
    D --> W[End]
    G --> W
    J --> W
    M --> W
    P --> W
    S --> W
    V --> W
```

---

### /company/deactivate

**Description:**
Deactivates a company record, marking the company as inactive.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the company record from the Companies table by id.
- If the company does not exist, return a not found error.
- Check that the requesting user has permission to deactivate the company.
- Update the company status to "Inactive".
- Log the deactivation event with user ID and timestamp.
- Return the updated company profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch company by id from Companies table]
    E --> F{Company exists?}
    F -- No --> G[Return error: Company not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Update company status to Inactive]
    K --> L[Log deactivation event]
    L --> M[Return updated company profile]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

## 5. VesselController

### /vessel/create

**Description:**  
Creates a new vessel record in the system.

**Implementation:**

- Validate that the request payload contains the following necessary properties: name, number, type, location, companyId.
- If any required property is missing or invalid, return an error response indicating the specific validation error.
- Check that the number is unique in the Vessels table. If a vessel with the same number exists, return a conflict error.
- Check that the companyId exists in the Companies table. If not, return a not found error.
- Create a new vessel record in the Vessels table with the provided details, set status to "Active", startDate to the current timestamp, and lastUpdated to the current timestamp.
- Log the vessel creation event with user ID and timestamp.
- Return the created vessel profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: name, number, type, location, companyId]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check number uniqueness in Vessels table]
    E --> F{Number already exists?}
    F -- Yes --> G[Return error: Vessel already exists]
    F -- No --> H[Check companyId exists in Companies table]
    H --> I{Company exists?}
    I -- No --> J[Return error: Company not found]
    I -- Yes --> K[Create vessel record with status Active, startDate, lastUpdated]
    K --> L[Log vessel creation event]
    L --> M[Return vessel profile in response]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

### /vessel/get

**Description:**  
Retrieves the details of a specific vessel by vessel ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the vessel record from the Vessels table by id.
- If the vessel does not exist, return a not found error.
- Check that the requesting user has permission to view vessel details.
- Return the vessel profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch vessel by id from Vessels table]
    E --> F{Vessel exists?}
    F -- No --> G[Return error: Vessel not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return vessel profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /vessel/list

**Description:**  
Retrieves a paginated list of vessels, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list vessels.
- Apply any provided filter criteria to the Vessels table query.
- Fetch the list of vessels according to the filter and paging parameters.
- Return the list of vessel profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch vessels]
    H --> I[Return vessel list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /vessel/update

**Description:**  
Updates the details of an existing vessel.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (name, number, type, location, companyId).
- If id is missing, return an error response.
- Fetch the vessel record from the Vessels table by id.
- If the vessel does not exist, return a not found error.
- Check that the requesting user has permission to update vessel details.
- Validate the updated fields for correct format and business rules.
- If updating the number, check for uniqueness.
- If updating the companyId, check that the new companyId exists in the Companies table.
- Update the vessel record with the provided fields and set lastUpdated to the current timestamp.
- Log the vessel update event with user ID and timestamp.
- Return the updated vessel profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch vessel by id from Vessels table]
    E --> F{Vessel exists?}
    F -- No --> G[Return error: Vessel not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[If number updated, check uniqueness]
    N --> O{Number unique?}
    O -- No --> P[Return error: Vessel already exists]
    O -- Yes --> Q[If companyId updated, check existence]
    Q --> R{Company exists?}
    R -- No --> S[Return error: Company not found]
    R -- Yes --> T[Update vessel record and set lastUpdated]
    T --> U[Log vessel update event]
    U --> V[Return updated vessel profile]
    D --> W[End]
    G --> W
    J --> W
    M --> W
    P --> W
    S --> W
    V --> W
```

---

### /vessel/deactivate

**Description:**  
Deactivates a vessel record, marking the vessel as inactive.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the vessel record from the Vessels table by id.
- If the vessel does not exist, return a not found error.
- Check that the requesting user has permission to deactivate the vessel.
- Update the vessel status to "Inactive".
- Log the deactivation event with user ID and timestamp.
- Return the updated vessel profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch vessel by id from Vessels table]
    E --> F{Vessel exists?}
    F -- No --> G[Return error: Vessel not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Update vessel status to Inactive]
    K --> L[Log deactivation event]
    L --> M[Return updated vessel profile]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

## 6. SensorController
### /sensor/create

**Description:**
Creates a new sensor record in the system.

**Implementation:**

- Validate that the request payload contains the following necessary properties: name, type, vessel, location.
- If any required property is missing or invalid, return an error response indicating the specific validation error.
- Check that the sensor name is unique for the given vessel in the Sensors table. If a sensor with the same name exists for the vessel, return a conflict error.
- Check that the vessel exists in the Vessels table. If not, return a not found error.
- Create a new sensor record in the Sensors table with the provided details, set status to "Active", and set lastUpdated to the current timestamp.
- Log the sensor creation event with user ID and timestamp.
- Return the created sensor profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: name, type, vessel, location]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check sensor name uniqueness for vessel]
    E --> F{Name exists for vessel?}
    F -- Yes --> G[Return error: Sensor already exists]
    F -- No --> H[Check vessel exists in Vessels table]
    H --> I{Vessel exists?}
    I -- No --> J[Return error: Vessel not found]
    I -- Yes --> K[Create sensor record with status Active, lastUpdated]
    K --> L[Log sensor creation event]
    L --> M[Return sensor profile in response]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

### /sensor/get

**Description:**
Retrieves the details of a specific sensor by sensor ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the sensor record from the Sensors table by id.
- If the sensor does not exist, return a not found error.
- Check that the requesting user has permission to view sensor details.
- Return the sensor profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch sensor by id from Sensors table]
    E --> F{Sensor exists?}
    F -- No --> G[Return error: Sensor not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return sensor profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /sensor/list

**Description:**
Retrieves a paginated list of sensors, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list sensors.
- Apply any provided filter criteria to the Sensors table query.
- Fetch the list of sensors according to the filter and paging parameters.
- Return the list of sensor profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch sensors]
    H --> I[Return sensor list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /sensor/update

**Description:**
Updates the details of an existing sensor.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (name, type, vessel, location).
- If id is missing, return an error response.
- Fetch the sensor record from the Sensors table by id.
- If the sensor does not exist, return a not found error.
- Check that the requesting user has permission to update sensor details.
- Validate the updated fields for correct format and business rules.
- If updating the name or vessel, check for uniqueness of the sensor name for the vessel.
- If updating the vessel, check that the new vessel exists in the Vessels table.
- Update the sensor record with the provided fields and set lastUpdated to the current timestamp.
- Log the sensor update event with user ID and timestamp.
- Return the updated sensor profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch sensor by id from Sensors table]
    E --> F{Sensor exists?}
    F -- No --> G[Return error: Sensor not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[If name or vessel updated, check uniqueness]
    N --> O{Name unique for vessel?}
    O -- No --> P[Return error: Sensor already exists]
    O -- Yes --> Q[If vessel updated, check existence]
    Q --> R{Vessel exists?}
    R -- No --> S[Return error: Vessel not found]
    R -- Yes --> T[Update sensor record and set lastUpdated]
    T --> U[Log sensor update event]
    U --> V[Return updated sensor profile]
    D --> W[End]
    G --> W
    J --> W
    M --> W
    P --> W
    S --> W
    V --> W
```

---

### /sensor/deactivate

**Description:**
Deactivates a sensor record, marking the sensor as inactive.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the sensor record from the Sensors table by id.
- If the sensor does not exist, return a not found error.
- Check that the requesting user has permission to deactivate the sensor.
- Update the sensor status to "Inactive".
- Log the deactivation event with user ID and timestamp.
- Return the updated sensor profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch sensor by id from Sensors table]
    E --> F{Sensor exists?}
    F -- No --> G[Return error: Sensor not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Update sensor status to Inactive]
    K --> L[Log deactivation event]
    L --> M[Return updated sensor profile]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

## 7. SubscriptionController

### /subscription/create

**Description:**
Creates a new subscription plan for a customer.

**Implementation:**

- Validate that the request payload contains the following necessary properties: name, type, customerId, startDate, endDate, price, billingFrequency, sensorLimit, features.
- If any required property is missing or invalid (e.g., dates, price, sensorLimit), return an error response indicating the specific validation error.
- Check that the customerId exists in the Customers table. If not, return a not found error.
- Check for uniqueness of the subscription name for the customer. If a subscription with the same name exists for the customer, return a conflict error.
- Create a new subscription record in the Subscriptions table with the provided details, set status to "Active", and set lastUpdated to the current timestamp.
- Log the subscription creation event with user ID and timestamp.
- Return the created subscription profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: name, type, customerId, startDate, endDate, price, billingFrequency, sensorLimit, features]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check customerId exists in Customers table]
    E --> F{Customer exists?}
    F -- No --> G[Return error: Customer not found]
    F -- Yes --> H[Check subscription name uniqueness for customer]
    H --> I{Name exists for customer?}
    I -- Yes --> J[Return error: Subscription already exists]
    I -- No --> K[Create subscription record with status Active, lastUpdated]
    K --> L[Log subscription creation event]
    L --> M[Return subscription profile in response]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

### /subscription/get

**Description:**
Retrieves the details of a specific subscription by subscription ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the subscription record from the Subscriptions table by id.
- If the subscription does not exist, return a not found error.
- Check that the requesting user has permission to view subscription details.
- Return the subscription profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch subscription by id from Subscriptions table]
    E --> F{Subscription exists?}
    F -- No --> G[Return error: Subscription not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return subscription profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /subscription/list

**Description:**
Retrieves a paginated list of subscriptions, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list subscriptions.
- Apply any provided filter criteria to the Subscriptions table query.
- Fetch the list of subscriptions according to the filter and paging parameters.
- Return the list of subscription profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch subscriptions]
    H --> I[Return subscription list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /subscription/update

**Description:**
Updates the details of an existing subscription.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (name, type, startDate, endDate, price, billingFrequency, sensorLimit, features).
- If id is missing, return an error response.
- Fetch the subscription record from the Subscriptions table by id.
- If the subscription does not exist, return a not found error.
- Check that the requesting user has permission to update subscription details.
- Validate the updated fields for correct format and business rules.
- If updating the name, check for uniqueness for the customer.
- Update the subscription record with the provided fields and set lastUpdated to the current timestamp.
- Log the subscription update event with user ID and timestamp.
- Return the updated subscription profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch subscription by id from Subscriptions table]
    E --> F{Subscription exists?}
    F -- No --> G[Return error: Subscription not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[If name updated, check uniqueness for customer]
    N --> O{Name unique for customer?}
    O -- No --> P[Return error: Subscription already exists]
    O -- Yes --> Q[Update subscription record and set lastUpdated]
    Q --> R[Log subscription update event]
    R --> S[Return updated subscription profile]
    D --> T[End]
    G --> T
    J --> T
    M --> T
    P --> T
    S --> T
```

---

### /subscription/deactivate

**Description:**
Deactivates a subscription record, marking the subscription as inactive.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the subscription record from the Subscriptions table by id.
- If the subscription does not exist, return a not found error.
- Check that the requesting user has permission to deactivate the subscription.
- Update the subscription status to "Inactive".
- Log the deactivation event with user ID and timestamp.
- Return the updated subscription profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch subscription by id from Subscriptions table]
    E --> F{Subscription exists?}
    F -- No --> G[Return error: Subscription not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Update subscription status to Inactive]
    K --> L[Log deactivation event]
    L --> M[Return updated subscription profile]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

## 8. AlertController

### /alert/create

**Description:**
Creates a new alert for a sensor or vessel event.

**Implementation:**

- Validate that the request payload contains the following necessary properties: type, entityId, entityType, value, threshold, status, message.
- If any required property is missing or invalid, return an error response indicating the specific validation error.
- Check that the entityId exists in the corresponding table (Sensors or Vessels) based on entityType. If not, return a not found error.
- Create a new alert record in the Alerts table with the provided details, set timestamp to the current time, and set deliveryStatus to "Sent".
- Determine the list of users to notify based on alert type and entity.
- Log the alert creation event with user ID and timestamp.
- Return the created alert profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: type, entityId, entityType, value, threshold, status, message]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check entityId exists in Sensors or Vessels table]
    E --> F{Entity exists?}
    F -- No --> G[Return error: Entity not found]
    F -- Yes --> H[Create alert record with timestamp, deliveryStatus Sent]
    H --> I[Determine users to notify]
    I --> J[Log alert creation event]
    J --> K[Return alert profile in response]
    D --> L[End]
    G --> L
    K --> L
```

---

### /alert/get

**Description:**
Retrieves the details of a specific alert by alert ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the alert record from the Alerts table by id.
- If the alert does not exist, return a not found error.
- Check that the requesting user has permission to view alert details.
- Return the alert profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch alert by id from Alerts table]
    E --> F{Alert exists?}
    F -- No --> G[Return error: Alert not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return alert profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /alert/list

**Description:**
Retrieves a paginated list of alerts, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list alerts.
- Apply any provided filter criteria to the Alerts table query.
- Fetch the list of alerts according to the filter and paging parameters.
- Return the list of alert profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch alerts]
    H --> I[Return alert list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /alert/update

**Description:**
Updates the details of an existing alert.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (type, value, threshold, status, message).
- If id is missing, return an error response.
- Fetch the alert record from the Alerts table by id.
- If the alert does not exist, return a not found error.
- Check that the requesting user has permission to update alert details.
- Validate the updated fields for correct format and business rules.
- Update the alert record with the provided fields.
- Log the alert update event with user ID and timestamp.
- Return the updated alert profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch alert by id from Alerts table]
    E --> F{Alert exists?}
    F -- No --> G[Return error: Alert not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[Update alert record]
    N --> O[Log alert update event]
    O --> P[Return updated alert profile]
    D --> Q[End]
    G --> Q
    J --> Q
    M --> Q
    P --> Q
```

---

### /alert/resolve

**Description:**
Marks an alert as resolved.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the alert record from the Alerts table by id.
- If the alert does not exist, return a not found error.
- Check that the requesting user has permission to resolve the alert.
- Update the alert status to "Resolved".
- Log the alert resolution event with user ID and timestamp.
- Return the updated alert profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch alert by id from Alerts table]
    E --> F{Alert exists?}
    F -- No --> G[Return error: Alert not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Update alert status to Resolved]
    K --> L[Log alert resolution event]
    L --> M[Return updated alert profile]
    D --> N[End]
    G --> N
    J --> N
    M --> N
```

---

## 9. NotificationController

### /notification/create

**Description:**
Creates a new notification for a user and event.

**Implementation:**

- Validate that the request payload contains the following necessary properties: userId, eventType, content, channel.
- If any required property is missing or invalid, return an error response indicating the specific validation error.
- Check that the userId exists in the Users table. If not, return a not found error.
- Create a new notification record in the Notifications table with the provided details, set status to "Sent", timestamp to the current time, and assign a preferenceId if applicable.
- Log the notification creation event with user ID and timestamp.
- Return the created notification profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: userId, eventType, content, channel]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check userId exists in Users table]
    E --> F{User exists?}
    F -- No --> G[Return error: User not found]
    F -- Yes --> H[Create notification record with status Sent, timestamp, preferenceId]
    H --> I[Log notification creation event]
    I --> J[Return notification profile in response]
    D --> K[End]
    G --> K
    J --> K
```

---

### /notification/get

**Description:**
Retrieves the details of a specific notification by notification ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the notification record from the Notifications table by id.
- If the notification does not exist, return a not found error.
- Check that the requesting user has permission to view notification details.
- Return the notification profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch notification by id from Notifications table]
    E --> F{Notification exists?}
    F -- No --> G[Return error: Notification not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return notification profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /notification/list

**Description:**
Retrieves a paginated list of notifications, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list notifications.
- Apply any provided filter criteria to the Notifications table query.
- Fetch the list of notifications according to the filter and paging parameters.
- Return the list of notification profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch notifications]
    H --> I[Return notification list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /notification/update

**Description:**
Updates the details of an existing notification.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (eventType, content, channel, status).
- If id is missing, return an error response.
- Fetch the notification record from the Notifications table by id.
- If the notification does not exist, return a not found error.
- Check that the requesting user has permission to update notification details.
- Validate the updated fields for correct format and business rules.
- Update the notification record with the provided fields.
- Log the notification update event with user ID and timestamp.
- Return the updated notification profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch notification by id from Notifications table]
    E --> F{Notification exists?}
    F -- No --> G[Return error: Notification not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[Update notification record]
    N --> O[Log notification update event]
    O --> P[Return updated notification profile]
    D --> Q[End]
    G --> Q
    J --> Q
    M --> Q
    P --> Q
```

---

## 10. ReportController

### /report/generate

**Description:**
Generates a new report based on specified criteria.

**Implementation:**

- Validate that the request payload contains the following necessary properties: type, criteria.
- If any required property is missing or invalid, return an error response indicating the specific validation error.
- Check that the requesting user has permission to generate reports.
- Generate the report data based on the specified type and criteria.
- Store the generated report in the Reports table with status "Completed", generatedBy as the user ID, generatedAt as the current timestamp, and deliveryStatus as "Sent".
- Log the report generation event with user ID and timestamp.
- Return the generated report profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: type, criteria]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Generate report data]
    H --> I[Store report in Reports table with status Completed]
    I --> J[Log report generation event]
    J --> K[Return report profile in response]
    D --> L[End]
    G --> L
    K --> L
```

---

### /report/get

**Description:**
Retrieves the details of a specific report by report ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the report record from the Reports table by id.
- If the report does not exist, return a not found error.
- Check that the requesting user has permission to view report details.
- Return the report profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch report by id from Reports table]
    E --> F{Report exists?}
    F -- No --> G[Return error: Report not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return report profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /report/list

**Description:**
Retrieves a paginated list of reports, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list reports.
- Apply any provided filter criteria to the Reports table query.
- Fetch the list of reports according to the filter and paging parameters.
- Return the list of report profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch reports]
    H --> I[Return report list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

## 11. SupportController

### /support/create

**Description:**
Creates a new support request from a user.

**Implementation:**

- Validate that the request payload contains the following necessary properties: userId, subject, description.
- If any required property is missing or invalid, return an error response indicating the specific validation error.
- Check that the userId exists in the Users table. If not, return a not found error.
- Create a new support request record in the SupportRequests table with the provided details, set status to "Open", createdAt to the current timestamp, and resolvedAt and resolution to null.
- Log the support request creation event with user ID and timestamp.
- Return the created support request profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: userId, subject, description]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check userId exists in Users table]
    E --> F{User exists?}
    F -- No --> G[Return error: User not found]
    F -- Yes --> H[Create support request with status Open, createdAt, resolvedAt null]
    H --> I[Log support request creation event]
    I --> J[Return support request profile in response]
    D --> K[End]
    G --> K
    J --> K
```

---

### /support/get

**Description:**
Retrieves the details of a specific support request by support request ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the support request record from the SupportRequests table by id.
- If the support request does not exist, return a not found error.
- Check that the requesting user has permission to view support request details.
- Return the support request profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch support request by id from SupportRequests table]
    E --> F{Support request exists?}
    F -- No --> G[Return error: Support request not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return support request profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /support/list

**Description:**
Retrieves a paginated list of support requests, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list support requests.
- Apply any provided filter criteria to the SupportRequests table query.
- Fetch the list of support requests according to the filter and paging parameters.
- Return the list of support request profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch support requests]
    H --> I[Return support request list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /support/update

**Description:**
Updates the details of an existing support request.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (subject, description, status, resolution).
- If id is missing, return an error response.
- Fetch the support request record from the SupportRequests table by id.
- If the support request does not exist, return a not found error.
- Check that the requesting user has permission to update support request details.
- Validate the updated fields for correct format and business rules.
- If updating status to "Resolved", set resolvedAt to the current timestamp and update the resolution field.
- Update the support request record with the provided fields.
- Log the support request update event with user ID and timestamp.
- Return the updated support request profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch support request by id from SupportRequests table]
    E --> F{Support request exists?}
    F -- No --> G[Return error: Support request not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N{Status updated to Resolved?}
    N -- Yes --> O[Set resolvedAt to now, update resolution]
    N -- No --> P[Update other fields]
    O --> Q[Update support request record]
    P --> Q
    Q --> R[Log support request update event]
    R --> S[Return updated support request profile]
    D --> T[End]
    G --> T
    J --> T
    M --> T
    S --> T
```

---

## 12. FeedbackController

### /feedback/create

**Description:**
Creates a new feedback entry from a user.

**Implementation:**

- Validate that the request payload contains the following necessary properties: userId, type, content.
- If any required property is missing or invalid, return an error response indicating the specific validation error.
- Check that the userId exists in the Users table. If not, return a not found error.
- Create a new feedback record in the Feedback table with the provided details, set submittedAt to the current timestamp, and status to "New".
- Log the feedback creation event with user ID and timestamp.
- Return the created feedback profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required fields: userId, type, content]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid or missing fields]
    C -- Yes --> E[Check userId exists in Users table]
    E --> F{User exists?}
    F -- No --> G[Return error: User not found]
    F -- Yes --> H[Create feedback record with submittedAt, status New]
    H --> I[Log feedback creation event]
    I --> J[Return feedback profile in response]
    D --> K[End]
    G --> K
    J --> K
```

---

### /feedback/get

**Description:**
Retrieves the details of a specific feedback entry by feedback ID.

**Implementation:**

- Validate that the request payload contains the required property: id.
- If id is missing, return an error response.
- Fetch the feedback record from the Feedback table by id.
- If the feedback does not exist, return a not found error.
- Check that the requesting user has permission to view feedback details.
- Return the feedback profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id]
    C -- Yes --> E[Fetch feedback by id from Feedback table]
    E --> F{Feedback exists?}
    F -- No --> G[Return error: Feedback not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Return feedback profile in response]
    D --> L[End]
    G --> L
    J --> L
    K --> L
```

---

### /feedback/list

**Description:**
Retrieves a paginated list of feedback entries, optionally filtered by criteria.

**Implementation:**

- Validate that the request payload contains paging information (page, size).
- If paging information is missing or invalid, return an error response.
- Check that the requesting user has permission to list feedback.
- Apply any provided filter criteria to the Feedback table query.
- Fetch the list of feedback entries according to the filter and paging parameters.
- Return the list of feedback profiles in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate paging info: page, size]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Invalid paging]
    C -- Yes --> E[Check permissions]
    E --> F{Permission granted?}
    F -- No --> G[Return error: Forbidden]
    F -- Yes --> H[Apply filters and fetch feedback]
    H --> I[Return feedback list in response]
    D --> J[End]
    G --> J
    I --> J
```

---

### /feedback/update

**Description:**
Updates the details of an existing feedback entry.

**Implementation:**

- Validate that the request payload contains the required property: id, and at least one updatable field (type, content, status).
- If id is missing, return an error response.
- Fetch the feedback record from the Feedback table by id.
- If the feedback does not exist, return a not found error.
- Check that the requesting user has permission to update feedback details.
- Validate the updated fields for correct format and business rules.
- Update the feedback record with the provided fields.
- Log the feedback update event with user ID and timestamp.
- Return the updated feedback profile in the response payload.

**Flow Diagram:**
```mermaid
flowchart TD
    A[Start] --> B[Validate required field: id and updatable fields]
    B --> C{Validation passed?}
    C -- No --> D[Return error: Missing id or fields]
    C -- Yes --> E[Fetch feedback by id from Feedback table]
    E --> F{Feedback exists?}
    F -- No --> G[Return error: Feedback not found]
    F -- Yes --> H[Check permissions]
    H --> I{Permission granted?}
    I -- No --> J[Return error: Forbidden]
    I -- Yes --> K[Validate updated fields]
    K --> L{Validation passed?}
    L -- No --> M[Return error: Invalid fields]
    L -- Yes --> N[Update feedback record]
    N --> O[Log feedback update event]
    O --> P[Return updated feedback profile]
    D --> Q[End]
    G --> Q
    J --> Q
    M --> Q
    P --> Q
```

---

*The document continues with each endpoint in the same format, covering all controllers and endpoints as defined in the API specification and functional requirements.*