# SmartBoat Fleet Management .NET API – Endpoints

This document defines the REST API endpoints for the SmartBoat Fleet Management .NET API System. All endpoints use POST or GET methods only. Each request and response is wrapped in a standard envelope as described below.

---

## Request Envelope

```json
{
  "header": {
    "ID": "guid",           // Unique request ID (GUID)
    "application": "guid",  // Application GUID
    "bank": "BANK"          // Always "BANK"
  },
  "payload": { ... }        // Entity-specific payload
}
```

## Response Envelope

```json
{
  "payload": { ... },       // DTO as defined per endpoint below
  "exception": {
    "id": "guid",           // Exception ID (GUID)
    "code": "string",       // Exception code
    "description": "string" // Exception description
  }
}
```
- On success: `payload` contains the result DTO, `exception` is null.
- On error: `payload` is null, `exception` contains error details.

---

## Controllers and Endpoints

### 1. UserController

#### /user/register
- **Method:** POST
- **Request Payload:** User registration fields
- **Response Payload DTO:**
```json
{
  "id": 123,
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "roleId": 1,
  "avatar": "https://example.com/avatar.jpg",
  "joined": "2024-01-01T12:00:00Z",
  "status": "Active",
  "lastLogin": "2024-05-01T10:00:00Z",
  "twoFactorEnabled": false,
  "company": "Acme Shipping",
  "department": "Operations",
  "phone": "+**********",
  "timezone": "Europe/Athens",
  "language": "en",
  "bio": "Fleet manager"
}
```

#### /user/login
- **Method:** POST
- **Request Payload:** 
```json
{
  "email": "<EMAIL>",
  "password": "string"
}
```
- **Response Payload DTO:**
```json
{
  "token": "jwt-token-string",
  "user": {
    "id": 123,
    "name": "John Doe",
    "email": "<EMAIL>",
    "roleId": 1,
    "avatar": "https://example.com/avatar.jpg",
    "joined": "2024-01-01T12:00:00Z",
    "status": "Active",
    "lastLogin": "2024-05-01T10:00:00Z",
    "twoFactorEnabled": false,
    "company": "Acme Shipping",
    "department": "Operations",
    "phone": "+**********",
    "timezone": "Europe/Athens",
    "language": "en",
    "bio": "Fleet manager"
  }
}
```

#### /user/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 123 }
```
- **Response Payload DTO:** (see /user/register)

#### /user/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 123,
    "name": "John Doe",
    "email": "<EMAIL>",
    "roleId": 1,
    "avatar": "https://example.com/avatar.jpg",
    "joined": "2024-01-01T12:00:00Z",
    "status": "Active",
    "lastLogin": "2024-05-01T10:00:00Z",
    "twoFactorEnabled": false,
    "company": "Acme Shipping",
    "department": "Operations",
    "phone": "+**********",
    "timezone": "Europe/Athens",
    "language": "en",
    "bio": "Fleet manager"
  }
  // ...more users
]
```

#### /user/update
- **Method:** POST
- **Request Payload:** User DTO (with id)
- **Response Payload DTO:** (see /user/register)

#### /user/deactivate
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 123 }
```
- **Response Payload DTO:** (see /user/register, with status "Inactive")

#### /user/enable-2fa
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 123, "enable": true }
```
- **Response Payload DTO:** (see /user/register, with twoFactorEnabled updated)

#### /user/reset-password
- **Method:** POST
- **Request Payload:** 
```json
{ "email": "<EMAIL>" }
```
- **Response Payload DTO:**
```json
{ "status": "Reset email sent" }
```

---

### 2. RoleController

#### /role/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "name": "Admin",
  "permissions": ["ManageUsers", "ViewReports"],
  "description": "Administrator role"
}
```
- **Response Payload DTO:**
```json
{
  "id": 1,
  "name": "Admin",
  "permissions": ["ManageUsers", "ViewReports"],
  "description": "Administrator role",
  "createdAt": "2024-01-01T12:00:00Z",
  "updatedAt": "2024-01-01T12:00:00Z"
}
```

#### /role/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 1 }
```
- **Response Payload DTO:** (see /role/create)

#### /role/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 1,
    "name": "Admin",
    "permissions": ["ManageUsers", "ViewReports"],
    "description": "Administrator role",
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  }
  // ...more roles
]
```

#### /role/update
- **Method:** POST
- **Request Payload:** Role DTO (with id)
- **Response Payload DTO:** (see /role/create)

#### /role/delete
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 1 }
```
- **Response Payload DTO:**
```json
{ "status": "Deleted" }
```

---

### 3. CustomerController

#### /customer/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "name": "Blue Ocean Ltd.",
  "contactPerson": "Alice Smith",
  "email": "<EMAIL>",
  "phone": "+**********"
}
```
- **Response Payload DTO:**
```json
{
  "id": 10,
  "name": "Blue Ocean Ltd.",
  "contactPerson": "Alice Smith",
  "email": "<EMAIL>",
  "phone": "+**********",
  "companies": 2,
  "vessels": 5,
  "sensors": 20,
  "status": "Active",
  "lastActive": "2024-05-01T10:00:00Z"
}
```

#### /customer/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 10 }
```
- **Response Payload DTO:** (see /customer/create)

#### /customer/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 10,
    "name": "Blue Ocean Ltd.",
    "contactPerson": "Alice Smith",
    "email": "<EMAIL>",
    "phone": "+**********",
    "companies": 2,
    "vessels": 5,
    "sensors": 20,
    "status": "Active",
    "lastActive": "2024-05-01T10:00:00Z"
  }
  // ...more customers
]
```

#### /customer/update
- **Method:** POST
- **Request Payload:** Customer DTO (with id)
- **Response Payload DTO:** (see /customer/create)

#### /customer/deactivate
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 10 }
```
- **Response Payload DTO:** (see /customer/create, with status "Inactive")

---

### 4. CompanyController

#### /company/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "name": "Acme Shipping",
  "location": "Piraeus, Greece",
  "industry": "Shipping",
  "customerId": 10
}
```
- **Response Payload DTO:**
```json
{
  "id": 100,
  "name": "Acme Shipping",
  "location": "Piraeus, Greece",
  "industry": "Shipping",
  "vessels": 3,
  "sensors": 12,
  "status": "Active",
  "customerId": 10,
  "lastUpdated": "2024-05-01T10:00:00Z"
}
```

#### /company/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 100 }
```
- **Response Payload DTO:** (see /company/create)

#### /company/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 100,
    "name": "Acme Shipping",
    "location": "Piraeus, Greece",
    "industry": "Shipping",
    "vessels": 3,
    "sensors": 12,
    "status": "Active",
    "customerId": 10,
    "lastUpdated": "2024-05-01T10:00:00Z"
  }
  // ...more companies
]
```

#### /company/update
- **Method:** POST
- **Request Payload:** Company DTO (with id)
- **Response Payload DTO:** (see /company/create)

#### /company/deactivate
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 100 }
```
- **Response Payload DTO:** (see /company/create, with status "Inactive")

---

### 5. VesselController

#### /vessel/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "name": "Poseidon",
  "number": "GR-12345",
  "type": "Cargo",
  "location": "Piraeus",
  "companyId": 100
}
```
- **Response Payload DTO:**
```json
{
  "id": 200,
  "name": "Poseidon",
  "number": "GR-12345",
  "type": "Cargo",
  "location": "Piraeus",
  "status": "Active",
  "startDate": "2024-01-01T12:00:00Z",
  "endDate": null,
  "image": "https://example.com/vessel.jpg",
  "onsigners": 10,
  "offsigners": 2,
  "sensors": 5,
  "lastUpdated": "2024-05-01T10:00:00Z",
  "companyId": 100
}
```

#### /vessel/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 200 }
```
- **Response Payload DTO:** (see /vessel/create)

#### /vessel/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 200,
    "name": "Poseidon",
    "number": "GR-12345",
    "type": "Cargo",
    "location": "Piraeus",
    "status": "Active",
    "startDate": "2024-01-01T12:00:00Z",
    "endDate": null,
    "image": "https://example.com/vessel.jpg",
    "onsigners": 10,
    "offsigners": 2,
    "sensors": 5,
    "lastUpdated": "2024-05-01T10:00:00Z",
    "companyId": 100
  }
  // ...more vessels
]
```

#### /vessel/update
- **Method:** POST
- **Request Payload:** Vessel DTO (with id)
- **Response Payload DTO:** (see /vessel/create)

#### /vessel/deactivate
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 200 }
```
- **Response Payload DTO:** (see /vessel/create, with status "Inactive")

---

### 6. SensorController

#### /sensor/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "name": "Temperature Sensor",
  "type": "Temperature",
  "vessel": "Poseidon",
  "location": "Engine Room"
}
```
- **Response Payload DTO:**
```json
{
  "id": 300,
  "name": "Temperature Sensor",
  "type": "Temperature",
  "vessel": "Poseidon",
  "location": "Engine Room",
  "status": "Active",
  "lastReading": "2024-05-01T10:00:00Z",
  "lastUpdated": "2024-05-01T10:00:00Z",
  "alertThreshold": "80"
}
```

#### /sensor/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 300 }
```
- **Response Payload DTO:** (see /sensor/create)

#### /sensor/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 300,
    "name": "Temperature Sensor",
    "type": "Temperature",
    "vessel": "Poseidon",
    "location": "Engine Room",
    "status": "Active",
    "lastReading": "2024-05-01T10:00:00Z",
    "lastUpdated": "2024-05-01T10:00:00Z",
    "alertThreshold": "80"
  }
  // ...more sensors
]
```

#### /sensor/update
- **Method:** POST
- **Request Payload:** Sensor DTO (with id)
- **Response Payload DTO:** (see /sensor/create)

#### /sensor/deactivate
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 300 }
```
- **Response Payload DTO:** (see /sensor/create, with status "Inactive")

---

### 7. SubscriptionController

#### /subscription/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "name": "Premium Plan",
  "type": "Annual",
  "customerId": 10,
  "startDate": "2024-01-01T12:00:00Z",
  "endDate": "2025-01-01T12:00:00Z",
  "price": 1200,
  "billingFrequency": "Yearly",
  "sensorLimit": 50,
  "features": ["Analytics", "Alerts"]
}
```
- **Response Payload DTO:**
```json
{
  "id": 500,
  "name": "Premium Plan",
  "type": "Annual",
  "customerId": 10,
  "customerName": "Blue Ocean Ltd.",
  "startDate": "2024-01-01T12:00:00Z",
  "endDate": "2025-01-01T12:00:00Z",
  "price": 1200,
  "billingFrequency": "Yearly",
  "status": "Active",
  "sensorLimit": 50,
  "features": ["Analytics", "Alerts"],
  "lastUpdated": "2024-05-01T10:00:00Z"
}
```

#### /subscription/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 500 }
```
- **Response Payload DTO:** (see /subscription/create)

#### /subscription/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 500,
    "name": "Premium Plan",
    "type": "Annual",
    "customerId": 10,
    "customerName": "Blue Ocean Ltd.",
    "startDate": "2024-01-01T12:00:00Z",
    "endDate": "2025-01-01T12:00:00Z",
    "price": 1200,
    "billingFrequency": "Yearly",
    "status": "Active",
    "sensorLimit": 50,
    "features": ["Analytics", "Alerts"],
    "lastUpdated": "2024-05-01T10:00:00Z"
  }
  // ...more subscriptions
]
```

#### /subscription/update
- **Method:** POST
- **Request Payload:** Subscription DTO (with id)
- **Response Payload DTO:** (see /subscription/create)

#### /subscription/deactivate
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 500 }
```
- **Response Payload DTO:** (see /subscription/create, with status "Inactive")

---

### 8. AlertController

#### /alert/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "type": "Sensor",
  "entityId": 300,
  "entityType": "Sensor",
  "value": "85",
  "threshold": "80",
  "status": "Active",
  "message": "Temperature exceeded threshold"
}
```
- **Response Payload DTO:**
```json
{
  "id": 1,
  "type": "Sensor",
  "entityId": 300,
  "entityType": "Sensor",
  "value": "85",
  "threshold": "80",
  "status": "Active",
  "message": "Temperature exceeded threshold",
  "timestamp": "2024-05-01T10:00:00Z",
  "deliveredTo": [123],
  "deliveryStatus": "Sent"
}
```

#### /alert/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 1 }
```
- **Response Payload DTO:** (see /alert/create)

#### /alert/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 1,
    "type": "Sensor",
    "entityId": 300,
    "entityType": "Sensor",
    "value": "85",
    "threshold": "80",
    "status": "Active",
    "message": "Temperature exceeded threshold",
    "timestamp": "2024-05-01T10:00:00Z",
    "deliveredTo": [123],
    "deliveryStatus": "Sent"
  }
  // ...more alerts
]
```

#### /alert/update
- **Method:** POST
- **Request Payload:** Alert DTO (with id)
- **Response Payload DTO:** (see /alert/create)

#### /alert/resolve
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 1 }
```
- **Response Payload DTO:** (see /alert/create, with status "Resolved")

---

### 9. NotificationController

#### /notification/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "userId": 123,
  "eventType": "Alert",
  "content": "Temperature exceeded threshold",
  "channel": "Email"
}
```
- **Response Payload DTO:**
```json
{
  "id": 1,
  "userId": 123,
  "eventType": "Alert",
  "content": "Temperature exceeded threshold",
  "channel": "Email",
  "status": "Sent",
  "timestamp": "2024-05-01T10:00:00Z",
  "preferenceId": 1
}
```

#### /notification/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 1 }
```
- **Response Payload DTO:** (see /notification/create)

#### /notification/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 1,
    "userId": 123,
    "eventType": "Alert",
    "content": "Temperature exceeded threshold",
    "channel": "Email",
    "status": "Sent",
    "timestamp": "2024-05-01T10:00:00Z",
    "preferenceId": 1
  }
  // ...more notifications
]
```

#### /notification/update
- **Method:** POST
- **Request Payload:** Notification DTO (with id)
- **Response Payload DTO:** (see /notification/create)

---

### 10. ReportController

#### /report/generate
- **Method:** POST
- **Request Payload:** 
```json
{
  "type": "Usage",
  "criteria": "lastMonth"
}
```
- **Response Payload DTO:**
```json
{
  "id": 1,
  "type": "Usage",
  "criteria": "lastMonth",
  "generatedBy": 123,
  "generatedAt": "2024-05-01T10:00:00Z",
  "status": "Completed",
  "deliveryChannel": "Download",
  "deliveryStatus": "Sent"
}
```

#### /report/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 1 }
```
- **Response Payload DTO:** (see /report/generate)

#### /report/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 1,
    "type": "Usage",
    "criteria": "lastMonth",
    "generatedBy": 123,
    "generatedAt": "2024-05-01T10:00:00Z",
    "status": "Completed",
    "deliveryChannel": "Download",
    "deliveryStatus": "Sent"
  }
  // ...more reports
]
```

---

### 11. SupportController

#### /support/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "userId": 123,
  "subject": "Cannot access vessel data",
  "description": "I receive an error when accessing vessel details."
}
```
- **Response Payload DTO:**
```json
{
  "id": 1,
  "userId": 123,
  "subject": "Cannot access vessel data",
  "description": "I receive an error when accessing vessel details.",
  "status": "Open",
  "createdAt": "2024-05-01T10:00:00Z",
  "resolvedAt": null,
  "resolution": null
}
```

#### /support/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 1 }
```
- **Response Payload DTO:** (see /support/create)

#### /support/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 1,
    "userId": 123,
    "subject": "Cannot access vessel data",
    "description": "I receive an error when accessing vessel details.",
    "status": "Open",
    "createdAt": "2024-05-01T10:00:00Z",
    "resolvedAt": null,
    "resolution": null
  }
  // ...more support requests
]
```

#### /support/update
- **Method:** POST
- **Request Payload:** SupportRequest DTO (with id)
- **Response Payload DTO:** (see /support/create)

---

### 12. FeedbackController

#### /feedback/create
- **Method:** POST
- **Request Payload:** 
```json
{
  "userId": 123,
  "type": "Usability",
  "content": "The dashboard is very intuitive."
}
```
- **Response Payload DTO:**
```json
{
  "id": 1,
  "userId": 123,
  "type": "Usability",
  "content": "The dashboard is very intuitive.",
  "submittedAt": "2024-05-01T10:00:00Z",
  "status": "New"
}
```

#### /feedback/get
- **Method:** POST
- **Request Payload:** 
```json
{ "id": 1 }
```
- **Response Payload DTO:** (see /feedback/create)

#### /feedback/list
- **Method:** GET
- **Request Payload:** 
```json
{ "filter": {}, "paging": { "page": 1, "size": 20 } }
```
- **Response Payload DTO:**
```json
[
  {
    "id": 1,
    "userId": 123,
    "type": "Usability",
    "content": "The dashboard is very intuitive.",
    "submittedAt": "2024-05-01T10:00:00Z",
    "status": "New"
  }
  // ...more feedback
]
```

#### /feedback/update
- **Method:** POST
- **Request Payload:** Feedback DTO (with id)
- **Response Payload DTO:** (see /feedback/create)

---

## General Notes

- All endpoints require authentication unless otherwise specified (e.g., /user/register, /user/login, /user/reset-password).
- All requests and responses use the envelope structure described above.
- All exceptions are returned in the `exception` field; on error, `payload` is null.
- All list endpoints support filtering and paging in the request payload.
- All entity references and payloads are as defined in the [Entities](./SmartBoat-Entities.md) document, but the response DTOs are shown explicitly above.