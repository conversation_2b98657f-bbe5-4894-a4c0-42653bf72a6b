**API Technical Design  
Domain: User**  
Document Version: 2.1

---

# **Overview**

The purpose of this documentation is to describe in detail the functionality of the Core User backend API.

---

# **Web API Ground Rules Section**

## *Requests*

Each API request is wrapped in a Request object and has its payload - mentioned as T. T is specified in each endpoint’s technical description.

**Example Request**

```json
{
  "header": {
    "ID": "{{$guid}}",
    "application": "03FC0B90-DFAD-11EE-8D86-0800200C9A66",
    "bank": "NBG",
    "UserId": "{{$user_guid}}"
  },
  "payload": {}
}
```

- request.Header.ID is a new GUID for each request. This way the request can be tracked down in the future.
- request.Header.application is a GUID for each application that invokes our web API.
- request.Header.bank always has the value “BANK”
- request.Header.UserId is the GUID Id for each user.

## *Responses*

Each API response is wrapped in a Response object.

- All the exceptions thrown during the execution of an endpoint are caught and the response.exception field is assigned the related exception info and response.payload = null
- In case of success (no exception thrown), the response.payload is described in each endpoint’s technical description. The response.exception = null

**Example Response**

```json
{
  "payload": {},
  "exception": {
    "id": "guid",
    "code": "string",
    "description": "string"
  }
}
```

## *Endpoint Execution Logic*

All endpoints are asynchronous.

No matter what happens during the execution of an endpoint’s logic, the HTTP response code is always 200-OK. To achieve this, the execution of each endpoint is handled by a mechanism called SafeExecutor.

SafeExecutor is a static class.

SafeExecutor wraps the provided action in a try-catch block. If any exception occurs, the response.exception field is assigned the exception information.

Exceptions are categorized as Business or Technical. They have their own class extending the system exception class, with additional fields:

- Code: string
- Description: string

When the SafeExecutor catches a Business/Technical exception, the mapping is straightforward. When it catches a simple exception, it is handled as a technical exception with a fixed code of “1001” and description “A technical exception has occurred, please contact your system administrator”.

Each endpoint’s logic is exposed as a method in the service layer and is also described in an interface. The controllers inject the interface of the service and safely via SafeExecutor invoke the corresponding method. The service layer is not aware of the Request/Response wrapper objects. There is a one-to-one mapping for endpoints and service methods. For example, for an endpoint named “CreateUser”, there should be a method “CreateUser” defined in the interface and implemented in the service layer. Types, Interfaces and Services are defined in separate files.

## *Database Layer Rules*

Dapper ORM is used to access, add, update or delete database data. We only use parameterized queries to avoid SQL-Injection scenarios. Database access has its own layer, usually mentioned as “Repositories”. Method signatures must describe what the method is about. For example,

| Task<User> SelectUserAsync(Guid userId) |
| --- |

The service layer injects the database access service and invokes its methods by providing the required data, without knowing any details about the queries that actually run.

Also, in terms of database structure, we never use foreign keys.

---

# **Common Types Section**

| **Request** | |
| --- | --- |
| Field Name | Type |
| Header | RequestHeader |
| Payload | T |

| **RequestHeader** | |
| --- | --- |
| Field Name | Type |
| Id | guid (Always new guid) |
| Application | guid (Always constant), Request Header Application Guid is constant and read-only and the default value is **03FC0B90-DFAD-11EE-8D86-0800200C9A66** |
| Bank | string |
| UserId | guid |

| **Response** | |
| --- | --- |
| Field Name | Type |
| Payload | T |
| Exception | ResponseException |

| **ResponseException** | |
| --- | --- |
| Field Name | Type |
| Id | guid |
| Code | string |
| Description | string |
| Category | string |

---

# **Database Layer Section**

| **Database** | **Description** |
| --- | --- |
| SmartBoat | Provides a detailed structure of User tables including field names, data types, and constraints. |

## *Environments*

| **Environment** | **Database Server** | **Database** |
| --- | --- | --- |
| Development | V00008065 | SmartBoat |
| QA |  |  |
| Production |  |  |

## *DB Tables*

### *Users*

| **Name** | **Data Type** | **Nullable** | **Unique** | **Description** |
| --- | --- | --- | --- | --- |
| Id | uniqueidentifier | false | true | Unique entry’s identifier to the table |
| Username | nvarchar(100) | false | true | User's login name |
| Email | nvarchar(200) | false | true | Email address of the user |
| PasswordHash | nvarchar(256) | false | false | Hashed password |
| FirstName | nvarchar(100) | false | false | User's first name |
| LastName | nvarchar(100) | false | false | User's last name |
| RoleId | uniqueidentifier | false | false | Role assigned to the user |
| Role | nvarchar(50) | false | false | User's role (e.g., Admin, Operator, Viewer) |
| Status | nvarchar(50) | false | false | Current status (e.g., Active, Inactive) |
| Created | datetime2(7) | false | false | Timestamp when the user was created |
| Changed | datetime2(7) | true | false | Timestamp when the user was last updated |
| LastLogin | datetime2(7) | true | false | Date/time of last login |
| TwoFactorEnabled | bit | false | false | Whether two-factor authentication is enabled |
| Avatar | nvarchar(500) | true | false | URL or path to the user's avatar image |
| Joined | datetime2(7) | false | false | Date/time when the user joined |
| Company | nvarchar(200) | true | false | Name of the company the user belongs to |
| CompanyId | uniqueidentifier | true | false | Associated company Id (nullable) |
| Department | nvarchar(100) | true | false | Department within the company |
| Phone | nvarchar(50) | true | false | Contact phone number |
| PhoneNumber | nvarchar(50) | true | false | User's phone number |
| Timezone | nvarchar(50) | true | false | User's preferred timezone |
| Language | nvarchar(10) | true | false | Preferred language code (e.g., 'en', 'el') |
| Bio | nvarchar(500) | true | false | Short biography or description |
| IsDeleted | bit | false | false | Soft delete flag |

---

# **Types Layer Section**

### *User*

Table Annotation: This entity maps to the database table Users.

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| Username | string | User's login name |
| Email | string | User's email address |
| PasswordHash | string | Hashed password |
| FirstName | string | User's first name |
| LastName | string | User's last name |
| RoleId | guid | Role assigned to the user |
| Role | string | User's role (e.g., Admin, Operator, Viewer) |
| Status | string | Current status (e.g., Active, Inactive) |
| Created | datetime | Date and time the user was created |
| Changed | datetime | Date and time the user was last updated |
| LastLogin | datetime | Date and time of last login |
| TwoFactorEnabled | bool | Whether two-factor authentication is enabled |
| Avatar | string | URL or path to the user's avatar image |
| Joined | datetime | Date/time when the user joined |
| Company | string | Name of the company the user belongs to |
| CompanyId | guid | Associated company Id (nullable) |
| Department | string | Department within the company |
| Phone | string | Contact phone number |
| PhoneNumber | string | User's phone number |
| Timezone | string | User's preferred timezone |
| Language | string | Preferred language code (e.g., "en", "el") |
| Bio | string | Short biography or description |
| IsDeleted | bool | Soft delete flag |

### *UserDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| Username | string | User's login name |
| Email | string | User's email address |
| FirstName | string | User's first name |
| LastName | string | User's last name |
| Role | string | User's role (e.g., Admin, Operator, Viewer) |
| Status | string | Current status (e.g., Active, Inactive) |
| Created | datetime | Date and time the user was created |
| Changed | datetime | Date and time the user was last updated |
| LastLogin | datetime | Date and time of last login |
| TwoFactorEnabled | bool | Whether two-factor authentication is enabled |
| Avatar | string | URL or path to the user's avatar image |
| Company | string | Name of the company the user belongs to |
| Department | string | Department within the company |
| Phone | string | Contact phone number |
| PhoneNumber | string | User's phone number |
| Timezone | string | User's preferred timezone |
| Language | string | Preferred language code (e.g., "en", "el") |
| Bio | string | Short biography or description |

### *CreateUserDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Username | string | User's login name |
| Email | string | User's email address |
| Password | string | User's password (plain, to be hashed) |
| FirstName | string | User's first name |
| LastName | string | User's last name |
| RoleId | guid | Role assigned to the user |
| Role | string | User's role (e.g., Admin, Operator, Viewer) |
| Status | string | Current status (e.g., Active, Inactive) |
| TwoFactorEnabled | bool | Whether two-factor authentication is enabled |
| Avatar | string | URL or path to the user's avatar image |
| Joined | datetime | Date/time when the user joined |
| Company | string | Name of the company the user belongs to |
| CompanyId | guid | Associated company Id (nullable) |
| Department | string | Department within the company |
| Phone | string | Contact phone number |
| PhoneNumber | string | User's phone number |
| Timezone | string | User's preferred timezone |
| Language | string | Preferred language code (e.g., "en", "el") |
| Bio | string | Short biography or description |

### *UpdateUserDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier |
| Email | string | User's email address |
| FirstName | string | User's first name |
| LastName | string | User's last name |
| RoleId | guid | Role assigned to the user |
| Role | string | User's role (e.g., Admin, Operator, Viewer) |
| Status | string | Current status (e.g., Active, Inactive) |
| TwoFactorEnabled | bool | Whether two-factor authentication is enabled |
| Avatar | string | URL or path to the user's avatar image |
| Company | string | Name of the company the user belongs to |
| CompanyId | guid | Associated company Id (nullable) |
| Department | string | Department within the company |
| Phone | string | Contact phone number |
| PhoneNumber | string | User's phone number |
| Timezone | string | User's preferred timezone |
| Language | string | Preferred language code (e.g., "en", "el") |
| Bio | string | Short biography or description |

### *DeleteUserDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | Unique entry’s identifier. |
| FieldsToDelete | List<string> | List of fields to be deleted. |

### *UserRequestDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Id | guid | User Id. It can be null. |
| Username | string | User's login name. It can be null. |
| Email | string | User's email address. It can be null. |

### *ListUserRequestDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| PageLimit | int | Page limit |
| PageOffset | int | Page offset |
| SortField | string | Sort field |
| SortOrder | string | Sort order |
| SearchTerm | string | Search |
| Username | string | Username filter |
| Email | string | Email filter |
| Role | string | Role filter |
| Status | string | Status filter |
| Company | string | Company filter |
| Department | string | Department filter |

### *MetadataDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| PageLimit | int | Page limit |
| PageOffset | int | Page offset |
| Total | int | Total number of pages. |

### *ReturnListUserDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Data | List<UserDto> | List of UserDto objects. |
| Metadata | MetadataDto | Pagination parameters. |

### *ChangePasswordDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| UserId | guid | User Id |
| OldPassword | string | Old password |
| NewPassword | string | New password |

### *ResetPasswordDto*

| **Name** | **Data Type** | **Description** |
| --- | --- | --- |
| Email | string | User's email address |
| NewPassword | string | New password |
| ResetToken | string | Password reset token |

---

# **Mapping Definitions Section**

### CreateUserDto to User

Source: CreateUserDto  
Target: User  
Map: CreateUserDto to User

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| - | Id | Guid.NewGuid() |
| Username | Username | Direct Mapping |
| Email | Email | Direct Mapping |
| Password | PasswordHash | Hash Password before mapping |
| FirstName | FirstName | Direct Mapping |
| LastName | LastName | Direct Mapping |
| RoleId | RoleId | Direct Mapping |
| Role | Role | Direct Mapping |
| Status | Status | Direct Mapping |
| TwoFactorEnabled | TwoFactorEnabled | Direct Mapping |
| Avatar | Avatar | Direct Mapping |
| Joined | Joined | Direct Mapping |
| Company | Company | Direct Mapping |
| CompanyId | CompanyId | Direct Mapping |
| Department | Department | Direct Mapping |
| Phone | Phone | Direct Mapping |
| PhoneNumber | PhoneNumber | Direct Mapping |
| Timezone | Timezone | Direct Mapping |
| Language | Language | Direct Mapping |
| Bio | Bio | Direct Mapping |
| - | Created | DateTime.Now |
| - | Changed | null |
| - | LastLogin | null |
| - | IsDeleted | false |

### User to UserDto

Source: User  
Target: UserDto  
Map: User to UserDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct mapping |
| Username | Username | Direct mapping |
| Email | Email | Direct mapping |
| FirstName | FirstName | Direct mapping |
| LastName | LastName | Direct mapping |
| Role | Role | Direct mapping |
| Status | Status | Direct mapping |
| Created | Created | Direct mapping |
| Changed | Changed | Direct mapping |
| LastLogin | LastLogin | Direct mapping |
| TwoFactorEnabled | TwoFactorEnabled | Direct mapping |
| Avatar | Avatar | Direct mapping |
| Company | Company | Direct mapping |
| Department | Department | Direct mapping |
| Phone | Phone | Direct mapping |
| PhoneNumber | PhoneNumber | Direct mapping |
| Timezone | Timezone | Direct mapping |
| Language | Language | Direct mapping |
| Bio | Bio | Direct mapping |

### UpdateUserDto to User

Source: UpdateUserDto  
Target: User  
Map: UpdateUserDto to User

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct mapping |
| Email | Email | Conditional Mapping (update if provided, else no change) |
| FirstName | FirstName | Conditional Mapping (update if provided, else no change) |
| LastName | LastName | Conditional Mapping (update if provided, else no change) |
| RoleId | RoleId | Conditional Mapping (update if provided, else no change) |
| Role | Role | Conditional Mapping (update if provided, else no change) |
| Status | Status | Conditional Mapping (update if provided, else no change) |
| TwoFactorEnabled | TwoFactorEnabled | Conditional Mapping (update if provided, else no change) |
| Avatar | Avatar | Conditional Mapping (update if provided, else no change) |
| Company | Company | Conditional Mapping (update if provided, else no change) |
| CompanyId | CompanyId | Conditional Mapping (update if provided, else no change) |
| Department | Department | Conditional Mapping (update if provided, else no change) |
| Phone | Phone | Conditional Mapping (update if provided, else no change) |
| PhoneNumber | PhoneNumber | Conditional Mapping (update if provided, else no change) |
| Timezone | Timezone | Conditional Mapping (update if provided, else no change) |
| Language | Language | Conditional Mapping (update if provided, else no change) |
| Bio | Bio | Conditional Mapping (update if provided, else no change) |
| - | Changed | DateTime.Now |

### DeleteUserDto to User (for partial delete)

Source: DeleteUserDto  
Target: User  
Map: DeleteUserDto to User (for partial delete)

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Id | Id | Direct mapping |
| FieldsToDelete | FieldsToDelete | List of fields to be set to null or default |

### ListUserRequestDto to ReturnListUserDto

Source: ListUserRequestDto  
Target: ReturnListUserDto  
Map: ListUserRequestDto to ReturnListUserDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| PageLimit | Metadata.PageLimit | Provided pageLimit value. |
| PageOffset | Metadata.PageOffset | Provided pageOffset value. |

### PagedResult<User> to ReturnListUserDto

Source: pagedResult  
Target: ReturnListUserDto  
Map: pagedResult to ReturnListUserDto

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Records | Data (List<UserDto>) | ToList() |
| TotalRecords | Metadata.Total | pagedResult.TotalRecords |

### ChangePasswordDto to User (for password update)

Source: ChangePasswordDto  
Target: User  
Map: ChangePasswordDto to User (for password update)

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| UserId | Id | Direct mapping |
| NewPassword | PasswordHash | Hash NewPassword before mapping |

### ResetPasswordDto to User (for password reset)

Source: ResetPasswordDto  
Target: User  
Map: ResetPasswordDto to User (for password reset)

| **Source** | **Target** | **Mapping Details** |
| --- | --- | --- |
| Email | Email | Direct mapping |
| NewPassword | PasswordHash | Hash NewPassword before mapping |
| ResetToken | ResetToken | Used for validation, not persisted |

---

# **Implementation Layer Section**

## *UserService*

### *Create*

Creates a user with the specified details

| **Arguments** | CreateUserDto request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**

1. **Validate** the request and its parameters:
   1. All required fields such as Username, Email, Password, FirstName, LastName, Role, and any other business-required fields must not be null or empty.
   2. If the request or the necessary parameters are null or invalid, throw the [USR-422](#api-exceptions) exception.
2. **Authorization Check:**
   1. Validate that the user has the permission to perform the Create operation.
3. **Check for Existing User:**
   1. Query the database for an existing user with the same Email or Username.
   2. If a user already exists, throw the [USR-409](#api-exceptions) exception.
4. **Hash Password:**
   1. Securely hash the provided password.
5. **Map** the User based on the CreateUserDto to User from the Mapping Definition Section.
6. **Perform Database Operation:**
   1. Insert the new User entity into the Users table.
   2. Handle any database errors by throwing the [USR-500](#api-exceptions) exception.
7. **Return the Result:**
   1. Return the new User's Id as a string.

### *Get*

Get the specified user

| **Arguments** | UserRequestDto request, Guid userId |
| --- | --- |
| **Return value** | UserDto |

**Implementation**

1. **Validate** the request and its parameters:
   1. At least one of Id, Username, or Email must be provided.
   2. If all are missing, throw the [USR-422](#api-exceptions) exception.
2. **Fetch User:**
   1. Retrieve the user from the database by Id, Username, or Email.
   2. If not found, throw the [USR-404](#api-exceptions) exception.
   3. Handle database errors by throwing the [USR-500](#api-exceptions) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
4. **Map** the UserDto based on the User to UserDto from the Mapping Definition Section.
5. **Return** the UserDto.

### *Update*

Updates a user with the specified details

| **Arguments** | UpdateUserDto request, Guid userId |
| --- | --- |
| **Return value** | string |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [USR-422](#api-exceptions) exception.
2. **Fetch User:**
   1. Retrieve the user by Id.
   2. If not found, throw the [USR-404](#api-exceptions) exception.
   3. Handle database errors by throwing the [USR-500](#api-exceptions) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Update operation.
4. **Check for Email Conflict:**
   1. If Email is being updated, ensure no other user has the same Email.
   2. If conflict, throw the [USR-409](#api-exceptions) exception.
5. **Map** the User based on the UpdateUserDto to User from the Mapping Definition Section.
6. **Perform Database Operation:**
   1. Update the User entity in the database.
   2. Handle any database errors by throwing the [USR-500](#api-exceptions) exception.
7. **Return the Result:**
   1. Return the User's Id as a string.

### *Delete*

Deletes a user with the specified details

| **Arguments** | DeleteUserDto request, Guid userId |
| --- | --- |
| **Return value** | bool |

**Implementation**

1. **Validate** the request and its parameters:
   1. “Id” must not be null.
   2. If the request or the necessary parameters are null or invalid, throw the [USR-422](#api-exceptions) exception.
2. **Fetch User:**
   1. Retrieve the User by Id.
   2. If not found, throw the [USR-404](#api-exceptions) exception.
   3. Handle database errors by throwing the [USR-500](#api-exceptions) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to perform the Delete operation.
4. **Perform Database Operation:**
   1. If request.FieldsToDelete is null:
      1. Perform a soft delete (set IsDeleted to true).
   2. Else:
      1. For each field in request.FieldsToDelete, set the corresponding property to null or default (excluding Id, Username, Email).
   3. Update the User entity in the database.
   4. Handle any database errors by throwing the [USR-500](#api-exceptions) exception.
5. **Return the Result:**
   1. Return true if deletion or field nullification succeeded, otherwise false.

### *GetList*

Get a user list with the specified details

| **Arguments** | ListUserRequestDto request, Guid userId |
| --- | --- |
| **Return value** | ReturnListUserDto |

**Implementation**

1. **Validate** the request and its parameters:
   1. "PageLimit” must not be null or > 0.
   2. “PageOffset” must not be null or ≥ 0.
   3. If the request or the necessary parameters are null or invalid, throw the [USR-422](#api-exceptions) exception.
2. **Authorization Check:**
   1. Validate that the user has the permission to perform the Read operation.
3. **Build Query Filters:**
   1. Apply filters from the request (e.g., Username, Email, Role, Status, Company, Department).
   2. Apply search term if provided.
4. **Perform Database Operation:**
   1. Query the Users table with pagination and filters.
   2. Handle any database errors by throwing the [USR-500](#api-exceptions) exception.
5. **Map Results:**
   1. Map the result set to a list of UserDto objects.
   2. Build metadata (total count, page info).
6. **Return the Result:**
   1. Return a ReturnListUserDto containing the list and metadata.

### *ChangePassword*

Changes a user's password

| **Arguments** | ChangePasswordDto request, Guid userId |
| --- | --- |
| **Return value** | bool |

**Implementation**

1. **Validate** the request and its parameters:
   1. UserId, OldPassword, and NewPassword must be provided.
   2. If missing, throw the [USR-422](#api-exceptions) exception.
2. **Fetch User:**
   1. Retrieve the user by UserId.
   2. If not found, throw the [USR-404](#api-exceptions) exception.
3. **Authorization Check:**
   1. Validate that the user has the permission to change the password (self or admin).
4. **Verify Old Password:**
   1. Compare OldPassword with the stored PasswordHash.
   2. If mismatch, throw the [USR-401](#api-exceptions) exception.
5. **Hash New Password:**
   1. Hash the NewPassword securely.
6. **Update Password:**
   1. Update the PasswordHash in the database.
   2. Handle any database errors by throwing the [USR-500](#api-exceptions) exception.
7. **Return the Result:**
   1. Return true if password change succeeded.

### *ResetPassword*

Resets a user's password using a reset token

| **Arguments** | ResetPasswordDto request |
| --- | --- |
| **Return value** | bool |

**Implementation**

1. **Validate** the request and its parameters:
   1. Email, NewPassword, and ResetToken must be provided.
   2. If missing, throw the [USR-422](#api-exceptions) exception.
2. **Fetch User:**
   1. Retrieve the user by Email.
   2. If not found, throw the [USR-404](#api-exceptions) exception.
3. **Validate Reset Token:**
   1. Verify the ResetToken is valid and not expired.
   2. If invalid, throw the [USR-401](#api-exceptions) exception.
4. **Hash New Password:**
   1. Hash the NewPassword securely.
5. **Update Password:**
   1. Update the PasswordHash in the database.
   2. Handle any database errors by throwing the [USR-500](#api-exceptions) exception.
6. **Return the Result:**
   1. Return true if password reset succeeded.

---

# **Core Service Dependencies**

| **Service** | **Method** | **Arguments** | **Return value** | **Argument Details** |
| --- | --- | --- | --- | --- |
| IPasswordHasher | Hash | string password | string | Hashes a password securely |
| IPasswordHasher | Verify | string password, string hash | bool | Verifies a password against a hash |

---

# **API Exceptions**

| **Code** | **Description** | **Category** |
| --- | --- | --- |
| **USR-500** | Technical Error | Technical |
| **USR-422** | Client Error | Business |
| **USR-404** | Not Found | Technical |
| **USR-409** | Conflict | Business |
| **USR-401** | Unauthorized | Business |

---

# **Interface Layer Section**

## *IUserService*

| **Method** | **Arguments** | **Return value** |
| --- | --- | --- |
| Create | CreateUserDto request, Guid userId | string |
| Get | UserRequestDto request, Guid userId | UserDto |
| Update | UpdateUserDto request, Guid userId | string |
| Delete | DeleteUserDto request, Guid userId | bool |
| GetList | ListUserRequestDto request, Guid userId | ReturnListUserDto |
| ChangePassword | ChangePasswordDto request, Guid userId | bool |
| ResetPassword | ResetPasswordDto request | bool |

---

# **Controller Layer Section**

## *UserController*

### /user/create

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Create |
| **Request** | [Request](#request)<[CreateUserDto](#createuserdto)> |
| **Response** | [Response](#response)<string> |

### /user/get

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Get |
| **Request** | [Request](#request)<[UserRequestDto](#userrequestdto)> |
| **Response** | [Response](#response)<[UserDto](#userdto)> |

### /user/update

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Update |
| **Request** | [Request](#request)<[UpdateUserDto](#updateuserdto)> |
| **Response** | [Response](#response)<string> |

### /user/delete

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | Delete |
| **Request** | [Request](#request)<[DeleteUserDto](#deleteuserdto)> |
| **Response** | [Response](#response)<bool> |

### /user/list

| **HTTP Request Method** | POST |
| --- | --- |
| **Method** | GetList |
| **Request** | [Request](#request)<[ListUserRequestDto](#listuserrequestdto)> |
| **Response** | [Response](#response)<[ReturnListUserDto](#returnlistuserdto)> |

---

**End of Section**