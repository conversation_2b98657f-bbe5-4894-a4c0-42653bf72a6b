@description('The name of the SmartBoat application')
param appName string = 'smartboat'

@description('The location for all resources')
param location string = resourceGroup().location

@description('The SQL Server admin username')
param sqlAdminUsername string

@description('The SQL Server admin password')
@secure()
param sqlAdminPassword string

@description('The Docker image to deploy')
param dockerImage string = 'nginx:latest'

@description('The Azure Container Registry URL')
param containerRegistryUrl string = ''

// Variables
var appServiceName = '${appName}-app'
var appServicePlanName = '${appName}-plan'
var sqlServerName = '${appName}-sql-${uniqueString(resourceGroup().id)}'
var sqlDatabaseName = 'SmartBoat'

// Deploy SQL Database
module database 'modules/database.bicep' = {
  name: 'database-deployment'
  params: {
    sqlServerName: sqlServerName
    sqlDatabaseName: sqlDatabaseName
    location: location
    sqlAdminUsername: sqlAdminUsername
    sqlAdminPassword: sqlAdminPassword
  }
}

// Deploy App Service
module appService 'modules/app-service.bicep' = {
  name: 'app-service-deployment'
  params: {
    appServiceName: appServiceName
    appServicePlanName: appServicePlanName
    location: location
    dockerImage: dockerImage
    containerRegistryUrl: containerRegistryUrl
    connectionString: 'Server=${database.outputs.sqlServerFqdn};Database=${database.outputs.sqlDatabaseName};User ID=${sqlAdminUsername};Password=${sqlAdminPassword};Encrypt=True;Connection Timeout=30;'
  }
}

// Outputs
output appServiceUrl string = appService.outputs.appServiceUrl
output appServiceName string = appService.outputs.appServiceName
output sqlServerName string = database.outputs.sqlServerName
output sqlDatabaseName string = database.outputs.sqlDatabaseName