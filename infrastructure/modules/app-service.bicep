@description('The name of the App Service')
param appServiceName string

@description('The name of the App Service Plan')
param appServicePlanName string

@description('The location for all resources')
param location string = resourceGroup().location

@description('The Docker image to deploy')
param dockerImage string = 'nginx:latest'

@description('The Azure Container Registry URL')
param containerRegistryUrl string = ''

@description('The SQL connection string')
@secure()
param connectionString string

// Create App Service Plan
resource appServicePlan 'Microsoft.Web/serverfarms@2023-01-01' = {
  name: appServicePlanName
  location: location
  properties: {
    reserved: true
  }
  sku: {
    name: 'B1'
    tier: 'Basic'
    size: 'B1'
    family: 'B'
    capacity: 1
  }
  kind: 'linux'
}

// Create App Service
resource appService 'Microsoft.Web/sites@2023-01-01' = {
  name: appServiceName
  location: location
  properties: {
    serverFarmId: appServicePlan.id
    siteConfig: {
      linuxFxVersion: 'DOCKER|${dockerImage}'
      appSettings: [
        {
          name: 'ASPNETCORE_ENVIRONMENT'
          value: 'Production'
        }
        {
          name: 'WEBSITES_PORT'
          value: '80'
        }
        {
          name: 'ConnectionStrings__DefaultConnection'
          value: connectionString
        }
      ]
    }
    httpsOnly: true
  }
}

// Output the App Service URL
output appServiceUrl string = appService.properties.defaultHostName
output appServiceName string = appService.name
