using Microsoft.Extensions.Logging;
using Moq;
using SmartBoat.API.Services;
using SmartBoat.API.Types;
using Nbg.NetCore.DatabaseService;
using Xunit;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartBoat.API.Tests
{
    public class CsvColumnMappingTests
    {
        private readonly Mock<IDatabaseService> _mockDatabaseService;
        private readonly Mock<ILogger<CsvColumnMapper>> _mockLogger;
        private readonly CsvColumnMapper _csvColumnMapper;

        public CsvColumnMappingTests()
        {
            _mockDatabaseService = new Mock<IDatabaseService>();
            _mockLogger = new Mock<ILogger<CsvColumnMapper>>();
            _csvColumnMapper = new CsvColumnMapper(_mockDatabaseService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task MapCsvRecordAsync_ShouldMapBasicNavigationFields()
        {
            // Arrange
            var recordDict = new Dictionary<string, object>
            {
                { "Speed", "12.5" },
                { "Coordinates", "37.9755°N 23.7348°E" },
                { "Location", "Piraeus Port" },
                { "Time", "2025-09-08 10:30:00" }
            };
            var vesselName = "Test Vessel";

            // Mock no custom mappings
            _mockDatabaseService.Setup(x => x.SelectAsync<CsvColumnMapping>(It.IsAny<object>()))
                .ReturnsAsync(new List<CsvColumnMapping>());

            // Act
            var result = await _csvColumnMapper.MapCsvRecordAsync(recordDict, vesselName);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Measurements.ContainsKey("speed"));
            Assert.True(result.Measurements.ContainsKey("coordinates"));
            Assert.True(result.Measurements.ContainsKey("location"));
            Assert.True(result.Measurements.ContainsKey("timestamp"));
            Assert.Equal(12.5m, result.Measurements["speed"]);
            Assert.Equal("37.9755°N 23.7348°E", result.Measurements["coordinates"]);
            Assert.Equal("Piraeus Port", result.Measurements["location"]);
        }

        [Fact]
        public async Task MapCsvRecordAsync_ShouldMapEngineFields()
        {
            // Arrange
            var recordDict = new Dictionary<string, object>
            {
                { "RPM_PORT", "1750" },
                { "RPM_STBD", "1850" },
                { "PORT Eng Water Temp", "18.2" },
                { "STBD Eng Water Temp", "19.1" },
                { "PORT Eng Oil Press", "45.5" },
                { "STBD Eng Oil Press", "46.2" }
            };
            var vesselName = "Test Vessel";

            // Mock no custom mappings
            _mockDatabaseService.Setup(x => x.SelectAsync<CsvColumnMapping>(It.IsAny<object>()))
                .ReturnsAsync(new List<CsvColumnMapping>());

            // Act
            var result = await _csvColumnMapper.MapCsvRecordAsync(recordDict, vesselName);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Measurements.ContainsKey("port_rpm"));
            Assert.True(result.Measurements.ContainsKey("stbd_rpm"));
            Assert.True(result.Measurements.ContainsKey("port_water_temperature"));
            Assert.True(result.Measurements.ContainsKey("stbd_water_temperature"));
            Assert.True(result.Measurements.ContainsKey("port_oil_pressure"));
            Assert.True(result.Measurements.ContainsKey("stbd_oil_pressure"));
            Assert.Equal(1750m, result.Measurements["port_rpm"]);
            Assert.Equal(1850m, result.Measurements["stbd_rpm"]);
            Assert.Equal(18.2m, result.Measurements["port_water_temperature"]);
            Assert.Equal(19.1m, result.Measurements["stbd_water_temperature"]);
        }

        [Fact]
        public async Task MapCsvRecordAsync_ShouldMapSystemFields()
        {
            // Arrange
            var recordDict = new Dictionary<string, object>
            {
                { "power Supply", "24.1" },
                { "SmartBoat Battery", "85.5" },
                { "Rudder", "15.2" },
                { "total NM", "1250.7" },
                { "GSM", "75" },
                { "Sensor", "SB-001" }
            };
            var vesselName = "Test Vessel";

            // Mock no custom mappings
            _mockDatabaseService.Setup(x => x.SelectAsync<CsvColumnMapping>(It.IsAny<object>()))
                .ReturnsAsync(new List<CsvColumnMapping>());

            // Act
            var result = await _csvColumnMapper.MapCsvRecordAsync(recordDict, vesselName);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Measurements.ContainsKey("power_supply"));
            Assert.True(result.Measurements.ContainsKey("battery"));
            Assert.True(result.Measurements.ContainsKey("rudder"));
            Assert.True(result.Measurements.ContainsKey("total_nm"));
            Assert.True(result.Measurements.ContainsKey("gsm_signal"));
            Assert.True(result.Measurements.ContainsKey("sensor_id"));
            Assert.Equal(24.1m, result.Measurements["power_supply"]);
            Assert.Equal(85.5m, result.Measurements["battery"]);
            Assert.Equal("SB-001", result.Measurements["sensor_id"]);
        }

        [Fact]
        public async Task MapCsvRecordAsync_ShouldMapGreekColumns()
        {
            // Arrange
            var recordDict = new Dictionary<string, object>
            {
                { "Όχημα", "Amazing Elli" },
                { "Ταχύτητα", "15.3" },
                { "Στροφές", "1800" },
                { "Συντεταγμένες", "38.0°N 23.7°E" }
            };
            var vesselName = "Amazing Elli";

            // Mock no custom mappings
            _mockDatabaseService.Setup(x => x.SelectAsync<CsvColumnMapping>(It.IsAny<object>()))
                .ReturnsAsync(new List<CsvColumnMapping>());

            // Act
            var result = await _csvColumnMapper.MapCsvRecordAsync(recordDict, vesselName);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Measurements.ContainsKey("vessel_name"));
            Assert.True(result.Measurements.ContainsKey("speed"));
            Assert.True(result.Measurements.ContainsKey("rpm"));
            Assert.True(result.Measurements.ContainsKey("coordinates"));
            Assert.Equal("Amazing Elli", result.Measurements["vessel_name"]);
            Assert.Equal(15.3m, result.Measurements["speed"]);
            Assert.Equal(1800m, result.Measurements["rpm"]);
        }

        [Fact]
        public async Task MapCsvRecordAsync_ShouldCalculateQualityScore()
        {
            // Arrange
            var recordDict = new Dictionary<string, object>
            {
                { "Speed", "12.5" },
                { "UnknownColumn1", "value1" },
                { "UnknownColumn2", "value2" }
            };
            var vesselName = "Test Vessel";

            // Mock no custom mappings
            _mockDatabaseService.Setup(x => x.SelectAsync<CsvColumnMapping>(It.IsAny<object>()))
                .ReturnsAsync(new List<CsvColumnMapping>());

            // Act
            var result = await _csvColumnMapper.MapCsvRecordAsync(recordDict, vesselName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.Measurements.Count); // Only speed should be mapped
            Assert.True(result.QualityScore > 0 && result.QualityScore < 1); // Should be 1/3 = 0.33
            Assert.Equal(2, result.UnmappedColumns.Count); // Two unknown columns
        }
    }
}
