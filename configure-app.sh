#!/bin/bash

# SmartBoat Application Configuration Script
# This script replaces placeholders in appsettings.Production.json with environment variables

echo "Configuring SmartBoat application..."

# Path to the production configuration file
CONFIG_FILE="/app/api/appsettings.Production.json"

# Check if the configuration file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Configuration file not found at $CONFIG_FILE"
    exit 1
fi

# Create a backup of the original configuration
cp "$CONFIG_FILE" "$CONFIG_FILE.backup"

# Replace placeholders with environment variables
echo "Replacing configuration placeholders..."

# Connection String
if [ -n "$ConnectionString" ]; then
    sed -i "s|#{ConnectionString}#|$ConnectionString|g" "$CONFIG_FILE"
    echo "✓ Connection string configured"
else
    echo "⚠ Warning: ConnectionString environment variable not set"
fi

# JWT Secret Key
if [ -n "$JwtSecretKey" ]; then
    sed -i "s|#{JwtSecretKey}#|$JwtSecretKey|g" "$CONFIG_FILE"
    echo "✓ JWT secret key configured"
else
    echo "⚠ Warning: JwtSecretKey environment variable not set"
fi

# Security Salt
if [ -n "$SecuritySalt" ]; then
    sed -i "s|#{SecuritySalt}#|$SecuritySalt|g" "$CONFIG_FILE"
    echo "✓ Security salt configured"
else
    echo "⚠ Warning: SecuritySalt environment variable not set"
fi

# App Service URL for CORS
if [ -n "$AppServiceUrl" ]; then
    sed -i "s|#{AppServiceUrl}#|$AppServiceUrl|g" "$CONFIG_FILE"
    echo "✓ App Service URL configured for CORS"
else
    echo "⚠ Warning: AppServiceUrl environment variable not set"
fi

# Generate a random JWT secret key if not provided
if [ -z "$JwtSecretKey" ]; then
    echo "Generating random JWT secret key..."
    RANDOM_JWT_KEY=$(openssl rand -base64 32)
    sed -i "s|#{JwtSecretKey}#|$RANDOM_JWT_KEY|g" "$CONFIG_FILE"
    echo "✓ Random JWT secret key generated"
fi

# Generate a random security salt if not provided
if [ -z "$SecuritySalt" ]; then
    echo "Generating random security salt..."
    RANDOM_SALT=$(openssl rand -base64 16)
    sed -i "s|#{SecuritySalt}#|$RANDOM_SALT|g" "$CONFIG_FILE"
    echo "✓ Random security salt generated"
fi

# Set default CORS origin if not provided
if [ -z "$AppServiceUrl" ]; then
    echo "Setting default CORS origin..."
    sed -i "s|#{AppServiceUrl}#|https://localhost|g" "$CONFIG_FILE"
    echo "✓ Default CORS origin set"
fi

# Ensure connection string is set (required for startup)
if [ -z "$ConnectionString" ]; then
    echo "⚠ WARNING: No ConnectionString provided, using ***** connection"
    sed -i "s|#{ConnectionString}#|Server=*****;Database=*****;User ID=*****;Password=*****;|g" "$CONFIG_FILE"
    echo "✓ Dummy connection string set"
fi

# Show what was configured
echo "Configuration complete!"
echo "Configuration file: $CONFIG_FILE"

# Validate the JSON is still valid
if command -v python3 &> /dev/null; then
    python3 -m json.tool "$CONFIG_FILE" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✓ Configuration file is valid JSON"
    else
        echo "✗ Error: Configuration file is not valid JSON"
        echo "Restoring backup..."
        cp "$CONFIG_FILE.backup" "$CONFIG_FILE"
        exit 1
    fi
fi

echo "Application configuration ready!"