# Multi-stage Dockerfile for SmartBoat Application
# Stage 1: Build React frontend
FROM node:18-alpine AS frontend-build
WORKDIR /app/frontend
COPY smartboat-platform/package*.json ./
RUN npm ci --only=production
COPY smartboat-platform/ .
RUN npm run build

# Stage 2: Build .NET API
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS backend-build
WORKDIR /app/backend

# Copy NuGet configuration and local packages first
COPY SmartBoat.API/nuget.config ./
COPY SmartBoat.API/.nugets/ ./.nugets/

# Copy project file and restore dependencies
COPY SmartBoat.API/*.csproj ./
RUN dotnet restore

# Copy source code and build
COPY SmartBoat.API/ .
RUN dotnet publish -c Release -o out

# Stage 3: Final runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app

# Install nginx, gettext (for envsubst), curl (for health checks), and dos2unix
RUN apt-get update && apt-get install -y nginx gettext-base curl dos2unix && rm -rf /var/lib/apt/lists/*

# Copy built applications
COPY --from=backend-build /app/backend/out ./api/
COPY --from=frontend-build /app/frontend/dist ./wwwroot/

# Copy configuration files
COPY nginx.conf /etc/nginx/nginx.conf
COPY startup.sh ./startup.sh
COPY configure-app.sh ./configure-app.sh

# Ensure correct line endings and permissions for scripts
RUN dos2unix ./startup.sh ./configure-app.sh 2>/dev/null || true
RUN chmod +x ./startup.sh ./configure-app.sh

# Expose port
EXPOSE 80

# Start both nginx and .NET API
CMD ["./startup.sh"]