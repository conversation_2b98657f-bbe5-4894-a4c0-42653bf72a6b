# CSV Processing Issues - Fixes Applied

## Issues Identified

### 1. Authorization Error
**Problem**: The scheduled job was using `Guid.Empty` as the user ID, causing "User not found or inactive" errors.

**Root Cause**: The `DailyEmailProcessingJob` was using `Guid.Empty` instead of a valid Super Admin user ID.

**Solution**: 
- Modified `DailyEmailProcessingJob.cs` to look up the Super Admin user from the database
- Added `GetSystemUserIdAsync()` method to retrieve the Super Admin user ID
- Added proper error handling and logging for system user lookup

### 2. Column Mapping Gaps
**Problem**: CSV headers were not being mapped, resulting in 0/3 columns mapped with quality score 0.00.

**Root Cause**: The default column mappings were missing many variations of the actual CSV headers.

**Analysis**: 
- CSV headers like `RPM_PORT`, `RPM_STBD` were normalized to `rpmport`, `rpmstbd`
- Default mappings only had `port_rpm` → `portrpm`, `stbd_rpm` → `stbdrpm`
- Missing engine-specific mappings for water temperature, oil pressure, fuel rate, etc.
- Missing Greek column name support
- Missing system fields like GSM, sensor ID

**Solution**: Added missing mappings to `DefaultColumnMappings` in `CsvColumnMapping.cs`:

#### Engine Port Mappings Added:
- `rpmport` → `port_rpm`
- `portenghours` → `port_engine_hours`
- `portengoilpress` → `port_oil_pressure`
- `portengfuelrate` → `port_fuel_rate`
- `portengwatertemp` → `port_water_temperature`
- `portengalarmcode` → `port_alarm_code`
- `portengwarningcode` → `port_warning_code`
- `portenginerunning` → `port_engine_running`

#### Engine Starboard Mappings Added:
- `rpmstbd` → `stbd_rpm`
- `stbdenghours` → `stbd_engine_hours`
- `stbdengoilpress` → `stbd_oil_pressure`
- `stbdengfuelrate` → `stbd_fuel_rate`
- `stbdengwatertemp` → `stbd_water_temperature`
- `stbdengalarmcode` → `stbd_alarm_code`
- `stbdengwarningcode` → `stbd_warning_code`

#### System Mappings Added:
- `gsm` → `gsm_signal`
- `sensor` → `sensor_id`

#### Greek Column Mappings Added:
- `όχημα` → `vessel_name`
- `ταχύτητα` → `speed`
- `στροφές` → `rpm`
- `συντεταγμένες` → `coordinates`
- `ομαδοποίηση` → `grouping`
- And more Greek translations

### 3. Email Processing Flow Update
**Problem**: EmailProcessingService was still using the old `ProcessCsvFileAsync` method instead of the new `ProcessCsvToSensorDataPointsAsync` method.

**Solution**: 
- Updated `ProcessEmails.EmailProcessingService.cs` to use `ProcessCsvToSensorDataPointsAsync`
- Removed the old bulk CSV sensor data storage logic
- Updated to work with the new SensorDataPoints flow

## Files Modified

1. **SmartBoat.API/Types/CsvColumnMapping.cs**
   - Added 30+ new column mappings for engine variations
   - Added Greek column name support
   - Added system field mappings (GSM, sensor ID)

2. **SmartBoat.API/Jobs/DailyEmailProcessingJob.cs**
   - Added database service dependency
   - Added `GetSystemUserIdAsync()` method
   - Updated to use Super Admin user ID instead of `Guid.Empty`

3. **SmartBoat.API/Implementations/EmailProcessingService/ProcessEmails.EmailProcessingService.cs**
   - Updated to use `ProcessCsvToSensorDataPointsAsync` instead of `ProcessCsvFileAsync`
   - Updated processing logic for new SensorDataPoints flow

## Testing

Created test files:
- **SmartBoat.API.Tests/CsvColumnMappingTests.cs**: Unit tests for column mapping functionality
- **test_csv_sample.csv**: Sample CSV with actual headers from logs for testing

## Expected Results

After these fixes:
1. **Authorization**: Scheduled jobs should use valid Super Admin user ID
2. **Column Mapping**: Should map 10+ columns instead of 0/3, improving quality score significantly
3. **Data Processing**: CSV data should be properly converted to SensorDataPoints with JSON measurements
4. **Greek Support**: Greek column names should be recognized and mapped
5. **Engine Data**: Dual-engine vessel data should be properly separated into port/starboard sensors

## Verification Steps

1. Check logs for successful column mapping (quality score > 0)
2. Verify SensorDataPoints are being created in the database
3. Confirm no more "User not found or inactive" errors
4. Test with Greek CSV headers
5. Verify dual-engine data separation

## Next Steps

1. Deploy changes to test environment
2. Monitor CSV processing logs
3. Verify data quality in SensorDataPoints table
4. Test with actual CSV files from email processing
5. Consider adding more column variations as needed
