# SmartBoat API

ASP.NET Core Web API for the SmartBoat platform.

## Database Setup

### Prerequisites
- Docker (for SQL Server)

### Quick Start

1. **Start SQL Server**
   ```bash
   docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=YourStrong@Passw0rd" -p 1433:1433 --name sqlserver -d mcr.microsoft.com/mssql/server:2022-latest
   ```

2. **Setup Database**
   ```bash
   ./Database/setup-database.sh
   ```

3. **Run Application**
   ```bash
   dotnet run
   ```

## Development

The application uses `appsettings.Development.json` for development configuration. The `launchSettings.json` automatically sets the environment to Development.

### Connection String
Default connection string points to:
- Server: localhost
- Database: Smartboat  
- User: SA
- Password: YourStrong@Passw0rd

## API Documentation

When running in Development mode, Swagger UI is available at: https://localhost:7001/swagger