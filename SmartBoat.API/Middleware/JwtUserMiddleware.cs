using Microsoft.AspNetCore.Http;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text.Json;
using SmartBoat.API.Types;
using System.Text;

namespace SmartBoat.API.Middleware
{
    public class JwtUserMiddleware
    {
        private readonly RequestDelegate _next;

        public JwtUserMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Skip auth endpoints
            if (context.Request.Path.StartsWithSegments("/api/auth"))
            {
                await _next(context);
                return;
            }

            // Extract JWT token from Authorization header
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
            if (authHeader != null && authHeader.StartsWith("Bearer "))
            {
                var token = authHeader.Substring("Bearer ".Length).Trim();
                
                try
                {
                    var handler = new JwtSecurityTokenHandler();
                    var jsonToken = handler.ReadJwtToken(token);
                    
                    // Extract user ID from token claims
                    var userIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "sub" || x.Type == "userId" || x.Type == ClaimTypes.NameIdentifier);
                    
                    if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                    {
                        // Read and modify request body
                        context.Request.EnableBuffering();
                        
                        using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, leaveOpen: true);
                        var body = await reader.ReadToEndAsync();
                        context.Request.Body.Position = 0;

                        if (!string.IsNullOrEmpty(body))
                        {
                            // Deserialize request to check if it has the expected structure
                            var jsonDocument = JsonDocument.Parse(body);
                            
                            // Check for both "Header" and "header" (case-sensitive)
                            string headerKey = null;
                            JsonElement headerElement = default;
                            
                            if (jsonDocument.RootElement.TryGetProperty("Header", out headerElement))
                            {
                                headerKey = "Header";
                            }
                            else if (jsonDocument.RootElement.TryGetProperty("header", out headerElement))
                            {
                                headerKey = "header";
                            }
                            
                            if (headerKey != null)
                            {
                                // Create a new request object with updated header
                                var requestDict = JsonSerializer.Deserialize<Dictionary<string, object>>(body);
                                if (requestDict != null)
                                {
                                    var headerDict = new Dictionary<string, object>();
                                    
                                    // Preserve existing header properties if they exist
                                    if (requestDict.ContainsKey(headerKey) && requestDict[headerKey] is JsonElement existingHeader)
                                    {
                                        if (existingHeader.ValueKind == JsonValueKind.Object)
                                        {
                                            foreach (var prop in existingHeader.EnumerateObject())
                                            {
                                                headerDict[prop.Name] = prop.Value;
                                            }
                                        }
                                    }
                                    
                                    // Set or update UserId
                                    headerDict["UserId"] = userId.ToString();
                                    
                                    requestDict[headerKey] = headerDict;
                                    
                                    // Serialize back to JSON
                                    var modifiedBody = JsonSerializer.Serialize(requestDict, new JsonSerializerOptions
                                    {
                                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                                    });
                                    
                                    // Replace request body
                                    var bodyBytes = Encoding.UTF8.GetBytes(modifiedBody);
                                    context.Request.Body = new MemoryStream(bodyBytes);
                                    context.Request.ContentLength = bodyBytes.Length;
                                }
                            }
                            else
                            {
                                // If no Header/header property exists, add it (use "Header" as default)
                                var requestDict = JsonSerializer.Deserialize<Dictionary<string, object>>(body) ?? new Dictionary<string, object>();
                                
                                requestDict["Header"] = new Dictionary<string, object>
                                {
                                    ["UserId"] = userId.ToString()
                                };
                                
                                var modifiedBody = JsonSerializer.Serialize(requestDict, new JsonSerializerOptions
                                {
                                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                                });
                                
                                var bodyBytes = Encoding.UTF8.GetBytes(modifiedBody);
                                context.Request.Body = new MemoryStream(bodyBytes);
                                context.Request.ContentLength = bodyBytes.Length;
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    // Invalid token - let the authentication middleware handle it
                }
            }

            await _next(context);
        }
    }
}