using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class UserService
    {
        public async Task<ReturnListUserDto> GetList(ListUserRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"USR-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new BusinessException("USR-422", "Client Error");
            }

            var filters = new Dictionary<string, object>();
            if (!string.IsNullOrWhiteSpace(request.Username)) filters.Add("Username", request.Username);
            if (!string.IsNullOrWhiteSpace(request.Email)) filters.Add("Email", request.Email);
            if (!string.IsNullOrWhiteSpace(request.Role)) filters.Add("Role", request.Role);
            if (!string.IsNullOrWhiteSpace(request.Status)) filters.Add("Status", request.Status);
            if (!string.IsNullOrWhiteSpace(request.Company)) filters.Add("Company", request.Company);
            if (!string.IsNullOrWhiteSpace(request.Department)) filters.Add("Department", request.Department);

            var query = new PagedQuery
            {
                PageNumber = (int)request.PageOffset + 1,
                PageSize = (int)request.PageLimit,
                Search = request.SearchTerm,
                OrderOptions = new OrderOptions
                {
                    OrderColumn = request.SortField ?? "Created",
                    OrderDirection = request.SortOrder ?? "ASC"
                }
            };

            try
            {
                var pagedResult = await _autoCodeDbOperationsService.SelectPagedWithFiltersAsync<User>(query, filters);
                var userDtos = new List<UserDto>();
                foreach (var user in pagedResult.Records)
                {
                    // Check authorization for each individual user
                    try
                    {
                        var isAuthorizedDto = new IsAuthorizedDto<User>
                        {
                            UserId = userId,
                            PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                            Entity = user
                        };
                        await _authorizationService.IsAuthorized(isAuthorizedDto);

                        // If authorized, add to result
                        userDtos.Add(new UserDto
                        {
                            Id = user.Id,
                            Username = user.Username,
                            Email = user.Email,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            Role = user.Role,
                            Status = user.Status,
                            Created = user.Created,
                            Changed = user.Changed,
                            LastLogin = user.LastLogin,
                            TwoFactorEnabled = user.TwoFactorEnabled,
                            Avatar = user.Avatar,
                            Company = user.Company,
                            Department = user.Department,
                            Phone = user.Phone,
                            PhoneNumber = user.PhoneNumber,
                            Timezone = user.Timezone,
                            Language = user.Language,
                            Bio = user.Bio
                        });
                    }
                    catch (Exception)
                    {
                        // If not authorized, skip this user
                        continue;
                    }
                }

                var metadata = new MetadataDto
                {
                    PageLimit = (int)request.PageLimit,
                    PageOffset = (int)request.PageOffset,
                    Total = pagedResult.TotalRecords
                };

                return new ReturnListUserDto
                {
                    Data = userDtos,
                    Metadata = metadata
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"USR-500: Technical Error. Error while fetching user list. UserId: {userId}");
                throw new TechnicalException("USR-500", "Technical Error");
            }
        }
    }
}