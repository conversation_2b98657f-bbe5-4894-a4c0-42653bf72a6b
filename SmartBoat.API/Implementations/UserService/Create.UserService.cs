using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using BCrypt.Net;

namespace SmartBoat.API.Services
{
    public partial class UserService
    {
        public async Task<string> Create(CreateUserDto request, Guid userId)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Username) || string.IsNullOrWhiteSpace(request.Email) || string.IsNullOrWhiteSpace(request.Password) || string.IsNullOrWhiteSpace(request.FirstName) || string.IsNullOrWhiteSpace(request.LastName) || string.IsNullOrWhiteSpace(request.Role))
            {
                _logger.LogInformation($"USR-422: Client Error. Required fields are null or empty. UserId: {userId}");
                throw new BusinessException("USR-422", "Client Error");
            }

            var existingUser = await _databaseService.SelectAsync<User>(new { Email = request.Email });
            if (existingUser != null)
            {
                _logger.LogInformation($"USR-409: Conflict. User with Email {request.Email} already exists. UserId: {userId}");
                throw new BusinessException("USR-409", "Conflict");
            }

            var user = new User
            {
                Id = Guid.NewGuid(),
                Username = request.Username,
                Email = request.Email,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password + _configuration["Security:Salt"]),
                FirstName = request.FirstName,
                LastName = request.LastName,
                RoleId = request.RoleId,
                Role = request.Role,
                Status = request.Status,
                TwoFactorEnabled = request.TwoFactorEnabled,
                Avatar = request.Avatar,
                Joined = request.Joined,
                Company = request.Company,
                CompanyId = request.CompanyId,
                Department = request.Department,
                Phone = request.Phone,
                PhoneNumber = request.PhoneNumber,
                Timezone = request.Timezone,
                Language = request.Language,
                Bio = request.Bio,
                Created = DateTime.Now,
                Changed = null,
                LastLogin = null,
                IsDeleted = false
            };

            var isAuthorizedDto = new IsAuthorizedDto<User>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = user
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                await _databaseService.InsertAsync<User>(user);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"USR-500: Technical Error. Error while creating User with Id: {user.Id}. UserId: {userId}");
                throw new TechnicalException("USR-500", "Technical Error");
            }

            return user.Id.ToString();
        }
    }
}