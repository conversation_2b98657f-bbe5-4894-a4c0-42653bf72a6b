using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class UserService
    {
        public async Task<UserDto> Get(UserRequestDto request, Guid userId)
        {
            if (request == null || (request.Id == null && string.IsNullOrWhiteSpace(request.Username) && string.IsNullOrWhiteSpace(request.Email)))
            {
                _logger.LogInformation($"USR-422: Client Error. At least one of Id, Username, or Email must be provided. UserId: {userId}");
                throw new BusinessException("USR-422", "Client Error");
            }

            User user = null;
            try
            {
                if (request.Id != null)
                {
                    user = await _databaseService.SelectByIdAsync<User, Guid>(request.Id.Value);
                }
                else if (!string.IsNullOrWhiteSpace(request.Username))
                {
                    user = (await _databaseService.SelectAsync<User>(new { Username = request.Username })).FirstOrDefault();
                }
                else if (!string.IsNullOrWhiteSpace(request.Email))
                {
                    user = (await _databaseService.SelectAsync<User>(new { Email = request.Email })).FirstOrDefault();
                }

                if (user == null)
                {
                    _logger.LogInformation($"USR-404: Not Found. User not found with Id: {request.Id}, Username: {request.Username}, or Email: {request.Email}. UserId: {userId}");
                    throw new TechnicalException("USR-404", "Not Found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"USR-500: Technical Error. Error while fetching User with Id: {request.Id}, Username: {request.Username}, or Email: {request.Email}. UserId: {userId}");
                throw new TechnicalException("USR-500", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<User>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = user
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            var userDto = new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Role = user.Role,
                Status = user.Status,
                Created = user.Created,
                Changed = user.Changed,
                LastLogin = user.LastLogin,
                TwoFactorEnabled = user.TwoFactorEnabled,
                Avatar = user.Avatar,
                Company = user.Company,
                Department = user.Department,
                Phone = user.Phone,
                PhoneNumber = user.PhoneNumber,
                Timezone = user.Timezone,
                Language = user.Language,
                Bio = user.Bio
            };

            return userDto;
        }
    }
}