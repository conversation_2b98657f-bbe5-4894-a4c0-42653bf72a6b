using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class UserService
    {
        public async Task<bool> Delete(DeleteUserDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"USR-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("USR-422", "Client Error");
            }

            User user = null;
            try
            {
                user = await _databaseService.SelectByIdAsync<User, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"USR-500: Technical Error. Error while fetching User with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("USR-500", "Technical Error");
            }

            if (user == null)
            {
                _logger.LogInformation($"USR-404: Technical Error. User not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("USR-404", "Not Found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<User>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Delete },
                Entity = user
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                if (request.FieldsToDelete == null)
                {
                    user.IsDeleted = true;
                }
                else
                {
                    foreach (var field in request.FieldsToDelete)
                    {
                        switch (field)
                        {
                            case "Email":
                                user.Email = null;
                                break;
                            case "FirstName":
                                user.FirstName = null;
                                break;
                            case "LastName":
                                user.LastName = null;
                                break;
                            case "RoleId":
                                user.RoleId = null;
                                break;
                            case "Role":
                                user.Role = null;
                                break;
                            case "Status":
                                user.Status = null;
                                break;
                            case "TwoFactorEnabled":
                                user.TwoFactorEnabled = false;
                                break;
                            case "Avatar":
                                user.Avatar = null;
                                break;
                            case "Company":
                                user.Company = null;
                                break;
                            case "CompanyId":
                                user.CompanyId = null;
                                break;
                            case "Department":
                                user.Department = null;
                                break;
                            case "Phone":
                                user.Phone = null;
                                break;
                            case "PhoneNumber":
                                user.PhoneNumber = null;
                                break;
                            case "Timezone":
                                user.Timezone = null;
                                break;
                            case "Language":
                                user.Language = null;
                                break;
                            case "Bio":
                                user.Bio = null;
                                break;
                        }
                    }
                }

                await _databaseService.UpdateAsync<User>(user, new { Id = user.Id });
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"USR-500: Technical Error. Error while updating User with Id: {user.Id}. UserId: {userId}");
                throw new TechnicalException("USR-500", "Technical Error");
            }
        }
    }
}