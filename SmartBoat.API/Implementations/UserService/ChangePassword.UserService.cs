using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class UserService
    {
        public async Task<bool> ChangePassword(ChangePasswordDto request, Guid userId)
        {
            if (request == null || request.UserId == null || string.IsNullOrWhiteSpace(request.OldPassword) || string.IsNullOrWhiteSpace(request.NewPassword))
            {
                _logger.LogInformation($"USR-422: Client Error. Required fields are missing. UserId: {userId}");
                throw new BusinessException("USR-422", "Client Error");
            }

            User user = await _databaseService.SelectByIdAsync<User, Guid>(request.UserId.Value);
            if (user == null)
            {
                _logger.LogInformation($"USR-404: Not Found. User not found with Id: {request.UserId}. UserId: {userId}");
                throw new TechnicalException("USR-404", "Not Found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<User>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = user
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            // TODO: Replace with actual password verification logic
            bool isOldPasswordValid = request.OldPassword == user.PasswordHash; // Placeholder
            if (!isOldPasswordValid)
            {
                _logger.LogInformation($"USR-401: Unauthorized. Old password mismatch for UserId: {request.UserId}. UserId: {userId}");
                throw new BusinessException("USR-401", "Unauthorized");
            }

            // TODO: Replace with actual password hashing logic
            string newPasswordHash = request.NewPassword; // Placeholder

            try
            {
                await _databaseService.UpdateAsync<User>(new { PasswordHash = newPasswordHash }, new { Id = request.UserId });
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"USR-500: Technical Error. Error while updating password for UserId: {request.UserId}. UserId: {userId}");
                throw new TechnicalException("USR-500", "Technical Error");
            }

            return true;
        }
    }
}