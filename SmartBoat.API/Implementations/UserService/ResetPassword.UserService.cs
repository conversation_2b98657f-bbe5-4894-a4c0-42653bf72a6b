using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class UserService
    {
        public async Task<bool> ResetPassword(ResetPasswordDto request)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Email) || string.IsNullOrWhiteSpace(request.NewPassword) || string.IsNullOrWhiteSpace(request.ResetToken))
            {
                _logger.LogInformation($"USR-422: Client Error. Email, NewPassword, or ResetToken is null or empty.");
                throw new BusinessException("USR-422", "Client Error");
            }

            try
            {
                var users = await _databaseService.SelectAsync<User>(new { Email = request.Email });
                var user = users?.FirstOrDefault();
                if (user == null)
                {
                    _logger.LogInformation($"USR-404: Not Found. User not found with Email: {request.Email}.");
                    throw new TechnicalException("USR-404", "Not Found");
                }

                // Validate Reset Token (pseudo-code, replace with actual token validation logic)
                if (!ValidateResetToken(user, request.ResetToken))
                {
                    _logger.LogInformation($"USR-401: Unauthorized. Invalid or expired ResetToken for Email: {request.Email}.");
                    throw new BusinessException("USR-401", "Unauthorized");
                }

                // TODO: Replace with actual password hashing logic
                var hashedPassword = request.NewPassword; // Placeholder
                var updatedUser = new User
                {
                    Id = user.Id,
                    PasswordHash = hashedPassword
                };

                var rowsAffected = await _databaseService.UpdateAsync<User>(updatedUser, new { Id = user.Id });
                if (rowsAffected == 0)
                {
                    _logger.LogInformation($"USR-500: Technical Error. Error while updating password for User with Id: {user.Id}.");
                    throw new TechnicalException("USR-500", "Technical Error");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"USR-500: Technical Error. Error while resetting password for User with Email: {request.Email}.");
                throw new TechnicalException("USR-500", "Technical Error");
            }
        }

        private bool ValidateResetToken(User user, string resetToken)
        {
            // Implement actual token validation logic here
            return true; // Placeholder
        }
    }
}