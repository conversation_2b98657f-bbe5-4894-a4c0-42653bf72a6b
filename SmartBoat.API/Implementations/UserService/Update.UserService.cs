using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class UserService
    {
        public async Task<string> Update(UpdateUserDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"USR-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("USR-422", "Client Error");
            }

            User user = null;
            try
            {
                user = await _databaseService.SelectByIdAsync<User, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"USR-500: Technical Error. Error while fetching User with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("USR-500", "Technical Error");
            }

            if (user == null)
            {
                _logger.LogInformation($"USR-404: Technical Error. User not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("USR-404", "Not Found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<User>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = user
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            if (!string.IsNullOrWhiteSpace(request.Email))
            {
                var existingUser = await _databaseService.SelectAsync<User>(new { Email = request.Email });
                if (existingUser != null && existingUser.Any(u => u.Id != request.Id))
                {
                    _logger.LogInformation($"USR-409: Conflict. Email already exists. UserId: {userId}");
                    throw new BusinessException("USR-409", "Conflict");
                }
            }

            user.Email = request.Email ?? user.Email;
            user.FirstName = request.FirstName ?? user.FirstName;
            user.LastName = request.LastName ?? user.LastName;
            user.RoleId = request.RoleId ?? user.RoleId;
            user.Role = request.Role ?? user.Role;
            user.Status = request.Status ?? user.Status;
            user.TwoFactorEnabled = request.TwoFactorEnabled ?? user.TwoFactorEnabled;
            user.Avatar = request.Avatar ?? user.Avatar;
            user.Company = request.Company ?? user.Company;
            user.CompanyId = request.CompanyId ?? user.CompanyId;
            user.Department = request.Department ?? user.Department;
            user.Phone = request.Phone ?? user.Phone;
            user.PhoneNumber = request.PhoneNumber ?? user.PhoneNumber;
            user.Timezone = request.Timezone ?? user.Timezone;
            user.Language = request.Language ?? user.Language;
            user.Bio = request.Bio ?? user.Bio;
            user.Changed = DateTime.Now;

            try
            {
                await _databaseService.UpdateAsync<User>(user, new { Id = user.Id });
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"USR-500: Technical Error. Error while updating User with Id: {user.Id}. UserId: {userId}");
                throw new TechnicalException("USR-500", "Technical Error");
            }

            return user.Id.ToString();
        }
    }
}