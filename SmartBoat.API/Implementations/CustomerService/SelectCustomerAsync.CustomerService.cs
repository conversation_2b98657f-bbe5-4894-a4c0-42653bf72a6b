using System;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CustomerService
    {
        public async Task<Customer> SelectCustomerAsync(Guid customerId, Guid userId)
        {
            if (customerId == null)
            {
                _logger.LogInformation($"DP-422: Client Error. CustomerId is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            try
            {
                var customer = await _databaseService.SelectByIdAsync<Customer, Guid>(customerId);

                if (customer == null)
                {
                    _logger.LogInformation($"DP-404: Technical Error. Customer not found with Id: {customerId}. UserId: {userId}");
                    throw new TechnicalException("DP-404", "Entity not found");
                }

                var authorizationDto = new IsAuthorizedDto<Customer>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck
                    {
                        OperationActionId = OperationAction.Read
                    },
                    Entity = customer
                };

                await _authorizationService.IsAuthorized(authorizationDto);

                return customer;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Customer with Id: {customerId}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}