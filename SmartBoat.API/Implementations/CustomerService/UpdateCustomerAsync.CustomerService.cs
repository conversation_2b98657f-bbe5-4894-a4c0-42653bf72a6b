using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class CustomerService
    {
        public async Task<Response<Customer>> UpdateCustomerAsync(Guid customerId, UpdateCustomerDto request, Guid userId)
        {
            if (customerId == null || request == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null or request is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var customer = await _databaseService.SelectByIdAsync<Customer, Guid>(customerId);
            if (customer == null)
            {
                _logger.LogInformation($"DP-404: Business Error. Customer not found with Id: {customerId}. UserId: {userId}");
                throw new BusinessException("DP-404", "Customer not found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Customer>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = customer
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            customer.Name = request.Name;
            customer.ContactPerson = request.ContactPerson;
            customer.Email = request.Email;
            customer.Phone = request.Phone;
            customer.Status = request.Status;
            customer.Changed = DateTime.UtcNow;

            try
            {
                await _databaseService.UpdateAsync<Customer>(customer, new { Id = customerId });
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while updating Customer with Id: {customerId}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            return new Response<Customer> { Payload = customer };
        }
    }
}