using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CustomerService : ICustomerService
    {
        public async Task<CustomerDto> Update(UpdateCustomerDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Validation Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            if (string.IsNullOrWhiteSpace(request.Name) && 
                string.IsNullOrWhiteSpace(request.ContactPerson) && 
                string.IsNullOrWhiteSpace(request.Email) && 
                string.IsNullOrWhiteSpace(request.Phone) && 
                string.IsNullOrWhiteSpace(request.Status))
            {
                _logger.LogInformation($"DP-422: Validation Error. No updatable fields provided. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            Customer customer;
            try
            {
                customer = await _databaseService.SelectByIdAsync<Customer, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Customer with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }

            if (customer == null)
            {
                _logger.LogInformation($"DP-404: Not Found Error. Customer not found with Id: {request.Id}. UserId: {userId}");
                throw new BusinessException("DP-404", "The requested customer was not found.");
            }

            var authorizationDto = new IsAuthorizedDto<Customer>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Update
                },
                Entity = customer
            };
            await _authorizationService.IsAuthorized(authorizationDto);

            // Check for duplicate email (excluding current customer)
            if (!string.IsNullOrWhiteSpace(request.Email))
            {
                try
                {
                    var existingCustomersWithEmail = await _databaseService.SelectAsync<Customer>(new { Email = request.Email });
                    // Allow the same email if it belongs to the current customer being updated
                    var duplicateCustomer = existingCustomersWithEmail?.FirstOrDefault(c => c.Id != customer.Id);
                    if (duplicateCustomer != null)
                    {
                        _logger.LogInformation($"DP-409: Conflict Error. Duplicate email: {request.Email} (used by customer ID: {duplicateCustomer.Id}). UserId: {userId}");
                        throw new BusinessException("DP-409", "Duplicate customer (e.g., same email or name).");
                    }
                }
                catch (BusinessException)
                {
                    throw; // Re-throw business exceptions without wrapping
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"DP-500: Technical Error. Error while checking email uniqueness. UserId: {userId}");
                    throw new TechnicalException("DP-500", "An internal server error occurred.");
                }
            }

            // Check for duplicate name (excluding current customer)
            if (!string.IsNullOrWhiteSpace(request.Name))
            {
                try
                {
                    var existingCustomersWithName = await _databaseService.SelectAsync<Customer>(new { Name = request.Name });
                    
                    // Debug logging to understand what we found
                    _logger.LogInformation($"DEBUG: Name check for '{request.Name}' - Found {existingCustomersWithName?.Count() ?? 0} customers. Current customer ID: {customer.Id}");
                    if (existingCustomersWithName != null)
                    {
                        foreach (var existingCustomer in existingCustomersWithName)
                        {
                            _logger.LogInformation($"DEBUG: Found customer with name '{request.Name}' - ID: {existingCustomer.Id}");
                        }
                    }
                    
                    // Allow the same name if it belongs to the current customer being updated
                    var duplicateCustomer = existingCustomersWithName?.FirstOrDefault(c => c.Id != customer.Id);
                    if (duplicateCustomer != null)
                    {
                        _logger.LogInformation($"DP-409: Conflict Error. Duplicate name: {request.Name} (used by customer ID: {duplicateCustomer.Id}, current customer ID: {customer.Id}). UserId: {userId}");
                        throw new BusinessException("DP-409", "Duplicate customer (e.g., same email or name).");
                    }
                    else
                    {
                        _logger.LogInformation($"DEBUG: Name check passed - no duplicate found for '{request.Name}' (excluding current customer {customer.Id})");
                    }
                }
                catch (BusinessException)
                {
                    throw; // Re-throw business exceptions without wrapping
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"DP-500: Technical Error. Error while checking name uniqueness. UserId: {userId}");
                    throw new TechnicalException("DP-500", "An internal server error occurred.");
                }
            }

            if (!string.IsNullOrWhiteSpace(request.Name)) customer.Name = request.Name;
            if (!string.IsNullOrWhiteSpace(request.ContactPerson)) customer.ContactPerson = request.ContactPerson;
            if (!string.IsNullOrWhiteSpace(request.Email)) customer.Email = request.Email;
            if (!string.IsNullOrWhiteSpace(request.Phone)) customer.Phone = request.Phone;
            if (!string.IsNullOrWhiteSpace(request.Status)) customer.Status = request.Status;
            
            customer.Changed = DateTime.Now;
            customer.LastActive = DateTime.Now;

            try
            {
                await _databaseService.UpdateAsync<Customer>(customer, new { Id = customer.Id });
                
                // Send entity notification after successful update
                await _entityNotificationService.NotifyEntityUpdated(
                    "Customer", 
                    customer.Id, 
                    customer.Name, 
                    null, // Customer is top-level, no companyId
                    userId);
            }
            catch (Exception ex)
            {
                // Check if this is a database constraint violation (unique constraint)
                if (ex.Message.Contains("UK_Customer_Name") || ex.Message.Contains("UNIQUE KEY constraint") && ex.Message.Contains("Name"))
                {
                    _logger.LogInformation($"DP-409: Conflict Error. Database constraint violation for name: {customer.Name}. UserId: {userId}");
                    throw new BusinessException("DP-409", "Duplicate customer (e.g., same email or name).");
                }
                if (ex.Message.Contains("UK_Customer_Email") || (ex.Message.Contains("UNIQUE KEY constraint") && ex.Message.Contains("Email")))
                {
                    _logger.LogInformation($"DP-409: Conflict Error. Database constraint violation for email: {customer.Email}. UserId: {userId}");
                    throw new BusinessException("DP-409", "Duplicate customer (e.g., same email or name).");
                }
                
                _logger.LogError(ex, $"DP-500: Technical Error. Unexpected database error while updating Customer with Id: {customer.Id}. Exception: {ex.Message}. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }

            var result = new CustomerDto
            {
                Id = customer.Id,
                Name = customer.Name,
                ContactPerson = customer.ContactPerson,
                Email = customer.Email,
                Phone = customer.Phone,
                Status = customer.Status,
                LastActive = customer.LastActive,
                Created = customer.Created,
                Changed = customer.Changed
            };

            return result;
        }
    }
}