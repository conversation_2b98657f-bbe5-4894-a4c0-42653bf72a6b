using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CustomerService : ICustomerService
    {
        public async Task<CustomerDto> Create(CreateCustomerDto request, Guid userId)
        {
            var customer = new Customer
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                ContactPerson = request.ContactPerson,
                Email = request.Email,
                Phone = request.Phone,
                Status = request.Status ?? "Active",
                Created = DateTime.Now,
                LastActive = DateTime.Now,
                Changed = null
            };

            var isAuthorizedDto = new IsAuthorizedDto<Customer>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = customer
            };

            await _authorizationService.IsAuthorized(isAuthorizedDto);
            
            if (request == null || string.IsNullOrWhiteSpace(request.Name) || string.IsNullOrWhiteSpace(request.ContactPerson) || string.IsNullOrWhiteSpace(request.Email) || string.IsNullOrWhiteSpace(request.Phone))
            {
                _logger.LogInformation($"DP-422: ValidationException. One or more required fields are missing or invalid. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            if (!IsValidEmail(request.Email))
            {
                _logger.LogInformation($"DP-422: ValidationException. Invalid email format. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            var existingCustomer = await _databaseService.SelectAsync<Customer>(new { Email = request.Email, Name = request.Name });
            if (existingCustomer.Any())
            {
                _logger.LogInformation($"DP-409: ConflictException. Duplicate customer (e.g., same email or name). UserId: {userId}");
                throw new BusinessException("DP-409", "Duplicate customer (e.g., same email or name).");
            }

            try
            {
                await _databaseService.InsertAsync<Customer>(customer);
                
                // Send entity notification after successful creation
                await _entityNotificationService.NotifyEntityCreated(
                    "Customer", 
                    customer.Id, 
                    customer.Name, 
                    null, // Customer is top-level, no companyId
                    userId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: InternalException. Error while creating Customer with Id: {customer.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }

            var customerDto = new CustomerDto
            {
                Id = customer.Id,
                Name = customer.Name,
                ContactPerson = customer.ContactPerson,
                Email = customer.Email,
                Phone = customer.Phone,
                Status = customer.Status,
                LastActive = customer.LastActive,
                Created = customer.Created,
                Changed = customer.Changed,
                Companies = new List<CompanyDto>(),
                Subscriptions = new List<SubscriptionDto>()
            };

            return customerDto;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}