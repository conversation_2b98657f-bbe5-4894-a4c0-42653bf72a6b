using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;

namespace SmartBoat.API.Services
{
    public partial class CustomerService : ICustomerService
    {
        public async Task<CustomerDto> Delete(DeleteCustomerDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Validation Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            Customer customer = null;
            try
            {
                customer = await _databaseService.SelectByIdAsync<Customer, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Customer with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }

            if (customer == null)
            {
                _logger.LogInformation($"DP-404: Not Found Error. Customer not found with Id: {request.Id}. UserId: {userId}");
                throw new BusinessException("DP-404", "The requested customer was not found.");
            }

            var authorizationDto = new IsAuthorizedDto<Customer>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Delete
                },
                Entity = customer
            };

            await _authorizationService.IsAuthorized(authorizationDto);

            // Store customer information before deletion for notification
            var customerName = customer.Name;
            var customerId = customer.Id;

            try
            {
                await _databaseService.DeleteAsync<Customer>(new { Id = request.Id });
                
                // Send entity notification after successful deletion
                await _entityNotificationService.NotifyEntityDeleted(
                    "Customer", 
                    customerId, 
                    customerName, 
                    null, // Customer is top-level, no companyId
                    userId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while deleting Customer with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }

            return new CustomerDto
            {
                Id = customer.Id,
                Name = customer.Name,
                ContactPerson = customer.ContactPerson,
                Email = customer.Email,
                Phone = customer.Phone,
                Status = customer.Status,
                LastActive = customer.LastActive,
                Created = customer.Created,
                Changed = customer.Changed,
                Companies = new List<CompanyDto>(),
                Subscriptions = new List<SubscriptionDto>()
            };
        }
    }
}