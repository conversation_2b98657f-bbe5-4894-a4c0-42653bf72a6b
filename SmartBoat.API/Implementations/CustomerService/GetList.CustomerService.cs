using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using PPG.Auth.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CustomerService : ICustomerService
    {
        public async Task<ReturnListCustomerDto> GetList(ListCustomerRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"DP-422: ValidationException. Invalid PageLimit or PageOffset. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            var pagedQuery = new PagedQuery
            {
                PageSize = (int)request.PageLimit,
                Offset = request.PageOffset,
                Search = request.SearchTerm,
                OrderOptions = new OrderOptions
                {
                    OrderColumn = request.SortField,
                    OrderDirection = request.SortOrder
                }
            };

            var filters = new Dictionary<string, object>();
            if (!string.IsNullOrWhiteSpace(request.Name))
            {
                filters.Add("Name", request.Name);
            }
            if (!string.IsNullOrWhiteSpace(request.Status))
            {
                filters.Add("Status", request.Status);
            }

            try
            {
                var pagedResult = await _autoCodeDbOperationsService.SelectPagedWithFiltersAsync<Customer>(pagedQuery, filters);

                var customerDtos = new List<CustomerDto>();
                foreach (var customer in pagedResult.Records)
                {
                    // Check authorization for each individual customer
                    try
                    {
                        var isAuthorizedDto = new IsAuthorizedDto<Customer>
                        {
                            UserId = userId,
                            PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                            Entity = customer
                        };

                        await _authorizationService.IsAuthorized(isAuthorizedDto);

                        // If authorized, add to result
                        var customerDto = new CustomerDto
                        {
                            Id = customer.Id,
                            Name = customer.Name,
                            ContactPerson = customer.ContactPerson,
                            Email = customer.Email,
                            Phone = customer.Phone,
                            Status = customer.Status,
                            LastActive = customer.LastActive,
                            Created = customer.Created,
                            Changed = customer.Changed,
                            Companies = new List<CompanyDto>(),
                            Subscriptions = new List<SubscriptionDto>()
                        };

                        customerDtos.Add(customerDto);
                    }
                    catch (Exception)
                    {
                        // If not authorized, skip this customer (don't add to result)
                        continue;
                    }
                }

                var returnListCustomerDto = new ReturnListCustomerDto
                {
                    Data = customerDtos,
                    Metadata = new MetadataDto
                    {
                        PageLimit = request.PageLimit,
                        PageOffset = request.PageOffset,
                        Total = pagedResult.TotalRecords
                    }
                };

                return returnListCustomerDto;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: InternalException. Error while fetching customers. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }
        }
    }
}