using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CustomerService : ICustomerService
    {
        public async Task<CustomerDto> Get(CustomerRequestDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: ValidationException. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            Customer customer = null;
            try
            {
                customer = await _databaseService.SelectByIdAsync<Customer, Guid>((Guid)request.Id);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: InternalException. Error while fetching Customer with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }

            if (customer == null)
            {
                _logger.LogInformation($"DP-404: NotFoundException. Customer not found with Id: {request.Id}. UserId: {userId}");
                throw new BusinessException("DP-404", "The requested customer was not found.");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Customer>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = customer
            };

            await _authorizationService.IsAuthorized(isAuthorizedDto);

            var customerDto = new CustomerDto
            {
                Id = customer.Id,
                Name = customer.Name,
                ContactPerson = customer.ContactPerson,
                Email = customer.Email,
                Phone = customer.Phone,
                Status = customer.Status,
                LastActive = customer.LastActive,
                Created = customer.Created,
                Changed = customer.Changed,
                Companies = new System.Collections.Generic.List<CompanyDto>(),
                Subscriptions = new System.Collections.Generic.List<SubscriptionDto>()
            };

            return customerDto;
        }
    }
}