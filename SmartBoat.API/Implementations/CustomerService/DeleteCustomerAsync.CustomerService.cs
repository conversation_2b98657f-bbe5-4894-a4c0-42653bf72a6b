using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CustomerService
    {
        public async Task DeleteCustomerAsync(Guid customerId, Guid userId)
        {
            if (customerId == null)
            {
                _logger.LogInformation($"DP-422: Client Error. CustomerId is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            try
            {
                var customer = await _databaseService.SelectByIdAsync<Customer, Guid>(customerId);
                if (customer == null)
                {
                    _logger.LogInformation($"DP-404: Technical Error. Customer not found with Id: {customerId}. UserId: {userId}");
                    throw new TechnicalException("DP-404", "Customer not found");
                }

                var isAuthorizedDto = new IsAuthorizedDto<Customer>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck
                    {
                        OperationActionId = OperationAction.Delete
                    },
                    Entity = customer
                };

                await _authorizationService.IsAuthorized(isAuthorizedDto);

                await _databaseService.DeleteAsync<Customer>(new { Id = customerId });
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while deleting Customer with Id: {customerId}. UserId: {userId}");
                throw new TechnicalException("DP-500", "A technical exception has occurred, please contact your system administrator");
            }
        }
    }
}