using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CustomerService
    {
        public async Task<Customer> InsertCustomerAsync(Customer customer)
        {
            if (customer == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Customer is null.");
                throw new BusinessException("DP-422", "Client Error");
            }

            if (string.IsNullOrWhiteSpace(customer.Name))
            {
                _logger.LogInformation($"DP-422: Client Error. Customer Name is null or empty.");
                throw new BusinessException("DP-422", "Client Error");
            }

            try
            {
                var isAuthorizedDto = new IsAuthorizedDto<Customer>
                {
                    UserId = Guid.Empty, // No UserId on Customer, set to Guid.Empty or pass as parameter if needed
                    PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                    Entity = customer
                };

                await _authorizationService.IsAuthorized(isAuthorizedDto);

                var rowsAffected = await _databaseService.InsertAsync<Customer>(customer);

                if (rowsAffected == 0)
                {
                    _logger.LogInformation($"DP-500: Technical Error. Error while inserting Customer with Id: {customer.Id}.");
                    throw new TechnicalException("DP-500", "Technical Error");
                }

                return customer;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while inserting Customer with Id: {customer.Id}.");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}