using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;
using PPG.Auth.Types;

namespace SmartBoat.API.Services
{
    public class EntityNotificationService : IEntityNotificationService
    {
        private readonly INotificationService _notificationService;
        private readonly IDatabaseService _databaseService;
        private readonly ILogger<EntityNotificationService> _logger;

        public EntityNotificationService(
            INotificationService notificationService,
            IDatabaseService databaseService,
            ILogger<EntityNotificationService> logger)
        {
            _notificationService = notificationService;
            _databaseService = databaseService;
            _logger = logger;
        }

        public async Task NotifyEntityCreated(string entityType, Guid? entityId, string entityName, Guid? companyId, Guid createdByUserId)
        {
            var eventType = $"{entityType}Created";
            var content = $"A new {entityType.ToLower()} '{entityName}' has been created.";
            
            await SendNotifications(eventType, content, companyId, createdByUserId);
        }

        public async Task NotifyEntityUpdated(string entityType, Guid? entityId, string entityName, Guid? companyId, Guid updatedByUserId)
        {
            var eventType = $"{entityType}Updated";
            var content = $"The {entityType.ToLower()} '{entityName}' has been updated.";
            
            await SendNotifications(eventType, content, companyId, updatedByUserId);
        }

        public async Task NotifyEntityDeleted(string entityType, Guid? entityId, string entityName, Guid? companyId, Guid deletedByUserId)
        {
            _logger.LogInformation($"Starting notification for {entityType} deletion. EntityId: {entityId}, EntityName: {entityName}, CompanyId: {companyId}, DeletedBy: {deletedByUserId}");
            
            var eventType = $"{entityType}Deleted";
            var content = $"The {entityType.ToLower()} '{entityName}' has been deleted.";
            
            await SendNotifications(eventType, content, companyId, deletedByUserId);
        }

        private async Task SendNotifications(string eventType, string content, Guid? companyId, Guid actionByUserId)
        {
            try
            {
                var usersToNotify = await GetUsersToNotify(companyId, actionByUserId);

                foreach (var user in usersToNotify)
                {
                    var notificationRequest = new CreateNotificationDto
                    {
                        UserId = user.Id,
                        EventType = eventType,
                        Content = content,
                        Channel = "System"
                    };

                    try
                    {
                        // Create notification directly in database to bypass authorization for system notifications
                        var notification = new Notification
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            EventType = eventType,
                            Content = content,
                            Channel = "System",
                            PreferenceId = null,
                            Status = "Sent",
                            Timestamp = DateTime.Now,
                            Created = DateTime.Now,
                            Changed = null
                        };

                        await _databaseService.InsertAsync<Notification>(notification);
                        _logger.LogInformation($"Created system notification for user {user.Id}: {eventType}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to create notification for user {user.Id}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending entity notifications: {ex.Message}");
            }
        }

        private async Task<List<User>> GetUsersToNotify(Guid? companyId, Guid actionByUserId)
        {
            var usersToNotify = new List<User>();

            try
            {
                // Get admin users (Super Admin and Platform Admin)
                var adminUsers = await _databaseService.SelectAsync<User>(new 
                { 
                    IsDeleted = false,
                    Status = "Active"
                });

                if (adminUsers != null)
                {
                    var admins = adminUsers.Where(u => 
                        (u.Role == "Super Admin" || u.Role == "Platform Admin") && 
                        u.Id != actionByUserId).ToList();
                    usersToNotify.AddRange(admins);
                }

                // If entity is associated with a company, get company users
                if (companyId.HasValue)
                {
                    var companyUsers = await _databaseService.SelectAsync<User>(new 
                    { 
                        CompanyId = companyId.Value,
                        IsDeleted = false,
                        Status = "Active"
                    });

                    if (companyUsers != null)
                    {
                        var relevantCompanyUsers = companyUsers.Where(u => 
                            (u.Role == "Customer Admin" || u.Role == "Customer Employee") && 
                            u.Id != actionByUserId).ToList();
                        usersToNotify.AddRange(relevantCompanyUsers);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error retrieving users to notify: {ex.Message}");
            }

            // Remove duplicates
            return usersToNotify.GroupBy(u => u.Id).Select(g => g.First()).ToList();
        }
    }
}