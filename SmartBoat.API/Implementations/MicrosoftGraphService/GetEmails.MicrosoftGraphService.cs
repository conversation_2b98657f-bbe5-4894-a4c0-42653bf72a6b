using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public partial class MicrosoftGraphService
    {
        public async Task<Response<List<EmailDto>>> GetEmailsAsync(Guid userId, int daysBack = 7)
        {
            try
            {
                Console.WriteLine($"🔐 [GRAPH AUTH] Authenticating with Microsoft Graph...");
                using var httpClient = await GetAuthenticatedHttpClientAsync(userId);
                Console.WriteLine($"✅ [GRAPH AUTH] Authentication successful");
                
                var startDate = DateTime.UtcNow.AddDays(-daysBack).ToString("yyyy-MM-ddTHH:mm:ssZ");
                var requestUri = $"me/messages?$filter=receivedDateTime ge {startDate}" +
                               "&$select=id,subject,receivedDateTime,from,body" +
                               "&$orderby=receivedDateTime desc" +
                               "&$top=50";

                Console.WriteLine($"🌐 [GRAPH API] Calling: GET {requestUri}");
                var response = await httpClient.GetAsync(requestUri);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"❌ [GRAPH API] Failed: {response.StatusCode} - {errorContent}");
                    _logger.LogError("Failed to fetch emails. Status: {StatusCode}, Error: {Error}", 
                        response.StatusCode, errorContent);
                    
                    return new Response<List<EmailDto>>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "GRAPH-" + (int)response.StatusCode,
                            Description = "Failed to fetch emails from Microsoft Graph",
                            Category = "Graph API Error"
                        }
                    };
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var jsonDocument = JsonDocument.Parse(responseContent);
                var emails = new List<EmailDto>();

                if (jsonDocument.RootElement.TryGetProperty("value", out var emailsArray))
                {
                    foreach (var emailElement in emailsArray.EnumerateArray())
                    {
                        var email = ParseEmailFromJson(emailElement);
                        if (email != null && HasZipDownloadLinks(email.Body))
                        {
                            emails.Add(email);
                        }
                    }
                }

                Console.WriteLine($"✅ [GRAPH API] Retrieved {emails.Count} emails with ZIP download links");
                _logger.LogInformation("Retrieved {EmailCount} emails with ZIP download links for user {UserId}", 
                    emails.Count, userId);

                return new Response<List<EmailDto>>
                {
                    Payload = emails,
                    Exception = null
                };
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Authentication failed for user {UserId}", userId);
                return new Response<List<EmailDto>>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "GRAPH-401",
                        Description = ex.Message,
                        Category = "Authentication Error"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching emails for user {UserId}", userId);
                return new Response<List<EmailDto>>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "GRAPH-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }

        private bool HasZipDownloadLinks(string? emailBody)
        {
            if (string.IsNullOrEmpty(emailBody))
                return false;

            // Look for ZIP download URLs in the email body
            var zipUrlPattern = @"https?://[^\s<]+\.zip(?:\?[^\s<]*)?";
            return System.Text.RegularExpressions.Regex.IsMatch(emailBody, zipUrlPattern, 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }

        private List<string> ExtractZipDownloadUrls(string? emailBody)
        {
            var urls = new List<string>();
            if (string.IsNullOrEmpty(emailBody))
                return urls;

            // First try to extract URLs from href attributes (HTML content)
            var hrefPattern = @"href=[""'](https?://[^""']*\.zip(?:\?[^""']*)?)[""']";
            var hrefMatches = System.Text.RegularExpressions.Regex.Matches(emailBody, hrefPattern, 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            foreach (System.Text.RegularExpressions.Match match in hrefMatches)
            {
                if (match.Groups.Count > 1)
                {
                    var cleanUrl = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(cleanUrl))
                    {
                        urls.Add(cleanUrl);
                    }
                }
            }

            // If no href URLs found, try extracting plain text URLs (fallback)
            if (urls.Count == 0)
            {
                var plainUrlPattern = @"https?://[^\s<>""']+\.zip(?:\?[^\s<>""']*)?";
                var plainMatches = System.Text.RegularExpressions.Regex.Matches(emailBody, plainUrlPattern, 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                foreach (System.Text.RegularExpressions.Match match in plainMatches)
                {
                    var cleanUrl = match.Value.Trim();
                    
                    // Remove any trailing HTML artifacts
                    cleanUrl = System.Text.RegularExpressions.Regex.Replace(cleanUrl, @"[""'>].*$", "");
                    
                    if (!string.IsNullOrEmpty(cleanUrl) && cleanUrl.EndsWith(".zip", StringComparison.OrdinalIgnoreCase))
                    {
                        urls.Add(cleanUrl);
                    }
                }
            }

            // Remove duplicates and validate URLs
            return urls.Distinct()
                      .Where(url => Uri.TryCreate(url, UriKind.Absolute, out _))
                      .ToList();
        }

        private EmailDto? ParseEmailFromJson(JsonElement emailElement)
        {
            try
            {
                var email = new EmailDto();
                
                if (emailElement.TryGetProperty("id", out var idElement))
                    email.Id = idElement.GetString() ?? "";
                
                if (emailElement.TryGetProperty("subject", out var subjectElement))
                    email.Subject = subjectElement.GetString() ?? "";
                
                if (emailElement.TryGetProperty("receivedDateTime", out var dateElement))
                {
                    if (DateTime.TryParse(dateElement.GetString(), out var receivedDate))
                        email.ReceivedDateTime = receivedDate;
                }
                
                if (emailElement.TryGetProperty("from", out var fromElement) &&
                    fromElement.TryGetProperty("emailAddress", out var emailAddressElement) &&
                    emailAddressElement.TryGetProperty("address", out var addressElement))
                {
                    email.FromEmail = addressElement.GetString();
                }

                if (emailElement.TryGetProperty("body", out var bodyElement))
                {
                    if (bodyElement.TryGetProperty("content", out var contentElement))
                    {
                        email.Body = contentElement.GetString();
                        
                        // Extract ZIP download URLs from the email body
                        email.ZipDownloadUrls = ExtractZipDownloadUrls(email.Body);
                    }
                }

                return email;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse email from JSON");
                return null;
            }
        }

        private AttachmentDto? ParseAttachmentFromJson(JsonElement attachmentElement)
        {
            try
            {
                var attachment = new AttachmentDto();
                
                if (attachmentElement.TryGetProperty("id", out var idElement))
                    attachment.Id = idElement.GetString() ?? "";
                
                if (attachmentElement.TryGetProperty("name", out var nameElement))
                    attachment.Name = nameElement.GetString() ?? "";
                
                if (attachmentElement.TryGetProperty("contentType", out var contentTypeElement))
                    attachment.ContentType = contentTypeElement.GetString() ?? "";
                
                if (attachmentElement.TryGetProperty("size", out var sizeElement))
                    attachment.Size = sizeElement.GetInt64();

                return attachment;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse attachment from JSON");
                return null;
            }
        }
    }
}