using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Text.Json;
using System.Text;

namespace SmartBoat.API.Services
{
    public partial class MicrosoftGraphService : IMicrosoftGraphService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ITokenStorageService _tokenStorageService;
        private readonly ILogger<MicrosoftGraphService> _logger;
        private readonly IConfiguration _configuration;

        public MicrosoftGraphService(
            IHttpClientFactory httpClientFactory,
            ITokenStorageService tokenStorageService,
            ILogger<MicrosoftGraphService> logger,
            IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory;
            _tokenStorageService = tokenStorageService;
            _logger = logger;
            _configuration = configuration;
        }

        private async Task<HttpClient> GetAuthenticatedHttpClientAsync(Guid userId)
        {
            var tokenResponse = await _tokenStorageService.GetValidAccessTokenAsync(userId);
            
            if (tokenResponse.Exception != null || string.IsNullOrEmpty(tokenResponse.Payload))
            {
                throw new UnauthorizedAccessException($"No valid token available: {tokenResponse.Exception?.Description ?? "Unknown error"}");
            }

            var httpClient = _httpClientFactory.CreateClient("MicrosoftGraph");
            httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResponse.Payload);

            return httpClient;
        }
    }
}