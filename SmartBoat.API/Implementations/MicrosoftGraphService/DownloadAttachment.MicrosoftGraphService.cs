using System;
using System.Net.Http;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public partial class MicrosoftGraphService
    {
        public async Task<Response<byte[]>> DownloadAttachmentAsync(string emailId, string attachmentId, Guid userId)
        {
            try
            {
                using var httpClient = await GetAuthenticatedHttpClientAsync(userId);
                
                var requestUri = $"me/messages/{emailId}/attachments/{attachmentId}/$value";
                Console.WriteLine($"⬇️  [GRAPH DOWNLOAD] Downloading attachment: {attachmentId}");
                var response = await httpClient.GetAsync(requestUri);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to download attachment {AttachmentId} from email {EmailId}. Status: {StatusCode}, Error: {Error}", 
                        attachmentId, emailId, response.StatusCode, errorContent);
                    
                    return new Response<byte[]>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "GRAPH-" + (int)response.StatusCode,
                            Description = "Failed to download attachment from Microsoft Graph",
                            Category = "Graph API Error"
                        }
                    };
                }

                var attachmentContent = await response.Content.ReadAsByteArrayAsync();
                
                Console.WriteLine($"✅ [GRAPH DOWNLOAD] Downloaded {attachmentContent.Length:N0} bytes");
                _logger.LogInformation("Downloaded attachment {AttachmentId} ({Size} bytes) for user {UserId}", 
                    attachmentId, attachmentContent.Length, userId);

                return new Response<byte[]>
                {
                    Payload = attachmentContent,
                    Exception = null
                };
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Authentication failed while downloading attachment for user {UserId}", userId);
                return new Response<byte[]>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "GRAPH-401",
                        Description = ex.Message,
                        Category = "Authentication Error"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachment {AttachmentId} for user {UserId}", attachmentId, userId);
                return new Response<byte[]>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "GRAPH-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }

        public async Task<Response<bool>> TestConnectionAsync(Guid userId)
        {
            try
            {
                using var httpClient = await GetAuthenticatedHttpClientAsync(userId);
                
                var response = await httpClient.GetAsync("me");

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Graph API connection test failed for user {UserId}. Status: {StatusCode}, Error: {Error}", 
                        userId, response.StatusCode, errorContent);
                    
                    return new Response<bool>
                    {
                        Payload = false,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "GRAPH-" + (int)response.StatusCode,
                            Description = "Microsoft Graph API connection test failed",
                            Category = "Graph API Error"
                        }
                    };
                }

                _logger.LogInformation("Graph API connection test successful for user {UserId}", userId);

                return new Response<bool>
                {
                    Payload = true,
                    Exception = null
                };
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Authentication failed during connection test for user {UserId}", userId);
                return new Response<bool>
                {
                    Payload = false,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "GRAPH-401",
                        Description = ex.Message,
                        Category = "Authentication Error"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing Graph API connection for user {UserId}", userId);
                return new Response<bool>
                {
                    Payload = false,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "GRAPH-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }
    }
}