// File: Deactivate.SubscriptionService.cs
using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class SubscriptionService : ISubscriptionService
    {
        public async Task<SubscriptionDto> Deactivate(DeleteSubscriptionDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: ValidationException. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            Subscription subscription = null;
            try
            {
                subscription = await _databaseService.SelectByIdAsync<Subscription, Guid>((Guid)request.Id);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: InternalException. Error while fetching Subscription with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }

            if (subscription == null)
            {
                _logger.LogInformation($"DP-404: NotFoundException. Subscription not found with Id: {request.Id}. UserId: {userId}");
                throw new BusinessException("DP-404", "The requested subscription was not found.");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Subscription>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Delete },
                Entity = subscription
            };

            await _authorizationService.IsAuthorized(isAuthorizedDto);

            // Deactivate the subscription
            subscription.Status = "Inactive";
            subscription.Changed = DateTime.Now;
            subscription.LastUpdated = DateTime.Now;

            try
            {
                await _databaseService.UpdateAsync<Subscription>(subscription, new { Id = subscription.Id });
                
                var subscriptionDto = new SubscriptionDto
                {
                    Id = subscription.Id,
                    Name = subscription.Name,
                    Type = subscription.Type,
                    VesselId = subscription.VesselId,
                    StartDate = subscription.StartDate,
                    EndDate = subscription.EndDate,
                    Price = subscription.Price,
                    BillingFrequency = subscription.BillingFrequency,
                    Status = subscription.Status,
                    SensorLimit = subscription.SensorLimit,
                    LastUpdated = subscription.LastUpdated,
                    Created = subscription.Created,
                    Changed = subscription.Changed
                };

                return subscriptionDto;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: InternalException. Error while deactivating subscription. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }
        }
    }
}