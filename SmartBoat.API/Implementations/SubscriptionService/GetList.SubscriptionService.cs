// File: GetList.SubscriptionService.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using PPG.Auth.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class SubscriptionService : ISubscriptionService
    {
        public async Task<ReturnListSubscriptionDto> GetList(ListSubscriptionRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"DP-422: ValidationException. Invalid PageLimit or PageOffset. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            var pagedQuery = new PagedQuery
            {
                PageSize = (int)request.PageLimit,
                Offset = request.PageOffset,
                Search = request.SearchTerm,
                OrderOptions = new OrderOptions
                {
                    OrderColumn = request.SortField,
                    OrderDirection = request.SortOrder
                }
            };

            var filters = new Dictionary<string, object>();
            if (!string.IsNullOrWhiteSpace(request.Name))
            {
                filters.Add("Name", request.Name);
            }
            if (!string.IsNullOrWhiteSpace(request.Status))
            {
                filters.Add("Status", request.Status);
            }
            if (request.VesselId.HasValue)
            {
                filters.Add("VesselId", request.VesselId.Value);
            }

            try
            {
                var pagedResult = await _autoCodeDbOperationsService.SelectPagedWithFiltersAsync<Subscription>(pagedQuery, filters);

                var subscriptionDtos = new List<SubscriptionDto>();
                foreach (var subscription in pagedResult.Records)
                {
                    // Check authorization for each individual subscription
                    try
                    {
                        var isAuthorizedDto = new IsAuthorizedDto<Subscription>
                        {
                            UserId = userId,
                            PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                            Entity = subscription
                        };

                        await _authorizationService.IsAuthorized(isAuthorizedDto);

                        // If authorized, add to result
                        var subscriptionDto = new SubscriptionDto
                        {
                            Id = subscription.Id,
                            Name = subscription.Name,
                            Type = subscription.Type,
                            VesselId = subscription.VesselId,
                            StartDate = subscription.StartDate,
                            EndDate = subscription.EndDate,
                            Price = subscription.Price,
                            BillingFrequency = subscription.BillingFrequency,
                            Status = subscription.Status,
                            SensorLimit = subscription.SensorLimit,
                            LastUpdated = subscription.LastUpdated,
                            Created = subscription.Created,
                            Changed = subscription.Changed
                        };

                        subscriptionDtos.Add(subscriptionDto);
                    }
                    catch (Exception)
                    {
                        // If not authorized, skip this subscription (don't add to result)
                        continue;
                    }
                }

                var returnListSubscriptionDto = new ReturnListSubscriptionDto
                {
                    Data = subscriptionDtos,
                    Metadata = new MetadataDto
                    {
                        PageLimit = request.PageLimit,
                        PageOffset = request.PageOffset,
                        Total = pagedResult.TotalRecords
                    }
                };

                return returnListSubscriptionDto;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: InternalException. Error while fetching subscriptions. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }
        }
    }
}