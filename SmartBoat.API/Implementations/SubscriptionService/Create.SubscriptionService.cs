// File: Create.SubscriptionService.cs
using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class SubscriptionService : ISubscriptionService
    {
        public async Task<SubscriptionDto> Create(CreateSubscriptionDto request, Guid userId)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Name) || request.VesselId == null)
            {
                _logger.LogInformation($"DP-422: ValidationException. Required fields missing. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            var subscription = new Subscription
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Type = request.Type ?? "Standard",
                VesselId = request.VesselId.Value,
                StartDate = request.StartDate ?? DateTime.Now,
                EndDate = request.EndDate,
                Price = request.Price ?? 0,
                BillingFrequency = request.BillingFrequency ?? "Monthly",
                Status = request.Status ?? "Active",
                SensorLimit = request.SensorLimit,
                Created = DateTime.Now,
                LastUpdated = DateTime.Now,
                Changed = null
            };

            try
            {
                await _databaseService.InsertAsync(subscription);
                
                var subscriptionDto = new SubscriptionDto
                {
                    Id = subscription.Id,
                    Name = subscription.Name,
                    Type = subscription.Type,
                    VesselId = subscription.VesselId,
                    StartDate = subscription.StartDate,
                    EndDate = subscription.EndDate,
                    Price = subscription.Price,
                    BillingFrequency = subscription.BillingFrequency,
                    Status = subscription.Status,
                    SensorLimit = subscription.SensorLimit,
                    LastUpdated = subscription.LastUpdated,
                    Created = subscription.Created,
                    Changed = subscription.Changed
                };

                return subscriptionDto;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: InternalException. Error while creating subscription. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }
        }
    }
}