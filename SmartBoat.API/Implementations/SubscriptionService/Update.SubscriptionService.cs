// File: Update.SubscriptionService.cs
using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class SubscriptionService : ISubscriptionService
    {
        public async Task<SubscriptionDto> Update(UpdateSubscriptionDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: ValidationException. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "One or more required fields are missing or invalid.");
            }

            Subscription subscription = null;
            try
            {
                subscription = await _databaseService.SelectByIdAsync<Subscription, Guid>((Guid)request.Id);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: InternalException. Error while fetching Subscription with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }

            if (subscription == null)
            {
                _logger.LogInformation($"DP-404: NotFoundException. Subscription not found with Id: {request.Id}. UserId: {userId}");
                throw new BusinessException("DP-404", "The requested subscription was not found.");
            }

            // Update fields if provided
            if (!string.IsNullOrWhiteSpace(request.Name))
                subscription.Name = request.Name;
            if (!string.IsNullOrWhiteSpace(request.Type))
                subscription.Type = request.Type;
            if (request.StartDate.HasValue)
                subscription.StartDate = request.StartDate.Value;
            if (request.EndDate.HasValue)
                subscription.EndDate = request.EndDate.Value;
            if (request.Price.HasValue)
                subscription.Price = request.Price.Value;
            if (!string.IsNullOrWhiteSpace(request.BillingFrequency))
                subscription.BillingFrequency = request.BillingFrequency;
            if (!string.IsNullOrWhiteSpace(request.Status))
                subscription.Status = request.Status;
            if (request.SensorLimit.HasValue)
                subscription.SensorLimit = request.SensorLimit.Value;
            
            subscription.Changed = DateTime.Now;
            subscription.LastUpdated = DateTime.Now;

            try
            {
                await _databaseService.UpdateAsync<Subscription>(subscription, new { Id = subscription.Id });
                
                var subscriptionDto = new SubscriptionDto
                {
                    Id = subscription.Id,
                    Name = subscription.Name,
                    Type = subscription.Type,
                    VesselId = subscription.VesselId,
                    StartDate = subscription.StartDate,
                    EndDate = subscription.EndDate,
                    Price = subscription.Price,
                    BillingFrequency = subscription.BillingFrequency,
                    Status = subscription.Status,
                    SensorLimit = subscription.SensorLimit,
                    LastUpdated = subscription.LastUpdated,
                    Created = subscription.Created,
                    Changed = subscription.Changed
                };

                return subscriptionDto;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: InternalException. Error while updating subscription. UserId: {userId}");
                throw new TechnicalException("DP-500", "An internal server error occurred.");
            }
        }
    }
}