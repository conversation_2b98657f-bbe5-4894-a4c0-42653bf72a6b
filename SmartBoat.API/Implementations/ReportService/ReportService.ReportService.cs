using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Implementations.ReportService
{
    public partial class ReportService : IReportService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;
        private readonly ILogger<ReportService> _logger;
        private readonly IReportRepository _reportRepository;

        public ReportService(
            IAuthorizationService authorizationService,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService,
            ILogger<ReportService> logger,
            IReportRepository reportRepository)
        {
            _authorizationService = authorizationService;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
            _logger = logger;
            _reportRepository = reportRepository;
        }
    }
}