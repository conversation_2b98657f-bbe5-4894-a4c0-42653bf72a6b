using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Implementations.ReportService
{
    public partial class ReportService
    {
        public async Task<ReturnListReportDto> GetList(ListReportRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"DP-422: Invalid request parameters. PageLimit: {request?.PageLimit}, PageOffset: {request?.PageOffset}. UserId: {userId}");
                throw new BusinessException("DP-422", "Invalid request parameters");
            }

            try
            {
                var pagedQuery = new PagedQuery
                {
                    PageSize = (int)request.PageLimit,
                    Offset = (int)request.PageOffset,
                    OrderOptions = new OrderOptions
                    {
                        OrderColumn = string.IsNullOrWhiteSpace(request.SortField) ? "GeneratedAt" : request.SortField,
                        OrderDirection = string.IsNullOrWhiteSpace(request.SortOrder) ? "desc" : request.SortOrder
                    },
                    Search = request.SearchTerm
                };

                var filters = new Dictionary<string, object>();
                if (!string.IsNullOrWhiteSpace(request.Type))
                    filters.Add("Type", request.Type);
                if (!string.IsNullOrWhiteSpace(request.Status))
                    filters.Add("Status", request.Status);
                if (request.GeneratedBy != null)
                    filters.Add("GeneratedBy", request.GeneratedBy);

                var pagedResult = await _reportRepository.GetPaged(pagedQuery, filters);

                var reportDtos = new List<ReportDto>();
                foreach (var report in pagedResult.Records)
                {
                    // Check authorization for each individual report
                    try
                    {
                        var isAuthorizedDto = new IsAuthorizedDto<Report>
                        {
                            UserId = userId,
                            PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                            Entity = report
                        };
                        await _authorizationService.IsAuthorized(isAuthorizedDto);

                        // If authorized, add to result
                        reportDtos.Add(new ReportDto
                        {
                            Id = report.Id,
                            Type = report.Type,
                            Criteria = report.Criteria,
                            GeneratedBy = report.GeneratedBy,
                            GeneratedAt = report.GeneratedAt,
                            Status = report.Status,
                            DeliveryChannel = report.DeliveryChannel,
                            DeliveryStatus = report.DeliveryStatus
                        });
                    }
                    catch (Exception)
                    {
                        // If not authorized, skip this report
                        continue;
                    }
                }

                var returnListReportDto = new ReturnListReportDto
                {
                    Data = reportDtos,
                    Metadata = new MetadataDto
                    {
                        PageLimit = (int)request.PageLimit,
                        PageOffset = (int)request.PageOffset,
                        Total = pagedResult.TotalRecords
                    }
                };

                return returnListReportDto;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching reports. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }
        }
    }
}