using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Implementations.ReportService
{
    public partial class ReportService
    {
        public async Task<ReportDto> Get(GetReportRequestDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Invalid request parameters. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Invalid request parameters");
            }

            Report report = null;
            try
            {
                report = await _databaseService.SelectByIdAsync<Report, int>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Report with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }

            if (report == null)
            {
                _logger.LogInformation($"DP-404: Resource not found. Report not found with Id: {request.Id}. UserId: {userId}");
                throw new BusinessException("DP-404", "Resource not found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Report>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = report
            };

            await _authorizationService.IsAuthorized(isAuthorizedDto);

            var reportDto = new ReportDto
            {
                Id = report.Id,
                Type = report.Type,
                Criteria = report.Criteria,
                GeneratedBy = report.GeneratedBy,
                GeneratedAt = report.GeneratedAt,
                Status = report.Status,
                DeliveryChannel = report.DeliveryChannel,
                DeliveryStatus = report.DeliveryStatus
            };

            return reportDto;
        }
    }
}