using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Implementations.ReportService
{
    public partial class ReportService
    {
        public async Task<string> Generate(GenerateReportDto request, Guid userId)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Type) || string.IsNullOrWhiteSpace(request.Criteria))
            {
                _logger.LogInformation($"DP-422: Invalid request parameters. Request is null or required fields are empty. UserId: {userId}");
                throw new BusinessException("DP-422", "Invalid request parameters");
            }

            var report = new Report
            {
                // Id is int? and should be set by the database (auto-increment)
                Type = request.Type,
                Criteria = request.Criteria,
                // TODO: Map userId (Guid) to int? if possible, otherwise set to null
                GeneratedBy = null,
                GeneratedAt = DateTime.Now.ToString("o"),
                Status = "Completed",
                DeliveryChannel = "Download",
                DeliveryStatus = "Sent"
            };

            var isAuthorizedDto = new IsAuthorizedDto<Report>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = report
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                var rowsAffected = await _databaseService.InsertAsync<Report>(report);
                if (rowsAffected <= 0)
                {
                    _logger.LogInformation($"DP-500: Technical Error. Error while inserting Report with Id: {report.Id}. UserId: {userId}");
                    throw new TechnicalException("DP-500", "Internal server error");
                }

                return report.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while generating Report with Id: {report.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }
        }
    }
}