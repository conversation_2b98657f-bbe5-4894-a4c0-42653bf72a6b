using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class SensorService
    {
        public async Task<ReturnListSensorDto> GetList(ListSensorRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"SB-422: Client Error. Invalid pagination parameters. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            var filters = new Dictionary<string, object>();
            if (!string.IsNullOrWhiteSpace(request.Name))
                filters.Add("Name", request.Name);
            if (!string.IsNullOrWhiteSpace(request.Type))
                filters.Add("Type", request.Type);
            if (request.VesselId != null)
                filters.Add("VesselId", request.VesselId);

            var query = new PagedQuery
            {
                PageSize = (int)request.PageLimit,
                Offset = request.PageOffset,
                Search = request.SearchTerm,
                OrderOptions = new OrderOptions
                {
                    OrderColumn = string.IsNullOrWhiteSpace(request.SortField) ? "Created" : request.SortField,
                    OrderDirection = string.IsNullOrWhiteSpace(request.SortOrder) ? "desc" : request.SortOrder
                }
            };

            Nbg.NetCore.DatabaseService.PagedResult<Sensor> pagedResult;
            try
            {
                pagedResult = await _autoCodeDbOperationsService.SelectPagedWithFiltersAsync<Sensor>(query, filters);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while fetching Sensors. UserId: {userId}");
                throw new TechnicalException("SB-500", "Technical Error");
            }

            var sensorDtos = new List<SensorDto>();
            foreach (var record in pagedResult.Records)
            {
                // Check authorization for each individual sensor
                try
                {
                    var authorizationDto = new IsAuthorizedDto<Sensor>
                    {
                        UserId = userId,
                        PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                        Entity = record
                    };
                    await _authorizationService.IsAuthorized(authorizationDto);

                    // If authorized, process the sensor
                    var sensorDto = new SensorDto();
                    VesselDto vesselDto = null;

                    if (record.VesselId != null)
                    {
                        try
                        {
                            var vesselRequest = new DeleteVesselDto { Id = record.VesselId };
                            var vesselService = _vesselServiceFactory.CreateVesselService();
                            vesselDto = await vesselService.Get(vesselRequest, userId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogInformation($"SB-500: Technical Error. Error while fetching Vessel with Id: {record.VesselId}. UserId: {userId}");
                        }
                    }

                    sensorDto.Id = record.Id;
                    sensorDto.Name = record.Name;
                    sensorDto.Type = record.Type;
                    sensorDto.Vessel = vesselDto;
                    sensorDto.Location = record.Location;
                    sensorDto.Status = record.Status;
                    sensorDto.LastReading = record.LastReading;
                    sensorDto.LastUpdated = record.LastUpdated;
                    sensorDto.AlertThreshold = record.AlertThreshold;
                    sensorDto.Created = record.Created;
                    sensorDto.Changed = record.Changed;

                    sensorDtos.Add(sensorDto);
                }
                catch (Exception)
                {
                    // If not authorized, skip this sensor
                    continue;
                }
            }

            var returnDto = new ReturnListSensorDto
            {
                Data = sensorDtos,
                Metadata = new MetadataDto
                {
                    PageLimit = request.PageLimit,
                    PageOffset = request.PageOffset,
                    Total = pagedResult.TotalRecords
                }
            };

            return returnDto;
        }
    }
}