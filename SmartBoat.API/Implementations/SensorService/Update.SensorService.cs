using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class SensorService
    {
        public async Task<string> Update(UpdateSensorDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"SB-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            if ((request.Name != null && request.Name == string.Empty) || (request.Type != null && request.Type == string.Empty))
            {
                _logger.LogInformation($"SB-422: Client Error. Name or Type is empty. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            List<Sensor> sensors;
            try
            {
                sensors = (await _databaseService.SelectAsync<Sensor>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while fetching Sensors. UserId: {userId}");
                throw new TechnicalException("SB-500", "Technical Error");
            }

            var sensor = sensors.FirstOrDefault(s => s.Id == request.Id);
            if (sensor == null)
            {
                _logger.LogInformation($"SB-404: Technical Error. Sensor not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("SB-404", "Technical Error");
            }

            foreach (var item in sensors)
            {
                if (sensor.Name != request.Name && item.Name == request.Name)
                {
                    _logger.LogInformation($"SB-422: Client Error. Sensor with the same name already exists. UserId: {userId}");
                    throw new BusinessException("SB-422", "Client Error");
                }
            }

            var isAuthorizedDto = new IsAuthorizedDto<Sensor>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = sensor
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            if (request.VesselId != null)
            {
                var vesselRequest = new DeleteVesselDto { Id = request.VesselId.Value };
                try
                {
                    var vesselService = _vesselServiceFactory.CreateVesselService();
                    var vessel = await vesselService.Get(vesselRequest, userId);
                    if (vessel == null)
                    {
                        _logger.LogInformation($"SB-404: Technical Error. Vessel not found with Id: {request.VesselId}. UserId: {userId}");
                        throw new TechnicalException("SB-404", "Technical Error");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation($"SB-500: Technical Error. Error while fetching Vessel with Id: {request.VesselId}. UserId: {userId}");
                    throw new TechnicalException("SB-500", "Technical Error");
                }
            }

            sensor.Name = request.Name ?? sensor.Name;
            sensor.Type = request.Type ?? sensor.Type;
            sensor.VesselId = request.VesselId ?? sensor.VesselId;
            sensor.Location = request.Location ?? sensor.Location;
            sensor.Status = request.Status ?? sensor.Status;
            sensor.AlertThreshold = request.AlertThreshold ?? sensor.AlertThreshold;
            sensor.LastUpdated = DateTime.Now;
            sensor.Changed = DateTime.Now;

            try
            {
                await _databaseService.UpdateAsync<Sensor>(sensor, new { Id = sensor.Id });
                var updatedSensor = (await _databaseService.SelectAsync<Sensor>(new { Id = sensor.Id })).FirstOrDefault();
                if (updatedSensor == null)
                {
                    _logger.LogInformation($"SB-404: Technical Error. Sensor not found with Id: {sensor.Id}. UserId: {userId}");
                    throw new TechnicalException("SB-404", "Technical Error");
                }
                
                // Get vessel information to obtain CompanyId for notification
                var vesselRequest = new DeleteVesselDto { Id = sensor.VesselId.Value };
                var vesselService = _vesselServiceFactory.CreateVesselService();
                var vessel = await vesselService.Get(vesselRequest, userId);
                
                // Send entity notification after successful update
                await _entityNotificationService.NotifyEntityUpdated(
                    "Sensor", 
                    sensor.Id.Value, 
                    sensor.Name, 
                    vessel?.Company?.Id, // Use vessel's CompanyId
                    userId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while updating Sensor with Id: {sensor.Id}. UserId: {userId}");
                throw new TechnicalException("SB-500", "Technical Error");
            }

            return sensor.Id.ToString();
        }
    }
}