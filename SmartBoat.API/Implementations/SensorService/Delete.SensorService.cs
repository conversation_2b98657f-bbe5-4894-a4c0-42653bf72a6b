using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class SensorService
    {
        public async Task<bool> Delete(DeleteSensorDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"SB-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            Sensor sensor = null;
            try
            {
                sensor = await _databaseService.SelectByIdAsync<Sensor, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while fetching Sensor with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("SB-500", "Technical Error");
            }

            if (sensor == null)
            {
                _logger.LogInformation($"SB-404: Technical Error. Sensor not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("SB-404", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Sensor>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Delete },
                Entity = sensor
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            // Store sensor information before deletion for notification
            var sensorName = sensor.Name;
            var sensorId = sensor.Id.Value;
            
            // Get vessel information to obtain CompanyId for notification
            var vesselRequest = new DeleteVesselDto { Id = sensor.VesselId.Value };
            var vesselService = _vesselServiceFactory.CreateVesselService();
            var vessel = await vesselService.Get(vesselRequest, userId);

            try
            {
                if (request.FieldsToDelete == null)
                {
                    await _databaseService.DeleteAsync<Sensor>(new { Id = request.Id });
                    
                    // Send entity notification after successful deletion
                    await _entityNotificationService.NotifyEntityDeleted(
                        "Sensor", 
                        sensorId, 
                        sensorName, 
                        vessel?.Company?.Id, // Use vessel's CompanyId
                        userId);
                    
                    return true;
                }
                else
                {
                    await _databaseService.NullifyColumnsAsync<Sensor>(request.FieldsToDelete, new { Id = request.Id });
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while deleting Sensor with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("SB-500", "Technical Error");
            }
        }
    }
}