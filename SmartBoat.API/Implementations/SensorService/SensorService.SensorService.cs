using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class SensorService : ISensorService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly ILogger<SensorService> _logger;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;
        private readonly IVesselServiceFactory _vesselServiceFactory;
        private readonly IEntityNotificationService _entityNotificationService;

        public SensorService(
            IAuthorizationService authorizationService,
            ILogger<SensorService> logger,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService,
            IVesselServiceFactory vesselServiceFactory,
            IEntityNotificationService entityNotificationService)
        {
            _authorizationService = authorizationService;
            _logger = logger;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
            _vesselServiceFactory = vesselServiceFactory;
            _entityNotificationService = entityNotificationService;
        }
public async Task<List<SensorDto>> GetListByVessel(Guid vesselId)
        {
            var sensors = await _databaseService.SelectAsync<Sensor>(new { VesselId = vesselId });
            return sensors?.Select(s => new SensorDto
            {
                Id = s.Id,
                Name = s.Name,
                Type = s.Type,
                Vessel = null, // Optionally map VesselDto if needed
                Location = s.Location,
                Status = s.Status,
                AlertThreshold = s.AlertThreshold,
                LastUpdated = s.LastUpdated,
                Created = s.Created,
                Changed = s.Changed
            }).ToList() ?? new List<SensorDto>();
        }
    }
}