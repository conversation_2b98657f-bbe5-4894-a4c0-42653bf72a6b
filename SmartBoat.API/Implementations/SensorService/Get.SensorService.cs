using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class SensorService
    {
        public async Task<SensorDto> Get(SensorRequestDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"SB-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            Sensor sensor = null;
            try
            {
                sensor = await _databaseService.SelectByIdAsync<Sensor, Guid>((Guid)request.Id);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while fetching Sensor with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("SB-500", "Technical Error");
            }

            if (sensor == null)
            {
                _logger.LogInformation($"SB-404: Technical Error. Sensor not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("SB-404", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Sensor>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = sensor
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            VesselDto vessel = null;
            try
            {
                var vesselRequest = new DeleteVesselDto { Id = sensor.VesselId };
                var vesselService = _vesselServiceFactory.CreateVesselService();
                vessel = await vesselService.Get(vesselRequest, userId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while fetching Vessel with Id: {sensor.VesselId}. UserId: {userId}");
            }

            var sensorDto = new SensorDto
            {
                Id = sensor.Id,
                Name = sensor.Name,
                Type = sensor.Type,
                Vessel = vessel,
                Location = sensor.Location,
                Status = sensor.Status,
                LastReading = sensor.LastReading,
                LastUpdated = sensor.LastUpdated,
                AlertThreshold = sensor.AlertThreshold,
                Created = sensor.Created,
                Changed = sensor.Changed
            };

            return sensorDto;
        }
    }
}