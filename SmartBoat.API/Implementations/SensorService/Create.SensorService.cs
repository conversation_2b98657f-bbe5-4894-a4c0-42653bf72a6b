using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class SensorService
    {
        public async Task<string> Create(CreateSensorDto request, Guid userId)
        {
            if (request == null || request.VesselId == null || string.IsNullOrWhiteSpace(request.Name) || string.IsNullOrWhiteSpace(request.Type))
            {
                _logger.LogInformation($"SB-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            Sensor sensor = new Sensor();

            var isAuthorizedDto = new IsAuthorizedDto<Sensor>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = sensor
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            var vesselRequest = new DeleteVesselDto { Id = request.VesselId.Value };
            var vesselService = _vesselServiceFactory.CreateVesselService();
            var vessel = await vesselService.Get(vesselRequest, userId);
            if (vessel == null)
            {
                _logger.LogInformation($"SB-404: Technical Error. Vessel not found with Id: {request.VesselId}. UserId: {userId}");
                throw new TechnicalException("SB-404", "Technical Error");
            }

            sensor = new Sensor
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Type = request.Type,
                VesselId = request.VesselId,
                Location = request.Location,
                Status = request.Status ?? "Active",
                AlertThreshold = request.AlertThreshold,
                Created = DateTime.Now,
                LastUpdated = DateTime.Now
            };

            try
            {
                await _databaseService.InsertAsync<Sensor>(sensor);
                var insertedSensor = await _databaseService.SelectByIdAsync<Sensor, Guid>(sensor.Id.Value);
                if (insertedSensor == null)
                {
                    _logger.LogInformation($"SB-404: Technical Error. Sensor not found with Id: {sensor.Id}. UserId: {userId}");
                    throw new TechnicalException("SB-404", "Technical Error");
                }
                sensor = insertedSensor;
                
                // Send entity notification after successful creation
                // For sensors, use the vessel's CompanyId
                await _entityNotificationService.NotifyEntityCreated(
                    "Sensor", 
                    sensor.Id.Value, 
                    sensor.Name, 
                    vessel.Company?.Id, // Use vessel's CompanyId
                    userId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while inserting Sensor with Id: {sensor.Id}. UserId: {userId}");
                throw new TechnicalException("SB-500", "Technical Error");
            }

            return sensor.Id.ToString();
        }
    }
}