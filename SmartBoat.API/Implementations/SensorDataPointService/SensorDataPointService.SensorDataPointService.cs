using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class SensorDataPointService : ISensorDataPointService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly ILogger<SensorDataPointService> _logger;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;
        private readonly IEntityNotificationService _entityNotificationService;

        public SensorDataPointService(
            IAuthorizationService authorizationService,
            ILogger<SensorDataPointService> logger,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService,
            IEntityNotificationService entityNotificationService)
        {
            _authorizationService = authorizationService;
            _logger = logger;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
            _entityNotificationService = entityNotificationService;
        }
    }
}