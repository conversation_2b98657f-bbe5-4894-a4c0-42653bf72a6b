using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;
using System.Text.Json;

namespace SmartBoat.API.Services
{
    public partial class SensorDataPointService
    {
        public async Task<List<SensorDataPointDto>> GetBySensor(Guid sensorId, Guid userId)
        {
            var sensorDataPoint = new SensorDataPoint();
            var isAuthorizedDto = new IsAuthorizedDto<SensorDataPoint>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = sensorDataPoint
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            // Verify sensor exists and user has access
            var sensor = await _databaseService.SelectByIdAsync<Sensor, Guid>(sensorId);
            if (sensor == null)
            {
                _logger.LogInformation($"SB-404: Technical Error. Sensor not found with Id: {sensorId}. UserId: {userId}");
                throw new TechnicalException("SB-404", "Technical Error");
            }

            try
            {
                var filters = new Dictionary<string, object> { { "SensorId", sensorId } };
                var sensorDataPoints = await _databaseService.SelectAsync<SensorDataPoint>(filters);

                var sensorDataPointDtos = sensorDataPoints?.Select(sdp => new SensorDataPointDto
                {
                    Id = sdp.Id ?? Guid.Empty,
                    SensorId = sdp.SensorId ?? Guid.Empty,
                    Timestamp = sdp.Timestamp ?? DateTime.MinValue,
                    TimestampUnix = sdp.TimestampUnix ?? 0,
                    Measurements = string.IsNullOrEmpty(sdp.Measurements) 
                        ? new Dictionary<string, object>() 
                        : JsonSerializer.Deserialize<Dictionary<string, object>>(sdp.Measurements),
                    Temperature = sdp.Temperature,
                    Humidity = sdp.Humidity,
                    Speed = sdp.Speed,
                    RPM = sdp.RPM,
                    QualityScore = sdp.QualityScore,
                    Source = sdp.Source,
                    Created = sdp.Created ?? DateTime.MinValue,
                    Changed = sdp.Changed
                }).OrderByDescending(sdp => sdp.Timestamp).ToList() ?? new List<SensorDataPointDto>();

                return sensorDataPointDtos;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while retrieving SensorDataPoints for sensor {sensorId}. UserId: {userId}. Error: {ex.Message}");
                throw new TechnicalException("SB-500", "Technical Error");
            }
        }
    }
}