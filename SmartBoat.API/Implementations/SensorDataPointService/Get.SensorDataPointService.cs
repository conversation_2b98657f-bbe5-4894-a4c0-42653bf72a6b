using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;
using System.Text.Json;

namespace SmartBoat.API.Services
{
    public partial class SensorDataPointService
    {
        public async Task<SensorDataPointDto> Get(SensorDataPointRequestDto request, Guid userId)
        {
            if (request == null || request.Id == Guid.Empty)
            {
                _logger.LogInformation($"SB-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            var sensorDataPoint = new SensorDataPoint();
            var isAuthorizedDto = new IsAuthorizedDto<SensorDataPoint>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = sensorDataPoint
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                var dataPoint = await _databaseService.SelectByIdAsync<SensorDataPoint, Guid>(request.Id);
                if (dataPoint == null)
                {
                    _logger.LogInformation($"SB-404: Technical Error. SensorDataPoint not found with Id: {request.Id}. UserId: {userId}");
                    throw new TechnicalException("SB-404", "Technical Error");
                }

                return new SensorDataPointDto
                {
                    Id = dataPoint.Id ?? Guid.Empty,
                    SensorId = dataPoint.SensorId ?? Guid.Empty,
                    Timestamp = dataPoint.Timestamp ?? DateTime.MinValue,
                    TimestampUnix = dataPoint.TimestampUnix ?? 0,
                    Measurements = string.IsNullOrEmpty(dataPoint.Measurements) 
                        ? new Dictionary<string, object>() 
                        : JsonSerializer.Deserialize<Dictionary<string, object>>(dataPoint.Measurements),
                    Temperature = dataPoint.Temperature,
                    Humidity = dataPoint.Humidity,
                    Speed = dataPoint.Speed,
                    RPM = dataPoint.RPM,
                    QualityScore = dataPoint.QualityScore,
                    Source = dataPoint.Source,
                    Created = dataPoint.Created ?? DateTime.MinValue,
                    Changed = dataPoint.Changed
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while retrieving SensorDataPoint with Id: {request.Id}. UserId: {userId}. Error: {ex.Message}");
                throw new TechnicalException("SB-500", "Technical Error");
            }
        }
    }
}