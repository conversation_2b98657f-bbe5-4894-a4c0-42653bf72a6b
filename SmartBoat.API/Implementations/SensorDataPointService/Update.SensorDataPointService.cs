using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;
using System.Text.Json;

namespace SmartBoat.API.Services
{
    public partial class SensorDataPointService
    {
        public async Task<string> Update(UpdateSensorDataPointDto request, Guid userId)
        {
            if (request == null || request.Id == Guid.Empty)
            {
                _logger.LogInformation($"SB-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            var sensorDataPoint = new SensorDataPoint();
            var isAuthorizedDto = new IsAuthorizedDto<SensorDataPoint>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = sensorDataPoint
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                var existingDataPoint = await _databaseService.SelectByIdAsync<SensorDataPoint, Guid>(request.Id);
                if (existingDataPoint == null)
                {
                    _logger.LogInformation($"SB-404: Technical Error. SensorDataPoint not found with Id: {request.Id}. UserId: {userId}");
                    throw new TechnicalException("SB-404", "Technical Error");
                }

                // Update only provided fields
                if (request.Measurements != null)
                {
                    existingDataPoint.Measurements = JsonSerializer.Serialize(request.Measurements);
                }

                if (request.QualityScore.HasValue)
                {
                    existingDataPoint.QualityScore = request.QualityScore.Value;
                }

                if (!string.IsNullOrEmpty(request.Source))
                {
                    existingDataPoint.Source = request.Source;
                }

                existingDataPoint.Changed = DateTime.Now;

                await _databaseService.UpdateAsync<SensorDataPoint>(existingDataPoint, new { Id = existingDataPoint.Id });

                return existingDataPoint.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while updating SensorDataPoint with Id: {request.Id}. UserId: {userId}. Error: {ex.Message}");
                throw new TechnicalException("SB-500", "Technical Error");
            }
        }
    }
}