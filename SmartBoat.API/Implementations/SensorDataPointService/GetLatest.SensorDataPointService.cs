using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;
using System.Text.Json;

namespace SmartBoat.API.Services
{
    public partial class SensorDataPointService
    {
        public async Task<List<SensorDataPointDto>> GetLatest(LatestSensorDataPointRequestDto request, Guid userId)
        {
            var sensorDataPoint = new SensorDataPoint();
            var isAuthorizedDto = new IsAuthorizedDto<SensorDataPoint>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = sensorDataPoint
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            var sensorIds = new List<Guid>();
            
            // Build sensor ID list
            if (request.SensorId.HasValue)
            {
                sensorIds.Add(request.SensorId.Value);
            }
            
            if (request.SensorIds != null && request.SensorIds.Any())
            {
                sensorIds.AddRange(request.SensorIds);
            }

            if (!sensorIds.Any())
            {
                _logger.LogInformation($"SB-422: Client Error. No sensor IDs provided. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            try
            {
                var latestDataPoints = new List<SensorDataPointDto>();

                // Get latest data point for each sensor
                foreach (var sensorId in sensorIds.Distinct())
                {
                    // Verify sensor exists
                    var sensor = await _databaseService.SelectByIdAsync<Sensor, Guid>(sensorId);
                    if (sensor == null)
                    {
                        _logger.LogInformation($"SB-404: Technical Error. Sensor not found with Id: {sensorId}. UserId: {userId}");
                        continue; // Skip non-existent sensors instead of throwing
                    }

                    // Get latest data point for this sensor
                    var filters = new { SensorId = sensorId };
                    var allDataPoints = await _databaseService.SelectAsync<SensorDataPoint>(filters);
                    var latestDataPoint = allDataPoints?.OrderByDescending(dp => dp.Timestamp).Take(1);
                    
                    if (latestDataPoint != null && latestDataPoint.Any())
                    {
                        var sdp = latestDataPoint.First();
                        latestDataPoints.Add(new SensorDataPointDto
                        {
                            Id = sdp.Id ?? Guid.Empty,
                            SensorId = sdp.SensorId ?? Guid.Empty,
                            Timestamp = sdp.Timestamp ?? DateTime.MinValue,
                            TimestampUnix = sdp.TimestampUnix ?? 0,
                            Measurements = string.IsNullOrEmpty(sdp.Measurements) 
                                ? new Dictionary<string, object>() 
                                : JsonSerializer.Deserialize<Dictionary<string, object>>(sdp.Measurements),
                            Temperature = sdp.Temperature,
                            Humidity = sdp.Humidity,
                            Speed = sdp.Speed,
                            RPM = sdp.RPM,
                            QualityScore = sdp.QualityScore,
                            Source = sdp.Source,
                            Created = sdp.Created ?? DateTime.MinValue,
                            Changed = sdp.Changed
                        });
                    }
                }

                return latestDataPoints.OrderByDescending(sdp => sdp.Timestamp).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while retrieving latest SensorDataPoints. UserId: {userId}. Error: {ex.Message}");
                throw new TechnicalException("SB-500", "Technical Error");
            }
        }
    }
}