using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;
using System.Text.Json;

namespace SmartBoat.API.Services
{
    public partial class SensorDataPointService
    {
        public async Task<ReturnListSensorDataPointDto> GetList(ListSensorDataPointRequestDto request, Guid userId)
        {
            var sensorDataPoint = new SensorDataPoint();
            var isAuthorizedDto = new IsAuthorizedDto<SensorDataPoint>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = sensorDataPoint
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                List<SensorDataPoint> sensorDataPoints = new List<SensorDataPoint>();

                // Handle single sensor ID filtering
                if (request.SensorId.HasValue)
                {
                    var filters = new { SensorId = request.SensorId.Value };
                    var results = await _databaseService.SelectAsync<SensorDataPoint>(filters);
                    sensorDataPoints = results?.ToList() ?? new List<SensorDataPoint>();
                }
                // Handle multiple sensor IDs - get data for each sensor separately and combine
                else if (request.SensorIds != null && request.SensorIds.Any())
                {
                    foreach (var sensorId in request.SensorIds.Distinct())
                    {
                        var filters = new { SensorId = sensorId };
                        var results = await _databaseService.SelectAsync<SensorDataPoint>(filters);
                        if (results != null)
                        {
                            sensorDataPoints.AddRange(results);
                        }
                    }
                }
                // If no sensor filter specified, get all (this might be limited by authorization)
                else
                {
                    var allResults = await _databaseService.SelectAsync<SensorDataPoint>(new { });
                    sensorDataPoints = allResults?.ToList() ?? new List<SensorDataPoint>();
                }

                // Apply time range filtering in memory (since we can't do complex SQL)
                if (request.StartTime.HasValue)
                {
                    sensorDataPoints = sensorDataPoints.Where(sdp => sdp.Timestamp >= request.StartTime.Value).ToList();
                }

                if (request.EndTime.HasValue)
                {
                    sensorDataPoints = sensorDataPoints.Where(sdp => sdp.Timestamp <= request.EndTime.Value).ToList();
                }

                // Apply ordering
                if (request.Descending == true)
                {
                    sensorDataPoints = sensorDataPoints.OrderByDescending(sdp => sdp.Timestamp).ToList();
                }
                else
                {
                    sensorDataPoints = sensorDataPoints.OrderBy(sdp => sdp.Timestamp).ToList();
                }

                // Apply limit
                if (request.Limit.HasValue && request.Limit.Value > 0)
                {
                    sensorDataPoints = sensorDataPoints.Take(request.Limit.Value).ToList();
                }

                var sensorDataPointDtos = sensorDataPoints?.Select(sdp => {
                    try
                    {
                        return new SensorDataPointDto
                        {
                            Id = sdp.Id ?? Guid.Empty,
                            SensorId = sdp.SensorId ?? Guid.Empty,
                            Timestamp = sdp.Timestamp ?? DateTime.MinValue,
                            TimestampUnix = sdp.TimestampUnix ?? 0,
                            Measurements = string.IsNullOrEmpty(sdp.Measurements) 
                                ? new Dictionary<string, object>() 
                                : JsonSerializer.Deserialize<Dictionary<string, object>>(sdp.Measurements) ?? new Dictionary<string, object>(),
                            
                            // New computed columns from actual CSV data
                            GSMSignal = sdp.GSMSignal,
                            PowerSupply = sdp.PowerSupply,
                            Battery = sdp.Battery,
                            Rudder = sdp.Rudder,
                            TotalNauticalMiles = sdp.TotalNauticalMiles,
                            Location = sdp.Location,
                            
                            // Generic computed columns (for backward compatibility)
                            Temperature = sdp.Temperature,
                            Humidity = sdp.Humidity,
                            Speed = sdp.Speed,
                            RPM = sdp.RPM,
                            
                            QualityScore = sdp.QualityScore,
                            Source = sdp.Source ?? "Unknown",
                            Created = sdp.Created ?? DateTime.MinValue,
                            Changed = sdp.Changed
                        };
                    }
                    catch (Exception ex)
                    {
                        // Log the specific issue but don't fail the entire operation
                        _logger.LogError(ex, "Error mapping sensor data point {Id}", sdp.Id);
                        return null;
                    }
                }).Where(dto => dto != null).ToList() ?? new List<SensorDataPointDto>();

                return new ReturnListSensorDataPointDto
                {
                    Items = sensorDataPointDtos,
                    TotalCount = sensorDataPointDtos.Count(),
                    PageSize = request.Limit ?? sensorDataPointDtos.Count(),
                    PageNumber = 1
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while retrieving SensorDataPoints. UserId: {userId}. Error: {ex.Message}");
                throw new TechnicalException("SB-500", "Technical Error");
            }
        }
    }
}