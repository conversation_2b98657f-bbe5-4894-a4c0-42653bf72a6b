using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class FeedbackService
    {
        public async Task<Feedback> SelectFeedbackAsync(Guid feedbackId)
        {
            if (feedbackId == null)
            {
                _logger.LogInformation($"DP-422: Client Error. FeedbackId is null.");
                throw new BusinessException("DP-422", "Client Error");
            }

            try
            {
                var feedback = await _databaseService.SelectByIdAsync<Feedback, Guid>(feedbackId);

                if (feedback == null)
                {
                    _logger.LogInformation($"DP-404: Technical Error. Feedback not found with Id: {feedbackId}.");
                    throw new TechnicalException("DP-404", "Entity not found");
                }

                return feedback;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Feedback with Id: {feedbackId}.");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}