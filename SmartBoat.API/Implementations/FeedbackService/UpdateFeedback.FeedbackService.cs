using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Services
{
    public partial class FeedbackService
    {
        public async Task<Feedback> UpdateFeedback(UpdateFeedbackRequest request)
        {
            if (request == null || request.Payload == null || request.Payload.FeedbackId == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Request or FeedbackId is null. UserId: {request?.Header?.UserId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            if (string.IsNullOrWhiteSpace(request.Payload.Type) ||
                string.IsNullOrWhiteSpace(request.Payload.Content) ||
                string.IsNullOrWhiteSpace(request.Payload.Status))
            {
                _logger.LogInformation($"DP-422: Client Error. Required fields are empty. UserId: {request.Header.UserId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            try
            {
                var existingFeedback = await _databaseService.SelectByIdAsync<Feedback, Guid>(request.Payload.FeedbackId.Value);
                if (existingFeedback == null)
                {
                    _logger.LogInformation($"DP-404: Business Error. Feedback not found with Id: {request.Payload.FeedbackId}. UserId: {request.Header.UserId}");
                    throw new BusinessException("DP-404", "Feedback not found");
                }

                var isAuthorizedDto = new IsAuthorizedDto<Feedback>
                {
                    UserId = request.Header.UserId,
                    PermissionToCheck = new PermissionToCheck
                    {
                        OperationActionId = OperationAction.Update
                    },
                    Entity = existingFeedback
                };
                await _authorizationService.IsAuthorized(isAuthorizedDto);

                var updatedFeedback = new Feedback
                {
                    Id = request.Payload.FeedbackId.Value,
                    Type = request.Payload.Type,
                    Content = request.Payload.Content,
                    Status = request.Payload.Status
                };

                var rowsAffected = await _databaseService.UpdateAsync<Feedback>(
                    updateData: updatedFeedback,
                    filter: new { Id = request.Payload.FeedbackId });

                if (rowsAffected == 0)
                {
                    _logger.LogInformation($"DP-500: Technical Error. Failed to update Feedback with Id: {request.Payload.FeedbackId}. UserId: {request.Header.UserId}");
                    throw new TechnicalException("DP-500", "Technical Error");
                }

                return await _databaseService.SelectByIdAsync<Feedback, Guid>(request.Payload.FeedbackId.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while updating Feedback with Id: {request.Payload.FeedbackId}. UserId: {request.Header.UserId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}