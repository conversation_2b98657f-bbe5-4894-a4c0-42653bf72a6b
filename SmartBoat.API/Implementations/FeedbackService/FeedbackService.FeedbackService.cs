using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class FeedbackService : IFeedbackService
    {
        private readonly IFeedbackService _feedbackService;
        private readonly IAuthorizationService _authorizationService;
        private readonly ILogger<FeedbackService> _logger;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;

        public FeedbackService(
            IFeedbackService feedbackService,
            IAuthorizationService authorizationService,
            ILogger<FeedbackService> logger,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService)
        {
            _feedbackService = feedbackService;
            _authorizationService = authorizationService;
            _logger = logger;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
        }
    }
}