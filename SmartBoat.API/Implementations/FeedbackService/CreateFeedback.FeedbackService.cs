using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class FeedbackService
    {
        public async Task<Feedback> CreateFeedback(FeedbackRequest request)
        {
            if (request == null || request.Payload == null || request.Header == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Request is null. UserId: {request?.Header?.UserId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            if (string.IsNullOrWhiteSpace(request.Payload.Type) || string.IsNullOrWhiteSpace(request.Payload.Content))
            {
                _logger.LogInformation($"DP-422: Client Error. Type or Content is null or empty. UserId: {request.Header.UserId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var feedback = new Feedback
            {
                Id = Guid.NewGuid(),
                Type = request.Payload.Type,
                Content = request.Payload.Content,
                SubmittedAt = DateTime.UtcNow,
                UserId = request.Header.UserId
            };

            var isAuthorizedDto = new IsAuthorizedDto<Feedback>
            {
                UserId = request.Header.UserId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = feedback
            };

            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                await _databaseService.InsertAsync<Feedback>(feedback);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while creating Feedback with Id: {feedback.Id}. UserId: {request.Header.UserId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            return feedback;
        }
    }
}