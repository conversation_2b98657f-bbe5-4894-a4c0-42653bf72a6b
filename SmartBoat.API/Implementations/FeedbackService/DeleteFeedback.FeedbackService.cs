using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class FeedbackService
    {
        public async Task DeleteFeedbackAsync(Guid feedbackId, Guid userId)
        {
            if (feedbackId == null)
            {
                _logger.LogInformation($"DP-422: Client Error. FeedbackId is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            try
            {
                var feedback = await _databaseService.SelectByIdAsync<Feedback, Guid>(feedbackId);
                if (feedback == null)
                {
                    _logger.LogInformation($"DP-404: Business Error. Feedback not found with Id: {feedbackId}. UserId: {userId}");
                    throw new BusinessException("DP-404", "Entity not found");
                }

                var isAuthorizedDto = new IsAuthorizedDto<Feedback>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck
                    {
                        OperationActionId = OperationAction.Delete
                    },
                    Entity = feedback
                };

                await _authorizationService.IsAuthorized(isAuthorizedDto);

                var rowsAffected = await _databaseService.DeleteAsync<Feedback>(new { Id = feedbackId });
                if (rowsAffected == 0)
                {
                    _logger.LogInformation($"DP-404: Business Error. Feedback not found with Id: {feedbackId}. UserId: {userId}");
                    throw new BusinessException("DP-404", "Entity not found");
                }
            }
            catch (Exception ex) when (!(ex is BusinessException))
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while deleting Feedback with Id: {feedbackId}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}