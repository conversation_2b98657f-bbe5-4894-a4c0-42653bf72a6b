using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class ImageService : IImageService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly ILogger<ImageService> _logger;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;

        public ImageService(
            IAuthorizationService authorizationService,
            ILogger<ImageService> logger,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService)
        {
            _authorizationService = authorizationService;
            _logger = logger;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
        }
    }
}