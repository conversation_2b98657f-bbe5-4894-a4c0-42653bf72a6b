using SmartBoat.API.Types;
using System;
using System.IO;
using System.Threading.Tasks;

namespace SmartBoat.API.Services
{
    public partial class ImageService
    {
        public async Task<string> Create(CreateImageDto createImageDto)
        {
            var imageId = Guid.NewGuid();
            
            // Read the file into a byte array
            byte[] imageData;
            using (var memoryStream = new MemoryStream())
            {
                await createImageDto.ImageFile.CopyToAsync(memoryStream);
                imageData = memoryStream.ToArray();
            }

            var image = new Image
            {
                Id = imageId,
                EntityId = createImageDto.EntityId,
                ImageData = imageData,
                FileName = createImageDto.ImageFile.FileName,
                ContentType = createImageDto.ImageFile.ContentType,
                UploadedDate = DateTime.UtcNow,
                UploadedBy = createImageDto.UploadedBy
            };

            try
            {
                await _databaseService.InsertAsync<Image>(image);
                var retrievedImage = await _databaseService.SelectByIdAsync<Image, Guid>(imageId);
                if (retrievedImage == null)
                {
                    _logger.LogError($"Image not found after creation with ID: {imageId}");
                    throw new Exception("Failed to create image");
                }
                
                _logger.LogInformation($"Image created successfully with ID: {imageId}");
                return imageId.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while creating image: {ex.Message}");
                throw new Exception("Failed to create image");
            }
        }
    }
}