using SmartBoat.API.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmartBoat.API.Services
{
    public partial class ImageService
    {
        public async Task<List<ImageDto>> GetByEntityId(Guid entityId)
        {
            try
            {
                var images = await _databaseService.SelectAsync<Image>(new { EntityId = entityId });
                
                if (images == null)
                {
                    return new List<ImageDto>();
                }

                return images.Select(img => new ImageDto
                {
                    Id = img.Id,
                    EntityId = img.EntityId,
                    FileName = img.FileName,
                    ContentType = img.ContentType,
                    UploadedDate = img.UploadedDate,
                    UploadedBy = img.UploadedBy
                }).OrderBy(img => img.UploadedDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while fetching images for entity {entityId}: {ex.Message}");
                throw new Exception("Failed to retrieve images");
            }
        }
    }
}