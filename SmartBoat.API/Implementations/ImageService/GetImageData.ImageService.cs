using SmartBoat.API.Types;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Services
{
    public partial class ImageService
    {
        public async Task<Image> GetImageData(Guid imageId)
        {
            try
            {
                var image = await _databaseService.SelectByIdAsync<Image, Guid>(imageId);
                
                if (image == null)
                {
                    _logger.LogError($"Image not found with ID: {imageId}");
                    throw new Exception($"Image with ID {imageId} not found");
                }

                return image;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while fetching image {imageId}: {ex.Message}");
                throw new Exception("Failed to retrieve image");
            }
        }
    }
}