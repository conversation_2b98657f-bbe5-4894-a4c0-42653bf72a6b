using SmartBoat.API.Types;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Services
{
    public partial class ImageService
    {
        public async Task<bool> Delete(Guid imageId, Guid userId)
        {
            try
            {
                var image = await _databaseService.SelectByIdAsync<Image, Guid>(imageId);
                
                if (image == null)
                {
                    _logger.LogError($"Image not found with ID: {imageId}");
                    throw new Exception($"Image with ID {imageId} not found");
                }

                await _databaseService.DeleteAsync<Image>(new { Id = imageId });
                
                _logger.LogInformation($"Image {imageId} deleted by user {userId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while deleting image {imageId}: {ex.Message}");
                throw new Exception("Failed to delete image");
            }
        }
    }
}