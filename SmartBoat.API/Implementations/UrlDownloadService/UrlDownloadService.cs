using Microsoft.Extensions.Logging;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using System.Net.Http;

namespace SmartBoat.API.Services
{
    public class UrlDownloadService : IUrlDownloadService
    {
        private readonly ILogger<UrlDownloadService> _logger;
        private readonly HttpClient _httpClient;

        public UrlDownloadService(ILogger<UrlDownloadService> logger, HttpClient httpClient)
        {
            _logger = logger;
            _httpClient = httpClient;
            
            // Set a reasonable timeout for ZIP file downloads
            _httpClient.Timeout = TimeSpan.FromMinutes(5);
        }

        public async Task<Response<byte[]>> DownloadZipFileAsync(string url, string fileName)
        {
            try
            {
                Console.WriteLine($"⬇️  [URL DOWNLOAD] Downloading ZIP from URL: {url}");
                _logger.LogInformation("Starting download of ZIP file from URL: {Url}", url);

                var response = await _httpClient.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to download ZIP file from {Url}. Status: {StatusCode}, Error: {Error}", 
                        url, response.StatusCode, errorContent);
                    
                    Console.WriteLine($"❌ [URL DOWNLOAD] Failed: {response.StatusCode} - {errorContent}");
                    
                    return new Response<byte[]>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "DOWNLOAD-" + (int)response.StatusCode,
                            Description = "Failed to download ZIP file from URL",
                            Category = "Download Error"
                        }
                    };
                }

                var fileContent = await response.Content.ReadAsByteArrayAsync();
                
                Console.WriteLine($"✅ [URL DOWNLOAD] Downloaded {fileContent.Length:N0} bytes");
                _logger.LogInformation("Successfully downloaded ZIP file {FileName} ({Size} bytes) from {Url}", 
                    fileName, fileContent.Length, url);

                // Validate it's actually a ZIP file by checking the magic bytes
                if (fileContent.Length < 4 || fileContent[0] != 0x50 || fileContent[1] != 0x4B)
                {
                    Console.WriteLine($"⚠️  [URL DOWNLOAD] Downloaded file is not a valid ZIP file");
                    _logger.LogWarning("Downloaded file from {Url} is not a valid ZIP file", url);
                    
                    return new Response<byte[]>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "DOWNLOAD-400",
                            Description = "Downloaded file is not a valid ZIP file",
                            Category = "Validation Error"
                        }
                    };
                }

                return new Response<byte[]>
                {
                    Payload = fileContent,
                    Exception = null
                };
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                _logger.LogError(ex, "Timeout while downloading ZIP file from {Url}", url);
                Console.WriteLine($"⏰ [URL DOWNLOAD] Timeout downloading from {url}");
                
                return new Response<byte[]>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "DOWNLOAD-408",
                        Description = "Download timeout",
                        Category = "Timeout Error"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading ZIP file from {Url}", url);
                Console.WriteLine($"❌ [URL DOWNLOAD] Error: {ex.Message}");
                
                return new Response<byte[]>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "DOWNLOAD-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }
    }
}