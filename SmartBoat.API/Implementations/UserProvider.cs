using System.Data;
using Dapper;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;

namespace SmartBoat.API.Implementations
{
    /// <summary>
    /// Implementation of IUserProvider that bridges main application's User data to Authorization module
    /// </summary>
    public class UserProvider : IUserProvider
    {
        private readonly IDbConnectionFactory _dbConnectionFactory;

        public UserProvider(IDbConnectionFactory dbConnectionFactory)
        {
            _dbConnectionFactory = dbConnectionFactory ?? throw new ArgumentNullException(nameof(dbConnectionFactory));
        }

        public async Task<AuthUser?> GetUserAsync(Guid userId)
        {
            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            
            var user = await connection.QuerySingleOrDefaultAsync<AuthUser>(
                @"SELECT 
                    Id, 
                    Username, 
                    Email, 
                    CASE WHEN IsDeleted = 0 THEN 1 ELSE 0 END as IsActive,
                    Created
                FROM Users 
                WHERE Id = @UserId",
                new { UserId = userId });

            return user;
        }

        public async Task<List<Guid>> GetUserRoleIdsAsync(Guid userId)
        {
            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            
            // Get the user's role ID from the Users table
            var roleId = await connection.QuerySingleOrDefaultAsync<Guid?>(
                "SELECT RoleId FROM Users WHERE Id = @UserId AND IsDeleted = 0",
                new { UserId = userId });

            if (roleId.HasValue)
            {
                return new List<Guid> { roleId.Value };
            }

            return new List<Guid>();
        }

        public async Task<bool> IsUserActiveAsync(Guid userId)
        {
            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            
            var isActive = await connection.QuerySingleOrDefaultAsync<bool?>(
                "SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END FROM Users WHERE Id = @UserId AND IsDeleted = 0",
                new { UserId = userId });

            return isActive ?? false;
        }

        public async Task<string?> GetUserRoleAsync(Guid userId)
        {
            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            
            var role = await connection.QuerySingleOrDefaultAsync<string?>(
                "SELECT Role FROM Users WHERE Id = @UserId AND IsDeleted = 0",
                new { UserId = userId });

            return role;
        }
    }
}