using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public partial class FileManagementService
    {
        public async Task<Response<List<ExtractedFileDto>>> ExtractZipFileAsync(byte[] zipContent, string fileName, Guid operationId)
        {
            try
            {
                var extractedFiles = new List<ExtractedFileDto>();
                var operationDir = GetOperationDirectory(operationId);
                var zipFilePath = Path.Combine(operationDir, fileName);

                // Save ZIP file temporarily
                await File.WriteAllBytesAsync(zipFilePath, zipContent);

                using var zipArchive = ZipFile.OpenRead(zipFilePath);
                
                foreach (var entry in zipArchive.Entries)
                {
                    if (entry.Length == 0 || string.IsNullOrEmpty(entry.Name))
                        continue;

                    try
                    {
                        using var entryStream = entry.Open();
                        using var memoryStream = new MemoryStream();
                        
                        await entryStream.CopyToAsync(memoryStream);
                        var fileContent = memoryStream.ToArray();

                        var extractedFile = new ExtractedFileDto
                        {
                            FileName = entry.Name,
                            Content = fileContent
                        };

                        extractedFiles.Add(extractedFile);
                        
                        _logger.LogInformation("Extracted file {FileName} ({Size} bytes) from ZIP {ZipFileName}", 
                            entry.Name, fileContent.Length, fileName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to extract file {FileName} from ZIP {ZipFileName}", 
                            entry.Name, fileName);
                        continue;
                    }
                }

                // Clean up the ZIP file
                File.Delete(zipFilePath);

                _logger.LogInformation("Successfully extracted {FileCount} files from ZIP {ZipFileName} for operation {OperationId}", 
                    extractedFiles.Count, fileName, operationId);

                return new Response<List<ExtractedFileDto>>
                {
                    Payload = extractedFiles,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting ZIP file {FileName} for operation {OperationId}", fileName, operationId);
                return new Response<List<ExtractedFileDto>>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "FILE-500",
                        Description = "Error extracting ZIP file",
                        Category = "File Processing Error"
                    }
                };
            }
        }

        public async Task<Response<string>> SaveTemporaryFileAsync(byte[] content, string fileName, Guid operationId)
        {
            try
            {
                var operationDir = GetOperationDirectory(operationId);
                var filePath = Path.Combine(operationDir, fileName);

                await File.WriteAllBytesAsync(filePath, content);

                _logger.LogInformation("Saved temporary file {FileName} ({Size} bytes) for operation {OperationId}", 
                    fileName, content.Length, operationId);

                return new Response<string>
                {
                    Payload = filePath,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving temporary file {FileName} for operation {OperationId}", fileName, operationId);
                return new Response<string>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "FILE-500",
                        Description = "Error saving temporary file",
                        Category = "File Processing Error"
                    }
                };
            }
        }

        public async Task<Response<bool>> CleanupTemporaryFilesAsync(Guid operationId)
        {
            try
            {
                var operationDir = GetOperationDirectory(operationId);
                
                if (Directory.Exists(operationDir))
                {
                    Directory.Delete(operationDir, recursive: true);
                    _logger.LogInformation("Cleaned up temporary files for operation {OperationId}", operationId);
                }

                return new Response<bool>
                {
                    Payload = true,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up temporary files for operation {OperationId}", operationId);
                return new Response<bool>
                {
                    Payload = false,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "FILE-500",
                        Description = "Error cleaning up temporary files",
                        Category = "File Processing Error"
                    }
                };
            }
        }
    }
}