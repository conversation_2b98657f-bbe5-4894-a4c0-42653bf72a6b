using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using Microsoft.Extensions.Logging;
using System.IO.Compression;

namespace SmartBoat.API.Services
{
    public partial class FileManagementService : IFileManagementService
    {
        private readonly ILogger<FileManagementService> _logger;
        private readonly string _tempDirectory;

        public FileManagementService(ILogger<FileManagementService> logger)
        {
            _logger = logger;
            _tempDirectory = Path.Combine(Path.GetTempPath(), "SmartBoat", "EmailProcessing");
            
            // Ensure temp directory exists
            if (!Directory.Exists(_tempDirectory))
            {
                Directory.CreateDirectory(_tempDirectory);
            }
        }

        private string GetOperationDirectory(Guid operationId)
        {
            var operationDir = Path.Combine(_tempDirectory, operationId.ToString());
            if (!Directory.Exists(operationDir))
            {
                Directory.CreateDirectory(operationDir);
            }
            return operationDir;
        }
    }
}