using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class SupportRequestService
    {
        public async Task<string> Create(CreateSupportRequestDto request, Guid userId)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Subject) || string.IsNullOrWhiteSpace(request.Description) || request.UserId == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Required parameters are missing. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var supportRequest = new SupportRequest
            {
                Id = Guid.NewGuid(),
                UserId = request.UserId,
                Subject = request.Subject,
                Description = request.Description,
                Status = "Open",
                CreatedAt = DateTime.UtcNow,
                Created = DateTime.UtcNow,
                ResolvedAt = null,
                Resolution = null
            };

            var isAuthorizedDto = new IsAuthorizedDto<SupportRequest>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = supportRequest
            };

            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                var user = await _databaseService.SelectByIdAsync<User, Guid>(request.UserId.Value);
                if (user == null)
                {
                    _logger.LogInformation($"DP-404: Not Found. User not found with Id: {request.UserId}. UserId: {userId}");
                    throw new BusinessException("DP-404", "Not Found");
                }

                await _databaseService.InsertAsync(supportRequest);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while creating SupportRequest with Id: {supportRequest.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            return supportRequest.Id.ToString();
        }
    }
}