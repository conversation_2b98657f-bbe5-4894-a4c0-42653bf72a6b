using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class SupportRequestService : ISupportRequestService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly ILogger<SupportRequestService> _logger;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;

        public SupportRequestService(
            IAuthorizationService authorizationService,
            ILogger<SupportRequestService> logger,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService)
        {
            _authorizationService = authorizationService;
            _logger = logger;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
        }
    }
}