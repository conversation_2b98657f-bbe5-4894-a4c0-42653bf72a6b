using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Services
{
    public partial class SupportRequestService
    {
        public async Task<string> Update(UpdateSupportRequestDto request, Guid userId)
        {
            if (request == null || request.Id == null ||
                (string.IsNullOrWhiteSpace(request.Subject) &&
                 string.IsNullOrWhiteSpace(request.Description) &&
                 string.IsNullOrWhiteSpace(request.Status) &&
                 string.IsNullOrWhiteSpace(request.Resolution)))
            {
                _logger.LogInformation($"DP-422: Client Error. Id or at least one updatable field is missing. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            SupportRequest entity;
            try
            {
                entity = await _databaseService.SelectByIdAsync<SupportRequest, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching SupportRequest with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (entity == null)
            {
                _logger.LogInformation($"DP-404: Not Found. SupportRequest not found with Id: {request.Id}. UserId: {userId}");
                throw new BusinessException("DP-404", "Not Found");
            }

            var authDto = new IsAuthorizedDto<SupportRequest>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = entity
            };

            await _authorizationService.IsAuthorized(authDto);

            bool isStatusUpdated = false;
            if (!string.IsNullOrWhiteSpace(request.Subject))
                entity.Subject = request.Subject;

            if (!string.IsNullOrWhiteSpace(request.Description))
                entity.Description = request.Description;

            if (!string.IsNullOrWhiteSpace(request.Status))
            {
                entity.Status = request.Status;
                isStatusUpdated = true;
            }

            if (!string.IsNullOrWhiteSpace(request.Resolution))
                entity.Resolution = request.Resolution;

            if (isStatusUpdated && entity.Status == "Resolved")
            {
                entity.ResolvedAt = DateTime.UtcNow;
            }

            entity.Changed = DateTime.UtcNow;

            try
            {
                await _databaseService.UpdateAsync<SupportRequest>(entity, new { Id = entity.Id });
                return entity.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while updating SupportRequest with Id: {entity.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}