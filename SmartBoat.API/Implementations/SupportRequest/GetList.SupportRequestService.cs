using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class SupportRequestService
    {
        public async Task<ReturnListSupportRequestDto> GetList(ListSupportRequestRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid pagination parameters. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            var query = new PagedQuery
            {
                PageSize = (int)request.PageLimit,
                PageNumber = request.PageOffset / request.PageLimit + 1
            };

            Nbg.NetCore.DatabaseService.PagedResult<SupportRequest> pagedResult;
            try
            {
                pagedResult = await _databaseService.SelectPagedAsync<SupportRequest>(query);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching SupportRequests. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            var userIds = pagedResult.Records.Select(x => x.UserId).Distinct().ToList();
            var users = await _databaseService.SelectAsync<User>(new { Id = userIds });

            var authorizedSupportRequests = new List<SupportRequestDto>();
            foreach (var supportRequest in pagedResult.Records)
            {
                // Check authorization for each individual support request
                try
                {
                    var authorizationDto = new IsAuthorizedDto<SupportRequest>
                    {
                        UserId = userId,
                        PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                        Entity = supportRequest
                    };
                    await _authorizationService.IsAuthorized(authorizationDto);

                    // If authorized, add to result
                    var supportRequestDto = new SupportRequestDto
                    {
                        Id = supportRequest.Id,
                        User = users.FirstOrDefault(u => u.Id == supportRequest.UserId) != null
                            ? new UserDto { Id = supportRequest.UserId, Username = users.FirstOrDefault(u => u.Id == supportRequest.UserId).Username }
                            : null,
                        Subject = supportRequest.Subject,
                        Description = supportRequest.Description,
                        Status = supportRequest.Status,
                        CreatedAt = supportRequest.CreatedAt,
                        ResolvedAt = supportRequest.ResolvedAt,
                        Resolution = supportRequest.Resolution
                    };
                    authorizedSupportRequests.Add(supportRequestDto);
                }
                catch (Exception)
                {
                    // If not authorized, skip this support request
                    continue;
                }
            }

            var result = new ReturnListSupportRequestDto
            {
                Data = authorizedSupportRequests,
                Metadata = new MetadataDto
                {
                    Total = pagedResult.TotalRecords,
                    PageLimit = request.PageLimit ?? 0,
                    PageOffset = request.PageOffset ?? 0
                }
            };

            return result;
        }
    }
}