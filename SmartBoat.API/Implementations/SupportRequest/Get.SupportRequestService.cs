using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class SupportRequestService
    {
        public async Task<SupportRequestDto> Get(SupportRequestRequestDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            SupportRequest supportRequest;
            try
            {
                supportRequest = await _databaseService.SelectByIdAsync<SupportRequest, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching SupportRequest with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (supportRequest == null)
            {
                _logger.LogInformation($"DP-404: Not Found. SupportRequest not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Not Found");
            }

            var authorizationDto = new IsAuthorizedDto<SupportRequest>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = supportRequest
            };
            await _authorizationService.IsAuthorized(authorizationDto);

            UserDto userDto = null;
            if (supportRequest.UserId != null)
            {
                var user = await _databaseService.SelectByIdAsync<User, Guid>(supportRequest.UserId.Value);
                if (user != null)
                {
                    userDto = new UserDto
                    {
                        Id = user.Id,
                        Username = user.Username,
                        Email = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName
                    };
                }
            }
            return new SupportRequestDto
            {
                Id = supportRequest.Id,
                User = userDto,
                Subject = supportRequest.Subject,
                Description = supportRequest.Description,
                Status = supportRequest.Status,
                CreatedAt = supportRequest.CreatedAt,
                ResolvedAt = supportRequest.ResolvedAt,
                Resolution = supportRequest.Resolution
            };
        }
    }
}