using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public partial class TokenStorageService
    {
        public async Task<Response<bool>> RefreshTokenIfNeededAsync(Guid userId)
        {
            try
            {
                var authToken = (await _databaseService.SelectAsync<AuthToken>(null)).FirstOrDefault();
                
                if (authToken == null || string.IsNullOrEmpty(authToken.RefreshToken))
                {
                    _logger.LogInformation($"TOKEN-404: No refresh token found. UserId: {userId}");
                    return new Response<bool>
                    {
                        Payload = false,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "TOKEN-404",
                            Description = "No refresh token found. Re-authentication required.",
                            Category = "Not Found"
                        }
                    };
                }

                // Check if refresh token is within 90-day limit
                var refreshTokenAge = authToken.RefreshTokenIssuedAt.HasValue 
                    ? (DateTime.UtcNow - authToken.RefreshTokenIssuedAt.Value).Days 
                    : 90;

                if (refreshTokenAge >= 90)
                {
                    _logger.LogInformation($"TOKEN-401: Refresh token expired. Age: {refreshTokenAge} days. UserId: {userId}");
                    await _databaseService.DeleteAsync<AuthToken>(new { Id = authToken.Id });
                    
                    return new Response<bool>
                    {
                        Payload = false,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "TOKEN-401",
                            Description = "Refresh token expired. Re-authentication required.",
                            Category = "Unauthorized"
                        }
                    };
                }

                // Call Microsoft Graph token refresh endpoint
                using var httpClient = new HttpClient();
                var tenantId = _configuration["MicrosoftGraph:TenantId"];
                var clientId = _configuration["MicrosoftGraph:ClientId"];
                
                var refreshData = new Dictionary<string, string>
                {
                    { "client_id", clientId },
                    { "scope", "User.Read Mail.Read offline_access" },
                    { "refresh_token", authToken.RefreshToken },
                    { "grant_type", "refresh_token" }
                };

                var content = new FormUrlEncodedContent(refreshData);
                var response = await httpClient.PostAsync(
                    $"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token", 
                    content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"TOKEN-401: Failed to refresh token. Status: {response.StatusCode}, Error: {errorContent}. UserId: {userId}");
                    
                    // Delete invalid token
                    await _databaseService.DeleteAsync<AuthToken>(new { Id = authToken.Id });
                    
                    return new Response<bool>
                    {
                        Payload = false,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "TOKEN-401",
                            Description = "Failed to refresh token. Re-authentication required.",
                            Category = "Unauthorized"
                        }
                    };
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);

                // Update the token with new values
                authToken.AccessToken = tokenResponse.GetProperty("access_token").GetString() ?? "";
                authToken.ExpiresAt = DateTime.UtcNow.AddSeconds(tokenResponse.GetProperty("expires_in").GetInt32());
                authToken.UpdatedAt = DateTime.UtcNow;

                // Update refresh token if provided (sometimes a new one is issued)
                if (tokenResponse.TryGetProperty("refresh_token", out var newRefreshToken))
                {
                    authToken.RefreshToken = newRefreshToken.GetString();
                    authToken.RefreshTokenIssuedAt = DateTime.UtcNow;
                }

                await _databaseService.UpdateAsync<AuthToken>(authToken, new { Id = authToken.Id });

                _logger.LogInformation($"TOKEN-200: Token refreshed successfully. UserId: {userId}");
                
                return new Response<bool>
                {
                    Payload = true,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"TOKEN-500: Technical Error. Error while refreshing token. UserId: {userId}");
                return new Response<bool>
                {
                    Payload = false,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "TOKEN-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }

        public async Task<Response<bool>> DeleteTokenAsync(Guid userId)
        {
            try
            {
                var authToken = (await _databaseService.SelectAsync<AuthToken>(null)).FirstOrDefault();
                
                if (authToken != null)
                {
                    await _databaseService.DeleteAsync<AuthToken>(new { Id = authToken.Id });
                }

                _logger.LogInformation($"TOKEN-200: Token deleted successfully. UserId: {userId}");
                
                return new Response<bool>
                {
                    Payload = true,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"TOKEN-500: Technical Error. Error while deleting token. UserId: {userId}");
                return new Response<bool>
                {
                    Payload = false,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "TOKEN-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }
    }
}