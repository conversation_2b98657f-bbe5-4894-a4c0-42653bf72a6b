using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class TokenStorageService
    {
        public async Task<Response<string>> SaveTokenAsync(SaveTokenDto tokenDto, Guid userId)
        {
            try
            {
                if (tokenDto == null || string.IsNullOrWhiteSpace(tokenDto.AccessToken))
                {
                    _logger.LogInformation($"TOKEN-422: Client Error. Access token is null or empty. UserId: {userId}");
                    return new Response<string>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "TOKEN-422",
                            Description = "Access token is required",
                            Category = "Client Error"
                        }
                    };
                }

                // Delete existing token (only one should exist)
                var existingToken = (await _databaseService.SelectAsync<AuthToken>(null)).FirstOrDefault();
                if (existingToken != null)
                {
                    await _databaseService.DeleteAsync<AuthToken>(new { Id = existingToken.Id });
                }

                var authToken = new AuthToken
                {
                    Id = Guid.NewGuid(),
                    AccessToken = tokenDto.AccessToken,
                    RefreshToken = tokenDto.RefreshToken,
                    ExpiresAt = tokenDto.ExpiresAt,
                    RefreshTokenIssuedAt = DateTime.UtcNow,
                    TokenType = "Bearer",
                    Scope = tokenDto.Scope ?? "User.Read Mail.Read offline_access",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _databaseService.InsertAsync<AuthToken>(authToken);

                _logger.LogInformation($"TOKEN-200: Token saved successfully. UserId: {userId}");
                
                return new Response<string>
                {
                    Payload = authToken.Id.ToString(),
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"TOKEN-500: Technical Error. Error while saving token. UserId: {userId}");
                return new Response<string>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "TOKEN-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }
    }
}