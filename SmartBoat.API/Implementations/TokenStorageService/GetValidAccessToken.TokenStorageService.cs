using System;
using System.Linq;
using System.Threading.Tasks;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public partial class TokenStorageService
    {
        public async Task<Response<string>> GetValidAccessTokenAsync(Guid userId)
        {
            try
            {
                var authToken = (await _databaseService.SelectAsync<AuthToken>(null)).FirstOrDefault();
                
                if (authToken == null)
                {
                    _logger.LogInformation($"TOKEN-404: No token found. UserId: {userId}");
                    return new Response<string>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "TOKEN-404",
                            Description = "No token found. Authentication required.",
                            Category = "Not Found"
                        }
                    };
                }

                var now = DateTime.UtcNow;
                
                // Check if access token is still valid (with 5-minute buffer)
                if (authToken.ExpiresAt > now.AddMinutes(5))
                {
                    return new Response<string>
                    {
                        Payload = authToken.AccessToken,
                        Exception = null
                    };
                }

                // Try to refresh the token
                var refreshResult = await RefreshTokenIfNeededAsync(userId);
                if (refreshResult.Exception != null)
                {
                    return new Response<string>
                    {
                        Payload = null,
                        Exception = refreshResult.Exception
                    };
                }

                // Get the refreshed token
                var refreshedToken = (await _databaseService.SelectAsync<AuthToken>(null)).FirstOrDefault();
                if (refreshedToken == null)
                {
                    return new Response<string>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "TOKEN-500",
                            Description = "Failed to retrieve refreshed token",
                            Category = "Technical Error"
                        }
                    };
                }

                return new Response<string>
                {
                    Payload = refreshedToken.AccessToken,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"TOKEN-500: Technical Error. Error while getting valid access token. UserId: {userId}");
                return new Response<string>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "TOKEN-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }
    }
}