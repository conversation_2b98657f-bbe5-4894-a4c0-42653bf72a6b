using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public partial class TokenStorageService
    {
        public async Task<Response<TokenStatusDto>> GetTokenStatusAsync(Guid userId)
        {
            try
            {
                var authToken = (await _databaseService.SelectAsync<AuthToken>(null)).FirstOrDefault();
                
                if (authToken == null)
                {
                    return new Response<TokenStatusDto>
                    {
                        Payload = new TokenStatusDto
                        {
                            HasToken = false,
                            IsExpired = true,
                            HasRefreshToken = false,
                            RequiresReAuthentication = true
                        },
                        Exception = null
                    };
                }

                var now = DateTime.UtcNow;
                var isExpired = authToken.ExpiresAt <= now;
                var refreshTokenAge = authToken.RefreshTokenIssuedAt.HasValue 
                    ? (now - authToken.RefreshTokenIssuedAt.Value).Days 
                    : 0;
                
                // Refresh tokens expire after 90 days
                var refreshTokenExpired = refreshTokenAge >= 90;
                var daysUntilRefreshExpiry = Math.Max(0, 90 - refreshTokenAge);

                return new Response<TokenStatusDto>
                {
                    Payload = new TokenStatusDto
                    {
                        HasToken = true,
                        IsExpired = isExpired,
                        HasRefreshToken = !string.IsNullOrEmpty(authToken.RefreshToken),
                        ExpiresAt = authToken.ExpiresAt,
                        RefreshTokenIssuedAt = authToken.RefreshTokenIssuedAt,
                        DaysUntilRefreshExpiry = daysUntilRefreshExpiry,
                        RequiresReAuthentication = refreshTokenExpired || string.IsNullOrEmpty(authToken.RefreshToken)
                    },
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"TOKEN-500: Technical Error. Error while getting token status. UserId: {userId}");
                return new Response<TokenStatusDto>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "TOKEN-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }
    }
}