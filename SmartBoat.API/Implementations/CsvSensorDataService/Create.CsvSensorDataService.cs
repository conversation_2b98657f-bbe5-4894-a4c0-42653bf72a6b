using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public partial class CsvSensorDataService
    {
        public async Task<Response<string>> CreateCsvSensorDataAsync(CsvSensorData sensorData, Guid userId)
        {
            try
            {
                if (sensorData == null)
                {
                    return new Response<string>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "CSV-422",
                            Description = "Sensor data is required",
                            Category = "Client Error"
                        }
                    };
                }

                sensorData.Id = Guid.NewGuid();
                sensorData.CreatedAt = DateTime.UtcNow;
                sensorData.ProcessedDate = DateTime.UtcNow;

                await _databaseService.InsertAsync<CsvSensorData>(sensorData);

                return new Response<string>
                {
                    Payload = sensorData.Id.ToString(),
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"CSV-500: Technical Error. Error while creating sensor data. UserId: {userId}");
                return new Response<string>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "CSV-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }

        public async Task<Response<List<CsvSensorData>>> CreateBulkCsvSensorDataAsync(List<CsvSensorData> sensorDataList, Guid userId)
        {
            try
            {
                if (sensorDataList == null || !sensorDataList.Any())
                {
                    return new Response<List<CsvSensorData>>
                    {
                        Payload = new List<CsvSensorData>(),
                        Exception = null
                    };
                }

                Console.WriteLine($"         💾 [DATABASE] Starting bulk insert of {sensorDataList.Count} records");
                _logger.LogInformation("Starting bulk insert of {RecordCount} sensor data records for user {UserId}", sensorDataList.Count, userId);

                // Prepare records for insertion
                foreach (var sensorData in sensorDataList)
                {
                    sensorData.Id = Guid.NewGuid();
                    sensorData.CreatedAt = DateTime.UtcNow;
                    if (sensorData.ProcessedDate == default(DateTime))
                    {
                        sensorData.ProcessedDate = DateTime.UtcNow;
                    }
                }

                // Insert all records in bulk
                var insertedRecords = new List<CsvSensorData>();
                var batchSize = 1000; // Process in batches to avoid memory issues
                
                for (int i = 0; i < sensorDataList.Count; i += batchSize)
                {
                    var batch = sensorDataList.Skip(i).Take(batchSize).ToList();
                    Console.WriteLine($"         💾 [DATABASE] Inserting batch {(i / batchSize) + 1}: {batch.Count} records");
                    
                    foreach (var record in batch)
                    {
                        await _databaseService.InsertAsync<CsvSensorData>(record);
                        insertedRecords.Add(record);
                    }
                }

                Console.WriteLine($"         ✅ [DATABASE] Successfully inserted {insertedRecords.Count} sensor data records");
                _logger.LogInformation("Successfully bulk inserted {RecordCount} sensor data records for user {UserId}", insertedRecords.Count, userId);

                return new Response<List<CsvSensorData>>
                {
                    Payload = insertedRecords,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"         ❌ [DATABASE] Bulk insert failed: {ex.Message}");
                _logger.LogError(ex, "Error during bulk insert of sensor data for user {UserId}", userId);
                
                return new Response<List<CsvSensorData>>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "CSV-500",
                        Description = "Technical Error during bulk insert",
                        Category = "Technical Error"
                    }
                };
            }
        }

        public async Task<Response<List<CsvSensorData>>> GetCsvSensorDataAsync(string vesselName, DateTime? startDate, DateTime? endDate, Guid userId)
        {
            // Placeholder implementation
            return new Response<List<CsvSensorData>>
            {
                Payload = new List<CsvSensorData>(),
                Exception = null
            };
        }

        public async Task<Response<List<string>>> GetVesselNamesAsync(Guid userId)
        {
            // Placeholder implementation
            return new Response<List<string>>
            {
                Payload = new List<string>(),
                Exception = null
            };
        }

        public async Task<Response<ProcessingSummaryDto>> GetSummaryAsync(Guid userId)
        {
            // Placeholder implementation
            return new Response<ProcessingSummaryDto>
            {
                Payload = new ProcessingSummaryDto(),
                Exception = null
            };
        }
    }
}