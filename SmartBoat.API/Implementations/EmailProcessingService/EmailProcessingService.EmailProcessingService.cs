using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class EmailProcessingService : IEmailProcessingService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;
        private readonly ITokenStorageService _tokenStorageService;
        private readonly ICsvSensorDataService _csvSensorDataService;
        private readonly IMicrosoftGraphService _microsoftGraphService;
        private readonly ICsvProcessingService _csvProcessingService;
        private readonly IFileManagementService _fileManagementService;
        private readonly IUrlDownloadService _urlDownloadService;
        private readonly ILogger<EmailProcessingService> _logger;

        public EmailProcessingService(
            IAuthorizationService authorizationService,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService,
            ITokenStorageService tokenStorageService,
            ICsvSensorDataService csvSensorDataService,
            IMicrosoftGraphService microsoftGraphService,
            ICsvProcessingService csvProcessingService,
            IFileManagementService fileManagementService,
            IUrlDownloadService urlDownloadService,
            ILogger<EmailProcessingService> logger)
        {
            _authorizationService = authorizationService;
            _databaseService = databaseService;
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
            _tokenStorageService = tokenStorageService;
            _csvSensorDataService = csvSensorDataService;
            _microsoftGraphService = microsoftGraphService;
            _csvProcessingService = csvProcessingService;
            _fileManagementService = fileManagementService;
            _urlDownloadService = urlDownloadService;
            _logger = logger;
        }
    }
}