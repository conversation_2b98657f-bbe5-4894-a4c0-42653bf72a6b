using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public partial class EmailProcessingService
    {
        public async Task<Response<ProcessingStatusDto>> ProcessEmailsAsync(ProcessEmailsDto request, Guid userId)
        {
            var operationId = Guid.NewGuid();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("🚀 [EMAIL PROCESSING] Starting email processing for user {UserId} with operation {OperationId}", userId, operationId);
                Console.WriteLine($"🚀 [EMAIL PROCESSING] Starting email processing operation {operationId}");

                // Fetch emails from Microsoft Graph
                Console.WriteLine($"📧 [EMAIL FETCH] Fetching emails from Microsoft Graph (last {request.DaysBack ?? 7} days)...");
                var emailsResponse = await _microsoftGraphService.GetEmailsAsync(userId, request.DaysBack ?? 7);
                if (emailsResponse.Exception != null)
                {
                    Console.WriteLine($"❌ [EMAIL FETCH] Failed to fetch emails: {emailsResponse.Exception.Description}");
                    return new Response<ProcessingStatusDto>
                    {
                        Payload = new ProcessingStatusDto
                        {
                            IsProcessing = false,
                            CurrentStatus = "Failed to fetch emails: " + emailsResponse.Exception.Description
                        },
                        Exception = emailsResponse.Exception
                    };
                }

                var emails = emailsResponse.Payload ?? new List<EmailDto>();
                var totalRecordsProcessed = 0;
                var vesselsFound = new HashSet<string>();
                var processedEmails = 0;

                _logger.LogInformation("Found {EmailCount} emails with ZIP attachments to process", emails.Count);
                Console.WriteLine($"📊 [EMAIL FETCH] Found {emails.Count} emails with ZIP attachments to process");

                foreach (var email in emails)
                {
                    try
                    {
                        Console.WriteLine($"\n📩 [EMAIL {processedEmails + 1}/{emails.Count}] Processing: {email.Subject}");
                        Console.WriteLine($"   📅 Received: {email.ReceivedDateTime:yyyy-MM-dd HH:mm:ss}");
                        Console.WriteLine($"   🆔 Email ID: {email.Id}");
                        Console.WriteLine($"   🔗 ZIP URLs: {email.ZipDownloadUrls.Count} download links");

                        // Check if email was already processed
                        if (await IsEmailAlreadyProcessedAsync(email.Id, request.ForceReprocess == true))
                        {
                            Console.WriteLine($"   ⏭️  Email already processed - skipping");

                            // Log as skipped
                            await LogEmailProcessingAsync(email.Id, email.Subject, email.ReceivedDateTime, "Skipped",
                                0, 0, email.ZipDownloadUrls, "Already processed successfully", 0);
                            continue;
                        }

                        var emailRecordCount = await ProcessSingleEmailAsync(email, userId, operationId, vesselsFound);
                        totalRecordsProcessed += emailRecordCount;
                        processedEmails++;

                        Console.WriteLine($"   ✅ Email processed successfully - {emailRecordCount} records added");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ Error processing email: {ex.Message}");
                        _logger.LogError(ex, "Error processing email {EmailId} ({Subject})", email.Id, email.Subject);

                        // Log the error but continue with other emails
                        await LogEmailProcessingAsync(email.Id, email.Subject, email.ReceivedDateTime, "Failed",
                            0, 0, email.ZipDownloadUrls, ex.Message, null);
                        continue;
                    }
                }

                // Cleanup temporary files
                Console.WriteLine($"\n🧹 [CLEANUP] Cleaning up temporary files...");
                await _fileManagementService.CleanupTemporaryFilesAsync(operationId);

                var duration = (int)(DateTime.UtcNow - startTime).TotalMilliseconds;
                Console.WriteLine($"\n🎉 [PROCESSING COMPLETE]");
                Console.WriteLine($"   📊 Processed: {processedEmails}/{emails.Count} emails");
                Console.WriteLine($"   📈 Total records: {totalRecordsProcessed}");
                Console.WriteLine($"   🚢 Vessels found: {vesselsFound.Count}");
                Console.WriteLine($"   ⏱️  Duration: {duration}ms");

                _logger.LogInformation("Completed email processing for user {UserId}. Processed {ProcessedEmails}/{TotalEmails} emails, {TotalRecords} records, {VesselCount} vessels in {Duration}ms",
                    userId, processedEmails, emails.Count, totalRecordsProcessed, vesselsFound.Count, duration);

                // Log overall processing summary with unique identifier
                var batchId = $"BATCH-{operationId}";
                await LogEmailProcessingAsync(batchId, $"Batch processing - {emails.Count} emails", DateTime.UtcNow, "Success",
                    totalRecordsProcessed, vesselsFound.Count, new List<string>(), null, duration);

                return new Response<ProcessingStatusDto>
                {
                    Payload = new ProcessingStatusDto
                    {
                        IsProcessing = false,
                        CurrentStatus = $"Completed successfully. Processed {processedEmails} emails, {totalRecordsProcessed} records, {vesselsFound.Count} vessels.",
                        LastProcessed = DateTime.UtcNow,
                        TotalEmails = emails.Count,
                        ProcessedEmails = processedEmails
                    },
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n💥 [PROCESSING ERROR] Fatal error during email processing:");
                Console.WriteLine($"   ❌ Error: {ex.Message}");
                Console.WriteLine($"   🧹 Cleaning up temporary files...");

                _logger.LogError(ex, "Error during email processing for user {UserId}", userId);

                // Cleanup temporary files in case of error
                await _fileManagementService.CleanupTemporaryFilesAsync(operationId);

                var duration = (int)(DateTime.UtcNow - startTime).TotalMilliseconds;
                await LogEmailProcessingAsync("Email processing batch", DateTime.UtcNow, "Failed",
                    0, 0, ex.Message, duration);

                return new Response<ProcessingStatusDto>
                {
                    Payload = new ProcessingStatusDto
                    {
                        IsProcessing = false,
                        CurrentStatus = "Processing failed: " + ex.Message
                    },
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "EMAIL-500",
                        Description = "Technical Error",
                        Category = "Technical Error"
                    }
                };
            }
        }

        private async Task<int> ProcessSingleEmailAsync(EmailDto email, Guid userId, Guid operationId,
            HashSet<string> vesselsFound)
        {
            var emailStartTime = DateTime.UtcNow;
            var processedAttachmentIds = new List<string>();

            try
            {
                _logger.LogInformation("Processing email {EmailId}: {Subject}", email.Id, email.Subject);

                var emailRecordCount = 0;
                var emailVesselCount = 0;

                foreach (var zipUrl in email.ZipDownloadUrls)
                {
                    try
                    {
                        var fileName = Path.GetFileName(new Uri(zipUrl).AbsolutePath);
                        Console.WriteLine($"      🔗 Processing ZIP URL: {fileName}");
                        Console.WriteLine($"         URL: {zipUrl}");

                        // Download ZIP from URL
                        var downloadResponse = await _urlDownloadService.DownloadZipFileAsync(zipUrl, fileName);

                        if (downloadResponse.Exception != null)
                        {
                            Console.WriteLine($"      ❌ Failed to download: {downloadResponse.Exception.Description}");
                            _logger.LogError("Failed to download ZIP from URL {Url}: {Error}",
                                zipUrl, downloadResponse.Exception.Description);
                            continue;
                        }

                        Console.WriteLine($"      ✅ Downloaded ZIP successfully");

                        // Extract ZIP files
                        var extractResponse = await _fileManagementService.ExtractZipFileAsync(
                            downloadResponse.Payload!, fileName, operationId);

                        if (extractResponse.Exception != null)
                        {
                            Console.WriteLine($"      ❌ Failed to extract ZIP: {extractResponse.Exception.Description}");
                            _logger.LogError("Failed to extract ZIP {FileName}: {Error}",
                                fileName, extractResponse.Exception.Description);
                            continue;
                        }

                        var csvFiles = extractResponse.Payload!.Where(f => f.IsCsvFile).ToList();
                        Console.WriteLine($"      📄 Extracted {csvFiles.Count} CSV files from ZIP");

                        // Process CSV files using the new SensorDataPoints flow
                        foreach (var extractedFile in csvFiles)
                        {
                            Console.WriteLine($"         📊 Processing CSV: {extractedFile.FileName}");

                            var csvResponse = await _csvProcessingService.ProcessCsvToSensorDataPointsAsync(
                                extractedFile.Content, extractedFile.FileName, email.FromEmail ?? "", userId);

                            if (csvResponse.Exception != null)
                            {
                                Console.WriteLine($"         ❌ Failed to process CSV: {csvResponse.Exception.Description}");
                                _logger.LogError("Failed to process CSV {FileName}: {Error}",
                                    extractedFile.FileName, csvResponse.Exception.Description);
                                continue;
                            }

                            var createdDataPointIds = csvResponse.Payload!;

                            if (createdDataPointIds.Any())
                            {
                                emailRecordCount += createdDataPointIds.Count;
                                Console.WriteLine($"         ✅ Created {createdDataPointIds.Count} sensor data points");
                                _logger.LogInformation("Created {DataPointCount} sensor data points from {FileName}",
                                    createdDataPointIds.Count, extractedFile.FileName);
                            }
                            else
                            {
                                Console.WriteLine($"         ⚠️  No sensor data points created from CSV");
                            }
                        }

                        // Track successfully processed URL
                        processedAttachmentIds.Add(zipUrl);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"      ❌ Error processing ZIP URL {zipUrl}: {ex.Message}");
                        _logger.LogError(ex, "Error processing ZIP URL {ZipUrl} from email {EmailId}",
                            zipUrl, email.Id);
                        continue;
                    }
                }

                emailVesselCount = vesselsFound.Count;
                var emailDuration = (int)(DateTime.UtcNow - emailStartTime).TotalMilliseconds;

                // Log individual email processing with attachment tracking
                await LogEmailProcessingAsync(email.Id, email.Subject, email.ReceivedDateTime, "Success",
                    emailRecordCount, emailVesselCount, processedAttachmentIds, null, emailDuration);

                _logger.LogInformation("Completed processing email {EmailId}. Records: {RecordCount}, Duration: {Duration}ms",
                    email.Id, emailRecordCount, emailDuration);

                return emailRecordCount;
            }
            catch (Exception ex)
            {
                var emailDuration = (int)(DateTime.UtcNow - emailStartTime).TotalMilliseconds;
                await LogEmailProcessingAsync(email.Id, email.Subject, email.ReceivedDateTime, "Failed",
                    0, 0, processedAttachmentIds, ex.Message, emailDuration);
                throw;
            }
        }

        private async Task<bool> IsEmailAlreadyProcessedAsync(string emailId, bool forceReprocess = false)
        {
            try
            {
                if (forceReprocess)
                {
                    Console.WriteLine($"🔄 [DUPLICATE CHECK] Force reprocess enabled - skipping duplicate check for email {emailId}");
                    return false;
                }

                var existingEntry = await _databaseService.SelectAsync<EmailProcessingLog>(new { EmailId = emailId, ProcessingStatus = "Success" });
                var isProcessed = existingEntry?.Any() == true;

                if (isProcessed)
                {
                    Console.WriteLine($"⏭️  [DUPLICATE CHECK] Email {emailId} already processed successfully - skipping");
                }
                else
                {
                    Console.WriteLine($"✅ [DUPLICATE CHECK] Email {emailId} not yet processed - will process");
                }

                return isProcessed;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error checking if email {EmailId} was already processed", emailId);
                return false; // If error checking, process the email to be safe
            }
        }

        private async Task LogEmailProcessingAsync(string emailId, string emailSubject, DateTime emailDate, string status,
            int recordsProcessed, int vesselsFound, List<string> attachmentIds, string? errorMessage, int? duration)
        {
            try
            {
                var logEntry = new EmailProcessingLog
                {
                    Id = Guid.NewGuid(),
                    EmailId = emailId,
                    EmailSubject = emailSubject,
                    EmailDate = emailDate,
                    ProcessingStatus = status,
                    RecordsProcessed = recordsProcessed,
                    VesselsFound = vesselsFound,
                    AttachmentIds = attachmentIds.Any() ? System.Text.Json.JsonSerializer.Serialize(attachmentIds) : null,
                    AttachmentCount = attachmentIds.Count,
                    ErrorMessage = errorMessage,
                    ProcessingDuration = duration,
                    ProcessedAt = DateTime.UtcNow
                };

                await _databaseService.InsertAsync(logEntry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log email processing entry for email {EmailId}", emailId);
            }
        }

        // Overload for backward compatibility
        private async Task LogEmailProcessingAsync(string emailSubject, DateTime emailDate, string status,
            int recordsProcessed, int vesselsFound, string? errorMessage, int? duration)
        {
            await LogEmailProcessingAsync("", emailSubject, emailDate, status, recordsProcessed, vesselsFound,
                new List<string>(), errorMessage, duration);
        }

        public async Task<Response<ProcessingStatusDto>> GetProcessingStatusAsync(Guid userId)
        {
            // Placeholder implementation
            return new Response<ProcessingStatusDto>
            {
                Payload = new ProcessingStatusDto
                {
                    IsProcessing = false,
                    CurrentStatus = "Ready"
                },
                Exception = null
            };
        }

        public async Task<Response<ProcessingSummaryDto>> GetProcessingSummaryAsync(Guid userId)
        {
            try
            {
                var summary = new ProcessingSummaryDto
                {
                    TotalRecords = 0,
                    RecentRecords = 0,
                    VesselSummaries = new List<VesselSummaryDto>(),
                    RecentProcessing = new List<RecentProcessingDto>()
                };

                try
                {
                    // Get only recent processing activity (last 10 entries) from EmailProcessingLog
                    var recentProcessing = await _databaseService.SelectAsync<EmailProcessingLog>(null);

                    _logger.LogInformation("Raw database query returned {Count} EmailProcessingLog entries",
                        recentProcessing?.Count() ?? 0);

                    if (recentProcessing != null)
                    {
                        var orderedProcessing = recentProcessing
                            .OrderByDescending(p => p.ProcessedAt)
                            .Take(10)
                            .ToList();

                        foreach (var log in orderedProcessing)
                        {
                            summary.RecentProcessing.Add(new RecentProcessingDto
                            {
                                ProcessedAt = log.ProcessedAt,
                                ProcessingStatus = log.ProcessingStatus,
                                RecordsProcessed = log.RecordsProcessed,
                                VesselsFound = log.VesselsFound,
                                ErrorMessage = log.ErrorMessage
                            });
                        }
                    }

                    _logger.LogInformation("Generated processing summary for user {UserId}: {ProcessingEntries} processing entries",
                        userId, summary.RecentProcessing.Count);

                    // Debug logging to help troubleshoot "Last sync: Never" issue
                    if (summary.RecentProcessing.Count > 0)
                    {
                        var latest = summary.RecentProcessing[0];
                        _logger.LogInformation("Latest processing entry: ProcessedAt={ProcessedAt}, Status={Status}, Records={Records}",
                            latest.ProcessedAt, latest.ProcessingStatus, latest.RecordsProcessed);
                    }
                    else
                    {
                        _logger.LogWarning("No recent processing entries found in EmailProcessingLog table");
                    }
                }
                catch (Exception dbEx)
                {
                    // Handle database table not existing or other database errors gracefully
                    _logger.LogWarning(dbEx, "Could not query EmailProcessingLog table for user {UserId}. Table may not exist yet. Error: {Error}",
                        userId, dbEx.Message);

                    // Check if it's a table not found error
                    if (dbEx.Message.Contains("Invalid object name 'EmailProcessingLog'") ||
                        dbEx.Message.Contains("doesn't exist") ||
                        dbEx.Message.Contains("not found"))
                    {
                        _logger.LogInformation("EmailProcessingLog table does not exist yet. Returning empty summary for user {UserId}", userId);
                    }

                    // Return empty summary instead of failing - this allows the UI to work even if the table doesn't exist
                    // The table will be created when the first email processing occurs
                }

                return new Response<ProcessingSummaryDto>
                {
                    Payload = summary,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating processing summary for user {UserId}", userId);
                return new Response<ProcessingSummaryDto>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "EMAIL-500",
                        Description = "Unable to retrieve processing summary. Please try again later.",
                        Category = "Technical Error"
                    }
                };
            }
        }

        // Note: Actual ScheduleProcessingAsync and GetScheduleStatusAsync implementations
        // are now in ScheduleManagement.EmailProcessingService.cs
        // The interface methods are implemented there as partial methods
    }
}