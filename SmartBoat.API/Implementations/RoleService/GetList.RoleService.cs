using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class RoleService
    {
        public async Task<ReturnListRoleDto> GetList(ListRoleRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid pagination parameters. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var pagedQuery = new PagedQuery
            {
                PageSize = (int)request.PageLimit,
                Offset = (int)request.PageOffset
            };

            try
            {
                var pagedResult = await _databaseService.SelectPagedAsync<Role>(pagedQuery);

                var roleDtos = new List<RoleDto>();
                foreach (var role in pagedResult.Records)
                {
                    // Check authorization for each individual role
                    try
                    {
                        var isAuthorizedDto = new IsAuthorizedDto<Role>
                        {
                            UserId = userId,
                            PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                            Entity = role
                        };
                        await _authorizationService.IsAuthorized(isAuthorizedDto);

                        // If authorized, process the role
                        // Get permissions from Authorization module
                        var userRolePermissions = await _databaseService.SelectAsync<PPG.Auth.Types.UserRolePermission>(new { UserRoleId = role.Id });
                        var permissionIds = userRolePermissions.Select(urp => urp.PermissionId).ToList();
                        
                        var permissions = new List<string>();
                        if (permissionIds.Any())
                        {
                            var authPermissions = await _databaseService.SelectAsync<PPG.Auth.Types.Permission>(
                                $"Id IN ({string.Join(",", permissionIds.Select(id => $"'{id}'"))})");
                            permissions = authPermissions.Select(p => p.MachineName).ToList();
                        }

                        var roleDto = new RoleDto
                        {
                            Id = role.Id,
                            Name = role.Name,
                            Description = role.Description,
                            Permissions = permissions,
                            Created = role.Created,
                            Changed = role.Changed
                        };
                        roleDtos.Add(roleDto);
                    }
                    catch (Exception)
                    {
                        // If not authorized, skip this role
                        continue;
                    }
                }

                var returnListRoleDto = new ReturnListRoleDto
                {
                    Data = roleDtos,
                    Metadata = new MetadataDto
                    {
                        PageLimit = (int)request.PageLimit,
                        PageOffset = (int)request.PageOffset,
                        Total = pagedResult.TotalRecords
                    }
                };

                return returnListRoleDto;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching roles. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}