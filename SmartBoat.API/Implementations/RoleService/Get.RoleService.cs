using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class RoleService
    {
        public async Task<RoleDto> Get(Guid id, Guid userId)
        {
            if (id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Role role = null;
            try
            {
                role = await _databaseService.SelectByIdAsync<Role, Guid>(id);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Role with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (role == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Role not found with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var authorizationDto = new IsAuthorizedDto<Role>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Read
                },
                Entity = role
            };
            await _authorizationService.IsAuthorized(authorizationDto);

            List<string> permissions = new List<string>();
            try
            {
                // Get permissions from Authorization module
                var userRolePermissions = await _databaseService.SelectAsync<PPG.Auth.Types.UserRolePermission>(new { UserRoleId = id });
                var permissionIds = userRolePermissions.Select(urp => urp.PermissionId).ToList();
                
                if (permissionIds.Any())
                {
                    var authPermissions = await _databaseService.SelectAsync<PPG.Auth.Types.Permission>(
                        $"Id IN ({string.Join(",", permissionIds.Select(pid => $"'{pid}'"))})");
                    permissions = authPermissions.Select(p => p.MachineName).ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching permissions for Role with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            var roleDto = new RoleDto
            {
                Id = role.Id,
                Name = role.Name,
                Description = role.Description,
                Permissions = permissions,
                Created = role.Created,
                Changed = role.Changed
            };

            return roleDto;
        }
    }
}