using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class RoleService
    {
        public async Task<RoleDto> Update(UpdateRoleDto request, Guid userId)
        {
            if (request == null || request.Id == null ||
                (string.IsNullOrWhiteSpace(request.Name) &&
                 string.IsNullOrWhiteSpace(request.Description) &&
                 request.Permissions == null))
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Role role = null;
            try
            {
                role = await _databaseService.SelectByIdAsync<Role, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Role with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (role == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Role not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Role>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = role
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            if (!string.IsNullOrWhiteSpace(request.Name))
            {
                try
                {
                    var existingRole = await _databaseService.SelectAsync<Role>(new { Name = request.Name });
                    if (existingRole != null && existingRole.Any(r => r.Id != request.Id))
                    {
                        _logger.LogInformation($"DP-409: Conflict. Role name already exists. UserId: {userId}");
                        throw new BusinessException("DP-409", "Conflict");
                    }
                    role.Name = request.Name;
                }
                catch (Exception ex)
                {
                    _logger.LogInformation($"DP-500: Technical Error. Error while checking role name uniqueness. UserId: {userId}");
                    throw new TechnicalException("DP-500", "Technical Error");
                }
            }

            if (!string.IsNullOrWhiteSpace(request.Description))
            {
                role.Description = request.Description;
            }

            role.Changed = DateTime.Now;

            try
            {
                await _databaseService.UpdateAsync<Role>(role, new { Id = role.Id });
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while updating Role with Id: {role.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (request.Permissions != null)
            {
                try
                {
                    // Delete existing UserRolePermissions for this role
                    await _databaseService.DeleteAsync<PPG.Auth.Types.UserRolePermission>(new { UserRoleId = role.Id });

                    // Map permission names to Authorization module permissions and create new UserRolePermissions
                    if (request.Permissions.Any())
                    {
                        var authPermissions = await _databaseService.SelectAsync<PPG.Auth.Types.Permission>(
                            $"MachineName IN ({string.Join(",", request.Permissions.Select(p => $"'{p}'"))})");
                        
                        var userRolePermissions = authPermissions.Select(ap => new PPG.Auth.Types.UserRolePermission
                        {
                            Id = Guid.NewGuid(),
                            UserRoleId = role.Id.Value,
                            PermissionId = ap.Id.Value
                        }).ToList();

                        if (userRolePermissions.Any())
                        {
                            await _databaseService.InsertAsync(userRolePermissions);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation($"DP-500: Technical Error. Error while updating permissions for Role with Id: {role.Id}. UserId: {userId}");
                    throw new TechnicalException("DP-500", "Technical Error");
                }
            }

            try
            {
                // Get updated permissions from Authorization module
                var userRolePermissions = await _databaseService.SelectAsync<PPG.Auth.Types.UserRolePermission>(new { UserRoleId = role.Id });
                var permissionIds = userRolePermissions.Select(urp => urp.PermissionId).ToList();
                
                var permissions = new List<string>();
                if (permissionIds.Any())
                {
                    var authPermissions = await _databaseService.SelectAsync<PPG.Auth.Types.Permission>(
                        $"Id IN ({string.Join(",", permissionIds.Select(pid => $"'{pid}'"))})");
                    permissions = authPermissions.Select(p => p.MachineName).ToList();
                }

                return new RoleDto
                {
                    Id = role.Id,
                    Name = role.Name,
                    Description = role.Description,
                    Permissions = permissions,
                    Created = role.Created,
                    Changed = role.Changed
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching permissions for Role with Id: {role.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}