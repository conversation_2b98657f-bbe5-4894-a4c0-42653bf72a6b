using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class RoleService
    {
        public async Task<RoleDto> Create(CreateRoleDto request, Guid userId)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Name) || request.Permissions == null || !request.Permissions.Any())
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var existingRole = await _databaseService.SelectAsync<Role>(new { Name = request.Name });
            if (existingRole.Any())
            {
                _logger.LogInformation($"DP-409: Conflict. Role with name {request.Name} already exists. UserId: {userId}");
                throw new BusinessException("DP-409", "Conflict");
            }

            var role = new Role
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Description = request.Description,
                Created = DateTime.Now,
                Changed = null
            };

            var isAuthorizedDto = new IsAuthorizedDto<Role>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = role
            };

            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                // Insert role first
                await _databaseService.InsertAsync(role);

                // Map permission names to Authorization module permissions and create UserRolePermissions
                if (request.Permissions != null && request.Permissions.Any())
                {
                    var authPermissions = await _databaseService.SelectAsync<PPG.Auth.Types.Permission>(
                        $"MachineName IN ({string.Join(",", request.Permissions.Select(p => $"'{p}'"))})");
                    
                    var userRolePermissions = authPermissions.Select(ap => new PPG.Auth.Types.UserRolePermission
                    {
                        Id = Guid.NewGuid(),
                        UserRoleId = role.Id.Value,
                        PermissionId = ap.Id.Value
                    }).ToList();

                    if (userRolePermissions.Any())
                    {
                        await _databaseService.InsertAsync(userRolePermissions);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while creating Role with Id: {role.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            var roleDto = new RoleDto
            {
                Id = role.Id,
                Name = role.Name,
                Description = role.Description,
                Permissions = request.Permissions,
                Created = role.Created,
                Changed = role.Changed
            };

            return roleDto;
        }
    }
}