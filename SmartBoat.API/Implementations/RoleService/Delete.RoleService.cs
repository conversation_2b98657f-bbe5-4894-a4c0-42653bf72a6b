using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class RoleService
    {
        public async Task<bool> Delete(Guid id, Guid userId)
        {
            if (id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Role entity = null;
            try
            {
                entity = await _databaseService.SelectByIdAsync<Role, Guid>(id);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Role with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (entity == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Role not found with Id: {id}. UserId: {userId}");
                throw new BusinessException("DP-404", "Not Found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Role>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Delete
                },
                Entity = entity
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                // UserRole type does not exist; replace with correct logic or remove this check as needed.
                var userCount = 0;
                if (userCount > 0)
                {
                    _logger.LogInformation($"DP-409: Conflict. Role has dependent users. RoleId: {id}. UserId: {userId}");
                    throw new BusinessException("DP-409", "Conflict");
                }

                // Delete UserRolePermissions first, then the role
                await _databaseService.DeleteAsync<PPG.Auth.Types.UserRolePermission>(new { UserRoleId = id });
                await _databaseService.DeleteAsync<Role>(new { Id = id });

                _logger.LogInformation($"Role deleted successfully. RoleId: {id}. UserId: {userId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while deleting Role with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}