using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using CsvHelper;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CsvProcessingService
    {
        public async Task<Response<List<Guid>>> ProcessCsvToSensorDataPointsAsync(byte[] csvContent, string fileName, string sourceEmail, Guid userId)
        {
            try
            {
                var createdDataPointIds = new List<Guid>();
                var vesselNameFromFile = ExtractVesselNameFromFileName(fileName);

                Console.WriteLine($"         🚢 [CSV TO SENSORS] Processing {fileName} for SensorDataPoints with userId: {userId}");
                _logger.LogInformation("Processing CSV file {FileName} to SensorDataPoints with userId: {UserId}", fileName, userId);

                using var stream = new MemoryStream(csvContent);
                using var reader = new StreamReader(stream, Encoding.UTF8);
                using var csv = new CsvReader(reader, GetCsvConfiguration());

                var records = new List<dynamic>();
                string[]? headers = null;

                if (csv.Read())
                {
                    csv.ReadHeader();
                    headers = csv.HeaderRecord;

                    Console.WriteLine($"         📋 [CSV TO SENSORS] Headers: {string.Join(", ", headers ?? new string[0])}");

                    while (csv.Read())
                    {
                        var record = csv.GetRecord<dynamic>();
                        if (record != null)
                        {
                            records.Add(record);
                        }
                    }
                }

                if (records.Count == 0)
                {
                    Console.WriteLine($"         ⚠️  [CSV TO SENSORS] No records found in CSV");
                    return new Response<List<Guid>>
                    {
                        Payload = createdDataPointIds,
                        Exception = null
                    };
                }

                // Group measurements by sensor and batch process them
                var sensorMeasurementBatches = new Dictionary<Guid, List<CreateSensorDataPointDto>>();
                var processedRecordCount = 0;
                var vesselName = "";

                foreach (var record in records)
                {
                    try
                    {
                        var recordDict = record as IDictionary<string, object>;
                        if (recordDict == null) continue;

                        // Extract vessel name from record or use filename fallback
                        vesselName = ExtractVesselNameFromRecord(recordDict) ?? vesselNameFromFile ?? "Unknown";
                        if (string.IsNullOrEmpty(vesselName))
                        {
                            Console.WriteLine($"         ⚠️  [CSV TO SENSORS] Skipping record - no vessel name found");
                            continue;
                        }

                        // Map CSV columns to measurements
                        var mappingResult = await _csvColumnMapper.MapCsvRecordAsync(recordDict, vesselName);

                        if (mappingResult.Measurements.Count == 0)
                        {
                            Console.WriteLine($"         ⚠️  [CSV TO SENSORS] No mappable measurements found in record");
                            continue;
                        }

                        // Get or create vessel sensor configuration
                        var vesselConfig = await _vesselSensorManager.GetOrCreateVesselSensorConfigurationAsync(
                            vesselName, mappingResult.VesselEngineConfiguration);

                        // Group measurements by sensor category and create data points
                        var measurementsByCategory = GroupMeasurementsByCategory(mappingResult.Measurements);

                        foreach (var categoryGroup in measurementsByCategory)
                        {
                            var sensorId = await _vesselSensorManager.GetSensorIdForMeasurementAsync(
                                vesselName, categoryGroup.Key, mappingResult.VesselEngineConfiguration);

                            // Extract timestamp from measurements or use current time
                            var timestamp = ExtractTimestamp(mappingResult.Measurements) ?? DateTime.UtcNow;

                            var dataPointDto = new CreateSensorDataPointDto
                            {
                                SensorId = sensorId,
                                Timestamp = timestamp,
                                Measurements = categoryGroup.Value,
                                QualityScore = mappingResult.QualityScore,
                                Source = "EMAIL_IMPORT"
                            };

                            if (!sensorMeasurementBatches.ContainsKey(sensorId))
                            {
                                sensorMeasurementBatches[sensorId] = new List<CreateSensorDataPointDto>();
                            }
                            sensorMeasurementBatches[sensorId].Add(dataPointDto);
                        }

                        processedRecordCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error processing individual record in CSV file {FileName}", fileName);
                        continue;
                    }
                }

                // Bulk create sensor data points for each sensor
                foreach (var batch in sensorMeasurementBatches)
                {
                    var sensorId = batch.Key;
                    var dataPoints = batch.Value;

                    Console.WriteLine($"         💾 [CSV TO SENSORS] Creating {dataPoints.Count} data points for sensor {sensorId}");

                    foreach (var dataPoint in dataPoints)
                    {
                        try
                        {
                            var dataPointIdString = await _sensorDataPointService.Create(dataPoint, userId);
                            if (Guid.TryParse(dataPointIdString, out var dataPointId))
                            {
                                createdDataPointIds.Add(dataPointId);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to create sensor data point for sensor {SensorId}", sensorId);
                            continue;
                        }
                    }
                }

                Console.WriteLine($"         ✅ [CSV TO SENSORS] Created {createdDataPointIds.Count} sensor data points from {processedRecordCount} CSV records for vessel {vesselName}");
                _logger.LogInformation("Successfully created {DataPointCount} sensor data points from {RecordCount} CSV records for vessel {VesselName}",
                    createdDataPointIds.Count, processedRecordCount, vesselName);

                return new Response<List<Guid>>
                {
                    Payload = createdDataPointIds,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing CSV file {FileName} to SensorDataPoints", fileName);
                return new Response<List<Guid>>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "CSV-500",
                        Description = "Error processing CSV file to sensor data points",
                        Category = "File Processing Error"
                    }
                };
            }
        }

        private string? ExtractVesselNameFromRecord(IDictionary<string, object> recordDict)
        {
            var possibleVesselKeys = new[] { "vessel", "vesselname", "vessel_name", "boat", "boatname" };

            foreach (var key in possibleVesselKeys)
            {
                var matchingKey = recordDict.Keys.FirstOrDefault(k =>
                    string.Equals(k, key, StringComparison.OrdinalIgnoreCase));

                if (matchingKey != null)
                {
                    var value = recordDict[matchingKey]?.ToString()?.Trim();
                    if (!string.IsNullOrEmpty(value))
                    {
                        return value;
                    }
                }
            }

            return null;
        }

        private Dictionary<string, Dictionary<string, object>> GroupMeasurementsByCategory(Dictionary<string, object> measurements)
        {
            var grouped = new Dictionary<string, Dictionary<string, object>>();

            foreach (var measurement in measurements)
            {
                var category = DetermineMeasurementCategory(measurement.Key);

                if (!grouped.ContainsKey(category))
                {
                    grouped[category] = new Dictionary<string, object>();
                }

                grouped[category][measurement.Key] = measurement.Value;
            }

            return grouped;
        }

        private string DetermineMeasurementCategory(string measurementKey)
        {
            var key = measurementKey.ToLowerInvariant();

            if (key.Contains("port") && (key.Contains("engine") || key.Contains("rpm")))
                return "engine_port";

            if ((key.Contains("stbd") || key.Contains("starboard")) && (key.Contains("engine") || key.Contains("rpm")))
                return "engine_stbd";

            if (key.Contains("engine") || key.Contains("rpm") || key.Contains("fuel") || key.Contains("oil"))
                return "engine_general";

            if (key.Contains("wind") || key.Contains("water") || key.Contains("sea") || key.Contains("depth"))
                return "environmental";

            if (key.Contains("speed") || key.Contains("course") || key.Contains("coordinates") || key.Contains("position"))
                return "navigation";

            return "systems";
        }

        private DateTime? ExtractTimestamp(Dictionary<string, object> measurements)
        {
            var timestampKeys = new[] { "timestamp", "datetime", "sensortime", "sensor_time", "time" };

            foreach (var key in timestampKeys)
            {
                if (measurements.TryGetValue(key, out var value))
                {
                    if (value is DateTime dateTime)
                        return dateTime;

                    if (DateTime.TryParse(value?.ToString(), out var parsedDateTime))
                        return parsedDateTime;
                }
            }

            return null;
        }
    }
}