using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using Microsoft.Extensions.Logging;
using CsvHelper;
using CsvHelper.Configuration;
using System.Globalization;
using System.Text;

namespace SmartBoat.API.Services
{
    public partial class CsvProcessingService : ICsvProcessingService
    {
        private readonly ILogger<CsvProcessingService> _logger;
        private readonly ICsvColumnMapper _csvColumnMapper;
        private readonly IVesselSensorManager _vesselSensorManager;
        private readonly ISensorDataPointService _sensorDataPointService;

        public CsvProcessingService(
            ILogger<CsvProcessingService> logger,
            ICsvColumnMapper csvColumnMapper,
            IVesselSensorManager vesselSensorManager,
            ISensorDataPointService sensorDataPointService)
        {
            _logger = logger;
            _csvColumnMapper = csvColumnMapper;
            _vesselSensorManager = vesselSensorManager;
            _sensorDataPointService = sensorDataPointService;
        }

        private CsvConfiguration GetCsvConfiguration()
        {
            return new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                MissingFieldFound = null,
                HeaderValidated = null,
                BadDataFound = null,
                DetectDelimiter = true,
                TrimOptions = TrimOptions.Trim
            };
        }

        private CsvSensorData ParseCsvRecord(dynamic record, string sourceEmail, string? fallbackVesselName = null)
        {
            var sensorData = new CsvSensorData
            {
                Id = Guid.NewGuid(),
                SourceEmail = sourceEmail,
                ProcessedDate = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow
            };

            try
            {
                // Map common CSV column names to our properties
                var recordDict = record as IDictionary<string, object>;
                if (recordDict == null) return sensorData;

                foreach (var kvp in recordDict)
                {
                    var columnName = kvp.Key?.ToString()?.Trim().ToLowerInvariant() ?? "";
                    var value = kvp.Value?.ToString()?.Trim();

                    if (string.IsNullOrEmpty(value)) continue;

                    switch (columnName)
                    {
                        case "vessel":
                        case "vesselname":
                        case "vessel_name":
                        case "boat":
                        case "boatname":
                            sensorData.VesselName = value;
                            break;

                        case "coordinates":
                        case "groupcoordinates":
                        case "group_coordinates":
                        case "position":
                            sensorData.GroupCoordinates = value;
                            break;

                        case "location":
                        case "address":
                            sensorData.Location = value;
                            break;

                        case "datetime":
                        case "timestamp":
                        case "sensortime":
                        case "sensor_time":
                        case "time":
                            if (DateTime.TryParse(value, out var sensorTime))
                                sensorData.SensorTime = sensorTime;
                            break;

                        case "speed":
                        case "velocity":
                            if (decimal.TryParse(value, out var speed))
                                sensorData.Speed = speed;
                            break;

                        case "seadepth":
                        case "sea_depth":
                        case "depth":
                        case "water_depth":
                            if (decimal.TryParse(value, out var seaDepth))
                                sensorData.SeaDepth = seaDepth;
                            break;

                        case "rpm":
                        case "engine_rpm":
                            if (decimal.TryParse(value, out var rpm))
                                sensorData.RPM = rpm;
                            break;

                        case "windangle":
                        case "wind_angle":
                            if (decimal.TryParse(value, out var windAngle))
                                sensorData.WindAngle = windAngle;
                            break;

                        case "watertemperature":
                        case "water_temperature":
                        case "temp":
                            if (decimal.TryParse(value, out var waterTemp))
                                sensorData.WaterTemperature = waterTemp;
                            break;

                        case "windspeed":
                        case "wind_speed":
                            if (decimal.TryParse(value, out var windSpeed))
                                sensorData.WindSpeed = windSpeed;
                            break;

                        case "oilpressure":
                        case "oil_pressure":
                            if (decimal.TryParse(value, out var oilPressure))
                                sensorData.OilPressure = oilPressure;
                            break;

                        case "rudder":
                        case "rudder_angle":
                            if (decimal.TryParse(value, out var rudder))
                                sensorData.Rudder = rudder;
                            break;

                        case "engineload":
                        case "engine_load":
                            if (decimal.TryParse(value, out var engineLoad))
                                sensorData.EngineLoad = engineLoad;
                            break;

                        case "cog":
                        case "course":
                            if (decimal.TryParse(value, out var cog))
                                sensorData.COG = cog;
                            break;

                        case "fuelrate":
                        case "fuel_rate":
                        case "fuel_consumption":
                            if (decimal.TryParse(value, out var fuelRate))
                                sensorData.FuelRate = fuelRate;
                            break;

                        case "totalnm":
                        case "total_nm":
                        case "nautical_miles":
                            if (decimal.TryParse(value, out var totalNm))
                                sensorData.TotalNM = totalNm;
                            break;

                        case "enginehours":
                        case "engine_hours":
                            if (decimal.TryParse(value, out var engineHours))
                                sensorData.EngineHours = engineHours;
                            break;

                        case "powersupply":
                        case "power_supply":
                        case "voltage":
                            if (decimal.TryParse(value, out var powerSupply))
                                sensorData.PowerSupply = powerSupply;
                            break;

                        case "enginealarmcode":
                        case "engine_alarm_code":
                        case "alarm_code":
                            sensorData.EngineAlarmCode = value;
                            break;

                        case "smartboatbattery":
                        case "smartboat_battery":
                        case "battery":
                            if (decimal.TryParse(value, out var battery))
                                sensorData.SmartBoatBattery = battery;
                            break;

                        case "enginewarningcode":
                        case "engine_warning_code":
                        case "warning_code":
                            sensorData.EngineWarningCode = value;
                            break;

                        case "gsmenginerunningindicator":
                        case "gsm_engine_running_indicator":
                        case "engine_running":
                            sensorData.GSMEngineRunningIndicator = value;
                            break;
                    }
                }

                // If no vessel name found in CSV data, use fallback from filename
                if (string.IsNullOrEmpty(sensorData.VesselName) && !string.IsNullOrEmpty(fallbackVesselName))
                {
                    sensorData.VesselName = fallbackVesselName;
                }

                return sensorData;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error parsing CSV record for vessel {VesselName}", sensorData.VesselName);
                return sensorData;
            }
        }

        private string? ExtractVesselNameFromFileName(string fileName)
        {
            try
            {
                // Remove file extension
                var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
                
                // Common patterns in IoT platform filenames:
                // "Amazing Elli_Daily_Data_report_2025-07-25_23-41_Sensor tracing.csv"
                // "VesselName_Daily_Data_report_YYYY-MM-DD_HH-mm_Something.csv"
                
                // Split by underscore and take the first part as vessel name
                var parts = nameWithoutExtension.Split('_');
                if (parts.Length > 0)
                {
                    var vesselName = parts[0].Trim();
                    
                    // Clean up common prefixes/suffixes
                    vesselName = vesselName.Replace("Daily", "").Replace("Data", "").Replace("Report", "").Trim();
                    
                    // Only return if it looks like a valid vessel name (not empty, not just numbers/dates)
                    if (!string.IsNullOrEmpty(vesselName) && 
                        vesselName.Length >= 2 && 
                        !DateTime.TryParse(vesselName, out _) &&
                        !int.TryParse(vesselName, out _))
                    {
                        return vesselName;
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error extracting vessel name from filename {FileName}", fileName);
                return null;
            }
        }
    }
}