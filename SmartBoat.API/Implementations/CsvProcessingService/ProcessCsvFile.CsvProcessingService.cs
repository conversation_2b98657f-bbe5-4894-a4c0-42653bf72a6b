using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using CsvHelper;
using System.Text;

namespace SmartBoat.API.Services
{
    public partial class CsvProcessingService
    {
        public async Task<Response<List<CsvSensorData>>> ProcessCsvFileAsync(byte[] csvContent, string fileName, string sourceEmail)
        {
            try
            {
                var sensorDataList = new List<CsvSensorData>();
                
                // Extract potential vessel name from filename as fallback
                var vesselNameFromFile = ExtractVesselNameFromFileName(fileName);
                Console.WriteLine($"         🚢 [CSV DEBUG] Extracted vessel name from filename: '{vesselNameFromFile}'");
                
                using var stream = new MemoryStream(csvContent);
                using var reader = new StreamReader(stream, Encoding.UTF8);
                using var csv = new CsvReader(reader, GetCsvConfiguration());

                // Read and process records in one pass
                var records = new List<dynamic>();
                string[]? headers = null;
                
                if (csv.Read())
                {
                    csv.ReadHeader();
                    headers = csv.HeaderRecord;
                    
                    Console.WriteLine($"         📋 [CSV DEBUG] Headers found: {string.Join(", ", headers ?? new string[0])}");
                    _logger.LogInformation("CSV Headers: {Headers}", string.Join(", ", headers ?? new string[0]));
                    
                    // Read all records
                    while (csv.Read())
                    {
                        var record = csv.GetRecord<dynamic>();
                        if (record != null)
                        {
                            records.Add(record);
                        }
                    }
                }
                
                Console.WriteLine($"         📊 [CSV DEBUG] Processing {records.Count} records from {fileName}");
                _logger.LogInformation("Processing {RecordCount} records from CSV file {FileName}", records.Count, fileName);

                var debugRecordCount = 0;
                foreach (var record in records)
                {
                    try
                    {
                        debugRecordCount++;
                        
                        // Debug first few records
                        if (debugRecordCount <= 3)
                        {
                            var recordDict = record as IDictionary<string, object>;
                            if (recordDict != null)
                            {
                                Console.WriteLine($"         🔍 [CSV DEBUG] Record {debugRecordCount} data:");
                                foreach (var kvp in recordDict.Take(10)) // Show first 10 columns
                                {
                                    Console.WriteLine($"           {kvp.Key}: '{kvp.Value}'");
                                }
                            }
                        }
                        
                        var sensorData = ParseCsvRecord(record, sourceEmail, vesselNameFromFile);
                        
                        // Debug vessel name extraction
                        if (debugRecordCount <= 3)
                        {
                            Console.WriteLine($"         🚢 [CSV DEBUG] Record {debugRecordCount} extracted vessel name: '{sensorData.VesselName}'");
                        }
                        
                        // Validate required fields
                        if (string.IsNullOrEmpty(sensorData.VesselName))
                        {
                            if (debugRecordCount <= 5) // Only log first few for debugging
                            {
                                Console.WriteLine($"         ⚠️  [CSV DEBUG] Record {debugRecordCount} has empty vessel name");
                            }
                            Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(_logger, "Skipping record with empty vessel name in file {FileName}", fileName);
                            continue;
                        }

                        if (sensorData.SensorTime == default(DateTime))
                        {
                            Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(_logger, "Skipping record with invalid sensor time for vessel {VesselName} in file {FileName}", 
                                sensorData.VesselName, fileName);
                            continue;
                        }

                        sensorDataList.Add(sensorData);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error processing individual record in file {FileName}", fileName);
                        continue;
                    }
                }

                _logger.LogInformation("Successfully processed {ValidRecords} valid records from {TotalRecords} total records in file {FileName}", 
                    sensorDataList.Count, records.Count, fileName);

                return new Response<List<CsvSensorData>>
                {
                    Payload = sensorDataList,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing CSV file {FileName}", fileName);
                return new Response<List<CsvSensorData>>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "CSV-500",
                        Description = "Error processing CSV file",
                        Category = "File Processing Error"
                    }
                };
            }
        }

        public async Task<Response<List<string>>> ExtractVesselNamesAsync(byte[] csvContent)
        {
            try
            {
                var vesselNames = new HashSet<string>();
                
                using var stream = new MemoryStream(csvContent);
                using var reader = new StreamReader(stream, Encoding.UTF8);
                using var csv = new CsvReader(reader, GetCsvConfiguration());

                var records = csv.GetRecords<dynamic>().ToList();

                foreach (var record in records)
                {
                    try
                    {
                        var recordDict = record as IDictionary<string, object>;
                        if (recordDict == null) continue;

                        foreach (var kvp in recordDict)
                        {
                            var columnName = kvp.Key?.ToString()?.Trim().ToLowerInvariant() ?? "";
                            var value = kvp.Value?.ToString()?.Trim();

                            if (string.IsNullOrEmpty(value)) continue;

                            if (columnName == "vessel" || columnName == "vesselname" || 
                                columnName == "vessel_name" || columnName == "boat" || 
                                columnName == "boatname")
                            {
                                vesselNames.Add(value);
                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error extracting vessel name from record");
                        continue;
                    }
                }

                var vesselNamesList = vesselNames.ToList();
                _logger.LogInformation("Extracted {VesselCount} unique vessel names from CSV", vesselNamesList.Count);

                return new Response<List<string>>
                {
                    Payload = vesselNamesList,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting vessel names from CSV");
                return new Response<List<string>>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "CSV-500",
                        Description = "Error extracting vessel names from CSV",
                        Category = "File Processing Error"
                    }
                };
            }
        }

        public async Task<Response<bool>> ValidateCsvFormatAsync(byte[] csvContent)
        {
            try
            {
                using var stream = new MemoryStream(csvContent);
                using var reader = new StreamReader(stream, Encoding.UTF8);
                using var csv = new CsvReader(reader, GetCsvConfiguration());

                // Try to read the header
                csv.Read();
                csv.ReadHeader();
                
                var headers = csv.HeaderRecord;
                if (headers == null || headers.Length == 0)
                {
                    return new Response<bool>
                    {
                        Payload = false,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "CSV-400",
                            Description = "CSV file has no headers",
                            Category = "Validation Error"
                        }
                    };
                }

                // Check for at least one vessel name column
                var hasVesselColumn = headers.Any(h => 
                {
                    var headerLower = h?.ToLowerInvariant() ?? "";
                    return headerLower.Contains("vessel") || headerLower.Contains("boat");
                });

                if (!hasVesselColumn)
                {
                    return new Response<bool>
                    {
                        Payload = false,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "CSV-400",
                            Description = "CSV file does not contain vessel/boat name column",
                            Category = "Validation Error"
                        }
                    };
                }

                // Try to read at least one record
                if (!csv.Read())
                {
                    return new Response<bool>
                    {
                        Payload = false,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "CSV-400",
                            Description = "CSV file contains no data records",
                            Category = "Validation Error"
                        }
                    };
                }

                _logger.LogInformation("CSV validation successful. Headers: {Headers}", string.Join(", ", headers));

                return new Response<bool>
                {
                    Payload = true,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating CSV format");
                return new Response<bool>
                {
                    Payload = false,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "CSV-500",
                        Description = "Error validating CSV format",
                        Category = "Validation Error"
                    }
                };
            }
        }
    }
}