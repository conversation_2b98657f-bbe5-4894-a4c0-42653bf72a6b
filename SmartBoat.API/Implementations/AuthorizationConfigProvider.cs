using PPG.Auth.Configuration;
using PPG.Auth.Interfaces;

namespace SmartBoat.API.Implementations
{
    /// <summary>
    /// Implementation of IAuthorizationConfigProvider that bridges main application's configuration 
    /// to Authorization module configuration
    /// </summary>
    public class AuthorizationConfigProvider : IAuthorizationConfigProvider
    {
        private readonly IConfiguration _configuration;
        private AuthorizationConfig? _cachedConfig;

        public AuthorizationConfigProvider(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        public AuthorizationConfig GetConfig()
        {
            if (_cachedConfig == null)
            {
                _cachedConfig = new AuthorizationConfig
                {
                    Cache = new CacheConfig
                    {
                        PermissionCacheKeyPrefix = _configuration.GetValue<string>("Caching:PermissionCacheKeyPrefix") ?? "auth:perm:",
                        CheckerTypesCacheKeyPrefix = _configuration.GetValue<string>("Caching:CheckerTypesCacheKeyPrefix") ?? "auth:types:",
                        PermissionCacheDurationInSeconds = _configuration.GetValue<int>("Caching:PermissionCacheDurationInSeconds", 1800),
                        ImplementedCheckerTypesCacheDurationInSeconds = _configuration.GetValue<int>("Caching:ImplementedCheckerTypesCacheDurationInSeconds", 3600)
                    },
                    SuperAdminRoleName = _configuration.GetValue<string>("Authorization:SuperAdminRoleName")
                };
            }

            return _cachedConfig;
        }

        public T GetValue<T>(string key, T defaultValue = default!)
        {
            return _configuration.GetValue<T>(key, defaultValue);
        }
    }
}