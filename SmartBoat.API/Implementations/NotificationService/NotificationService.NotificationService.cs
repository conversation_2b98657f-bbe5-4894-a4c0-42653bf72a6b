using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class NotificationService : INotificationService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;
        private readonly ILogger<NotificationService> _logger;
        private readonly IUserService _userService;
        private readonly INotificationPreferenceService _notificationPreferenceService;

        public NotificationService(
            IAuthorizationService authorizationService,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService,
            ILogger<NotificationService> logger,
            IUserService userService,
            INotificationPreferenceService notificationPreferenceService)
        {
            _authorizationService = authorizationService;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
            _logger = logger;
            _userService = userService;
            _notificationPreferenceService = notificationPreferenceService;
        }
    }
}