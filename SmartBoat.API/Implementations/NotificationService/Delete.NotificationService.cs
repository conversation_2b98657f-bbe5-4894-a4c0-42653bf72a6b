using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class NotificationService
    {
        public async Task<bool> Delete(DeleteNotificationDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Notification notification = null;
            try
            {
                notification = await _databaseService.SelectByIdAsync<Notification, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Notification with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (notification == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Notification not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Notification>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Delete },
                Entity = notification
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                if (request.FieldsToDelete == null)
                {
                    await _databaseService.DeleteAsync<Notification>(new { Id = request.Id });
                    return true;
                }
                else
                {
                    var columnsToNullify = new List<string>();
                    foreach (var field in request.FieldsToDelete)
                    {
                        if (field != "Id" && field != "UserId")
                        {
                            columnsToNullify.Add(field);
                        }
                    }

                    if (columnsToNullify.Count > 0)
                    {
                        await _databaseService.NullifyColumnsAsync<Notification>(columnsToNullify, new { Id = request.Id });
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while deleting Notification with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            return false;
        }
    }
}