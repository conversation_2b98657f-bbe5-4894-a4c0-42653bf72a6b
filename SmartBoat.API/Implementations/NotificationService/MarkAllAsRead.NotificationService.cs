using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class NotificationService
    {
        public async Task<string> MarkAllAsRead(Guid userId)
        {
            try
            {
                // Get all unread notifications for the user
                var userNotifications = await _databaseService.SelectAsync<Notification>(new 
                { 
                    UserId = userId,
                    Status = "Sent"  // Assuming "Sent" means unread
                });

                if (userNotifications == null || !userNotifications.Any())
                {
                    return "No unread notifications found";
                }

                int updatedCount = 0;
                foreach (var notification in userNotifications)
                {
                    try
                    {
                        notification.Status = "Read";
                        notification.Changed = DateTime.UtcNow;
                        
                        await _databaseService.UpdateAsync<Notification>(notification, new { Id = notification.Id });
                        updatedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to mark notification {notification.Id} as read for user {userId}: {ex.Message}");
                        // Continue with other notifications
                    }
                }

                return $"Marked {updatedCount} notifications as read";
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while marking all notifications as read for UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}