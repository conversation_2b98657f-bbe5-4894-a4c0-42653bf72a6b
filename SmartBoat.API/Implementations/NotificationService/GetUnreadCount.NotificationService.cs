using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class NotificationService
    {
        public async Task<int> GetUnreadCount(Guid userId)
        {
            try
            {
                // Get all unread notifications for the user
                var userNotifications = await _databaseService.SelectAsync<Notification>(new 
                { 
                    UserId = userId,
                    Status = "Sent"  // Assuming "Sent" means unread
                });

                if (userNotifications == null)
                {
                    return 0;
                }

                // Since we're filtering by UserId, all notifications belong to the authenticated user
                return userNotifications.Count();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while getting unread count for UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}