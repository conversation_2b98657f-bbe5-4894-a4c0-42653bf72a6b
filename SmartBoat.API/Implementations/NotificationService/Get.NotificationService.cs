using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class NotificationService
    {
        public async Task<NotificationDto> Get(NotificationRequestDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Notification notification = null;
            try
            {
                // SECURITY: Fetch notification that belongs to authenticated user only
                var notifications = await _databaseService.SelectAsync<Notification>(new { 
                    Id = request.Id.Value,
                    UserId = userId 
                });
                notification = notifications.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Notification with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (notification == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Notification not found with Id: {request.Id} for UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            UserDto userDto = null;
            if (notification.UserId != null)
            {
                try
                {
                    var userRequest = new UserRequestDto { Id = notification.UserId.Value };
                    userDto = await _userService.Get(userRequest, userId);
                }
                catch (Exception ex)
                {
                    _logger.LogInformation($"Error while fetching User with Id: {notification.UserId}. UserId: {userId}");
                }
            }

            NotificationPreferenceDto preferenceDto = null;
            if (notification.PreferenceId != null)
            {
                try
                {
                    var preferenceRequest = new NotificationPreferenceRequestDto { Id = notification.PreferenceId.Value };
                    preferenceDto = await _notificationPreferenceService.Get(preferenceRequest, userId);
                }
                catch (Exception ex)
                {
                    _logger.LogInformation($"Error while fetching NotificationPreference with Id: {notification.PreferenceId}. UserId: {userId}");
                }
            }

            var notificationDto = new NotificationDto
            {
                Id = notification.Id,
                User = userDto,
                EventType = notification.EventType,
                Content = notification.Content,
                Channel = notification.Channel,
                Status = notification.Status,
                Timestamp = notification.Timestamp,
                Preference = preferenceDto,
                Created = notification.Created,
                Changed = notification.Changed
            };

            return notificationDto;
        }
    }
}