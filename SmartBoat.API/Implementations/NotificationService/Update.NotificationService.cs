using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class NotificationService
    {
        public async Task<string> Update(UpdateNotificationDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var updatableFields = new List<string> { "EventType", "Content", "Channel", "Status", "PreferenceId" };
            if (!updatableFields.Any(field => request.GetType().GetProperty(field)?.GetValue(request) != null))
            {
                _logger.LogInformation($"DP-422: Client Error. No updatable fields provided. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Notification notification;
            try
            {
                notification = await _databaseService.SelectByIdAsync<Notification, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Notification with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (notification == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Notification not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Notification>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = notification
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            if (request.UserId != null)
            {
                try
                {
                    var user = await _userService.Get(new UserRequestDto { Id = request.UserId.Value }, userId);
                    if (user == null)
                    {
                        _logger.LogInformation($"DP-404: Technical Error. User not found with Id: {request.UserId}. UserId: {userId}");
                        throw new TechnicalException("DP-404", "Technical Error");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation($"DP-500: Technical Error. Error while fetching User with Id: {request.UserId}. UserId: {userId}");
                    throw new TechnicalException("DP-500", "Technical Error");
                }
            }

            if (request.PreferenceId != null)
            {
                try
                {
                    var preference = await _notificationPreferenceService.Get(new NotificationPreferenceRequestDto { Id = request.PreferenceId.Value }, userId);
                    if (preference == null)
                    {
                        _logger.LogInformation($"DP-404: Technical Error. NotificationPreference not found with Id: {request.PreferenceId}. UserId: {userId}");
                        throw new TechnicalException("DP-404", "Technical Error");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation($"DP-500: Technical Error. Error while fetching NotificationPreference with Id: {request.PreferenceId}. UserId: {userId}");
                    throw new TechnicalException("DP-500", "Technical Error");
                }
            }

            notification.UserId = request.UserId ?? notification.UserId;
            notification.EventType = request.EventType ?? notification.EventType;
            notification.Content = request.Content ?? notification.Content;
            notification.Channel = request.Channel ?? notification.Channel;
            notification.Status = request.Status ?? notification.Status;
            notification.PreferenceId = request.PreferenceId ?? notification.PreferenceId;
            notification.Changed = DateTime.Now;

            try
            {
                await _databaseService.UpdateAsync<Notification>(notification, new { Id = notification.Id });
                var updatedNotification = await _databaseService.SelectByIdAsync<Notification, Guid>(notification.Id.Value);
                if (updatedNotification == null)
                {
                    _logger.LogInformation($"DP-404: Technical Error. Notification not found with Id: {notification.Id}. UserId: {userId}");
                    throw new TechnicalException("DP-404", "Technical Error");
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while updating Notification with Id: {notification.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            return notification.Id.ToString();
        }
    }
}