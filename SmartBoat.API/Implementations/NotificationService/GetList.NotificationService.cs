using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class NotificationService
    {
        public async Task<ReturnListNotificationDto> GetList(ListNotificationRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var filters = new Dictionary<string, object>();
            // SECURITY: Always filter by authenticated user's ID, ignore request.UserId
            filters.Add("UserId", userId);
            if (request.Status != null)
                filters.Add("Status", request.Status);
            if (request.EventType != null)
                filters.Add("EventType", request.EventType);
            if (request.Channel != null)
                filters.Add("Channel", request.Channel);

            Nbg.NetCore.DatabaseService.PagedResult<Notification> pagedResult;
            try
            {
                pagedResult = await _autoCodeDbOperationsService.SelectPagedWithFiltersAsync<Notification>(
                    new PagedQuery
                    {
                        PageSize = (int)request.PageLimit,
                        Offset = request.PageOffset,
                        Search = request.SearchTerm,
                        OrderOptions = new OrderOptions
                        {
                            OrderColumn = request.SortField,
                            OrderDirection = request.SortOrder
                        }
                    },
                    filters
                );
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching notifications. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            var returnListNotificationDto = new ReturnListNotificationDto
            {
                Data = new List<NotificationDto>(),
                Metadata = new MetadataDto
                {
                    PageLimit = request.PageLimit,
                    PageOffset = request.PageOffset,
                    Total = pagedResult.TotalRecords
                }
            };

            foreach (var notification in pagedResult.Records)
            {
                // Since we're filtering by UserId at database level, all notifications belong to the authenticated user
                var notificationDto = new NotificationDto
                {
                    Id = notification.Id,
                    User = await _userService.Get(new UserRequestDto { Id = notification.UserId }, userId),
                    EventType = notification.EventType,
                    Content = notification.Content,
                    Channel = notification.Channel,
                    Status = notification.Status,
                    Timestamp = notification.Timestamp,
                    Preference = notification.PreferenceId != null ? await _notificationPreferenceService.Get(new NotificationPreferenceRequestDto { Id = notification.PreferenceId }, userId) : null,
                    Created = notification.Created,
                    Changed = notification.Changed
                };
                returnListNotificationDto.Data.Add(notificationDto);
            }

            return returnListNotificationDto;
        }
    }
}