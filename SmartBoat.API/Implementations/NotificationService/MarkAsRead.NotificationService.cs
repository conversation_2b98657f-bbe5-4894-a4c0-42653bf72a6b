using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class NotificationService
    {
        public async Task<string> MarkAsRead(Guid notificationId, Guid userId)
        {
            Notification notification = null;
            try
            {
                // SECURITY: Fetch notification that belongs to authenticated user only
                var notifications = await _databaseService.SelectAsync<Notification>(new { 
                    Id = notificationId,
                    UserId = userId 
                });
                notification = notifications.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Notification with Id: {notificationId}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (notification == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Notification not found with Id: {notificationId} for UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            notification.Status = "Read";
            notification.Changed = DateTime.UtcNow;

            try
            {
                await _databaseService.UpdateAsync<Notification>(notification, new { Id = notificationId });
                return notification.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while marking Notification as read with Id: {notificationId}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}