using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class NotificationService
    {
        public async Task<string> Create(CreateNotificationDto request, Guid userId)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.UserId.ToString()) || string.IsNullOrWhiteSpace(request.EventType) || string.IsNullOrWhiteSpace(request.Content) || string.IsNullOrWhiteSpace(request.Channel))
            {
                _logger.LogInformation($"DP-422: Client Error. Request or required fields are null or empty. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Notification entity = new Notification
            {
                Id = Guid.NewGuid(),
                UserId = request.UserId,
                EventType = request.EventType,
                Content = request.Content,
                Channel = request.Channel,
                PreferenceId = request.PreferenceId,
                Status = "Sent",
                Timestamp = DateTime.Now,
                Created = DateTime.Now,
                Changed = null
            };

            IsAuthorizedDto<Notification> authDto = new IsAuthorizedDto<Notification>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = entity
            };

            await _authorizationService.IsAuthorized(authDto);

            UserDto user = await _userService.Get(new UserRequestDto { Id = request.UserId }, userId);
            if (user == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. User not found with Id: {request.UserId}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            if (request.PreferenceId != null)
            {
                NotificationPreferenceDto preference = await _notificationPreferenceService.Get(new NotificationPreferenceRequestDto { Id = request.PreferenceId }, userId);
                if (preference == null)
                {
                    _logger.LogInformation($"DP-404: Technical Error. NotificationPreference not found with Id: {request.PreferenceId}. UserId: {userId}");
                    throw new TechnicalException("DP-404", "Technical Error");
                }
            }

            try
            {
                await _databaseService.InsertAsync<Notification>(entity);
                Notification insertedNotification = await _databaseService.SelectByIdAsync<Notification, Guid>(entity.Id.Value);
                if (insertedNotification == null)
                {
                    _logger.LogInformation($"DP-404: Technical Error. Notification not found with Id: {entity.Id}. UserId: {userId}");
                    throw new TechnicalException("DP-404", "Technical Error");
                }
                return insertedNotification.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while inserting Notification with Id: {entity.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}