using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class AuditLogService
    {
        public async Task<Nbg.NetCore.DatabaseService.PagedResult<AuditLog>> ListAuditLogs(ListAuditLogRequestDto request, Guid userId)
        {
            if (request == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Request is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            if (request.PageLimit == null || request.PageLimit <= 0)
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid PageLimit. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            try
            {
                var query = new PagedQuery
                {
                    PageSize = (int)request.PageLimit,
                    PageNumber = request.PageOffset > 0 ? (int)(request.PageOffset / request.PageLimit) + 1 : 1
                };

                if (!string.IsNullOrWhiteSpace(request.SortField) && !string.IsNullOrWhiteSpace(request.SortOrder))
                {
                    query.OrderOptions = new OrderOptions
                    {
                        OrderColumn = request.SortField,
                        OrderDirection = request.SortOrder.ToUpper() == "DESC" ? "DESC" : "ASC"
                    };
                }

                var filter = new
                {
                    UserId = request.UserId,
                    EntityType = request.EntityType,
                    EntityId = request.EntityId,
                    Action = request.Action
                };

                var dateFilter = request.FromDate != null || request.ToDate != null
                    ? new { Timestamp = new { From = request.FromDate, To = request.ToDate } }
                    : null;

                var pagedResult = await _databaseService.SelectPagedAsync<AuditLog>(query, filter);
                return pagedResult;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while listing AuditLogs. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}