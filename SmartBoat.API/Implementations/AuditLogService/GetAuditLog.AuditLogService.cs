using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class AuditLogService
    {
        public async Task<AuditLog> GetAuditLog(Guid id, Guid userId)
        {
            if (id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            try
            {
                var auditLog = await _databaseService.SelectByIdAsync<AuditLog, Guid>(id);

                if (auditLog == null)
                {
                    _logger.LogInformation($"DP-404: Technical Error. AuditLog not found with Id: {id}. UserId: {userId}");
                    throw new TechnicalException("DP-404", "Technical Error");
                }

                var isAuthorizedDto = new IsAuthorizedDto<AuditLog>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck
                    {
                        OperationActionId = OperationAction.Read
                    },
                    Entity = auditLog
                };

                await _authorizationService.IsAuthorized(isAuthorizedDto);

                return auditLog;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching AuditLog with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}