using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Services
{
    public partial class AuditLogService
    {
        public async Task<AuditLogDto> Get(Guid id, Guid userId)
        {
            if (id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            AuditLog auditLog = null;
            try
            {
                auditLog = await _databaseService.SelectByIdAsync<AuditLog, Guid>(id);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching AuditLog with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (auditLog == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. AuditLog not found with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<AuditLog>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = auditLog
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            var auditLogDto = new AuditLogDto
            {
                Id = auditLog.Id,
                UserId = auditLog.UserId,
                EntityType = auditLog.EntityType,
                EntityId = auditLog.EntityId,
                Action = auditLog.Action,
                Details = auditLog.Details,
                Timestamp = auditLog.Timestamp,
                Created = auditLog.Created,
                Changed = auditLog.Changed
            };

            return auditLogDto;
        }
    }
}