using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.ControllersExceptions;
using SmartBoat.API.Interfaces;

namespace SmartBoat.API.Services
{
    public partial class AuditLogService
    {
        public async Task<ResponsePayload> GetAuditLogById(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                _logger.LogInformation("DP-422: Client Error. Id is null or empty.");
                throw new BusinessException("DP-422", "Client Error");
            }

            if (!Guid.TryParse(id, out Guid guidId))
            {
                _logger.LogInformation($"DP-422: Client Error. Id is not a valid GUID. Id: {id}");
                throw new BusinessException("DP-422", "Client Error");
            }

            // NOTE: You may need to provide a userId here, depending on your authorization logic.
            Guid userId = Guid.Empty; // Placeholder, replace with actual userId as needed.

            // Assuming Get returns an AuditLogDto
            var auditLogDto = await Get(guidId, userId);

            // Map AuditLogDto to ResponsePayload
            var responsePayload = new ResponsePayload
            {
                // You may need to map properties from auditLogDto to ResponsePayload as appropriate.
                // If ResponsePayload is a generic wrapper, assign the DTO to a property like 'Data' or similar.
                // For now, assuming a property 'Data' exists:
                // Data = auditLogDto
            };

            return responsePayload;
        }
    }
}