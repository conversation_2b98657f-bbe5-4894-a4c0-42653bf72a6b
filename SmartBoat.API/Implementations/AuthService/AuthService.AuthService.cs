using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using Nbg.NetCore.DatabaseService;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;

namespace SmartBoat.API.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUserService _userService;
        private readonly IJwtService _jwtService;
        private readonly IDatabaseService _databaseService;
        private readonly ILogger<AuthService> _logger;
        private readonly IConfiguration _configuration;

        public AuthService(
            IUserService userService,
            IJwtService jwtService,
            IDatabaseService databaseService,
            ILogger<AuthService> logger,
            IConfiguration configuration)
        {
            _userService = userService;
            _jwtService = jwtService;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<LoginResponseDto> LoginAsync(LoginRequestDto request)
        {
            try
            {
                var user = await GetUserByEmailAsync(request.Email);
                if (user == null)
                {
                    throw new UnauthorizedAccessException("Invalid email or password");
                }

                if (!await ValidatePasswordAsync(request.Email, request.Password))
                    throw new UnauthorizedAccessException("Invalid email or password");

                if (user.Status != "Active")
                {
                    throw new UnauthorizedAccessException("Account is not active");
                }

                await UpdateLastLoginAsync(user.Id!.Value);

                var token = _jwtService.GenerateToken(user);
                var expiresAt = DateTime.UtcNow.AddMinutes(60);

                return new LoginResponseDto
                {
                    Token = token,
                    User = user,
                    ExpiresAt = expiresAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for email: {Email}", request.Email);
                throw;
            }
        }

        public async Task<bool> ValidatePasswordAsync(string email, string password)
        {
            try
            {
                var user = (await _databaseService.SelectAsync<User>(new { Email = email, IsDeleted = false })).FirstOrDefault();
                
                if (user == null || string.IsNullOrEmpty(user.PasswordHash))
                {
                    return false;
                }

                return VerifyPassword(password, user.PasswordHash, _configuration["Security:Salt"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating password for email: {Email}", email);
                return false;
            }
        }

        private async Task<UserDto?> GetUserByEmailAsync(string email)
        {
            try
            {
                var user = (await _databaseService.SelectAsync<User>(new { Email = email, IsDeleted = false })).FirstOrDefault();
                
                if (user == null)
                {
                    return null;
                }

                return new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Role = user.Role,
                    Status = user.Status,
                    Created = user.Created,
                    Changed = user.Changed,
                    LastLogin = user.LastLogin,
                    TwoFactorEnabled = user.TwoFactorEnabled,
                    Avatar = user.Avatar,
                    Company = user.Company,
                    Department = user.Department,
                    Phone = user.Phone,
                    PhoneNumber = user.PhoneNumber,
                    Timezone = user.Timezone,
                    Language = user.Language,
                    Bio = user.Bio
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by email: {Email}", email);
                return null;
            }
        }

        private async Task UpdateLastLoginAsync(Guid userId)
        {
            try
            {
                var user = await _databaseService.SelectByIdAsync<User, Guid>(userId);
                if (user != null)
                {
                    user.LastLogin = DateTime.UtcNow;
                    await _databaseService.UpdateAsync<User>(user, new { Id = user.Id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating last login for user: {UserId}", userId);
            }
        }

        private static bool VerifyPassword(string password, string storedHash, string salt = "")
        {
            if (storedHash.StartsWith("$2"))
            {
                return BCrypt.Net.BCrypt.Verify(password+salt, storedHash);
            }
            
            using var sha256 = SHA256.Create();
            var hashedPassword = Convert.ToBase64String(sha256.ComputeHash(Encoding.UTF8.GetBytes(password)));
            return hashedPassword == storedHash;
        }
    }
}