using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Implementations
{
    /// <summary>
    /// Service implementation for managing notification preferences
    /// </summary>
    public class NotificationPreferenceService : INotificationPreferenceService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;
        private readonly ILogger<NotificationPreferenceService> _logger;
        private readonly IUserService _userService;

        public NotificationPreferenceService(
            IAuthorizationService authorizationService,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService,
            ILogger<NotificationPreferenceService> logger,
            IUserService userService)
        {
            _authorizationService = authorizationService;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
            _logger = logger;
            _userService = userService;
        }

        /// <summary>
        /// Retrieves a specific notification preference by its ID
        /// </summary>
        /// <param name="request">The data required to retrieve the notification preference</param>
        /// <param name="userId">The ID of the user requesting the preference</param>
        /// <returns>The requested notification preference details</returns>
        public async Task<NotificationPreferenceDto> Get(NotificationPreferenceRequestDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"SB-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            NotificationPreference notificationPreference = null;
            try
            {
                notificationPreference = await _databaseService.SelectByIdAsync<NotificationPreference, Guid>((Guid)request.Id);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while fetching NotificationPreference with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("SB-500", "Technical Error");
            }

            if (notificationPreference == null)
            {
                _logger.LogInformation($"SB-404: Technical Error. NotificationPreference not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("SB-404", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<NotificationPreference>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = notificationPreference
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            // Fetch related user
            UserDto user = null;
            if (notificationPreference.UserId != null)
            {
                try
                {
                    var userRequest = new UserRequestDto { Id = notificationPreference.UserId };
                    user = await _userService.Get(userRequest, userId);
                }
                catch (Exception ex)
                {
                    _logger.LogInformation($"SB-500: Technical Error. Error while fetching User with Id: {notificationPreference.UserId}. UserId: {userId}");
                    // Continue without user data
                }
            }

            var notificationPreferenceDto = new NotificationPreferenceDto
            {
                Id = notificationPreference.Id,
                User = user,
                EventType = notificationPreference.EventType,
                Channel = notificationPreference.Channel,
                Enabled = notificationPreference.Enabled,
                UpdatedAt = notificationPreference.UpdatedAt,
                Created = notificationPreference.Created,
                Changed = notificationPreference.Changed
            };

            return notificationPreferenceDto;
        }
    }
}