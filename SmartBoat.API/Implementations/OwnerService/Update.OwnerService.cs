using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class OwnerService
    {
        public async Task<OwnerDto> Update(UpdateOwnerDto request, Guid userId)
        {
            // 1. Validate Input Parameters
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Request or Id is null. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            if (string.IsNullOrWhiteSpace(request.Name))
            {
                _logger.LogInformation($"DP-422: Client Error. Owner name is required. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            if (request.CompanyId == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Company is required. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            // 2. Fetch and Validate Owner
            Owner existingOwner;
            try
            {
                existingOwner = await _databaseService.SelectByIdAsync<Owner, Guid>(request.Id.Value);
                if (existingOwner == null)
                {
                    _logger.LogInformation($"DP-404: Owner not found with Id: {request.Id}. UserId: {userId}");
                    throw new BusinessException("DP-404", "Owner not found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DP-500: Technical Error. Error while fetching Owner with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            // 3. Authorization Check
            var authDto = new IsAuthorizedDto<Owner>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Update
                },
                Entity = existingOwner
            };

            await _authorizationService.IsAuthorized(authDto);

            // 4. Verify that the company exists
            var existingCompany = await _databaseService.SelectAsync<Company>(new { Id = request.CompanyId });
            if (existingCompany == null || !existingCompany.Any())
            {
                _logger.LogInformation($"DP-404: Business Error. Company with ID {request.CompanyId} not found. UserId: {userId}");
                throw new BusinessException("DP-404", "Company not found");
            }

            // 5. Update Owner
            existingOwner.Name = request.Name;
            existingOwner.Email = request.Email;
            existingOwner.Phone = request.Phone;
            existingOwner.Address = request.Address;
            existingOwner.CompanyId = request.CompanyId;
            existingOwner.Status = request.Status ?? existingOwner.Status;
            existingOwner.LastUpdated = DateTime.UtcNow;
            existingOwner.Changed = DateTime.UtcNow;

            try
            {
                await _databaseService.UpdateAsync<Owner>(existingOwner, new { Id = existingOwner.Id });
                
                // Send notifications for owner update
                await _entityNotificationService.NotifyEntityUpdated("Owner", existingOwner.Id, existingOwner.Name, existingOwner.CompanyId, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DP-500: Technical Error. Error while updating Owner with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            // 6. Get company details for the response
            var company = existingCompany.First();
            var companyDto = new CompanyDto
            {
                Id = company.Id,
                Name = company.Name,
                Location = company.Location,
                Industry = company.Industry,
                Status = company.Status,
                CustomerId = company.CustomerId,
                LastUpdated = company.LastUpdated,
                Created = company.Created,
                Changed = company.Changed
            };

            // 7. Return Updated Owner
            return new OwnerDto
            {
                Id = existingOwner.Id,
                Name = existingOwner.Name,
                Email = existingOwner.Email,
                Phone = existingOwner.Phone,
                Address = existingOwner.Address,
                CompanyId = existingOwner.CompanyId,
                Company = companyDto,
                Status = existingOwner.Status,
                LastUpdated = existingOwner.LastUpdated,
                Created = existingOwner.Created,
                Changed = existingOwner.Changed
            };
        }
    }
}