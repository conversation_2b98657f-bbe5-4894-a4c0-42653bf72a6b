using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class OwnerService
    {
        public async Task<OwnerDto> Get(DeleteOwnerDto request, Guid userId)
        {
            // 1. Validate Input Parameters
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Request or Id is null. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            // 2. <PERSON><PERSON> and Validate Owner
            Owner owner;
            try
            {
                owner = await _databaseService.SelectByIdAsync<Owner, Guid>(request.Id.Value);
                if (owner == null)
                {
                    _logger.LogInformation($"DP-404: Owner not found with Id: {request.Id}. UserId: {userId}");
                    throw new BusinessException("DP-404", "Owner not found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DP-500: Technical Error. Error while fetching Owner with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            // 3. Authorization Check
            var authDto = new IsAuthorizedDto<Owner>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Read
                },
                Entity = owner
            };

            await _authorizationService.IsAuthorized(authDto);

            // 4. Get Company details
            Company company = null;
            CompanyDto companyDto = null;
            if (owner.CompanyId != null)
            {
                try
                {
                    company = await _databaseService.SelectByIdAsync<Company, Guid>(owner.CompanyId.Value);
                    if (company != null)
                    {
                        companyDto = new CompanyDto
                        {
                            Id = company.Id,
                            Name = company.Name,
                            Location = company.Location,
                            Industry = company.Industry,
                            Status = company.Status,
                            CustomerId = company.CustomerId,
                            LastUpdated = company.LastUpdated,
                            Created = company.Created,
                            Changed = company.Changed
                        };
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"Warning: Could not fetch company details for Owner {request.Id}. UserId: {userId}");
                }
            }

            // 5. Return the Result
            try
            {
                return new OwnerDto
                {
                    Id = owner.Id,
                    Name = owner.Name,
                    Email = owner.Email,
                    Phone = owner.Phone,
                    Address = owner.Address,
                    CompanyId = owner.CompanyId,
                    Company = companyDto,
                    Status = owner.Status,
                    LastUpdated = owner.LastUpdated,
                    Created = owner.Created,
                    Changed = owner.Changed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DP-500: Technical Error. Error while mapping Owner with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}