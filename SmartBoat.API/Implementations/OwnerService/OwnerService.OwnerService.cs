using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class OwnerService : IOwnerService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly ILogger<OwnerService> _logger;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;
        private readonly IEntityNotificationService _entityNotificationService;

        public OwnerService(
            IAuthorizationService authorizationService,
            ILogger<OwnerService> logger,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService,
            IEntityNotificationService entityNotificationService)
        {
            _authorizationService = authorizationService;
            _logger = logger;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
            _entityNotificationService = entityNotificationService;
        }
    }
}