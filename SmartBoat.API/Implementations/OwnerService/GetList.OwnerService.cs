using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class OwnerService
    {
        public async Task<ReturnListOwnerDto> GetList(ListOwnerRequestDto request, Guid userId)
        {
            // Validate Input Parameters
            if (request == null || request.PageLimit == null || request.PageLimit <= 0)
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            try
            {
                // Apply Filters and Fetch Owners
                var pagedQuery = new PagedQuery
                {
                    PageNumber = (int)request.PageOffset + 1,
                    PageSize = (int)request.PageLimit,
                    OrderOptions = new OrderOptions
                    {
                        OrderColumn = string.IsNullOrWhiteSpace(request.SortField) ? "Created" : request.SortField,
                        OrderDirection = string.IsNullOrWhiteSpace(request.SortOrder) ? "desc" : request.SortOrder
                    }
                };

                // Get all owners - since SelectAsync requires a filter object, we use a workaround
                // We'll get owners with any status (which should include all owners)
                // This avoids the SQL syntax error from using empty object or null
                var allOwners = new List<Owner>();
                
                try
                {
                    // Try to get owners with status 'Active' first (most common)
                    _logger.LogInformation($"Fetching Active owners for userId: {userId}");
                    var activeOwners = await _databaseService.SelectAsync<Owner>(new { Status = "Active" });
                    _logger.LogInformation($"Found {activeOwners.Count()} Active owners");
                    allOwners.AddRange(activeOwners);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error fetching Active owners for userId: {userId}");
                }
                
                try
                {
                    // Then get owners with status 'Inactive'
                    _logger.LogInformation($"Fetching Inactive owners for userId: {userId}");
                    var inactiveOwners = await _databaseService.SelectAsync<Owner>(new { Status = "Inactive" });
                    _logger.LogInformation($"Found {inactiveOwners.Count()} Inactive owners");
                    allOwners.AddRange(inactiveOwners);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error fetching Inactive owners for userId: {userId}");
                }
                
                try
                {
                    // Also get owners with null status (just in case)
                    _logger.LogInformation($"Fetching null status owners for userId: {userId}");
                    var nullStatusOwners = await _databaseService.SelectAsync<Owner>(new { Status = (string)null });
                    _logger.LogInformation($"Found {nullStatusOwners.Count()} null status owners");
                    allOwners.AddRange(nullStatusOwners);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error fetching null status owners for userId: {userId}");
                }
                
                _logger.LogInformation($"Total owners fetched before authorization: {allOwners.Count}");

                // Apply search filter if needed
                if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                {
                    allOwners = allOwners.Where(o => o.Name.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                // Apply pagination manually
                var totalRecords = allOwners.Count();
                var pagedOwners = allOwners
                    .Skip((int)request.PageOffset * (int)request.PageLimit)
                    .Take((int)request.PageLimit)
                    .ToList();

                // Create a mock paged result to maintain existing code structure
                var pagedResult = new { Records = pagedOwners, TotalRecords = totalRecords };

                // Manual mapping to OwnerDto list with individual authorization
                var ownerDtos = new List<OwnerDto>();
                foreach (var ownerRecord in pagedResult.Records)
                {
                    // Check authorization for each individual owner
                    try
                    {
                        var authDto = new IsAuthorizedDto<Owner>
                        {
                            UserId = userId,
                            PermissionToCheck = new PermissionToCheck
                            {
                                OperationActionId = OperationAction.Read
                            },
                            Entity = ownerRecord
                        };
                        await _authorizationService.IsAuthorized(authDto);

                        // Get company details for this owner
                        CompanyDto companyDto = null;
                        if (ownerRecord.CompanyId.HasValue)
                        {
                            try
                            {
                                var company = await _databaseService.SelectByIdAsync<Company, Guid>(ownerRecord.CompanyId.Value);
                                if (company != null)
                                {
                                    companyDto = new CompanyDto
                                    {
                                        Id = company.Id,
                                        Name = company.Name,
                                        Location = company.Location,
                                        Industry = company.Industry,
                                        Status = company.Status,
                                        CustomerId = company.CustomerId,
                                        LastUpdated = company.LastUpdated,
                                        Created = company.Created,
                                        Changed = company.Changed
                                    };
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, $"Warning: Could not fetch company details for Owner {ownerRecord.Id}. UserId: {userId}");
                            }
                        }

                        // If authorized, add to result
                        ownerDtos.Add(new OwnerDto
                        {
                            Id = ownerRecord.Id,
                            Name = ownerRecord.Name,
                            Email = ownerRecord.Email,
                            Phone = ownerRecord.Phone,
                            Address = ownerRecord.Address,
                            CompanyId = ownerRecord.CompanyId,
                            Company = companyDto,
                            Status = ownerRecord.Status,
                            LastUpdated = ownerRecord.LastUpdated,
                            Created = ownerRecord.Created,
                            Changed = ownerRecord.Changed
                        });
                    }
                    catch (Exception)
                    {
                        // If not authorized, skip this owner
                        _logger.LogDebug($"User {userId} not authorized to view owner {ownerRecord.Id}");
                    }
                }

                return new ReturnListOwnerDto
                {
                    Data = ownerDtos,
                    Metadata = new MetadataDto
                    {
                        PageLimit = request.PageLimit,
                        PageOffset = request.PageOffset,
                        Total = pagedResult.TotalRecords
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DP-500: Technical Error. Error while fetching owners list. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}