using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class OwnerService
    {
        public async Task<bool> Delete(DeleteOwnerDto request, Guid userId)
        {
            // 1. Validate Input Parameters
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Request or Id is null. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            // 2. <PERSON><PERSON> and Validate Owner
            Owner existingOwner;
            try
            {
                existingOwner = await _databaseService.SelectByIdAsync<Owner, Guid>(request.Id.Value);
                if (existingOwner == null)
                {
                    _logger.LogInformation($"DP-404: Owner not found with Id: {request.Id}. UserId: {userId}");
                    throw new BusinessException("DP-404", "Owner not found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DP-500: Technical Error. Error while fetching Owner with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            // 3. Authorization Check
            var authDto = new IsAuthorizedDto<Owner>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Delete
                },
                Entity = existingOwner
            };

            await _authorizationService.IsAuthorized(authDto);

            // 4. Check if owner has vessels assigned
            var ownedVessels = await _databaseService.SelectAsync<Vessel>(new { OwnerId = request.Id });
            if (ownedVessels != null && ownedVessels.Any())
            {
                _logger.LogInformation($"DP-409: Business Error. Cannot delete owner with Id: {request.Id} because it has {ownedVessels.Count()} vessels assigned. UserId: {userId}");
                throw new BusinessException("DP-409", "Cannot delete owner with assigned vessels");
            }

            // 5. Delete Owner
            try
            {
                await _databaseService.DeleteAsync<Owner>(existingOwner);
                
                // Send notifications for owner deletion
                await _entityNotificationService.NotifyEntityDeleted("Owner", existingOwner.Id, existingOwner.Name, existingOwner.CompanyId, userId);
                
                _logger.LogInformation($"Owner with Id: {request.Id} successfully deleted. UserId: {userId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DP-500: Technical Error. Error while deleting Owner with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}