using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class OwnerService
    {
        public async Task<OwnerDto> Create(CreateOwnerDto request, Guid userId)
        {
            if (request == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Request is null. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            if (string.IsNullOrWhiteSpace(request.Name))
            {
                _logger.LogInformation($"DP-422: Client Error. Owner name is required. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            if (request.CompanyId == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Company is required. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            var owner = new Owner();
            var authorizationDto = new IsAuthorizedDto<Owner>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Create
                },
                Entity = owner
            };

            await _authorizationService.IsAuthorized(authorizationDto);

            // Verify that the company exists
            var existingCompany = await _databaseService.SelectAsync<Company>(new { Id = request.CompanyId });
            if (existingCompany == null || !existingCompany.Any())
            {
                _logger.LogInformation($"DP-404: Business Error. Company with ID {request.CompanyId} not found. UserId: {userId}");
                throw new BusinessException("DP-404", "Company not found");
            }

            // Manual mapping from CreateOwnerDto to Owner
            owner = new Owner
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Email = request.Email,
                Phone = request.Phone,
                Address = request.Address,
                CompanyId = request.CompanyId,
                Status = request.Status ?? "Active",
                LastUpdated = DateTime.UtcNow,
                Created = DateTime.UtcNow,
                Changed = null
            };

            try
            {
                await _databaseService.InsertAsync<Owner>(owner);
                
                // Send notifications for owner creation
                await _entityNotificationService.NotifyEntityCreated("Owner", owner.Id, owner.Name, owner.CompanyId, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DP-500: Technical Error. Error while creating Owner with Id: {owner.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            // Get company details for the response
            var company = existingCompany.First();
            var companyDto = new CompanyDto
            {
                Id = company.Id,
                Name = company.Name,
                Location = company.Location,
                Industry = company.Industry,
                Status = company.Status,
                CustomerId = company.CustomerId,
                LastUpdated = company.LastUpdated,
                Created = company.Created,
                Changed = company.Changed
            };

            // Manual mapping from Owner to OwnerDto
            return new OwnerDto
            {
                Id = owner.Id,
                Name = owner.Name,
                Email = owner.Email,
                Phone = owner.Phone,
                Address = owner.Address,
                CompanyId = owner.CompanyId,
                Company = companyDto,
                Status = owner.Status,
                LastUpdated = owner.LastUpdated,
                Created = owner.Created,
                Changed = owner.Changed
            };
        }
    }
}