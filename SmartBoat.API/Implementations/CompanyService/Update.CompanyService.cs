using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class CompanyService
    {
        public async Task<CompanyDto> Update(UpdateCompanyDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var company = await _databaseService.SelectByIdAsync<Company, Guid>(request.Id.Value);
            if (company == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Entity not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Company not found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Company>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = company
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            // Check for duplicate name (excluding current company)
            if (!string.IsNullOrWhiteSpace(request.Name))
            {
                try
                {
                    var existingCompaniesWithName = await _databaseService.SelectAsync<Company>(new { Name = request.Name });
                    // Allow the same name if it belongs to the current company being updated
                    var duplicateCompany = existingCompaniesWithName?.FirstOrDefault(c => c.Id != company.Id);
                    if (duplicateCompany != null)
                    {
                        _logger.LogInformation($"DP-409: Conflict Error. Duplicate company name: {request.Name} (used by company ID: {duplicateCompany.Id}, current company ID: {company.Id}). UserId: {userId}");
                        throw new BusinessException("DP-409", "Company with this name already exists");
                    }
                }
                catch (BusinessException)
                {
                    throw; // Re-throw business exceptions without wrapping
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"DP-500: Technical Error. Error while checking name uniqueness. UserId: {userId}");
                    throw new TechnicalException("DP-500", "Technical Error");
                }
            }

            // Manual mapping from UpdateCompanyDto to Company
            company.Name = request.Name ?? company.Name;
            company.Location = request.Location ?? company.Location;
            company.Industry = request.Industry ?? company.Industry;
            company.Status = request.Status ?? company.Status;
            company.CustomerId = request.CustomerId ?? company.CustomerId;
            company.LastUpdated = DateTime.UtcNow;
            company.Changed = DateTime.UtcNow;

            try
            {
                await _databaseService.UpdateAsync<Company>(company, new { Id = company.Id });
                
                // Send notifications for company update
                await _entityNotificationService.NotifyEntityUpdated("Company", company.Id, company.Name, company.Id, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DP-500: Technical Error. Error while updating Company with Id: {company.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            // Manual mapping from Company to CompanyDto
            return new CompanyDto
            {
                Id = company.Id,
                Name = company.Name,
                Location = company.Location,
                Industry = company.Industry,
                Status = company.Status,
                CustomerId = company.CustomerId,
                LastUpdated = company.LastUpdated,
                Created = company.Created,
                Changed = company.Changed
            };
        }
    }
}