using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class CompanyService
    {
        public async Task<CompanyDto> Get(CompanyRequestDto request, Guid userId)
        {
            // 1. Validate Input Parameters
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Request or Id is null. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            // 2. Fetch and Validate Company
            Company company;
            try
            {
                company = await _databaseService.SelectByIdAsync<Company, Guid>(request.Id.Value);
                if (company == null)
                {
                    _logger.LogInformation($"DP-404: Company not found with Id: {request.Id}. UserId: {userId}");
                    throw new BusinessException("DP-404", "Company not found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Company with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            // 3. Authorization Check
            var authDto = new IsAuthorizedDto<Company>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Read
                },
                Entity = company
            };

            await _authorizationService.IsAuthorized(authDto);

            // 4. Return the Result
            try
            {
                return new CompanyDto
                {
                    Id = company.Id,
                    Name = company.Name,
                    Location = company.Location,
                    Industry = company.Industry,
                    Status = company.Status,
                    CustomerId = company.CustomerId,
                    LastUpdated = company.LastUpdated,
                    Created = company.Created,
                    Changed = company.Changed
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while mapping Company with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}