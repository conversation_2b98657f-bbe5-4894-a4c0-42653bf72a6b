using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CompanyService
    {
        public async Task<ReturnListCompanyDto> GetList(ListCompanyRequestDto request, Guid userId)
        {
            // Validate Input Parameters
            if (request == null || request.PageLimit == null || request.PageLimit <= 0)
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            try
            {
                // Apply Filters and Fetch Companies
                var pagedQuery = new PagedQuery
                {
                    PageNumber = (int)request.PageOffset + 1,
                    PageSize = (int)request.PageLimit,
                    OrderOptions = new OrderOptions
                    {
                        OrderColumn = string.IsNullOrWhiteSpace(request.SortField) ? "Created" : request.SortField,
                        OrderDirection = string.IsNullOrWhiteSpace(request.SortOrder) ? "desc" : request.SortOrder
                    }
                };

                var filters = new Dictionary<string, object>();
                if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                {
                    filters.Add("Name", request.SearchTerm);
                }

                var pagedResult = await _autoCodeDbOperationsService.SelectPagedWithFiltersAsync<Company>(pagedQuery, filters);

                // Manual mapping to CompanyDto list with individual authorization
                var companyDtos = new List<CompanyDto>();
                foreach (var companyRecord in pagedResult.Records)
                {
                    // Check authorization for each individual company
                    try
                    {
                        var authDto = new IsAuthorizedDto<Company>
                        {
                            UserId = userId,
                            PermissionToCheck = new PermissionToCheck
                            {
                                OperationActionId = OperationAction.Read
                            },
                            Entity = companyRecord
                        };
                        await _authorizationService.IsAuthorized(authDto);

                        // If authorized, add to result
                        companyDtos.Add(new CompanyDto
                        {
                            Id = companyRecord.Id,
                            Name = companyRecord.Name,
                            Location = companyRecord.Location,
                            Industry = companyRecord.Industry,
                            Status = companyRecord.Status,
                            CustomerId = companyRecord.CustomerId,
                            LastUpdated = companyRecord.LastUpdated,
                            Created = companyRecord.Created,
                            Changed = companyRecord.Changed
                        });
                    }
                    catch (Exception)
                    {
                        // If not authorized, skip this company
                        continue;
                    }
                }

                // Return the Result
                return new ReturnListCompanyDto
                {
                    Data = companyDtos,
                    Metadata = new MetadataDto
                    {
                        PageLimit = request.PageLimit,
                        PageOffset = request.PageOffset,
                        Total = pagedResult.TotalRecords
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching companies. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}