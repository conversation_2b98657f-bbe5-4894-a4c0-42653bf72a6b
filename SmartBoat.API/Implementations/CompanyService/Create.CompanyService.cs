using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CompanyService
    {
        public async Task<CompanyDto> Create(CreateCompanyDto request, Guid userId)
        {
            if (request == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Request is null. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            if (string.IsNullOrWhiteSpace(request.Name))
            {
                _logger.LogInformation($"DP-422: Client Error. Company name is required. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            var company = new Company();
            var authorizationDto = new IsAuthorizedDto<Company>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Create
                },
                Entity = company
            };

            await _authorizationService.IsAuthorized(authorizationDto);

            var existingCompany = await _databaseService.SelectAsync<Company>(new { request.Name });
            if (existingCompany != null && existingCompany.Any())
            {
                _logger.LogInformation($"DP-409: Business Error. Company with name {request.Name} already exists. UserId: {userId}");
                throw new BusinessException("DP-409", "Company with this name already exists");
            }

            // Manual mapping from CreateCompanyDto to Company
            company = new Company
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Location = request.Location,
                Industry = request.Industry,
                Status = request.Status,
                CustomerId = request.CustomerId,
                LastUpdated = DateTime.UtcNow,
                Created = DateTime.UtcNow,
                Changed = null
            };

            try
            {
                await _databaseService.InsertAsync<Company>(company);
                
                // Send notifications for company creation
                await _entityNotificationService.NotifyEntityCreated("Company", company.Id, company.Name, company.Id, userId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while creating Company with Id: {company.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            // Manual mapping from Company to CompanyDto
            return new CompanyDto
            {
                Id = company.Id,
                Name = company.Name,
                Location = company.Location,
                Industry = company.Industry,
                Status = company.Status,
                CustomerId = company.CustomerId,
                LastUpdated = company.LastUpdated,
                Created = company.Created,
                Changed = company.Changed
            };
        }
    }
}