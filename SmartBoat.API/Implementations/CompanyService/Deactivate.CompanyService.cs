using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class CompanyService
    {
        public async Task<CompanyDto> Deactivate(CompanyRequestDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new TechnicalException("DP-422", "Client Error");
            }

            var company = await _databaseService.SelectByIdAsync<Company, Guid>(request.Id.Value);
            if (company == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Company not found with Id: {request.Id}. UserId: {userId}");
                throw new BusinessException("DP-404", "Company not found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Company>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = company
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            company.Status = "Inactive";
            await _databaseService.UpdateAsync<Company>(company, new { Id = company.Id });

            return new CompanyDto
            {
                Id = company.Id,
                Name = company.Name,
                Location = company.Location,
                Industry = company.Industry,
                Status = company.Status,
                CustomerId = company.CustomerId,
                LastUpdated = company.LastUpdated,
                Created = company.Created,
                Changed = company.Changed
            };
        }
    }
}