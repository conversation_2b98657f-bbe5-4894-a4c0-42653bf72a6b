using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CompanyService
    {
        public async Task<bool> Delete(DeleteCompanyDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Company company = null;
            try
            {
                company = await _databaseService.SelectByIdAsync<Company, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Company with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (company == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Company not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var authDto = new IsAuthorizedDto<Company>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Delete
                },
                Entity = company
            };
            await _authorizationService.IsAuthorized(authDto);

            try
            {
                // Store company name for notification before deletion
                var companyName = company.Name;
                var companyId = company.Id;
                
                // Get all vessels for this company
                var vessels = await _databaseService.SelectAsync<Vessel>(new { CompanyId = company.Id });
                
                // Delete all sensors and path points for each vessel
                if (vessels != null)
                {
                    foreach (var vessel in vessels)
                    {
                        await _databaseService.DeleteAsync<Sensor>(new { VesselId = vessel.Id });
                        await _databaseService.DeleteAsync<VesselPathPoint>(new { VesselId = vessel.Id });
                    }
                    
                    // Delete all vessels for this company
                    await _databaseService.DeleteAsync<Vessel>(new { CompanyId = company.Id });
                }
                
                // Delete the company itself
                await _databaseService.DeleteAsync<Company>(new { Id = company.Id });

                // Send notifications for company deletion
                await _entityNotificationService.NotifyEntityDeleted("Company", companyId, companyName, companyId, userId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while deleting Company with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}