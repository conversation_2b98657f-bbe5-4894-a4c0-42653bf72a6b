using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class VesselService
    {
        public async Task<string> Update(UpdateVesselDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var updatableFields = new List<string> { "Name", "Number", "Type", "Location", "Status", "StartDate", "EndDate", "Image", "Onsigners", "Offsigners", "CompanyId" };
            var hasUpdatableField = updatableFields.Any(field => request.GetType().GetProperty(field)?.GetValue(request) != null);

            if (!hasUpdatableField)
            {
                _logger.LogInformation($"DP-422: Client Error. No updatable fields provided. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Vessel vessel = null;
            try
            {
                var vessels = await _databaseService.SelectAsync<Vessel>(new { Id = request.Id });
                vessel = vessels.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Vessel with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (vessel == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Vessel not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Vessel>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update },
                Entity = vessel
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            if (!string.IsNullOrWhiteSpace(request.Number))
            {
                var existingVessel = await _databaseService.SelectAsync<Vessel>(new { Number = request.Number });
                if (existingVessel.Any() && existingVessel.First().Id != request.Id)
                {
                    _logger.LogInformation($"DP-422: Client Error. Vessel Number is not unique. UserId: {userId}");
                    throw new BusinessException("DP-422", "Client Error");
                }
            }

            if (request.CompanyId != null)
            {
                try
                {
                    var company = await _companyService.Get(new CompanyRequestDto { Id = request.CompanyId }, userId);
                    if (company == null)
                    {
                        _logger.LogInformation($"DP-404: Technical Error. Company not found with Id: {request.CompanyId}. UserId: {userId}");
                        throw new TechnicalException("DP-404", "Technical Error");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation($"DP-500: Technical Error. Error while fetching Company with Id: {request.CompanyId}. UserId: {userId}");
                    throw new TechnicalException("DP-500", "Technical Error");
                }
            }

            vessel.Name = request.Name ?? vessel.Name;
            vessel.Number = request.Number ?? vessel.Number;
            vessel.Type = request.Type ?? vessel.Type;
            vessel.Location = request.Location ?? vessel.Location;
            vessel.Status = request.Status ?? vessel.Status;
            vessel.StartDate = request.StartDate ?? vessel.StartDate;
            vessel.EndDate = request.EndDate ?? vessel.EndDate;
            vessel.Image = request.Image ?? vessel.Image;
            vessel.Onsigners = request.Onsigners ?? vessel.Onsigners;
            vessel.Offsigners = request.Offsigners ?? vessel.Offsigners;
            vessel.CompanyId = request.CompanyId ?? vessel.CompanyId;
            vessel.LastUpdated = DateTime.Now;
            vessel.Changed = DateTime.Now;

            try
            {
                await _databaseService.UpdateAsync<Vessel>(vessel, new { Id = request.Id });
                var updatedVessels = await _databaseService.SelectAsync<Vessel>(new { Id = request.Id });
                vessel = updatedVessels.FirstOrDefault();
                
                // Send notifications for vessel update
                await _entityNotificationService.NotifyEntityUpdated("Vessel", vessel.Id.Value, vessel.Name, vessel.CompanyId, userId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while updating Vessel with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (vessel == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Vessel not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            return vessel.Id.ToString();
        }
    }
}