using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class VesselService
    {
        public async Task<VesselDto> Get(DeleteVesselDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Vessel vessel = null;
            try
            {
                vessel = await _databaseService.SelectByIdAsync<Vessel, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Vessel with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (vessel == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Vessel not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Vessel>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = vessel
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            CompanyDto company = null;
            try
            {
                var companyRequest = new CompanyRequestDto { Id = vessel.CompanyId };
                company = await _companyService.Get(companyRequest, userId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"Error while fetching Company for Vessel with Id: {request.Id}. UserId: {userId}");
            }

            List<SensorDto> sensors = new List<SensorDto>();
            try
            {
                var sensorList = await _sensorService.GetListByVessel(vessel.Id.Value);
                if (sensorList != null)
                {
                    sensors = sensorList.ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"Error while fetching Sensors for Vessel with Id: {request.Id}. UserId: {userId}");
            }

            List<VesselPathPointDto> pathPoints = new List<VesselPathPointDto>();
            try
            {
                var pathPointsList = await _databaseService.SelectAsync<VesselPathPoint>(new { VesselId = vessel.Id });
                if (pathPointsList != null)
                {
                    pathPoints = pathPointsList.Select(p => new VesselPathPointDto
                    {
                        Id = p.Id,
                        VesselId = p.VesselId,
                        Lat = p.Lat,
                        Lng = p.Lng,
                        Timestamp = p.Timestamp,
                        Location = p.Location,
                        Created = p.Created,
                        Changed = p.Changed
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"Error while fetching PathPoints for Vessel with Id: {request.Id}. UserId: {userId}");
            }

            var vesselDto = new VesselDto
            {
                Id = vessel.Id,
                Name = vessel.Name,
                Number = vessel.Number,
                Type = vessel.Type,
                Location = vessel.Location,
                Status = vessel.Status,
                StartDate = vessel.StartDate,
                EndDate = vessel.EndDate,
                Image = vessel.Image,
                Onsigners = vessel.Onsigners,
                Offsigners = vessel.Offsigners,
                Company = company,
                Sensors = sensors,
                PathPoints = pathPoints,
                LastUpdated = vessel.LastUpdated,
                Created = vessel.Created,
                Changed = vessel.Changed
            };

            return vesselDto;
        }
    }
}