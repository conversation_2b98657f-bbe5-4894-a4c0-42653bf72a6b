using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class VesselService : IVesselService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly ILogger<VesselService> _logger;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;
        private readonly ICompanyService _companyService;
        private readonly ISensorService _sensorService;
        private readonly IEntityNotificationService _entityNotificationService;

        public VesselService(
            IAuthorizationService authorizationService,
            ILogger<VesselService> logger,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService,
            ICompanyService companyService,
            ISensorService sensorService,
            IEntityNotificationService entityNotificationService)
        {
            _authorizationService = authorizationService;
            _logger = logger;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
            _companyService = companyService;
            _sensorService = sensorService;
            _entityNotificationService = entityNotificationService;
        }
    }
}