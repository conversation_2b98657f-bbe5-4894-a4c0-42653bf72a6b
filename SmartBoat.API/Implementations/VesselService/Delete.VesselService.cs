using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class VesselService
    {
        public async Task<bool> Delete(DeleteVesselDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Vessel vessel = null;
            try
            {
                vessel = await _databaseService.SelectByIdAsync<Vessel, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Vessel with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }

            if (vessel == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Vessel not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var authDto = new IsAuthorizedDto<Vessel>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Delete
                },
                Entity = vessel
            };
            await _authorizationService.IsAuthorized(authDto);

            try
            {
                // Store vessel info for notification before deletion
                var vesselName = vessel.Name;
                var vesselId = vessel.Id.Value;
                var companyId = vessel.CompanyId;
                
                if (request.FieldsToDelete == null)
                {
                    // First, delete all sensors associated with this vessel
                    await _databaseService.DeleteAsync<Sensor>(new { VesselId = vessel.Id });
                    
                    // Then delete other related entities and the vessel itself
                    var relatedEntities = new Dictionary<Type, object>
                    {
                        { typeof(VesselPathPoint), new { VesselId = vessel.Id } }
                    };

                    await _databaseService.DeleteWithMultipleRelatedTypesAsync<Vessel>(
                        mainEntityFilter: new { Id = vessel.Id },
                        relatedEntityFilters: relatedEntities);
                        
                    // Send notifications for vessel deletion
                    await _entityNotificationService.NotifyEntityDeleted("Vessel", vesselId, vesselName, companyId, userId);
                }
                else
                {
                    var fieldsToNullify = request.FieldsToDelete
                        .Where(f => f != "Name" && f != "Number" && f != "CompanyId")
                        .ToList();

                    if (fieldsToNullify.Any())
                    {
                        await _databaseService.NullifyColumnsAsync<Vessel>(
                            fieldsToNullify,
                            new { Id = vessel.Id });
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while deleting Vessel with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}