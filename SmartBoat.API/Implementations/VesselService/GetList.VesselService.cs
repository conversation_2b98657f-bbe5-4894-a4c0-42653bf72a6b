using SmartBoat.API.ControllersExceptions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class VesselService
    {
        public async Task<ReturnListVesselDto> GetList(ListVesselRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid pagination parameters. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            var filters = new Dictionary<string, object>();
            if (!string.IsNullOrWhiteSpace(request.Name)) filters.Add("Name", request.Name);
            if (!string.IsNullOrWhiteSpace(request.Number)) filters.Add("Number", request.Number);
            if (!string.IsNullOrWhiteSpace(request.Type)) filters.Add("Type", request.Type);
            if (!string.IsNullOrWhiteSpace(request.Status)) filters.Add("Status", request.Status);
            if (request.CompanyId != null) filters.Add("CompanyId", request.CompanyId);

            try
            {
                var query = new PagedQuery
                {
                    PageSize = (int)request.PageLimit,
                    Offset = request.PageOffset,
                    Search = request.SearchTerm,
                    OrderOptions = new OrderOptions
                    {
                        OrderColumn = string.IsNullOrWhiteSpace(request.SortField) ? "Created" : request.SortField,
                        OrderDirection = string.IsNullOrWhiteSpace(request.SortOrder) ? "desc" : request.SortOrder
                    }
                };

                var pagedResult = await _autoCodeDbOperationsService.SelectPagedWithFiltersAsync<Vessel>(query, filters);

                var vesselDtos = new List<VesselDto>();
                foreach (var record in pagedResult.Records)
                {
                    // Check authorization for each individual vessel
                    try
                    {
                        var authorizationDto = new IsAuthorizedDto<Vessel>
                        {
                            UserId = userId,
                            PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                            Entity = record
                        };
                        await _authorizationService.IsAuthorized(authorizationDto);

                        // If authorized, process the vessel
                        var vesselDto = new VesselDto();
                        CompanyDto company = null;

                        try
                        {
                            company = await _companyService.Get(new CompanyRequestDto { Id = record.CompanyId }, userId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogInformation($"Error fetching company for vessel {record.Id}. UserId: {userId}");
                        }

                        var sensors = new List<SensorDto>();
                        try
                        {
                            sensors = await _sensorService.GetListByVessel(record.Id.Value);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogInformation($"Error fetching sensors for vessel {record.Id}. UserId: {userId}");
                        }

                        vesselDto = new VesselDto
                        {
                            Id = record.Id,
                            Name = record.Name,
                            Number = record.Number,
                            Type = record.Type,
                            Location = record.Location,
                            Status = record.Status,
                            StartDate = record.StartDate,
                            EndDate = record.EndDate,
                            Image = record.Image,
                            Onsigners = record.Onsigners,
                            Offsigners = record.Offsigners,
                            Company = company,
                            Sensors = sensors,
                            LastUpdated = record.LastUpdated,
                            Created = record.Created,
                            Changed = record.Changed
                        };

                        vesselDtos.Add(vesselDto);
                    }
                    catch (Exception)
                    {
                        // If not authorized, skip this vessel
                        continue;
                    }
                }

                return new ReturnListVesselDto
                {
                    Data = vesselDtos,
                    Metadata = new MetadataDto
                    {
                        PageLimit = (int)request.PageLimit,
                        PageOffset = request.PageOffset,
                        Total = pagedResult.TotalRecords
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching vessels. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}