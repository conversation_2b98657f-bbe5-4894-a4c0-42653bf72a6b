using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Implementations.AlertService
{
    public partial class AlertService
    {
        public async Task<bool> Delete(DeleteAlertDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Invalid request parameters. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Invalid request parameters");
            }

            Alert alert = null;
            try
            {
                alert = await _databaseService.SelectByIdAsync<Alert, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Alert with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }

            if (alert == null)
            {
                _logger.LogInformation($"DP-404: Resource not found. Alert not found with Id: {request.Id}. UserId: {userId}");
                throw new BusinessException("DP-404", "Resource not found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Alert>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Delete },
                Entity = alert
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                if (request.FieldsToDelete == null)
                {
                    await _databaseService.DeleteAsync<Alert>(new { Id = request.Id });
                }
                else
                {
                    await _databaseService.NullifyColumnsAsync<Alert>(request.FieldsToDelete, new { Id = request.Id });
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while deleting Alert with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }
        }
    }
}