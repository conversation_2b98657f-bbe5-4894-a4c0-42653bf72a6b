using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Implementations.AlertService
{
    public partial class AlertService
    {
        public async Task<string> Update(UpdateAlertDto request, Guid userId)
        {
            if (request == null || request.Id == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Alert alert;
            try
            {
                alert = await _databaseService.SelectByIdAsync<Alert, Guid>(request.Id.Value);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Alert with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }

            if (alert == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Alert not found with Id: {request.Id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Resource not found");
            }

            var authorizationDto = new IsAuthorizedDto<Alert>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck
                {
                    OperationActionId = OperationAction.Update
                },
                Entity = alert
            };
            await _authorizationService.IsAuthorized(authorizationDto);

            if (request.Type != null) alert.Type = request.Type;
            if (request.EntityId != null) alert.EntityId = request.EntityId;
            if (request.EntityType != null) alert.EntityType = request.EntityType;
            if (request.Value != null) alert.Value = request.Value;
            if (request.Threshold != null) alert.Threshold = request.Threshold;
            if (request.Status != null) alert.Status = request.Status;
            if (request.Message != null) alert.Message = request.Message;
            if (request.Timestamp != null) alert.Timestamp = request.Timestamp;
            if (request.DeliveryStatus != null) alert.DeliveryStatus = request.DeliveryStatus;
            alert.Changed = DateTime.Now;

            try
            {
                await _databaseService.UpdateAsync<Alert>(alert, new { Id = alert.Id });
                var updatedAlert = await _databaseService.SelectByIdAsync<Alert, Guid>(alert.Id.Value);

                if (updatedAlert == null)
                {
                    _logger.LogInformation($"DP-404: Technical Error. Alert not found after update with Id: {alert.Id}. UserId: {userId}");
                    throw new TechnicalException("DP-404", "Resource not found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while updating Alert with Id: {alert.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }

            return alert.Id.ToString();
        }
    }
}