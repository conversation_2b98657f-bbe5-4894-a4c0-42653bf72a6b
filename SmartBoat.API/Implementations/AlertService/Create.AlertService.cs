using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Implementations.AlertService
{
    public partial class AlertService
    {
        public async Task<string> Create(CreateAlertDto request, Guid userId)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Type) || request.EntityId == null || string.IsNullOrWhiteSpace(request.EntityType) || string.IsNullOrWhiteSpace(request.Status) || string.IsNullOrWhiteSpace(request.Message))
            {
                _logger.LogInformation($"DP-422: Invalid request parameters. UserId: {userId}");
                throw new BusinessException("DP-422", "Invalid request parameters");
            }

            Alert alert = new Alert
            {
                Id = Guid.NewGuid(),
                Type = request.Type,
                EntityId = request.EntityId,
                EntityType = request.EntityType,
                Value = request.Value,
                Threshold = request.Threshold,
                Status = request.Status,
                Message = request.Message,
                Timestamp = DateTime.Now,
                DeliveryStatus = request.DeliveryStatus,
                Created = DateTime.Now,
                Changed = null
            };

            var isAuthorizedDto = new IsAuthorizedDto<Alert>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = alert
            };

            await _authorizationService.IsAuthorized(isAuthorizedDto);

            try
            {
                await _databaseService.InsertAsync<Alert>(alert);
                var retrievedAlert = await _databaseService.SelectByIdAsync<Alert, Guid>(alert.Id.Value);
                if (retrievedAlert == null)
                {
                    _logger.LogInformation($"DP-404: Resource not found. AlertId: {alert.Id}. UserId: {userId}");
                    throw new BusinessException("DP-404", "Resource not found");
                }
                return retrievedAlert.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Internal server error. Error while creating Alert with Id: {alert.Id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }
        }
    }
}