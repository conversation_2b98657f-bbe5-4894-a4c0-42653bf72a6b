using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Implementations.AlertService
{
    public partial class AlertService
    {
        public async Task<ReturnListAlertDto> GetList(ListAlertRequestDto request, Guid userId)
        {
            if (request == null || request.PageLimit == null || request.PageLimit <= 0 || request.PageOffset == null || request.PageOffset < 0)
            {
                _logger.LogInformation($"DP-422: Invalid request parameters. PageLimit: {request?.PageLimit}, PageOffset: {request?.PageOffset}. UserId: {userId}");
                throw new BusinessException("DP-422", "Invalid request parameters");
            }

            var filters = new Dictionary<string, object>();
            if (request.Type != null) filters.Add("Type", request.Type);
            if (request.EntityId != null) filters.Add("EntityId", request.EntityId);
            if (request.Status != null) filters.Add("Status", request.Status);
            if (request.DeliveryStatus != null) filters.Add("DeliveryStatus", request.DeliveryStatus);
            if (request.DateFrom != null) filters.Add("DateFrom", request.DateFrom);
            if (request.DateTo != null) filters.Add("DateTo", request.DateTo);

            var query = new PagedQuery
            {
                PageSize = (int)request.PageLimit,
                Offset = request.PageOffset,
                Search = request.SearchTerm,
                OrderOptions = new OrderOptions
                {
                    OrderColumn = !string.IsNullOrWhiteSpace(request.SortField) ? request.SortField : "Created",
                    OrderDirection = !string.IsNullOrWhiteSpace(request.SortOrder) ? request.SortOrder : "desc"
                }
            };

            Nbg.NetCore.DatabaseService.PagedResult<Alert> pagedResult;
            try
            {
                pagedResult = await _autoCodeDbOperationsService.SelectPagedWithFiltersAsync<Alert>(query, filters);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Alerts. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }

            var alertDtos = new List<AlertDto>();
            foreach (var record in pagedResult.Records)
            {
                // Check authorization for each individual alert
                try
                {
                    var isAuthorizedDto = new IsAuthorizedDto<Alert>
                    {
                        UserId = userId,
                        PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                        Entity = record
                    };
                    await _authorizationService.IsAuthorized(isAuthorizedDto);

                    // If authorized, add to result
                    var alertDto = new AlertDto
                    {
                        Id = record.Id,
                        Type = record.Type,
                        EntityId = record.EntityId,
                        EntityType = record.EntityType,
                        Value = record.Value,
                        Threshold = record.Threshold,
                        Status = record.Status,
                        Message = record.Message,
                        Timestamp = record.Timestamp,
                        DeliveryStatus = record.DeliveryStatus,
                        Created = record.Created,
                        Changed = record.Changed
                    };
                    alertDtos.Add(alertDto);
                }
                catch (Exception)
                {
                    // If not authorized, skip this alert
                    continue;
                }
            }

            var returnListAlertDto = new ReturnListAlertDto
            {
                Data = alertDtos,
                Metadata = new MetadataDto
                {
                    PageLimit = request.PageLimit,
                    PageOffset = request.PageOffset,
                    Total = pagedResult.TotalRecords
                }
            };

            return returnListAlertDto;
        }
    }
}