using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Implementations.AlertService
{
    public partial class AlertService
    {
        public async Task<AlertDto> Get(Guid id, Guid userId)
        {
            if (id == null)
            {
                _logger.LogInformation($"DP-422: Invalid request parameters. Id is null. UserId: {userId}");
                throw new BusinessException("DP-422", "Invalid request parameters");
            }

            Alert alert = null;
            try
            {
                alert = await _databaseService.SelectByIdAsync<Alert, Guid>(id);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while fetching Alert with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-500", "Internal server error");
            }

            if (alert == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Alert not found with Id: {id}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Resource not found");
            }

            var isAuthorizedDto = new IsAuthorizedDto<Alert>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Read },
                Entity = alert
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            var alertDto = new AlertDto
            {
                Id = alert.Id,
                Type = alert.Type,
                EntityId = alert.EntityId,
                EntityType = alert.EntityType,
                Value = alert.Value,
                Threshold = alert.Threshold,
                Status = alert.Status,
                Message = alert.Message,
                Timestamp = alert.Timestamp,
                DeliveryStatus = alert.DeliveryStatus,
                Created = alert.Created,
                Changed = alert.Changed
            };

            return alertDto;
        }
    }
}