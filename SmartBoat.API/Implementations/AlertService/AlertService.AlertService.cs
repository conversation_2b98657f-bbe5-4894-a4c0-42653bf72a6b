using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Implementations.AlertService
{
    public partial class AlertService : IAlertService
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly ILogger<AlertService> _logger;
        private readonly IDatabaseService _databaseService;
        private readonly IAutoCodeDbOperationsService _autoCodeDbOperationsService;

        public AlertService(
            IAuthorizationService authorizationService,
            ILogger<AlertService> logger,
            IDatabaseService databaseService,
            IAutoCodeDbOperationsService autoCodeDbOperationsService)
        {
            _authorizationService = authorizationService;
            _logger = logger;
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _autoCodeDbOperationsService = autoCodeDbOperationsService;
        }
    }
}