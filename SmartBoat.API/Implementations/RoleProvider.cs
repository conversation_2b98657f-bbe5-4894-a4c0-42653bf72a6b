using System.Data;
using Dapper;
using PPG.Auth.Interfaces;

namespace SmartBoat.API.Implementations
{
    /// <summary>
    /// Implementation of IRoleProvider that bridges main application's Role data to Authorization module
    /// </summary>
    public class RoleProvider : IRoleProvider
    {
        private readonly IDbConnectionFactory _dbConnectionFactory;

        public RoleProvider(IDbConnectionFactory dbConnectionFactory)
        {
            _dbConnectionFactory = dbConnectionFactory ?? throw new ArgumentNullException(nameof(dbConnectionFactory));
        }

        public async Task<bool> RolesExistAsync(IEnumerable<Guid> roleIds)
        {
            if (!roleIds.Any()) return true;

            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            
            var roleIdList = roleIds.ToList();
            var existingCount = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Role WHERE Id IN @RoleIds",
                new { RoleIds = roleIdList });

            return existingCount == roleIdList.Count;
        }

        public async Task<Dictionary<Guid, string>> GetRoleNamesAsync(IEnumerable<Guid> roleIds)
        {
            if (!roleIds.Any()) return new Dictionary<Guid, string>();

            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            
            var roles = await connection.QueryAsync<(Guid Id, string Name)>(
                "SELECT Id, Name FROM Role WHERE Id IN @RoleIds",
                new { RoleIds = roleIds.ToList() });

            return roles.ToDictionary(r => r.Id, r => r.Name);
        }

        public async Task<Guid?> GetRoleIdAsync(string roleName)
        {
            if (string.IsNullOrEmpty(roleName)) return null;

            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            
            var roleId = await connection.QuerySingleOrDefaultAsync<Guid?>(
                "SELECT Id FROM Role WHERE Name = @RoleName",
                new { RoleName = roleName });

            return roleId;
        }

        public async Task<bool> RoleExistsAsync(string roleName)
        {
            if (string.IsNullOrEmpty(roleName)) return false;

            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            
            var exists = await connection.QuerySingleAsync<bool>(
                "SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END FROM Role WHERE Name = @RoleName",
                new { RoleName = roleName });

            return exists;
        }
    }
}