using SmartBoat.API.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace SmartBoat.API.Implementations
{
    /// <summary>
    /// Factory implementation for creating IVesselService instances
    /// This breaks the circular dependency between SensorService and VesselService
    /// </summary>
    public class VesselServiceFactory : IVesselServiceFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public VesselServiceFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Creates an instance of IVesselService using the service provider
        /// </summary>
        /// <returns>IVesselService instance</returns>
        public IVesselService CreateVesselService()
        {
            return _serviceProvider.GetRequiredService<IVesselService>();
        }
    }
}