# PPG Authorization Module

A fully decoupled, reusable authorization system for .NET applications with fine-grained permission control, entity-based authorization, and intelligent caching.

## 🚀 Quick Start

### 1. Add Services to DI Container

```csharp
// In Program.cs or Startup.cs
services.AddMyPackageControllers();                    // Add authorization controllers
services.AddAuthorizationScopedImplementations();      // Add core services

// Register your application-specific providers
services.AddScoped<IUserProvider, YourUserProvider>();
services.AddScoped<IRoleProvider, YourRoleProvider>();
services.AddScoped<IAuthorizationConfigProvider, YourConfigProvider>();
```

### 2. Setup Database

```bash
# Run the authorization database setup script
sqlcmd -S localhost -U sa -P YourPassword -i Database/setup-authorization.sql
```

### 3. Create Permission Checkers in Your Application

**Important**: Permission checkers are located in your **main application**, not in the Authorization module!

```csharp
// File: YourApp/PermissionCheckers/CustomChecker.cs
using PPG.Auth.Types;
using PPG.Auth.Helpers;

namespace YourApp.PermissionCheckers
{
    public class ReadOwnCustomerChecker<Customer> : PermissionCheckerBase<Customer>
    {
        public override async Task<PermissionCheckerResult> CheckAsync(Customer customer, Guid userId)
        {
            var creatorId = ReflectionHelper.GetPropertyValue<Guid?>(customer, "CreatorId");
            return creatorId == userId ? PermissionCheckerResult.Allowed : PermissionCheckerResult.Forbidden;
        }
    }
}
```

### 4. Use in Your Controllers

```csharp
[ApiController]
public class YourController : ControllerBase
{
    private readonly IAuthorizationService _authorizationService;
    
    public YourController(IAuthorizationService authorizationService)
    {
        _authorizationService = authorizationService;
    }
    
    [HttpGet]
    public async Task<IActionResult> GetCustomer(Guid customerId)
    {
        var customer = await GetCustomerById(customerId);
        
        // Check if user can read this customer
        await _authorizationService.IsAuthorized(new IsAuthorizedDto<Customer>
        {
            Entity = customer,
            UserId = GetCurrentUserId(),
            PermissionToCheck = new PermissionToCheck 
            { 
                OperationActionId = OperationAction.Read 
            }
        });
        
        return Ok(customer);
    }
}
```

## 📋 Features

### Core Capabilities
- ✅ **Fine-grained permissions** - Entity-specific authorization
- ✅ **Extensible permission checkers** - Implement in your main application
- ✅ **Intelligent caching** - Automatic permission and type caching
- ✅ **Decoupled design** - Independent from your user/role management
- ✅ **Operation-based permissions** - CRUD operation support
- ✅ **Named permissions** - Simple string-based permissions
- ✅ **Exception handling** - Centralized, sanitized error responses
- ✅ **Database-driven** - Runtime configurable permissions
- ✅ **Auto-discovery** - Automatically finds permission checkers in your app
- ✅ **Super admin bypass** - Configurable super admin role that bypasses all authorization

### Permission Types

1. **Named Permissions** - Simple string-based permissions
   ```csharp
   PermissionToCheck = new PermissionToCheck { Name = "ManageUsers" }
   ```

2. **Operation Permissions** - CRUD-based with entity awareness
   ```csharp
   PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Update }
   ```

### Permission Checker Architecture

Permission checkers are located in your **main application** for optimal extensibility and business domain alignment.

**Location**: `YourApp/PermissionCheckers/` (e.g., `SmartBoat.API/PermissionCheckers/`)

| Checker | Purpose | Logic |
|---------|---------|--------|
| `ReadAnyGenericChecker<T>` | Read any entity | Always returns Neutral |
| `ReadOwnGenericChecker<T>` | Read owned entities | Checks `CreatorId` property |
| `CreateGenericChecker<T>` | Create entities | Generic create logic |
| `UpdateOwnGenericChecker<T>` | Update owned entities | Ownership validation |
| `DeleteOwnGenericChecker<T>` | Delete owned entities | Ownership validation |
| `DeleteAnyGenericChecker<T>` | Delete any entity | Admin-level permissions |

**Key Benefits:**
- ✅ **Easy Extensibility** - Add custom checkers without modifying the auth module
- ✅ **Business Domain Alignment** - Permission logic stays with business logic
- ✅ **True Modularity** - Authorization framework remains generic and reusable
- ✅ **Auto-Discovery** - Framework automatically finds all permission checkers

## 🏗️ Architecture

### High-Level Flow

```mermaid
graph TB
    subgraph "Main Application"
        A[Client Request] --> B[Controller]
        F[Custom Permission Checkers]
        UP[User Provider Implementation]
        RP[Role Provider Implementation]
    end
    subgraph "Authorization Module"
        C[Authorization Service]
        D[Permission Service]
        G[Cache]
        H[Database]
    end
    B --> C
    C --> D
    C --> UP
    C -.->|Auto-Discovery| F
    D --> G
    D --> H
```

### Core Components

- **AuthorizationService** - Main orchestrator (in Auth Module)
- **PermissionService** - Permission data management (in Auth Module)
- **CacheService** - Intelligent caching layer (in Auth Module)
- **Permission Checkers** - Custom business logic (**in Main Application**)
- **Providers** - Abstraction for user/role data (implemented in Main Application)

## 🔧 Implementation Guide

### Step 1: Implement Required Providers

#### IUserProvider
```csharp
public class YourUserProvider : IUserProvider
{
    public async Task<AuthUser?> GetUserAsync(Guid userId)
    {
        // Return user data from your system
    }
    
    public async Task<List<Guid>> GetUserRoleIdsAsync(Guid userId)
    {
        // Return user's role IDs
    }
    
    public async Task<bool> IsUserActiveAsync(Guid userId)
    {
        // Check if user exists and is active
    }
    
    public async Task<string?> GetUserRoleAsync(Guid userId)
    {
        // Return user's role name for super admin checking
    }
}
```

#### IRoleProvider
```csharp
public class YourRoleProvider : IRoleProvider
{
    public async Task<bool> RolesExistAsync(IEnumerable<Guid> roleIds)
    {
        // Validate role existence
    }
    
    public async Task<Dictionary<Guid, string>> GetRoleNamesAsync(IEnumerable<Guid> roleIds)
    {
        // Return role ID to name mapping
    }
    
    // ... other methods
}
```

#### IAuthorizationConfigProvider
```csharp
public class YourConfigProvider : IAuthorizationConfigProvider
{
    public AuthorizationConfig GetConfig()
    {
        return new AuthorizationConfig
        {
            Cache = new CacheConfig
            {
                PermissionCacheDurationInSeconds = 1800, // 30 minutes
                ImplementedCheckerTypesCacheDurationInSeconds = 3600 // 1 hour
            },
            SuperAdminRoleName = "Super Admin" // Configure your super admin role name
        };
    }
}
```

### Step 2: Create Custom Permission Checkers

**Create checkers in your main application** (e.g., `YourApp/PermissionCheckers/`):

```csharp
// File: YourApp/PermissionCheckers/CustomerAccessChecker.cs
using PPG.Auth.Types;
using PPG.Auth.Helpers;

namespace YourApp.PermissionCheckers
{
    public class CustomerAccessChecker<Customer> : PermissionCheckerBase<Customer>
    {
        public override async Task<PermissionCheckerResult> CheckAsync(Customer customer, Guid userId)
        {
            // Custom business logic
            if (customer.CompanyId == await GetUserCompanyId(userId))
            {
                return PermissionCheckerResult.Allowed;
            }
            
            return PermissionCheckerResult.Forbidden;
        }
    }
}
```

**✨ Auto-Discovery**: The framework automatically discovers all permission checkers in your application - no registration required!

### Step 3: Setup Permissions in Database

```sql
-- Create a permission with custom checker
INSERT INTO Permissions (Id, Entity, CheckerName, MachineName, DisplayName, OperationActionId)
VALUES (
    NEWID(),
    'Customer',
    'CustomerAccessChecker',
    'ReadCustomer', 
    'Read Customer Data',
    2 -- Read operation
);

-- Assign permission to role
INSERT INTO UserRolePermissions (Id, UserRoleId, PermissionId)
VALUES (NEWID(), @RoleId, @PermissionId);
```

## 📊 Performance & Caching

### Caching Strategy
- **Permission Cache**: 30 minutes TTL (configurable)
- **Type Cache**: 1 hour TTL (configurable)
- **Automatic Invalidation**: On permission changes

### Cache Keys
- User permissions: `auth:perm:{userId}`
- Permission checker types: `auth:types:`

### Performance Tips
1. **Batch Permission Checks**: Group related authorization calls
2. **Cache Warming**: Pre-load permissions for active users
3. **Monitor Cache Hit Rates**: Adjust TTL based on usage patterns

## 🔒 Security Features

### Super Admin Bypass
The authorization module supports a configurable super admin role that bypasses all permission checks:

```json
// appsettings.json
{
  "Authorization": {
    "SuperAdminRoleName": "Super Admin"
  }
}
```

Key security considerations:
- ✅ **Configurable** - Each application defines its own super admin role name
- ✅ **Decoupled** - Authorization module doesn't hardcode role names
- ✅ **Auditable** - Super admin access is still logged and tracked
- ✅ **Flexible** - Can be disabled by not configuring the role name

### Exception Sanitization
All exceptions are converted to standardized responses:
```csharp
{
    "payload": null,
    "exception": {
        "code": "AUTH-403",
        "description": "User not authorized to perform this action"
    }
}
```

### Authorization Patterns
- Always validate user existence and active status
- Permission checks throw exceptions (fail-secure)
- Audit trail for all permission operations
- Generic type safety for entity-based permissions
- Super admin bypass for administrative operations

## 📡 API Endpoints

The module provides REST endpoints for permission management:

### Permission Management
- `POST /api/permissions/create` - Create new permission
- `POST /api/permissions/update` - Update existing permission
- `POST /api/permissions/list` - List permissions
- `POST /api/permissions/delete` - Delete permission

### Cache Management
- `POST /api/permissions/clear-cache` - Clear all permission caches
- `POST /api/permissions/clear-cache/user` - Clear specific user's cache
- `POST /api/cache/clear` - Clear all application caches

## 🚨 Error Handling

### Exception Types
| Exception | Code | Description |
|-----------|------|-------------|
| `UnauthorizedException` | AUTH-403 | User not authorized |
| `AuthorizationBusinessException` | AUTH-400 | Business logic error |
| `AuthorizationTechnicalException` | AUTH-500 | Technical error |

### Usage with SafeExecutor
```csharp
return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
{
    // Your authorization-protected code here
    return await SomeBusinessLogic();
});
```

## 🧪 Testing

### Unit Testing Permission Checkers
```csharp
[Test]
public async Task ReadOwnChecker_ShouldAllowOwner()
{
    // Arrange
    var checker = new ReadOwnGenericChecker<Customer>();
    var customer = new Customer { CreatorId = userId };
    
    // Act
    var result = await checker.CheckAsync(customer, userId);
    
    // Assert
    Assert.AreEqual(PermissionCheckerResult.Allowed, result);
}
```

### Integration Testing
```csharp
[Test]
public async Task AuthorizationService_ShouldThrowForUnauthorizedUser()
{
    // Arrange
    var request = new IsAuthorizedDto<Customer>
    {
        UserId = unauthorizedUserId,
        PermissionToCheck = new PermissionToCheck { Name = "ReadCustomer" }
    };
    
    // Act & Assert
    await Assert.ThrowsAsync<UnauthorizedException>(
        () => authorizationService.IsAuthorized(request)
    );
}
```

## 📈 Monitoring & Observability

### Key Metrics to Track
- Authorization request frequency
- Cache hit/miss ratios
- Permission check latency
- Failed authorization attempts
- Custom checker execution time

### Logging Integration
```csharp
// Authorization attempts are automatically logged
// Custom checkers can add specific logging:
public class CustomChecker<T> : PermissionCheckerBase<T>
{
    private readonly ILogger<CustomChecker<T>> _logger;
    
    public override async Task<PermissionCheckerResult> CheckAsync(T entity, Guid userId)
    {
        _logger.LogInformation("Checking custom permission for user {UserId}", userId);
        // ... checker logic
    }
}
```

## 🔄 Migration Guide

### From Basic Authorization
1. Install the authorization module
2. Implement required providers
3. Run database setup script
4. Move permission checkers to your main application
5. Replace authorization attributes with service calls
6. Create custom permission checkers for business logic

### Database Migration
```sql
-- The module automatically creates required tables
-- Your existing user/role tables remain unchanged
-- Only adds: Permissions, UserRolePermissions
```

## 📚 Advanced Usage

### Super Admin Configuration

The super admin feature allows designated users to bypass all authorization checks while maintaining system decoupling:

#### Configuration Setup
```json
// appsettings.json
{
  "Authorization": {
    "SuperAdminRoleName": "Super Admin"
  }
}
```

#### Implementation Flow
1. **User Role Check** - System checks if user has the configured super admin role
2. **Bypass Logic** - If super admin, all permission checks return allowed
3. **Fallback** - If not super admin, normal permission checking applies
4. **Audit Trail** - All access (including super admin) is logged

#### Code Example
```csharp
// The authorization service automatically handles super admin bypass
var result = await _authorizationService.IsAuthorized(new IsAuthorizedDto<Customer>
{
    Entity = customer,
    UserId = userId, // If this user has "Super Admin" role, check passes automatically
    PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Delete }
});
```

#### Security Benefits
- **Emergency Access** - Allows administrative access when permission system needs maintenance
- **System Recovery** - Enables fixing permission issues without bypassing the authorization module
- **Flexibility** - Each application can define its own super admin role name
- **Auditability** - Super admin access is still tracked and logged

### Hierarchical Permissions
```csharp
// File: YourApp/PermissionCheckers/HierarchicalChecker.cs
public class HierarchicalChecker<T> : PermissionCheckerBase<T>
{
    public override async Task<PermissionCheckerResult> CheckAsync(T entity, Guid userId)
    {
        // Check if user has parent permission that includes this one
        var hasParentPermission = await CheckParentPermission(userId);
        return hasParentPermission ? PermissionCheckerResult.Allowed : PermissionCheckerResult.Neutral;
    }
}
```

### Multi-Tenant Support
```csharp
// File: YourApp/PermissionCheckers/TenantAwareChecker.cs
public class TenantAwareChecker<T> : PermissionCheckerBase<T>
{
    public override async Task<PermissionCheckerResult> CheckAsync(T entity, Guid userId)
    {
        var userTenantId = await GetUserTenantId(userId);
        var entityTenantId = GetEntityTenantId(entity);
        
        return userTenantId == entityTenantId 
            ? PermissionCheckerResult.Allowed 
            : PermissionCheckerResult.Forbidden;
    }
}
```

## 🎯 Extensibility

### How to Add New Permission Checkers

1. **Create a new file** in your `PermissionCheckers/` directory
2. **Inherit from** `PermissionCheckerBase<TEntity>`
3. **Implement** your business logic in `CheckAsync()`
4. **That's it!** - Framework auto-discovers your checker

```csharp
// File: YourApp/PermissionCheckers/NewBusinessRuleChecker.cs
using PPG.Auth.Types;

namespace YourApp.PermissionCheckers
{
    public class NewBusinessRuleChecker<TEntity> : PermissionCheckerBase<TEntity>
    {
        public override async Task<PermissionCheckerResult> CheckAsync(TEntity entity, Guid userId)
        {
            // Your custom business logic here
            return PermissionCheckerResult.Allowed;
        }
    }
}
```

## 🤝 Contributing

1. Follow the existing code patterns
2. Add unit tests for new permission checkers
3. Update documentation for new features
4. Ensure backward compatibility

## 📄 License

This module is part of the SmartBoat platform and follows the project's licensing terms.

## 🆘 Troubleshooting

### Common Issues

**Q: Permission checks are slow**  
A: Check cache configuration and hit rates. Consider increasing TTL values.

**Q: Custom checker not being discovered**  
A: Ensure your checker inherits from `PermissionCheckerBase<T>` and is in a loaded assembly.

**Q: Database connection errors**  
A: Verify connection string and ensure authorization tables are created.

**Q: User always unauthorized**  
A: Check that user has assigned roles and roles have permission mappings.

### Debug Mode
Enable detailed logging by setting log level to `Debug` for `PPG.Auth` namespace.

---

For detailed technical analysis and architecture diagrams, see [TECHNICAL_ANALYSIS.md](./TECHNICAL_ANALYSIS.md).