using PPG.Auth.Types;

namespace PPG.Auth.Interfaces
{
    /// <summary>
    /// Provides user data for authorization purposes, decoupling from main application's user management
    /// </summary>
    public interface IUserProvider
    {
        /// <summary>
        /// Gets basic user information needed for authorization
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>Authorization user data or null if not found</returns>
        Task<AuthUser?> GetUserAsync(Guid userId);

        /// <summary>
        /// Gets all role IDs assigned to a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of role IDs assigned to the user</returns>
        Task<List<Guid>> GetUserRoleIdsAsync(Guid userId);

        /// <summary>
        /// Checks if a user exists and is active
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>True if user exists and is active</returns>
        Task<bool> IsUserActiveAsync(Guid userId);

        /// <summary>
        /// Gets the role name of a user for super admin checking
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>Role name of the user or null if not found</returns>
        Task<string?> GetUserRoleAsync(Guid userId);
    }
}