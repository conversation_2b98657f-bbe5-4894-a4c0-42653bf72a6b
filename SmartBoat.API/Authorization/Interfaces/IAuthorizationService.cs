using PPG.Auth.Types;

namespace PPG.Auth.Interfaces
{
    /// <summary>
    /// Interface for managing authorization.
    /// </summary>
    public interface IAuthorizationService
    {
        /// <summary>
        /// Checks if user is authorized.
        /// </summary>
        /// <param name="createSeverityDto">The data transfer object containing the information needed to check if user is Authorized</param>
        Task IsAuthorized<TEntity>(IsAuthorizedDto<TEntity> isAuthorizedDto) where TEntity : class;
    }
}
