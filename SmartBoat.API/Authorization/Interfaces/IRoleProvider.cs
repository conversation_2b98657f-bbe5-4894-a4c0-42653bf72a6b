namespace PPG.Auth.Interfaces
{
    /// <summary>
    /// Provides role data for authorization purposes, decoupling from main application's role management
    /// </summary>
    public interface IRoleProvider
    {
        /// <summary>
        /// Checks if roles exist by their IDs
        /// </summary>
        /// <param name="roleIds">List of role IDs to validate</param>
        /// <returns>True if all roles exist</returns>
        Task<bool> RolesExistAsync(IEnumerable<Guid> roleIds);

        /// <summary>
        /// Gets role names by their IDs
        /// </summary>
        /// <param name="roleIds">List of role IDs</param>
        /// <returns>Dictionary mapping role ID to role name</returns>
        Task<Dictionary<Guid, string>> GetRoleNamesAsync(IEnumerable<Guid> roleIds);

        /// <summary>
        /// Gets role ID by role name
        /// </summary>
        /// <param name="roleName">The role name</param>
        /// <returns>Role ID or null if not found</returns>
        Task<Guid?> GetRoleIdAsync(string roleName);

        /// <summary>
        /// Checks if a role exists by name
        /// </summary>
        /// <param name="roleName">The role name</param>
        /// <returns>True if role exists</returns>
        Task<bool> RoleExistsAsync(string roleName);
    }
}