using PPG.Auth.Configuration;

namespace PPG.Auth.Interfaces
{
    /// <summary>
    /// Provides configuration settings for the Authorization module
    /// Abstracts the module from direct IConfiguration dependency
    /// </summary>
    public interface IAuthorizationConfigProvider
    {
        /// <summary>
        /// Gets the authorization configuration settings
        /// </summary>
        AuthorizationConfig GetConfig();

        /// <summary>
        /// Gets a specific configuration value by key
        /// </summary>
        /// <typeparam name="T">Type of the configuration value</typeparam>
        /// <param name="key">Configuration key</param>
        /// <param name="defaultValue">Default value if key not found</param>
        /// <returns>Configuration value or default</returns>
        T GetValue<T>(string key, T defaultValue = default!);
    }
}