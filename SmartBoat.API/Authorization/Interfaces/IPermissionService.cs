using PPG.Auth.Types;

namespace PPG.Auth.Interfaces
{
    /// <summary>
    /// Interface for managing permissions.
    /// </summary>
    public interface IPermissionService
    {
        /// <summary>
        /// Creates a new permission based on the provided data.
        /// </summary>
        /// <param name="createPermissionDto">The data transfer object containing the details for creating a new permission.</param>
        /// <returns>A string representing the result of the creation operation.</returns>
        Task<string> CreatePermission(CreatePermissionDto createPermissionDto);

        /// <summary>
        /// Updates a permission based on the provided data.
        /// </summary>
        /// <param name="updatePermissionDto">The data transfer object containing the details for updateing a permission.</param>
        /// <returns>A string representing the result of the update operation.</returns>
        Task<string> UpdatePermission(UpdatePermissionDto updatePermissionDto);


        /// <summary>
        /// Retrieves a list of permissions based on the provided request.
        /// </summary>
        /// <param name="listApiPermissionRequestDto">The data transfer object containing the request details to retrieve the list of permissions.</param>
        /// <returns>A ReturnListProductCategoryDto object representing the list of permissions.</returns>
        Task<ReturnListPermissionDto> GetListPermission(ListApiPermissionRequestDto listApiPermissionRequestDto);

        /// <summary>
        /// Deletes a permission based on the provided data.
        /// </summary>
        /// <param name="deletePermissionDto">The data transfer object containing the details for deleting a permission.</param>
        /// <returns>A string representing the result of the deletion operation.</returns>
        Task<string> DeletePermission(DeletePermissionDto deletePermissionDto);

        void InvalidateAllUserPermissions();
        void InvalidateUserPermissions(Guid userId);
        Task<List<Permission>> GetUserPermissionsAsync(Guid userId);
    }
}
