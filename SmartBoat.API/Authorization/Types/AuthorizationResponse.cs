namespace PPG.Auth.Types
{
    /// <summary>
    /// Authorization module specific response wrapper
    /// </summary>
    public class AuthorizationResponse<T>
    {
        public T? Payload { get; set; }
        public AuthorizationResponseException? Exception { get; set; }
        public bool IsSuccess => Exception == null;
    }

    /// <summary>
    /// Authorization module specific exception response
    /// </summary>
    public class AuthorizationResponseException
    {
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}