// File: IsAuthorizedDto.cs

namespace PPG.Auth.Types
{
    public class IsAuthorizedDto<TEntity>
    {
        public TEntity? Entity { get; set; }
        public Guid? UserId { get; set; }
        public PermissionToCheck? PermissionToCheck { get; set; }
    }

    public class PermissionToCheck
    {
        public string? Name { get; set; }
        public OperationAction? OperationActionId { get; set; }
    }
}
