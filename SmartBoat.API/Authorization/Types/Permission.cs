// File: Permission.cs
namespace PPG.Auth.Types
{
    public class Permission
    {
        public Guid? Id { get; set; }
        public string? Entity { get; set; }
        public string? CheckerName { get; set; }
        public string? MachineName { get; set; }
        public string? DisplayName { get; set; }
        public int? OperationActionId { get; set; }
        public int? Version { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
        public Guid? CreatorId { get; set; }
        public Guid? ChangedUser { get; set; }
    }
}
