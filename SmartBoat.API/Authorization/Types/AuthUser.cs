namespace PPG.Auth.Types
{
    /// <summary>
    /// Simplified user representation for authorization purposes
    /// Decoupled from main application's User entity
    /// </summary>
    public class AuthUser
    {
        public Guid Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime Created { get; set; }
    }
}