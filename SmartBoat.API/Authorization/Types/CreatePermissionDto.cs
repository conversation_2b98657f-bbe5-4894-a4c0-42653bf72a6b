// File: CreatePermissionDto.cs
namespace PPG.Auth.Types
{
    public class CreatePermissionDto
    {
        public string? Entity { get; set; }
        public string? CheckerName { get; set; }
        public string? MachineName { get; set; }
        public string? DisplayName { get; set; }
        public int? OperationActionId { get; set; }
        public List<Guid> UserRoleIds { get; set; } = new List<Guid>();
    }
}
