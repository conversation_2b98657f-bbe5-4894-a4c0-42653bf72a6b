using Microsoft.AspNetCore.Mvc;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using PPG.Auth.Exceptions;
using PPG.Auth.Helpers;

namespace PPG.Auth.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PermissionsController : ControllerBase
    {
        private readonly IPermissionService _permissionService;
        private readonly IAuthorizationService _authorizationService;

        public PermissionsController(IPermissionService permissionService, IAuthorizationService authorizationService)
        {
            _permissionService = permissionService;
            _authorizationService = authorizationService;
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreatePermission([FromBody] AuthRequest<CreatePermissionDto> request)
        {
            return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
            {
                // Extract user ID from request header or claims
                var userId = GetUserIdFromRequest(request.Header);
                
                // Check if user has permission to create permissions
                await _authorizationService.IsAuthorized(new IsAuthorizedDto<Permission>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck { Name = "CreatePermission" }
                });

                return await _permissionService.CreatePermission(request.Payload);
            });
        }

        [HttpPost("update")]
        public async Task<IActionResult> UpdatePermission([FromBody] AuthRequest<UpdatePermissionDto> request)
        {
            return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
            {
                // Extract user ID from request header or claims
                var userId = GetUserIdFromRequest(request.Header);
                
                // Check if user has permission to update permissions
                await _authorizationService.IsAuthorized(new IsAuthorizedDto<Permission>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck { Name = "UpdatePermission" }
                });

                return await _permissionService.UpdatePermission(request.Payload);
            });
        }

        [HttpPost("list")]
        public async Task<IActionResult> GetListPermission([FromBody] AuthRequest<ListApiPermissionRequestDto> request)
        {
            return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
            {
                // Extract user ID from request header or claims
                var userId = GetUserIdFromRequest(request.Header);
                
                // Check if user has permission to list permissions
                await _authorizationService.IsAuthorized(new IsAuthorizedDto<Permission>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck { Name = "ListPermissions" }
                });

                return await _permissionService.GetListPermission(request.Payload);
            });
        }

        [HttpPost("delete")]
        public async Task<IActionResult> DeletePermission([FromBody] AuthRequest<DeletePermissionDto> request)
        {
            return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
            {
                // Extract user ID from request header or claims
                var userId = GetUserIdFromRequest(request.Header);
                
                // Check if user has permission to delete permissions
                await _authorizationService.IsAuthorized(new IsAuthorizedDto<Permission>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck { Name = "DeletePermission" }
                });

                return await _permissionService.DeletePermission(request.Payload);
            });
        }

        //Clear all permissions from the cache
        [HttpPost("clear-cache")]
        public async Task<IActionResult> ClearCache([FromBody] AuthRequest<object> request)
        {
            return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
            {
                // Extract user ID from request header or claims
                var userId = GetUserIdFromRequest(request.Header);
                
                // Check if user has permission to clear cache
                await _authorizationService.IsAuthorized(new IsAuthorizedDto<Permission>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck { Name = "ManagePermissionCache" }
                });

                _permissionService.InvalidateAllUserPermissions();
                return "Cache cleared";
            });
        }

        // Clear all permissions for a specific user from the cache
        [HttpPost("clear-cache/user")]
        public async Task<IActionResult> ClearCacheForUser([FromBody] AuthRequest<ClearUserCacheDto> request)
        {
            return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
            {
                // Extract user ID from request header or claims
                var userId = GetUserIdFromRequest(request.Header);
                
                // Check if user has permission to clear cache
                await _authorizationService.IsAuthorized(new IsAuthorizedDto<Permission>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck { Name = "ManagePermissionCache" }
                });

                _permissionService.InvalidateUserPermissions(request.Payload.UserId.Value);
                return "Cache cleared";
            });
        }

        private Guid GetUserIdFromRequest(AuthRequestHeader header)
        {
            // TODO: Implement proper user ID extraction from JWT token or session
            // For now, extract from header if available, otherwise throw exception
            if (header?.UserId == null)
            {
                throw new AuthorizationBusinessException("AUTH-401", "User authentication required");
            }
            return header.UserId.Value;
        }
    }
}
