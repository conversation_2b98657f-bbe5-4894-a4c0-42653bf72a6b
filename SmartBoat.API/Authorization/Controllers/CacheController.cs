using Microsoft.AspNetCore.Mvc;
using PPG.Auth.Interfaces;
using PPG.Auth.Exceptions;
using PPG.Auth.Helpers;
using PPG.Auth.Types;

namespace PPG.Auth.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CacheController : ControllerBase
    {
        private readonly ICacheService _cacheService;
        private readonly IAuthorizationConfigProvider _configProvider;
        private readonly IAuthorizationService _authorizationService;

        public CacheController(ICacheService cacheService, IAuthorizationConfigProvider configProvider, IAuthorizationService authorizationService)
        {
            _cacheService = cacheService;
            _configProvider = configProvider;
            _authorizationService = authorizationService;
        }

        //Clear all cache
        [HttpPost("clear-cache")]
        public async Task<IActionResult> ClearCache([FromBody] AuthRequest<object> request)
        {
            return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
            {
                // Extract user ID from request header or claims
                var userId = GetUserIdFromRequest(request.Header);
                
                // Check if user has permission to clear cache
                await _authorizationService.IsAuthorized(new IsAuthorizedDto<Permission>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck { Name = "ManageCache" }
                });

                _cacheService.InvalidateCache();
                return "Cache cleared";
            });
        }

        //Clear all permissions from the cache
        [HttpPost("clear-cache-permissions")]
        public async Task<IActionResult> ClearCachePermissions([FromBody] AuthRequest<object> request)
        {
            return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
            {
                // Extract user ID from request header or claims
                var userId = GetUserIdFromRequest(request.Header);
                
                // Check if user has permission to clear permission cache
                await _authorizationService.IsAuthorized(new IsAuthorizedDto<Permission>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck { Name = "ManagePermissionCache" }
                });

                var cacheKey = _configProvider.GetConfig().Cache.PermissionCacheKeyPrefix;
                _cacheService.InvalidateCacheByPrefix(cacheKey);
                return "Cache cleared";
            });
        }

        //Clear all types from the cache
        [HttpPost("clear-cache-types")]
        public async Task<IActionResult> ClearCacheTypes([FromBody] AuthRequest<object> request)
        {
            return await AuthorizationSafeExecutor.ExecuteAsync(async () =>
            {
                // Extract user ID from request header or claims
                var userId = GetUserIdFromRequest(request.Header);
                
                // Check if user has permission to clear types cache
                await _authorizationService.IsAuthorized(new IsAuthorizedDto<Permission>
                {
                    UserId = userId,
                    PermissionToCheck = new PermissionToCheck { Name = "ManageCache" }
                });

                var cacheKey = _configProvider.GetConfig().Cache.CheckerTypesCacheKeyPrefix;
                _cacheService.InvalidateCacheByPrefix(cacheKey);
                return "Cache cleared";
            });
        }

        private Guid GetUserIdFromRequest(AuthRequestHeader header)
        {
            // TODO: Implement proper user ID extraction from JWT token or session
            // For now, extract from header if available, otherwise throw exception
            if (header?.UserId == null)
            {
                throw new AuthorizationBusinessException("AUTH-401", "User authentication required");
            }
            return header.UserId.Value;
        }

    }
}
