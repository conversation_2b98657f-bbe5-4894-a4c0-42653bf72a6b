﻿namespace PPG.Auth.Helpers
{
    public static class ReflectionUtils
    {
        /// <summary>
        /// Searches for a type by its class name across all loaded assemblies.
        /// </summary>
        /// <param name="className">The name of the class without the namespace.</param>
        /// <returns>The Type if found; otherwise, null.</returns>
        public static Type GetTypeByName(string className)
        {
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                var type = assembly.GetTypes().FirstOrDefault(t => t.Name == className);
                if (type != null)
                {
                    return type;
                }
            }

            return null;
        }
    }
}
