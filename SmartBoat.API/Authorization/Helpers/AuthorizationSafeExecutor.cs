using Microsoft.AspNetCore.Mvc;
using PPG.Auth.Types;
using PPG.Auth.Exceptions;

namespace PPG.Auth.Helpers
{
    /// <summary>
    /// Authorization module specific safe executor for handling exceptions in controllers
    /// </summary>
    public static class AuthorizationSafeExecutor
    {
        public static async Task<IActionResult> ExecuteAsync<T>(Func<Task<T>> func)
        {
            try
            {
                var result = await func();
                return new OkObjectResult(new AuthorizationResponse<T> { Payload = result });
            }
            catch (UnauthorizedException ex)
            {
                var response = new AuthorizationResponse<T>
                {
                    Exception = new AuthorizationResponseException
                    {
                        Code = ex.Code,
                        Description = ex.Description
                    }
                };
                return new OkObjectResult(response);
            }
            catch (AuthorizationBusinessException ex)
            {
                var response = new AuthorizationResponse<T>
                {
                    Exception = new AuthorizationResponseException
                    {
                        Code = ex.Code,
                        Description = ex.Description
                    }
                };
                return new OkObjectResult(response);
            }
            catch (AuthorizationTechnicalException ex)
            {
                var response = new AuthorizationResponse<T>
                {
                    Exception = new AuthorizationResponseException
                    {
                        Code = ex.Code,
                        Description = ex.Description
                    }
                };
                return new OkObjectResult(response);
            }
            catch (Exception)
            {
                var response = new AuthorizationResponse<T>
                {
                    Exception = new AuthorizationResponseException
                    {
                        Code = "AUTH-500",
                        Description = "An unexpected error occurred in the authorization system"
                    }
                };
                return new OkObjectResult(response);
            }
        }

        public static async Task<IActionResult> ExecuteAsync(Func<Task<IActionResult>> func)
        {
            try
            {
                return await func();
            }
            catch (UnauthorizedException ex)
            {
                var response = new AuthorizationResponse<object>
                {
                    Exception = new AuthorizationResponseException
                    {
                        Code = ex.Code,
                        Description = ex.Description
                    }
                };
                return new OkObjectResult(response);
            }
            catch (AuthorizationBusinessException ex)
            {
                var response = new AuthorizationResponse<object>
                {
                    Exception = new AuthorizationResponseException
                    {
                        Code = ex.Code,
                        Description = ex.Description
                    }
                };
                return new OkObjectResult(response);
            }
            catch (AuthorizationTechnicalException ex)
            {
                var response = new AuthorizationResponse<object>
                {
                    Exception = new AuthorizationResponseException
                    {
                        Code = ex.Code,
                        Description = ex.Description
                    }
                };
                return new OkObjectResult(response);
            }
            catch (Exception)
            {
                var response = new AuthorizationResponse<object>
                {
                    Exception = new AuthorizationResponseException
                    {
                        Code = "AUTH-500",
                        Description = "An unexpected error occurred in the authorization system"
                    }
                };
                return new OkObjectResult(response);
            }
        }
    }
}