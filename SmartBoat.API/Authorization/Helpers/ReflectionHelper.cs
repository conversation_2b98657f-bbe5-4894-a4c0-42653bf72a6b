using System.Reflection;

namespace PPG.Auth.Helpers
{
    public static class ReflectionHelper
    {
        /// <summary>
        /// Safely gets a property value from an entity using reflection
        /// </summary>
        /// <typeparam name="T">The expected return type</typeparam>
        /// <param name="entity">The entity to get the property from</param>
        /// <param name="propertyName">The name of the property</param>
        /// <returns>The property value if found and castable, otherwise null</returns>
        public static T? GetPropertyValue<T>(object? entity, string propertyName)
        {
            if (entity == null || string.IsNullOrWhiteSpace(propertyName))
                return default(T);

            try
            {
                var property = entity.GetType().GetProperty(propertyName);
                if (property == null)
                    return default(T);

                var value = property.GetValue(entity);
                if (value == null)
                    return default(T);

                // Handle nullable types
                if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(Nullable<>))
                {
                    var underlyingType = Nullable.GetUnderlyingType(typeof(T));
                    if (underlyingType != null && value.GetType() == underlyingType)
                    {
                        return (T?)value;
                    }
                }

                // Direct casting for non-nullable types
                if (value is T directValue)
                    return directValue;

                // Try to convert if possible
                if (typeof(T) == typeof(Guid) && value is Guid guidValue)
                    return (T)(object)guidValue;

                return default(T);
            }
            catch
            {
                // Return default value if any reflection operation fails
                return default(T);
            }
        }

        /// <summary>
        /// Checks if an entity has a specific property
        /// </summary>
        /// <param name="entity">The entity to check</param>
        /// <param name="propertyName">The property name to look for</param>
        /// <returns>True if the property exists, false otherwise</returns>
        public static bool HasProperty(object? entity, string propertyName)
        {
            if (entity == null || string.IsNullOrWhiteSpace(propertyName))
                return false;

            try
            {
                return entity.GetType().GetProperty(propertyName) != null;
            }
            catch
            {
                return false;
            }
        }
    }
}