namespace PPG.Auth.Exceptions
{
    /// <summary>
    /// Business logic exception within authorization module (e.g., user not found, permission denied)
    /// </summary>
    public class AuthorizationBusinessException : AuthorizationException
    {
        public AuthorizationBusinessException(string code, string description) 
            : base(code, description)
        {
        }

        public AuthorizationBusinessException(string code, string description, Exception innerException) 
            : base(code, description, innerException)
        {
        }
    }
}