namespace PPG.Auth.Exceptions
{
    /// <summary>
    /// Base exception for all authorization-related errors
    /// </summary>
    public abstract class AuthorizationException : Exception
    {
        public string Code { get; }
        public string Description { get; }

        protected AuthorizationException(string code, string description) : base(description)
        {
            Code = code;
            Description = description;
        }

        protected AuthorizationException(string code, string description, Exception innerException) 
            : base(description, innerException)
        {
            Code = code;
            Description = description;
        }
    }
}