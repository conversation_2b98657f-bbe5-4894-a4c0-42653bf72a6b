namespace PPG.Auth.Exceptions
{
    /// <summary>
    /// Specific exception for authorization failures (user not authorized to perform action)
    /// </summary>
    public class UnauthorizedException : AuthorizationBusinessException
    {
        public Guid? UserId { get; }
        public string? Permission { get; }
        public string? Resource { get; }

        public UnauthorizedException(string message) 
            : base("AUTH-403", message)
        {
        }

        public UnauthorizedException(Guid userId, string permission, string? resource = null) 
            : base("AUTH-403", $"User {userId} is not authorized to {permission}" + (resource != null ? $" on {resource}" : ""))
        {
            UserId = userId;
            Permission = permission;
            Resource = resource;
        }

        public UnauthorizedException(string code, string message) 
            : base(code, message)
        {
        }
    }
}