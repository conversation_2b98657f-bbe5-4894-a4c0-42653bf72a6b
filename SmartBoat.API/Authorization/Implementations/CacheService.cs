using Microsoft.Extensions.Caching.Memory;
using PPG.Auth.Interfaces;
using System.Collections.Concurrent;

namespace PPG.Auth.Services
{
    public class CacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ConcurrentDictionary<string, byte> _cachedKeys = new ConcurrentDictionary<string, byte>();

        public CacheService(IMemoryCache memoryCache)
        {
            _memoryCache = memoryCache;
        }

        public void InvalidateCache()
        {
            foreach (var key in _cachedKeys.Keys)
            {
                _memoryCache.Remove(key);
            }

            _cachedKeys.Clear();
        }

        public void InvalidateCacheByKey(string key)
        {
            _memoryCache.Remove(key);
            _cachedKeys.TryRemove(key, out _); // Remove from the tracked keys
        }

        public bool CacheItemExistsByKey(string key)
        {
            return _memoryCache.TryGetValue(key, out _);
        }

        public T GetCacheItemByKey<T>(string key)
        {
            return _memoryCache.Get<T>(key);
        }

        public void CacheData<T>(string key, T data, int durationInSeconds)
        {
            _memoryCache.Set(key, data, TimeSpan.FromSeconds(durationInSeconds));
            _cachedKeys.TryAdd(key, 0); // Track the key
        }

        public void InvalidateCacheByPrefix(string prefix)
        {
            foreach (var key in _cachedKeys.Keys.ToList())
            {
                if (key.StartsWith(prefix))
                {
                    InvalidateCacheByKey(key); // Remove from the cache (memory cache
                }
            }
        }
    }

}
