using System.Data;
using Dapper;
using Microsoft.Extensions.Caching.Memory;

using PPG.Auth.Helpers;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using PPG.Auth.Exceptions;

namespace PPG.Auth.Services
{
    public class PermissionService : IPermissionService
    {
        private readonly ICacheService _cacheService;
        private readonly IDbConnectionFactory _dbConnectionFactory;
        private readonly IRoleProvider _roleProvider;
        private readonly IUserProvider _userProvider;
        private readonly IAuthorizationConfigProvider _configProvider;
        private readonly string CacheKeyPrefix;

        public PermissionService(
            IDbConnectionFactory dbConnectionFactory, 
            IMemoryCache memoryCache, 
            ICacheService cacheService, 
            IRoleProvider roleProvider,
            IUserProvider userProvider,
            IAuthorizationConfigProvider configProvider)
        {
            _dbConnectionFactory = dbConnectionFactory ?? throw new ArgumentNullException(nameof(dbConnectionFactory));
            _cacheService = cacheService;
            _roleProvider = roleProvider;
            _userProvider = userProvider;
            _configProvider = configProvider;
            CacheKeyPrefix = _configProvider.GetConfig().Cache.PermissionCacheKeyPrefix;
        }

        public async Task<string> UpdatePermission(UpdatePermissionDto updatePermissionDto)
        {
            // Step 1: Check that the Permission exists by PermissionName
            var existingPermission = await ExecuteWithConnectionAsync(async connection =>
                await connection.QueryFirstOrDefaultAsync<Permission>(
                    "SELECT * FROM Permissions WHERE Name = @PermissionId",
                    new { PermissionId = updatePermissionDto.PermissionId }));

            if (existingPermission == null) {
                throw new AuthorizationBusinessException("AUTH-404", "Permission not found");
            }

            // Step 2: Validate that all role names exist through provider
            foreach (var roleName in updatePermissionDto.UserRoleNames)
            {
                var roleExists = await _roleProvider.RoleExistsAsync(roleName);
                if (!roleExists)
                {
                    throw new AuthorizationBusinessException("AUTH-404", $"Role '{roleName}' not found");
                }
            }

            // Step 3: Update the UserRolePermissions table
            return await ExecuteWithTransactionAsync(async (connection, transaction) =>
            {
                // Delete existing permissions for this permission
                await connection.ExecuteAsync(
                    "DELETE FROM UserRolePermissions WHERE PermissionId = @PermissionId",
                    new { PermissionId = updatePermissionDto.PermissionId }, transaction);

                // Get role IDs from names
                var userRolePermissions = new List<UserRolePermission>();
                foreach (var userRoleName in updatePermissionDto.UserRoleNames)
                {
                    var roleId = await _roleProvider.GetRoleIdAsync(userRoleName);
                    if (roleId.HasValue)
                    {
                        userRolePermissions.Add(new UserRolePermission
                        {
                            Id = Guid.NewGuid(),
                            PermissionId = updatePermissionDto.PermissionId,
                            UserRoleId = roleId.Value
                        });
                    }
                }

                // Insert new permissions
                if (userRolePermissions.Any())
                {
                    await connection.ExecuteAsync(
                        "INSERT INTO UserRolePermissions (Id, PermissionId, UserRoleId) " +
                        "VALUES (@Id, @PermissionId, @UserRoleId)",
                        userRolePermissions, transaction);
                }

                return updatePermissionDto.PermissionId.ToString();
            });
        }

        public async Task<string> CreatePermission(CreatePermissionDto createPermissionDto)
        {
            // Step 1: Validate the request payload
            if (string.IsNullOrEmpty(createPermissionDto.MachineName) || string.IsNullOrEmpty(createPermissionDto.Entity))
            {
                throw new AuthorizationBusinessException("AUTH-400", "MachineName and Entity are required");
            }

            // Step 2: Check if the specified entity exists as a class
            var entity = ReflectionUtils.GetTypeByName(createPermissionDto.Entity);
            if(entity == null)
            {
                throw new AuthorizationBusinessException("AUTH-400", $"Entity '{createPermissionDto.Entity}' not found");
            }

            // Step 3: Validate that all role IDs exist through provider
            var rolesExist = await _roleProvider.RolesExistAsync(createPermissionDto.UserRoleIds);
            if (!rolesExist)
            {
                throw new AuthorizationBusinessException("AUTH-404", "One or more user roles not found");
            }

            // Step 4: Check if a permission with the same MachineName already exists by counting
            var existingPermissionCount = await ExecuteWithConnectionAsync(async connection =>
                await connection.ExecuteScalarAsync<int>(
                    "SELECT COUNT(*) FROM Permissions WHERE MachineName = @MachineName",
                    new { MachineName = createPermissionDto.MachineName }));

            if (existingPermissionCount != 0)
            {
                throw new AuthorizationBusinessException("AUTH-409", $"Permission with MachineName '{createPermissionDto.MachineName}' already exists");
            }

            // Step 5: Check if a permission for the same entity and Checker already exists by counting
            var existingPermissionForEntityCount = await ExecuteWithConnectionAsync(async connection =>
                await connection.ExecuteScalarAsync<int>(
                    "SELECT COUNT(*) FROM Permissions WHERE Entity = @Entity AND CheckerName = @CheckerName",
                    new { Entity = createPermissionDto.Entity, CheckerName = createPermissionDto.CheckerName }));

            if (existingPermissionForEntityCount != 0)
            {
                throw new AuthorizationBusinessException("AUTH-409", $"Permission for entity '{createPermissionDto.Entity}' and checker '{createPermissionDto.CheckerName}' already exists");
            }

            // Step 6: Create a new Permission object
            var permission = new Permission
            {
                Id = Guid.NewGuid(),
                MachineName = createPermissionDto.MachineName,
                Entity = createPermissionDto.Entity,
                CheckerName = createPermissionDto.CheckerName,
                DisplayName = createPermissionDto.DisplayName,
                OperationActionId = createPermissionDto.OperationActionId,
                Created = DateTime.UtcNow
            };

            var userRolePermissions = new List<UserRolePermission>();
            foreach (var userRoleId in createPermissionDto.UserRoleIds)
            {
                userRolePermissions.Add(new UserRolePermission
                {
                    Id = Guid.NewGuid(),
                    PermissionId = permission.Id,
                    UserRoleId = userRoleId
                });
            }

            // Step 7: Perform Database Operations in a Single Transaction
            return await ExecuteWithTransactionAsync(async (connection, transaction) =>
            {
                connection.Execute(
                    "INSERT INTO Permissions (Id, Entity, CheckerName, MachineName, DisplayName, OperationActionId, Created) " +
                    "VALUES (@Id, @Entity, @CheckerName, @MachineName, @DisplayName, @OperationActionId, @Created)",
                    permission, transaction);

                connection.Execute(
                    "INSERT INTO UserRolePermissions (Id, PermissionId, UserRoleId) " +
                    "VALUES (@Id, @PermissionId, @UserRoleId)",
                    userRolePermissions, transaction);

                return permission.Id.ToString();
            });
        }

        public async Task<ReturnListPermissionDto> GetListPermission(ListApiPermissionRequestDto request)
        {
            // Step 1: Validate the request payload
            if (request.PageLimit <= 0 || request.PageOffset < 0)
            {
                throw new AuthorizationBusinessException("AUTH-400", "PageLimit must be greater than 0 and PageOffset must be non-negative");
            }

            return await ExecuteWithConnectionAsync(async connection =>
            {
                // Step 2: Fetch the list of permissions from the database
                // Use explicit mapping to prevent SQL injection, even with validation
                var allowedSortFields = new Dictionary<string, string>
                {
                    { "Id", "[Id]" },
                    { "Entity", "[Entity]" },
                    { "MachineName", "[MachineName]" },
                    { "DisplayName", "[DisplayName]" },
                    { "Created", "[Created]" },
                    { "Changed", "[Changed]" }
                };
                
                var sortField = string.IsNullOrEmpty(request.SortField) || !allowedSortFields.ContainsKey(request.SortField) 
                    ? allowedSortFields["Id"] : allowedSortFields[request.SortField];
                
                var sortOrder = string.IsNullOrEmpty(request.SortOrder) || 
                               (!request.SortOrder.Equals("asc", StringComparison.OrdinalIgnoreCase) && 
                                !request.SortOrder.Equals("desc", StringComparison.OrdinalIgnoreCase))
                    ? "ASC" : request.SortOrder.ToUpper();

                var sql = $"SELECT * FROM Permissions ORDER BY {sortField} {sortOrder} " +
                         $"OFFSET @PageOffset ROWS FETCH NEXT @PageLimit ROWS ONLY";

                var permissions = await connection.QueryAsync<Permission>(sql, new 
                { 
                    PageOffset = request.PageOffset, 
                    PageLimit = request.PageLimit 
                });

                // Step 3: For each permission, fetch the associated roles and map to PermissionDto
                var permissionDtos = new List<PermissionDto>();
                foreach (var permission in permissions)
                {
                    var permissionDto = new PermissionDto
                    {
                        Id = permission.Id,
                        Entity = permission.Entity,
                        CheckerName = permission.CheckerName,
                        MachineName = permission.MachineName,
                        DisplayName = permission.DisplayName,
                        OperationActionId = permission.OperationActionId,
                        Version = permission.Version,
                        Created = permission.Created,
                        Changed = permission.Changed,
                        CreatorId = permission.CreatorId,
                        ChangedUser = permission.ChangedUser
                    };

                    // Get role IDs for this permission
                    var roleIds = await connection.QueryAsync<Guid>(
                        "SELECT UserRoleId FROM UserRolePermissions WHERE PermissionId = @PermissionId",
                        new { PermissionId = permission.Id });

                    // Get role names through provider
                    var roleNames = await _roleProvider.GetRoleNamesAsync(roleIds);
                    permissionDto.UserRoles = roleNames.Values.ToList();
                    permissionDtos.Add(permissionDto);
                }

                // Step 4: Create metadata
                var totalPermissions = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Permissions");
                var totalPages = totalPermissions / request.PageLimit;
                if (totalPermissions % request.PageLimit != 0)
                {
                    totalPages += 1;
                }

                var metadataDto = new AuthMetadataDto
                {
                    PageLimit = request.PageLimit,
                    PageOffset = request.PageOffset,
                    Total = totalPages
                };

                return new ReturnListPermissionDto
                {
                    Data = permissionDtos.ToList(),
                    Metadata = metadataDto
                };
            });
        }

        public async Task<List<Permission>> GetUserPermissionsAsync(Guid userId)
        {
            string cacheKey = GetCacheKeyOfAUser(userId);

            // Try to get permissions from cache
            if (!_cacheService.CacheItemExistsByKey(cacheKey))
            {
                var permissions = await ExecuteWithConnectionAsync(async connection =>
                {
                    // Get user's role IDs through provider
                    var userRoleIds = await _userProvider.GetUserRoleIdsAsync(userId);
                    
                    if (!userRoleIds.Any())
                        return new List<Permission>();

                    // Get permissions for these roles from authorization database
                    var roleIdParams = string.Join(",", userRoleIds.Select((_, i) => $"@RoleId{i}"));
                    var parameters = new Dictionary<string, object>();
                    for (int i = 0; i < userRoleIds.Count; i++)
                    {
                        parameters[$"RoleId{i}"] = userRoleIds[i];
                    }

                    var sql = $@"
                        SELECT p.* 
                        FROM Permissions p 
                        INNER JOIN UserRolePermissions urp ON p.Id = urp.PermissionId 
                        WHERE urp.UserRoleId IN ({roleIdParams})";

                    return (await connection.QueryAsync<Permission>(sql, parameters)).ToList();
                });

                var permissionCheckersCacheDuration = _configProvider.GetConfig().Cache.PermissionCacheDurationInSeconds;
                _cacheService.CacheData(cacheKey, permissions, permissionCheckersCacheDuration);
                return permissions;
            }

            return _cacheService.GetCacheItemByKey<List<Permission>>(cacheKey);
        }

        public async Task<string> DeletePermission(DeletePermissionDto deletePermissionDto)
        {
            // Step 1: Check that the Permission exists by PermissionId
            var existingPermission = await ExecuteWithConnectionAsync(async connection =>
                await connection.QueryFirstOrDefaultAsync<Permission>(
                    "SELECT * FROM Permissions WHERE Id = @PermissionId",
                    new { PermissionId = deletePermissionDto.Id }));

            if (existingPermission == null)
            {
                throw new AuthorizationBusinessException("AUTH-404", "Permission not found");
            }

            // Step 2: Delete the permission
            return await ExecuteWithTransactionAsync(async (connection, transaction) =>
            {
                await connection.ExecuteAsync(
                    "DELETE FROM Permissions WHERE Id = @PermissionId",
                    new { PermissionId = deletePermissionDto.Id }, transaction);

                await connection.ExecuteAsync(
                    "DELETE FROM UserRolePermissions WHERE PermissionId = @PermissionId",
                    new { PermissionId = deletePermissionDto.Id }, transaction);

                return deletePermissionDto.Id.ToString();
            });
        }

        public void InvalidateAllUserPermissions()
        {
            _cacheService.InvalidateCacheByPrefix(CacheKeyPrefix);
        }

        public void InvalidateUserPermissions(Guid userId)
        {
            string cacheKey = GetCacheKeyOfAUser(userId);
            _cacheService.InvalidateCacheByKey(cacheKey);
        }

        private string GetCacheKeyOfAUser(Guid userId)
        {
            return $"{CacheKeyPrefix}{userId.ToString()}";
        }

        /// <summary>
        /// Helper method to create and manage database connections properly
        /// </summary>
        private async Task<T> ExecuteWithConnectionAsync<T>(Func<IDbConnection, Task<T>> operation)
        {
            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            return await operation(connection);
        }

        /// <summary>
        /// Helper method for non-async database operations
        /// </summary>
        private T ExecuteWithConnection<T>(Func<IDbConnection, T> operation)
        {
            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            return operation(connection);
        }

        /// <summary>
        /// Helper method for transaction-based operations
        /// </summary>
        private async Task<T> ExecuteWithTransactionAsync<T>(Func<IDbConnection, IDbTransaction, Task<T>> operation)
        {
            using var connection = _dbConnectionFactory.CreateConnection();
            connection.Open();
            using var transaction = connection.BeginTransaction();
            
            try
            {
                var result = await operation(connection, transaction);
                transaction.Commit();
                return result;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }
    }
}