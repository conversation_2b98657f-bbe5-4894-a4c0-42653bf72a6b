using System.Reflection;
using PPG.Auth.Interfaces;
using PPG.Auth.Types;
using PPG.Auth.Exceptions;

namespace PPG.Auth.Implementations
{
    public class AuthorizationService : IAuthorizationService
    {
        private readonly IPermissionService _permissionService;
        private readonly ICacheService _cacheService;
        private readonly IUserProvider _userProvider;
        private readonly IAuthorizationConfigProvider _configProvider;
        private readonly string _cacheTypesKey;

        public AuthorizationService(
            IPermissionService permissionService,
            ICacheService cacheService,
            IUserProvider userProvider,
            IAuthorizationConfigProvider configProvider)
        {
            _permissionService = permissionService;
            _cacheService = cacheService;
            _userProvider = userProvider;
            _configProvider = configProvider;
            _cacheTypesKey = _configProvider.GetConfig().Cache.CheckerTypesCacheKeyPrefix;
        }

        public async Task IsAuthorized<TEntity>(IsAuthorizedDto<TEntity> isAuthorizedDto) where TEntity : class
        {
            var entity = isAuthorizedDto.Entity;
            var entityName = entity != null ? entity.GetType().Name : null;

            // Validate IsAuthorizedDto
            if (isAuthorizedDto.UserId == null)
            {
                throw new AuthorizationBusinessException("AUTH-400", "UserId is required.");
            }

            if (isAuthorizedDto.PermissionToCheck == null)
            {
                throw new AuthorizationBusinessException("AUTH-400", "PermissionToCheck is required.");
            }

            // Store validated UserId to avoid repeated nullable access
            var userId = isAuthorizedDto.UserId.Value;

            // Validate user exists and is active through provider
            var isUserActive = await _userProvider.IsUserActiveAsync(userId);
            if (!isUserActive)
            {
                throw new AuthorizationBusinessException("AUTH-404", "User not found or inactive.");
            }

            // Check if user has super admin role (bypass all authorization checks)
            if (await IsSuperAdminAsync(userId))
            {
                return; // Allow everything for super admin
            }

            // Fetch user permissions
            var userPermissions = await _permissionService.GetUserPermissionsAsync(userId);

            // Get all implemented permissions checkers
            var implementedPermissionsCheckersTypes = GetImplementedpermissionsCheckersTypes();

            // Get permissions to check.
            var userPermissionsToCheck = new List<Permission>();
            if (isAuthorizedDto.PermissionToCheck != null && isAuthorizedDto.PermissionToCheck.Name != null)
            {
                userPermissionsToCheck = userPermissions.Where(p => p.MachineName == isAuthorizedDto.PermissionToCheck.Name).ToList();
            }
            else if (isAuthorizedDto.PermissionToCheck != null && isAuthorizedDto.PermissionToCheck.OperationActionId != null)
            {
                userPermissionsToCheck = userPermissions
                    .Where(p => p.Entity != null && p.Entity == entityName)
                    .Where(p => p.OperationActionId == (int?)isAuthorizedDto.PermissionToCheck.OperationActionId).ToList();
            }

            // If user has no permissions, throw exception
            if (userPermissionsToCheck == null || !userPermissionsToCheck.Any())
            {
                throw new UnauthorizedException(userId,
                    isAuthorizedDto.PermissionToCheck.Name ?? isAuthorizedDto.PermissionToCheck.OperationActionId?.ToString(),
                    entityName);
            }

            // Check if user has permissions
            bool? isAuthorized = null;
            foreach (var permission in userPermissionsToCheck)
            {
                // if we do not check just the permission name, we need to have a permission checker
                if (isAuthorizedDto.PermissionToCheck.Name == null && isAuthorizedDto.PermissionToCheck.OperationActionId != null)
                {
                    // is there a permission checker for this Operation
                    var permissionCheckerType = implementedPermissionsCheckersTypes
                        .SingleOrDefault(t => (t.Name.Contains('`') ? t.Name.Split('`')[0] : t.Name) == permission.CheckerName);

                    // if there no permission checker for this Operation, throw exception
                    if (permissionCheckerType == null)
                    {
                        throw new UnauthorizedException(userId, permission.CheckerName, entityName);
                    }

                    var genericType = permissionCheckerType.MakeGenericType(typeof(TEntity));
                    var permissionChecker = Activator.CreateInstance(genericType) as PermissionCheckerBase<TEntity>;

                    if (permissionChecker != null)
                    {
                        // Check if user is authorized
                        var checkerResult = await permissionChecker.CheckAsync(entity, userId);

                        // If user is forbidden, return false without checking other permissions
                        if (checkerResult == PermissionCheckerResult.Forbidden) { isAuthorized = false; break; }

                        // If user is allowed, set isAuthorized to true
                        if (checkerResult == PermissionCheckerResult.Allowed) { isAuthorized = true; }

                        // ckeckerResult.Neutral is not considered because it means that the permission checker does not have an opinion about the permission
                        if (checkerResult == PermissionCheckerResult.Neutral) { }
                    }
                }

                // if there is no permission checker, check if user has the permission just by its name
                if (isAuthorizedDto.PermissionToCheck.Name != null && permission.MachineName == isAuthorizedDto.PermissionToCheck.Name)
                {
                    isAuthorized = true;
                }
            }

            if(isAuthorized == false)
            {
                throw new UnauthorizedException(userId,
                    isAuthorizedDto.PermissionToCheck.Name ?? isAuthorizedDto.PermissionToCheck.OperationActionId?.ToString(),
                    entityName);
            }
        }

        private List<Type> GetImplementedpermissionsCheckersTypes()
        {
            List<Type> permissionsCheckersTypes = new List<Type>();

            if (!_cacheService.CacheItemExistsByKey(_cacheTypesKey))
            {
                var entryAssembly = Assembly.GetEntryAssembly();

                var allAssemblies = new List<AssemblyName> { entryAssembly.GetName() };
                allAssemblies.AddRange(entryAssembly.GetReferencedAssemblies());

                foreach (var assembly in allAssemblies)
                {
                    // get all types that extend from PermissionCheckerBase<TEntity>
                    var types = Assembly.Load(assembly).GetExportedTypes()
                        .Where(t => t.BaseType != null && t.BaseType.IsGenericType && t.BaseType.GetGenericTypeDefinition() == typeof(PermissionCheckerBase<>));

                    //var types = typeof(PermissionCheckerBase<TEntity>).GetTypes
                    if (types != null && types.Any()) { permissionsCheckersTypes.AddRange(types); }
                }

                var permissionCheckersCacheDuration = _configProvider.GetConfig().Cache.ImplementedCheckerTypesCacheDurationInSeconds;
                _cacheService.CacheData(_cacheTypesKey, permissionsCheckersTypes, permissionCheckersCacheDuration);
            }

            permissionsCheckersTypes = _cacheService.GetCacheItemByKey<List<Type>>(_cacheTypesKey);

            return permissionsCheckersTypes;
        }

        private async Task<bool> IsSuperAdminAsync(Guid userId)
        {
            try
            {
                var config = _configProvider.GetConfig();
                var superAdminRole = config.SuperAdminRoleName;

                // If no super admin role is configured, no one has super admin privileges
                if (string.IsNullOrEmpty(superAdminRole))
                    return false;

                // Get user's role from the user provider
                var userRole = await _userProvider.GetUserRoleAsync(userId);

                // Check if the user's role matches the configured super admin role (case-insensitive)
                return string.Equals(userRole, superAdminRole, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                // If any error occurs, deny super admin access for security
                return false;
            }
        }
    }
}
