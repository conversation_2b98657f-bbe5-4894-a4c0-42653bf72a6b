using System.Data;
using Microsoft.Data.SqlClient;
using PPG.Auth.Interfaces;

namespace PPG.Auth.Implementations
{
    /// <summary>
    /// Factory for creating SQL Server database connections
    /// </summary>
    public class DbConnectionFactory : IDbConnectionFactory
    {
        private readonly string _connectionString;

        public DbConnectionFactory(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        public IDbConnection CreateConnection()
        {
            return new SqlConnection(_connectionString);
        }
    }
}