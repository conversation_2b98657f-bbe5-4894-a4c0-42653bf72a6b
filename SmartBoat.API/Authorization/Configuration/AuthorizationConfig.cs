namespace PPG.Auth.Configuration
{
    /// <summary>
    /// Configuration settings for the Authorization module
    /// </summary>
    public class AuthorizationConfig
    {
        /// <summary>
        /// Cache configuration for permission caching
        /// </summary>
        public CacheConfig Cache { get; set; } = new CacheConfig();

        /// <summary>
        /// Role name that bypasses all authorization checks (super admin)
        /// If null or empty, no role will have super admin privileges
        /// </summary>
        public string? SuperAdminRoleName { get; set; } = null;
    }

    /// <summary>
    /// Cache-related configuration settings
    /// </summary>
    public class CacheConfig
    {
        /// <summary>
        /// Prefix for permission cache keys
        /// </summary>
        public string PermissionCacheKeyPrefix { get; set; } = "auth:perm:";

        /// <summary>
        /// Prefix for checker types cache keys
        /// </summary>
        public string CheckerTypesCacheKeyPrefix { get; set; } = "auth:types:";

        /// <summary>
        /// Duration in seconds for permission cache
        /// </summary>
        public int PermissionCacheDurationInSeconds { get; set; } = 1800; // 30 minutes

        /// <summary>
        /// Duration in seconds for implemented checker types cache
        /// </summary>
        public int ImplementedCheckerTypesCacheDurationInSeconds { get; set; } = 3600; // 1 hour
    }
}