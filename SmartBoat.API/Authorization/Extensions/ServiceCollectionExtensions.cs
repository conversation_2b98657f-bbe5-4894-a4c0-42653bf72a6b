﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.Caching.Memory;
using PPG.Auth.Controllers;
using PPG.Auth.Implementations;
using PPG.Auth.Interfaces;
using System.Reflection;
using PPG.Auth.Services;

namespace PPG.Auth.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddMyPackageControllers(this IServiceCollection services)
        {
            var assembly = typeof(PermissionsController).Assembly;

            services.AddControllers()
                .PartManager.ApplicationParts.Add(new AssemblyPart(assembly));

            return services;
        }

        public static IServiceCollection AddAuthorizationScopedImplementations(this IServiceCollection services)
        {
            // Note: Provider implementations (IUserProvider, IRoleProvider) should be registered 
            // by the main application since they depend on main application's data access
            services.AddScoped<IPermissionService, PermissionService>();
            services.AddScoped<IAuthorizationService, AuthorizationService>();
            services.AddSingleton<ICacheService, CacheService>();
            services.AddMemoryCache();

            return services;
        }
    }
}