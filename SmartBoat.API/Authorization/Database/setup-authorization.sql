-- ============================================================================
-- SmartBoat Authorization Module Database Setup
-- ============================================================================
-- This script creates authorization tables and seeds core permissions
-- Called by main setup-database.sh script

USE SmartBoat;
GO

PRINT 'Setting up Authorization Module...';

-- ============================================================================
-- 1. CREATE AUTHORIZATION TABLES
-- ============================================================================

-- Create Permissions table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Permissions')
BEGIN
    CREATE TABLE Permissions (
        Id uniqueidentifier NOT NULL PRIMARY KEY,
        Entity nvarchar(200) NOT NULL,
        CheckerName nvarchar(200) NULL,
        MachineName nvarchar(200) NOT NULL UNIQUE,
        DisplayName nvarchar(200) NOT NULL,
        OperationActionId int NULL,
        Version int NULL,
        Created datetime2(7) NULL,
        Changed datetime2(7) NULL,
        CreatorId uniqueidentifier NULL,
        ChangedUser uniqueidentifier NULL
    );
    PRINT 'Created Permissions table';
END
ELSE
BEGIN
    PRINT 'Permissions table already exists';
END

-- Create UserRolePermissions table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'UserRolePermissions')
BEGIN
    CREATE TABLE UserRolePermissions (
        Id uniqueidentifier NOT NULL PRIMARY KEY,
        UserRoleId uniqueidentifier NOT NULL,
        PermissionId uniqueidentifier NOT NULL
    );
    PRINT 'Created UserRolePermissions table';
END
ELSE
BEGIN
    PRINT 'UserRolePermissions table already exists';
END

-- ============================================================================
-- 2. SEED CORE AUTHORIZATION PERMISSIONS
-- ============================================================================

PRINT 'Seeding core authorization permissions...';

-- Core Permission Management Permissions (from PermissionsController)
INSERT INTO Permissions (Id, Entity, CheckerName, MachineName, DisplayName, OperationActionId, Version, Created, CreatorId)
SELECT NEWID(), 'Permission', NULL, 'CreatePermission', 'Create Permission', NULL, 1, GETUTCDATE(), NULL
WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE MachineName = 'CreatePermission');

INSERT INTO Permissions (Id, Entity, CheckerName, MachineName, DisplayName, OperationActionId, Version, Created, CreatorId)
SELECT NEWID(), 'Permission', NULL, 'UpdatePermission', 'Update Permission', NULL, 1, GETUTCDATE(), NULL
WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE MachineName = 'UpdatePermission');

INSERT INTO Permissions (Id, Entity, CheckerName, MachineName, DisplayName, OperationActionId, Version, Created, CreatorId)
SELECT NEWID(), 'Permission', NULL, 'ListPermissions', 'List Permissions', NULL, 1, GETUTCDATE(), NULL
WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE MachineName = 'ListPermissions');

INSERT INTO Permissions (Id, Entity, CheckerName, MachineName, DisplayName, OperationActionId, Version, Created, CreatorId)
SELECT NEWID(), 'Permission', NULL, 'DeletePermission', 'Delete Permission', NULL, 1, GETUTCDATE(), NULL
WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE MachineName = 'DeletePermission');

INSERT INTO Permissions (Id, Entity, CheckerName, MachineName, DisplayName, OperationActionId, Version, Created, CreatorId)
SELECT NEWID(), 'Permission', NULL, 'ManagePermissionCache', 'Manage Permission Cache', NULL, 1, GETUTCDATE(), NULL
WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE MachineName = 'ManagePermissionCache');

-- Core Cache Management Permissions (from CacheController)
INSERT INTO Permissions (Id, Entity, CheckerName, MachineName, DisplayName, OperationActionId, Version, Created, CreatorId)
SELECT NEWID(), 'Cache', NULL, 'ManageCache', 'Manage Cache', NULL, 1, GETUTCDATE(), NULL
WHERE NOT EXISTS (SELECT 1 FROM Permissions WHERE MachineName = 'ManageCache');

-- ============================================================================
-- 3. SUMMARY
-- ============================================================================

DECLARE @CorePermissions int = (SELECT COUNT(*) FROM Permissions WHERE Entity IN ('Permission', 'Cache'));

PRINT '============================================================================';
PRINT 'Authorization Module Setup Complete';
PRINT '============================================================================';
PRINT 'Core Authorization Permissions Created: ' + CAST(@CorePermissions AS nvarchar(10));
PRINT '';
PRINT 'Permissions seeded:';
PRINT '- Permission management (Create, Update, List, Delete permissions)';
PRINT '- Cache management (Manage cache and permission cache)';
PRINT '';
PRINT 'Entity-specific permissions should be seeded by main project if needed.';
PRINT '============================================================================';