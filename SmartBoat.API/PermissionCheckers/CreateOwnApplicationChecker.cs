
using PPG.Auth.Helpers;
using PPG.Auth.Types;

namespace SmartBoat.API.PermissionCheckers
{
    public class CreateOwnApplicationChecker<TEntity> : PermissionCheckerBase<TEntity>
    {
        public override async Task<PermissionCheckerResult> CheckAsync(TEntity entity, Guid userId)
        {
            if (entity == null)
            {
                return PermissionCheckerResult.Forbidden;
            }

            // Safely get the OrganizationId property using reflection
            var entityOrganizationId = ReflectionHelper.GetPropertyValue<Guid?>(entity, "OrganizationId");

            // If entity doesn't have OrganizationId property or it's null, deny access
            if (!entityOrganizationId.HasValue)
            {
                return PermissionCheckerResult.Forbidden;
            }

            // For now, just check that organization exists
            // TODO: Add actual organization membership validation
            return PermissionCheckerResult.Allowed;
        }
    }
}