

using PPG.Auth.Helpers;
using PPG.Auth.Types;

namespace SmartBoat.API.PermissionCheckers
{
    public class DeleteOwnGenericChecker<TEntity> : PermissionCheckerBase<TEntity>
    {
        public override async Task<PermissionCheckerResult> CheckAsync(TEntity entity, Guid userId)
        {
            if (entity == null)
            {
                return PermissionCheckerResult.Forbidden;
            }

            // Check different ownership patterns for delete operations
            
            // 1. CreatorId (standard ownership pattern)
            var entityCreatorId = ReflectionHelper.GetPropertyValue<Guid?>(entity, "CreatorId");
            if (entityCreatorId.HasValue)
            {
                return entityCreatorId.Value == userId 
                    ? PermissionCheckerResult.Allowed 
                    : PermissionCheckerResult.Forbidden;
            }

            // 2. UserId (for user-targeted/created entities: Notification, SupportRequest, Feedback, AuditLog)
            var entityUserId = ReflectionHelper.GetPropertyValue<Guid?>(entity, "UserId");
            if (entityUserId.HasValue)
            {
                return entityUserId.Value == userId 
                    ? PermissionCheckerResult.Allowed 
                    : PermissionCheckerResult.Forbidden;
            }

            // 3. Id property (for entities where user can delete their own record: User entity)
            // Note: Deleting own user account should be handled carefully in business logic
            var entityId = ReflectionHelper.GetPropertyValue<Guid?>(entity, "Id");
            if (entityId.HasValue)
            {
                return entityId.Value == userId 
                    ? PermissionCheckerResult.Allowed 
                    : PermissionCheckerResult.Forbidden;
            }

            // 4. GeneratedBy (for Report entity - handles int type)
            var generatedByInt = ReflectionHelper.GetPropertyValue<int?>(entity, "GeneratedBy");
            if (generatedByInt.HasValue)
            {
                // Note: This is a data model inconsistency - Report uses int while User uses Guid
                // For now, we'll assume they don't match (proper fix would be to align data types)
                return PermissionCheckerResult.Forbidden;
            }

            // If no ownership pattern found, deny access
            return PermissionCheckerResult.Forbidden;
        }
    }
}
