
using PPG.Auth.Helpers;
using PPG.Auth.Types;

namespace SmartBoat.API.PermissionCheckers
{
    public class ReadGroupG<PERSON>ic<PERSON>hecker<TEntity> : PermissionCheckerBase<TEntity>
    {
        public override async Task<PermissionCheckerResult> CheckAsync(TEntity entity, Guid userId)
        {
            if (entity == null)
            {
                return PermissionCheckerResult.Forbidden;
            }

            // For group access, we need to verify business relationships between entities and users
            // Since this involves complex business logic (company memberships, hierarchies, etc.),
            // we return Neutral to let the role-based permission system handle these checks
            // using the full business context and services available in the main application

            // 1. Direct company relationship (User, Vessel, Company)
            var entityCompanyId = ReflectionHelper.GetPropertyValue<Guid?>(entity, "CompanyId");
            if (entityCompanyId.HasValue)
            {
                // Entity has company relationship - let role-based permissions verify user's company membership
                return PermissionCheckerResult.Neutral;
            }

            // 2. User-targeted entities (Notification, SupportRequest, Feedback, AuditLog)
            var entityUserId = ReflectionHelper.GetPropertyValue<Guid?>(entity, "UserId");
            if (entityUserId.HasValue)
            {
                // Entity is targeted to a specific user - let role-based permissions verify group access
                return PermissionCheckerResult.Neutral;
            }

            // 3. Sensor hierarchy (Sensor -> Vessel -> Company)
            var vesselId = ReflectionHelper.GetPropertyValue<Guid?>(entity, "VesselId");
            if (vesselId.HasValue)
            {
                // Entity has vessel relationship - let role-based permissions handle hierarchy
                return PermissionCheckerResult.Neutral;
            }

            // 4. Alert relationships (Alert -> EntityId/EntityType)
            var entityIdRef = ReflectionHelper.GetPropertyValue<Guid?>(entity, "EntityId");
            var entityType = ReflectionHelper.GetPropertyValue<string>(entity, "EntityType");
            if (entityIdRef.HasValue && !string.IsNullOrEmpty(entityType))
            {
                // Entity references other entities - let role-based permissions handle complex relationships
                return PermissionCheckerResult.Neutral;
            }

            // 5. Company relationships via CustomerId
            var customerId = ReflectionHelper.GetPropertyValue<Guid?>(entity, "CustomerId");
            if (customerId.HasValue)
            {
                // Entity has customer relationship - let role-based permissions handle customer access
                return PermissionCheckerResult.Neutral;
            }

            // 6. Global entities without specific relationships (Role, Customer)
            var entityName = typeof(TEntity).Name;
            if (entityName == "Role" || entityName == "Customer")
            {
                // Global entities - let role-based permissions handle access control
                return PermissionCheckerResult.Neutral;
            }

            // 7. Report entity (has inconsistent data types)
            var generatedBy = ReflectionHelper.GetPropertyValue<int?>(entity, "GeneratedBy");
            if (generatedBy.HasValue)
            {
                // Report entity - let role-based permissions handle access control
                return PermissionCheckerResult.Neutral;
            }

            // If no clear relationship pattern found, deny access
            return PermissionCheckerResult.Forbidden;
        }
    }
}
