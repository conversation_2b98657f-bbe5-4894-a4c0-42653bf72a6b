# SmartBoat API - Claude Development Guide

This file contains information for <PERSON> to understand the project structure and common tasks.

## Project Overview
- **Technology**: ASP.NET Core Web API (.NET 8)
- **Database**: SQL Server (via Docker)
- **Architecture**: Service-based architecture with repositories

## Database
- **Connection String**: Located in `appsettings.Development.json`
- **Setup Script**: `./Database/setup-database.sh` (uses Docker sqlcmd)
- **Tables**: All SQL files are in the `Database/` folder

## Running the Project
```bash
# Start SQL Server
docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=YourStrong@Passw0rd" -p 1433:1433 --name sqlserver -d mcr.microsoft.com/mssql/server:2022-latest

# Setup database
./Database/setup-database.sh

# Run application
dotnet run
```

## Project Structure
- `Controllers/` - API controllers
- `Implementations/` - Service implementations (grouped by service)
- `Interfaces/` - Service interfaces
- `Types/` - DTOs and models
- `Database/` - SQL table definitions
- `Jobs/` - Quartz.NET background job implementations
- `Authorization/` - Permission and authorization system
- `Notification/` - Notification system

## Common Commands
- **Build**: `dotnet build`
- **Run**: `dotnet run`
- **Test**: No test framework configured yet

## Development Notes
- Environment automatically set to Development via `Properties/launchSettings.json`
- Swagger available at https://localhost:7001/swagger in Development
- Database setup script uses Docker to avoid requiring local SQL tools

## Email Processing System Status

### ✅ PRODUCTION READY - Fully Implemented
- **Microsoft Graph Authentication**: ✅ COMPLETED (OAuth2 PKCE with token refresh)
- **Manual Email Processing**: ✅ COMPLETED (Full pipeline: fetch → download → parse → store)
- **Scheduled Processing**: ✅ COMPLETED (Quartz.NET daily at midnight Greece time)
- **Frontend Integration**: ✅ COMPLETED (Schedule toggles, real-time status, Greece time display)
- **Token Management**: ✅ COMPLETED (90-day lifecycle with automatic refresh)
- **Database Schema**: ✅ COMPLETED (AuthTokens, CsvSensorData, EmailProcessingLog, EmailProcessingSchedule)

### Current Implementation Status (September 2025)
- **Authentication**: Microsoft Graph integration fully operational
- **Manual Processing**: Complete email → ZIP → CSV → database pipeline working
- **Scheduled Processing**: Quartz.NET job configured for midnight Europe/Athens timezone
- **Database Operations**: Bulk inserts, duplicate prevention, audit trail fully functional
- **Frontend**: Schedule toggle, Greece time display, error handling, success feedback
- **Production Ready**: All core functionality implemented and tested

### Scheduling System Details
- **Execution Time**: Daily at 00:00 (midnight) Greece time (Europe/Athens timezone)
- **Automatic DST**: Handles Eastern European Time (UTC+2) and Eastern European Summer Time (UTC+3)
- **Job Framework**: Quartz.NET with dependency injection and hosted service integration
- **Persistence**: Schedule configuration stored in EmailProcessingSchedule table
- **User Control**: Frontend toggle to enable/disable scheduled processing
- **Error Handling**: Comprehensive logging, graceful failure handling, retry logic

### Key Files Implemented

#### Backend Core Implementation
- `Jobs/DailyEmailProcessingJob.cs` - **NEW** Quartz.NET background job for midnight Greece time execution
- `Implementations/EmailProcessingService/ProcessEmails.EmailProcessingService.cs` - **COMPLETED** Full email processing pipeline
- `Implementations/EmailProcessingService/ScheduleManagement.EmailProcessingService.cs` - **NEW** Real schedule management implementation
- `Implementations/MicrosoftGraphService/GetEmails.MicrosoftGraphService.cs` - Microsoft Graph email fetching
- `Controllers/EmailProcessingController.cs` - All REST endpoints functional with schedule management
- `Program.cs` - **UPDATED** Quartz.NET registration with Europe/Athens timezone configuration

#### Database Schema
- `Database/AuthTokens.sql` - Microsoft Graph token storage with 90-day refresh cycle
- `Database/CsvSensorData.sql` - Parsed sensor data storage with duplicate prevention
- `Database/EmailProcessingLog.sql` - Processing audit trail and error logging
- `Database/EmailProcessingSchedule.sql` - **NEW** Schedule configuration persistence

#### Data Models & Types
- `Types/EmailProcessingSchedule.cs` - **NEW** Schedule models and DTOs
- `Types/ProcessingStatusDto.cs` - Processing status responses
- `Types/ProcessingSummaryDto.cs` - Processing history and statistics
- `Interfaces/IEmailProcessingService.cs` - **UPDATED** Added schedule management methods

#### Frontend Integration
- `EmailProcessingSettings.tsx` - **ENHANCED** Schedule toggle, Greece time display, real-time status
- `services/emailProcessingService.js` - **UPDATED** Added schedule management API calls
- `hooks/queries/useEmailProcessingQueries.js` - **UPDATED** Added useUpdateSchedule hook
- `microsoftAuth.js` - OAuth2 PKCE implementation with proper error handling

### API Endpoints Available

#### Email Processing
- `POST /api/emailprocessing/process-emails` - Manual email sync with customizable options
- `POST /api/emailprocessing/summary` - Processing history and statistics
- `POST /api/emailprocessing/token-status` - Microsoft Graph token status and expiration

#### Token Management  
- `POST /api/emailprocessing/exchange-token` - OAuth2 authorization code exchange
- `POST /api/emailprocessing/save-token` - Manual token storage (fallback)

#### Schedule Management
- `POST /api/emailprocessing/schedule-status` - Get current schedule configuration and next run time
- `POST /api/emailprocessing/schedule` - Simple enable/disable toggle (legacy)
- `POST /api/emailprocessing/update-schedule` - **NEW** Full schedule configuration management

### Testing & Operations

#### Running the Complete System
```bash
# 1. Start SQL Server and setup database
docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=YourStrong@Passw0rd" -p 1433:1433 --name sqlserver -d mcr.microsoft.com/mssql/server:2022-latest
./Database/setup-database.sh

# 2. Run application (Quartz.NET starts automatically)
dotnet run

# 3. Access Swagger UI for API testing
# https://localhost:7001/swagger
```

#### Testing Schedule System
```bash
# Test schedule status
curl -X POST http://localhost:5000/api/emailprocessing/schedule-status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"header": {"userId": "test-user-id"}, "payload": {}}'

# Enable scheduled processing
curl -X POST http://localhost:5000/api/emailprocessing/update-schedule \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "header": {"userId": "test-user-id"}, 
    "payload": {
      "isEnabled": true, 
      "daysBack": 7, 
      "description": "Daily email processing enabled"
    }
  }'
```

### Troubleshooting

#### Common Issues
- **Schedule not running**: Check Quartz.NET logs for job registration errors
- **Timezone issues**: Verify Europe/Athens timezone is available on the system
- **Token expired**: Use frontend to re-authenticate with Microsoft Graph
- **Database connection**: Ensure SQL Server container is running and accessible

#### Monitoring
- **Application Logs**: Check for Quartz.NET job execution at midnight Greece time
- **Database Logs**: Monitor EmailProcessingLog table for processing results
- **Schedule Status**: Use API endpoints or frontend UI to verify schedule configuration
- **Token Health**: Monitor token expiration and refresh cycle (90-day limit)

### Future Enhancements
- Custom schedule intervals (hourly, weekly, monthly)
- Multiple schedule configurations per user
- Advanced processing options and filtering
- Real-time processing progress via WebSocket
- Performance monitoring and alerting integration