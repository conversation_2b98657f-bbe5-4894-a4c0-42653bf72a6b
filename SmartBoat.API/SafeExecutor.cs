using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.ControllersExceptions;
using SmartBoat.API.Types;
using PPG.Auth.Exceptions;

namespace SmartBoat.API.Controllers
{
    public static class SafeExecutor
    {
        public static async Task<IActionResult> ExecuteAsync(Func<Task<IActionResult>> func)
        {
            try
            {
                return await func();
            }
            catch (BusinessException ex)
            {
                var response = new Response<object>
                {
                    Exception = new ResponseException
                    {
                        Code = ex.Code,
                        Description = ex.Description
                    }
                };
                return new OkObjectResult(response);
            }
            catch (TechnicalException ex)
            {
                var response = new Response<object>
                {
                    Exception = new ResponseException
                    {
                        Code = ex.Code,
                        Description = ex.Description
                    }
                };
                return new OkObjectResult(response);
            }
            catch (AuthorizationException ex)
            {
                var response = new Response<object>
                {
                    Exception = new ResponseException
                    {
                        Code = ex.Code,
                        Description = ex.Description
                    }
                };
                return new OkObjectResult(response);
            }
            catch (Exception)
            {
                var response = new Response<object>
                {
                    Exception = new ResponseException
                    {
                        Code = "1001",
                        Description = "A technical exception has occurred, please contact your system administrator"
                    }
                };
                return new OkObjectResult(response);
            }
        }
    }
}