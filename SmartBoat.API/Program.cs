using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Data.SqlClient;
using System.Data;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Services;
using SmartBoat.API.Implementations.AlertService;
using SmartBoat.API.Implementations;
using PPG.Auth.Interfaces;
using PPG.Auth.Implementations;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using PPG.Auth.Extensions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using SmartBoat.API.Middleware;
using Quartz;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add JWT Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        var jwtSettings = builder.Configuration.GetSection("Jwt");
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]!)),
            ValidateIssuer = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidateAudience = true,
            ValidAudience = jwtSettings["Audience"],
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };
    });

builder.Services.AddAuthorization();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        var corsConfig = builder.Configuration.GetSection("Cors");
        var allowedOrigins = corsConfig.GetSection("AllowedOrigins").Get<string[]>() ?? new[] { "http://localhost:3000" };
        
        policy.WithOrigins(allowedOrigins)
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// Memory Cache
builder.Services.AddMemoryCache();

// Database Connection
builder.Services.AddScoped<IDbConnection>(provider =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    var conn = new SqlConnection(connectionString);
    conn.Open();
    return conn;
});
builder.Services.AddScoped<IAutoCodeDbOperationsService, AutoCodeDbOperationsService>();
builder.Services.AddScoped<IDatabaseService>(provider =>
{
    var autoCodeService = provider.GetRequiredService<IAutoCodeDbOperationsService>();
    return autoCodeService.GetDatabaseService();
});


builder.Services.AddDatabaseService();


builder.Services.AddAuthorizationScopedImplementations();
builder.Services.AddMyPackageControllers();

// Database Connection Factory for Authorization Module
builder.Services.AddSingleton<PPG.Auth.Interfaces.IDbConnectionFactory>(sp =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    return new PPG.Auth.Implementations.DbConnectionFactory(connectionString);
});

// Authorization Provider Services (bridge main app to authorization module)
builder.Services.AddScoped<IUserProvider, UserProvider>();
builder.Services.AddScoped<IRoleProvider, RoleProvider>();
builder.Services.AddScoped<IAuthorizationConfigProvider, AuthorizationConfigProvider>();

// Authorization Services (registered via extension method)
// Note: Providers are registered above, core authorization services below

// Core Services
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<ICompanyService, CompanyService>();
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<ISubscriptionService, SubscriptionService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IVesselService, VesselService>();
builder.Services.AddScoped<IOwnerService, OwnerService>();
builder.Services.AddScoped<IImageService, ImageService>();

// Factory to break circular dependency between Sensor and Vessel services
builder.Services.AddScoped<IVesselServiceFactory, VesselServiceFactory>();
builder.Services.AddScoped<ISensorService, SensorService>();
builder.Services.AddScoped<ISensorDataPointService, SensorDataPointService>();
builder.Services.AddScoped<IAuditLogService, AuditLogService>();
builder.Services.AddScoped<IAlertService, AlertService>();

// Notification Services
builder.Services.AddScoped<INotificationPreferenceService, NotificationPreferenceService>();
builder.Services.AddScoped<INotificationService, NotificationService>();
builder.Services.AddScoped<IEntityNotificationService, EntityNotificationService>();

// JWT and Auth Services
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IAuthService, AuthService>();

// Email Processing Services
builder.Services.AddScoped<ITokenStorageService, TokenStorageService>();
builder.Services.AddScoped<ICsvSensorDataService, CsvSensorDataService>();
builder.Services.AddScoped<IEmailProcessingService, EmailProcessingService>();
builder.Services.AddHttpClient<IUrlDownloadService, UrlDownloadService>();
builder.Services.AddScoped<IMicrosoftGraphService, MicrosoftGraphService>();
builder.Services.AddScoped<ICsvProcessingService, CsvProcessingService>();
builder.Services.AddScoped<IFileManagementService, FileManagementService>();

// CSV Column Mapping Services
builder.Services.AddScoped<ICsvColumnMapper, CsvColumnMapper>();
builder.Services.AddScoped<IVesselSensorManager, VesselSensorManager>();

// Microsoft Graph HTTP Client
builder.Services.AddHttpClient("MicrosoftGraph", client =>
{
    client.BaseAddress = new Uri("https://graph.microsoft.com/v1.0/");
    client.DefaultRequestHeaders.Add("User-Agent", "SmartBoat-API/1.0");
});

// Quartz.NET for scheduled email processing
builder.Services.Configure<QuartzOptions>(options =>
{
    options.Scheduling.IgnoreDuplicates = true;
    options.Scheduling.OverWriteExistingData = true;
});

builder.Services.AddQuartz(q =>
{
    // Configure the daily email processing job
    var jobKey = new JobKey("daily-email-processing-job");
    q.AddJob<SmartBoat.API.Jobs.DailyEmailProcessingJob>(opts => 
        opts.WithIdentity(jobKey)
            .WithDescription("Daily email processing job that runs at midnight Greece time"));
    
    // Configure trigger for midnight Greece time (Europe/Athens timezone handles DST automatically)
    q.AddTrigger(opts => opts
        .ForJob(jobKey)
        .WithIdentity("daily-email-processing-trigger")
        .WithDescription("Triggers daily email processing at midnight Greece time")
        .WithCronSchedule("0 0 0 * * ?", cronOptions => 
            cronOptions.InTimeZone(TimeZoneInfo.FindSystemTimeZoneById("Europe/Athens")))
        .StartNow()); // Start scheduling immediately
});

// Add Quartz.NET hosted service
builder.Services.AddQuartzHostedService(options =>
{
    options.WaitForJobsToComplete = true;
});

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowFrontend");
app.UseHttpsRedirection();
app.UseAuthentication();
app.UseMiddleware<JwtUserMiddleware>();
app.UseAuthorization();
app.MapControllers();

app.Run();