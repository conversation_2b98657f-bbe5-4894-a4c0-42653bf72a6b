using Microsoft.Extensions.Logging;
using Quartz;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using System;
using System.Linq;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Jobs
{
    /// <summary>
    /// Background job for automated daily email processing at midnight Greece time
    /// </summary>
    [DisallowConcurrentExecution]
    public class DailyEmailProcessingJob : IJob
    {
        private readonly IEmailProcessingService _emailProcessingService;
        private readonly ILogger<DailyEmailProcessingJob> _logger;
        private readonly IDatabaseService _databaseService;

        public DailyEmailProcessingJob(
            IEmailProcessingService emailProcessingService,
            ILogger<DailyEmailProcessingJob> logger,
            IDatabaseService databaseService)
        {
            _emailProcessingService = emailProcessingService;
            _logger = logger;
            _databaseService = databaseService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var jobId = Guid.NewGuid();
            var startTime = DateTime.UtcNow;

            _logger.LogInformation("🕛 [SCHEDULED JOB] Starting daily email processing job {JobId} at {StartTime} UTC",
                jobId, startTime);

            try
            {
                // Get the Super Admin user ID for system operations
                var systemUserId = await GetSystemUserIdAsync();

                // Use default processing options for scheduled runs
                var processOptions = new ProcessEmailsDto
                {
                    DaysBack = 7,           // Look back 7 days for emails
                    ForceReprocess = false  // Don't reprocess already processed emails
                };

                _logger.LogInformation("📧 [SCHEDULED JOB] Processing emails from the last {DaysBack} days",
                    processOptions.DaysBack);

                // Execute the same processing logic used for manual processing
                var result = await _emailProcessingService.ProcessEmailsAsync(processOptions, systemUserId);

                var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;

                if (result.Exception == null)
                {
                    _logger.LogInformation("✅ [SCHEDULED JOB] Daily email processing completed successfully in {Duration}ms. Job ID: {JobId}",
                        duration, jobId);

                    if (result.Payload != null)
                    {
                        _logger.LogInformation("📊 [SCHEDULED JOB] Processing results: {ProcessedEmails}/{TotalEmails} emails processed",
                            result.Payload.ProcessedEmails, result.Payload.TotalEmails);
                    }
                }
                else
                {
                    _logger.LogError("❌ [SCHEDULED JOB] Daily email processing failed after {Duration}ms. Job ID: {JobId}. Error: {Error}",
                        duration, jobId, result.Exception.Description);
                }
            }
            catch (Exception ex)
            {
                var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;
                _logger.LogError(ex, "💥 [SCHEDULED JOB] Fatal error during scheduled email processing after {Duration}ms. Job ID: {JobId}",
                    duration, jobId);

                // Don't rethrow - we want the job to complete even if processing fails
                // Quartz will handle retries based on configuration
            }

            _logger.LogInformation("🏁 [SCHEDULED JOB] Daily email processing job {JobId} completed", jobId);
        }

        /// <summary>
        /// Gets the Super Admin user ID for system operations
        /// </summary>
        /// <returns>Super Admin user ID</returns>
        private async Task<Guid> GetSystemUserIdAsync()
        {
            try
            {
                // Look for the Super Admin user (system user for scheduled operations)
                var superAdminUsers = await _databaseService.SelectAsync<User>(new { Role = "Super Admin", Status = "Active" });
                var superAdminUser = superAdminUsers?.FirstOrDefault();

                if (superAdminUser != null && superAdminUser.Id.HasValue)
                {
                    _logger.LogInformation("🔑 [SCHEDULED JOB] Using Super Admin user {UserId} ({Username}) for system operations",
                        superAdminUser.Id.Value, superAdminUser.Username);
                    return superAdminUser.Id.Value;
                }

                // Fallback: look for any active Super Admin by username
                var fallbackUsers = await _databaseService.SelectAsync<User>(new { Username = "superadmin", Status = "Active" });
                var fallbackUser = fallbackUsers?.FirstOrDefault();

                if (fallbackUser != null && fallbackUser.Id.HasValue)
                {
                    _logger.LogInformation("🔑 [SCHEDULED JOB] Using fallback Super Admin user {UserId} ({Username}) for system operations",
                        fallbackUser.Id.Value, fallbackUser.Username);
                    return fallbackUser.Id.Value;
                }

                // If no Super Admin found, try to create one or use a fallback
                _logger.LogError("❌ [SCHEDULED JOB] No Super Admin user found for system operations.");
                _logger.LogInformation("🔧 [SCHEDULED JOB] Attempting to find any active user as fallback...");

                // Fallback: try to find any active user
                var anyActiveUsers = await _databaseService.SelectAsync<User>(new { Status = "Active" });
                var anyActiveUser = anyActiveUsers?.FirstOrDefault();

                if (anyActiveUser != null && anyActiveUser.Id.HasValue)
                {
                    _logger.LogWarning("⚠️ [SCHEDULED JOB] Using fallback active user {UserId} ({Username}) for system operations",
                        anyActiveUser.Id.Value, anyActiveUser.Username);
                    return anyActiveUser.Id.Value;
                }

                _logger.LogError("💥 [SCHEDULED JOB] No active users found at all. Scheduled processing will fail.");
                return Guid.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "💥 [SCHEDULED JOB] Error retrieving system user ID. Using empty GUID (will fail authorization).");
                return Guid.Empty;
            }
        }
    }
}