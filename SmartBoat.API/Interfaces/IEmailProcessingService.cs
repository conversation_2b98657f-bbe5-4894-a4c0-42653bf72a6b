using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface IEmailProcessingService
    {
        Task<Response<ProcessingStatusDto>> ProcessEmailsAsync(ProcessEmailsDto request, Guid userId);
        Task<Response<ProcessingStatusDto>> GetProcessingStatusAsync(Guid userId);
        Task<Response<ProcessingSummaryDto>> GetProcessingSummaryAsync(Guid userId);
        Task<Response<bool>> ScheduleProcessingAsync(bool enabled, Guid userId);
        Task<Response<ScheduleStatusDto>> GetScheduleStatusAsync(Guid userId);
        Task<Response<ScheduleStatusDto>> UpdateScheduleAsync(UpdateScheduleDto updateScheduleDto, Guid userId);
    }
}