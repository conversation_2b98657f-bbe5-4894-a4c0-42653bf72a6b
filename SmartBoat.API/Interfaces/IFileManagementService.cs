using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface IFileManagementService
    {
        Task<Response<List<ExtractedFileDto>>> ExtractZipFileAsync(byte[] zipContent, string fileName, Guid operationId);
        Task<Response<bool>> CleanupTemporaryFilesAsync(Guid operationId);
        Task<Response<string>> SaveTemporaryFileAsync(byte[] content, string fileName, Guid operationId);
    }
}