using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for managing owner-related operations.
    /// </summary>
    public interface IOwnerService
    {
        /// <summary>
        /// Creates a new owner based on the provided data.
        /// </summary>
        /// <param name="request">The data required to create an owner.</param>
        /// <param name="userId">The ID of the user creating the owner.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the created owner details.</returns>
        Task<OwnerDto> Create(CreateOwnerDto request, Guid userId);

        /// <summary>
        /// Retrieves the details of a specific owner.
        /// </summary>
        /// <param name="request">The data required to identify the owner.</param>
        /// <param name="userId">The ID of the user requesting the owner details.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the owner details.</returns>
        Task<OwnerDto> Get(DeleteOwnerDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of owners based on the provided criteria.
        /// </summary>
        /// <param name="request">The criteria for filtering and paginating the list of owners.</param>
        /// <param name="userId">The ID of the user requesting the list of owners.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of owners and related metadata.</returns>
        Task<ReturnListOwnerDto> GetList(ListOwnerRequestDto request, Guid userId);

        /// <summary>
        /// Updates the details of an existing owner.
        /// </summary>
        /// <param name="request">The data required to update the owner.</param>
        /// <param name="userId">The ID of the user updating the owner.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the updated owner details.</returns>
        Task<OwnerDto> Update(UpdateOwnerDto request, Guid userId);

        /// <summary>
        /// Deletes a specific owner and all its related data.
        /// </summary>
        /// <param name="request">The data required to identify the owner to delete.</param>
        /// <param name="userId">The ID of the user deleting the owner.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if deletion was successful.</returns>
        Task<bool> Delete(DeleteOwnerDto request, Guid userId);
    }
}