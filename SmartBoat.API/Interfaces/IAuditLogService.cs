using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for the service layer that handles the business logic for the AuditLog Web API endpoints.
    /// </summary>
    public interface IAuditLogService
    {
        /// <summary>
        /// Creates an audit log entry based on the provided payload.
        /// </summary>
        /// <param name="payload">The payload containing the data for the audit log.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the response payload.</returns>
        Task<ResponsePayload> CreateAuditLog(AuditLogPayload payload);

        /// <summary>
        /// Retrieves the audit log entries based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria for retrieving the audit logs.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the response payload.</returns>
        Task<ResponsePayload> GetAuditLogs(AuditLogFilter filter);

        /// <summary>
        /// Updates an existing audit log entry based on the provided payload.
        /// </summary>
        /// <param name="payload">The payload containing the updated data for the audit log.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the response payload.</returns>
        Task<ResponsePayload> UpdateAuditLog(AuditLogPayload payload);

        /// <summary>
        /// Deletes an audit log entry based on the provided identifier.
        /// </summary>
        /// <param name="id">The identifier of the audit log entry to delete.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the response payload.</returns>
        Task<ResponsePayload> DeleteAuditLog(string id);

        /// <summary>
        /// Retrieves the details of a specific audit log entry based on the provided identifier.
        /// </summary>
        /// <param name="id">The identifier of the audit log entry to retrieve.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the response payload.</returns>
        Task<ResponsePayload> GetAuditLogById(string id);
    }

    /// <summary>
    /// Represents the payload for creating or updating an audit log entry.
    /// </summary>
    public class AuditLogPayload
    {
        // Define properties for the audit log payload
    }

    /// <summary>
    /// Represents the filter criteria for retrieving audit log entries.
    /// </summary>
    public class AuditLogFilter
    {
        // Define properties for the audit log filter
    }

    /// <summary>
    /// Represents the response payload returned by the API endpoints.
    /// </summary>
    public class ResponsePayload
    {
        // Define properties for the response payload
    }
}