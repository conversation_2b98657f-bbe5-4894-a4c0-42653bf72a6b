using SmartBoat.API.Types;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for report-related operations
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// Generates a new report based on the provided request
        /// </summary>
        /// <param name="request">DTO containing parameters for report generation</param>
        /// <param name="userId">ID of the user requesting the report</param>
        /// <returns>Task that resolves to a string containing the report identifier or location</returns>
        Task<string> Generate(GenerateReportDto request, Guid userId);

        /// <summary>
        /// Retrieves a specific report based on the provided request
        /// </summary>
        /// <param name="request">DTO containing parameters to identify the report</param>
        /// <param name="userId">ID of the user requesting the report</param>
        /// <returns>Task that resolves to the requested ReportDto</returns>
        Task<ReportDto> Get(GetReportRequestDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of reports based on the provided criteria
        /// </summary>
        /// <param name="request">DTO containing filtering and pagination parameters</param>
        /// <param name="userId">ID of the user requesting the report list</param>
        /// <returns>Task that resolves to a paginated list of reports</returns>
        Task<ReturnListReportDto> GetList(ListReportRequestDto request, Guid userId);
    }
}