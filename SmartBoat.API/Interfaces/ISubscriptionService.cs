using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Defines the contract for managing subscription-related operations.
    /// </summary>
    public interface ISubscriptionService
    {
        /// <summary>
        /// Creates a new subscription based on the provided data.
        /// </summary>
        /// <param name="request">The data required to create a subscription.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the created subscription details.</returns>
        Task<SubscriptionDto> Create(CreateSubscriptionDto request, Guid userId);

        /// <summary>
        /// Retrieves the details of a specific subscription.
        /// </summary>
        /// <param name="request">The data required to identify the subscription.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the subscription details.</returns>
        Task<SubscriptionDto> Get(SubscriptionRequestDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of subscriptions based on the provided criteria.
        /// </summary>
        /// <param name="request">The data required to filter and paginate the subscription list.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of subscriptions and pagination details.</returns>
        Task<ReturnListSubscriptionDto> GetList(ListSubscriptionRequestDto request, Guid userId);

        /// <summary>
        /// Updates the details of an existing subscription.
        /// </summary>
        /// <param name="request">The data required to update the subscription.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the updated subscription details.</returns>
        Task<SubscriptionDto> Update(UpdateSubscriptionDto request, Guid userId);

        /// <summary>
        /// Deactivates a specific subscription.
        /// </summary>
        /// <param name="request">The data required to identify the subscription to deactivate.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the details of the deactivated subscription.</returns>
        Task<SubscriptionDto> Deactivate(DeleteSubscriptionDto request, Guid userId);
    }
}