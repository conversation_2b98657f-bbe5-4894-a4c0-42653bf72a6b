using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Defines the contract for managing customer-related operations.
    /// </summary>
    public interface ICustomerService
    {
        /// <summary>
        /// Creates a new customer based on the provided data.
        /// </summary>
        /// <param name="request">The data required to create a customer.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the created customer details.</returns>
        Task<CustomerDto> Create(CreateCustomerDto request, Guid userId);

        /// <summary>
        /// Retrieves the details of a specific customer.
        /// </summary>
        /// <param name="request">The data required to identify the customer.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the customer details.</returns>
        Task<CustomerDto> Get(CustomerRequestDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of customers based on the provided criteria.
        /// </summary>
        /// <param name="request">The data required to filter and paginate the customer list.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of customers and pagination details.</returns>
        Task<ReturnListCustomerDto> GetList(ListCustomerRequestDto request, Guid userId);

        /// <summary>
        /// Updates the details of an existing customer.
        /// </summary>
        /// <param name="request">The data required to update the customer.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the updated customer details.</returns>
        Task<CustomerDto> Update(UpdateCustomerDto request, Guid userId);

        /// <summary>
        /// Deletes a specific customer.
        /// </summary>
        /// <param name="request">The data required to identify the customer to delete.</param>
        /// <param name="userId">The unique identifier of the user performing the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the details of the deleted customer.</returns>
        Task<CustomerDto> Delete(DeleteCustomerDto request, Guid userId);
    }
}