using SmartBoat.API.Types;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for sensor data point operations
    /// </summary>
    public interface ISensorDataPointService
    {
        /// <summary>
        /// Creates a new sensor data point
        /// </summary>
        /// <param name="request">Sensor data point creation data</param>
        /// <param name="userId">ID of the user creating the data point</param>
        /// <returns>Operation result message</returns>
        Task<string> Create(CreateSensorDataPointDto request, Guid userId);

        /// <summary>
        /// Retrieves a single sensor data point's details
        /// </summary>
        /// <param name="request">Sensor data point request parameters</param>
        /// <param name="userId">ID of the requesting user</param>
        /// <returns>Sensor data point transfer object</returns>
        Task<SensorDataPointDto> Get(SensorDataPointRequestDto request, Guid userId);

        /// <summary>
        /// Updates an existing sensor data point
        /// </summary>
        /// <param name="request">Sensor data point update data</param>
        /// <param name="userId">ID of the user updating the data point</param>
        /// <returns>Operation result message</returns>
        Task<string> Update(UpdateSensorDataPointDto request, Guid userId);

        /// <summary>
        /// Deletes a sensor data point
        /// </summary>
        /// <param name="request">Sensor data point deletion data</param>
        /// <param name="userId">ID of the user deleting the data point</param>
        /// <returns>True if deletion was successful</returns>
        Task<bool> Delete(DeleteSensorDataPointDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of sensor data points with filtering and pagination
        /// </summary>
        /// <param name="request">List request parameters with sensor filtering</param>
        /// <param name="userId">ID of the requesting user</param>
        /// <returns>Paginated list of sensor data points</returns>
        Task<ReturnListSensorDataPointDto> GetList(ListSensorDataPointRequestDto request, Guid userId);

        /// <summary>
        /// Retrieves the latest sensor data point for specified sensor(s)
        /// </summary>
        /// <param name="request">Latest data point request parameters</param>
        /// <param name="userId">ID of the requesting user</param>
        /// <returns>Latest sensor data point(s)</returns>
        Task<List<SensorDataPointDto>> GetLatest(LatestSensorDataPointRequestDto request, Guid userId);

        /// <summary>
        /// Retrieves all sensor data points for a specific sensor
        /// </summary>
        /// <param name="sensorId">ID of the sensor</param>
        /// <param name="userId">ID of the requesting user</param>
        /// <returns>List of sensor data points</returns>
        Task<List<SensorDataPointDto>> GetBySensor(Guid sensorId, Guid userId);
    }
}