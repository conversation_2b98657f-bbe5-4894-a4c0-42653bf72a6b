using SmartBoat.API.Types;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for managing alert operations asynchronously
    /// </summary>
    public interface IAlertService
    {
        /// <summary>
        /// Creates a new alert
        /// </summary>
        /// <param name="request">Data transfer object containing alert creation details</param>
        /// <param name="userId">ID of the user creating the alert</param>
        /// <returns>A string message indicating the result of the operation</returns>
        Task<string> Create(CreateAlertDto request, Guid userId);

        /// <summary>
        /// Retrieves a specific alert by its ID
        /// </summary>
        /// <param name="id">The unique identifier of the alert</param>
        /// <param name="userId">ID of the user requesting the alert</param>
        /// <returns>An AlertDto containing the alert details</returns>
        Task<AlertDto> Get(Guid id, Guid userId);

        /// <summary>
        /// Updates an existing alert
        /// </summary>
        /// <param name="request">Data transfer object containing alert update details</param>
        /// <param name="userId">ID of the user updating the alert</param>
        /// <returns>A string message indicating the result of the operation</returns>
        Task<string> Update(UpdateAlertDto request, Guid userId);

        /// <summary>
        /// Deletes an existing alert
        /// </summary>
        /// <param name="request">Data transfer object containing alert deletion details</param>
        /// <param name="userId">ID of the user deleting the alert</param>
        /// <returns>A boolean indicating whether the deletion was successful</returns>
        Task<bool> Delete(DeleteAlertDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of alerts based on filter criteria
        /// </summary>
        /// <param name="request">Data transfer object containing filtering and pagination details</param>
        /// <param name="userId">ID of the user requesting the list</param>
        /// <returns>A ReturnListAlertDto containing the filtered list of alerts and pagination information</returns>
        Task<ReturnListAlertDto> GetList(ListAlertRequestDto request, Guid userId);
    }
}