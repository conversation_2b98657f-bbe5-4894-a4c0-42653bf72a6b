using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for managing notifications.
    /// </summary>
    public interface INotificationService
    {
        /// <summary>
        /// Creates a new notification.
        /// </summary>
        /// <param name="request">The data required to create the notification.</param>
        /// <param name="userId">The ID of the user creating the notification.</param>
        /// <returns>A string indicating the result of the operation.</returns>
        Task<string> Create(CreateNotificationDto request, Guid userId);

        /// <summary>
        /// Retrieves a specific notification by its ID.
        /// </summary>
        /// <param name="request">The data required to retrieve the notification.</param>
        /// <param name="userId">The ID of the user requesting the notification.</param>
        /// <returns>The requested notification details.</returns>
        Task<NotificationDto> Get(NotificationRequestDto request, Guid userId);

        /// <summary>
        /// Updates an existing notification.
        /// </summary>
        /// <param name="request">The data required to update the notification.</param>
        /// <param name="userId">The ID of the user updating the notification.</param>
        /// <returns>A string indicating the result of the operation.</returns>
        Task<string> Update(UpdateNotificationDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of notifications based on the provided criteria.
        /// </summary>
        /// <param name="request">The data required to retrieve the list of notifications.</param>
        /// <param name="userId">The ID of the user requesting the list of notifications.</param>
        /// <returns>A list of notifications matching the criteria.</returns>
        Task<ReturnListNotificationDto> GetList(ListNotificationRequestDto request, Guid userId);

        /// <summary>
        /// Marks a single notification as read.
        /// </summary>
        /// <param name="notificationId">The ID of the notification to mark as read.</param>
        /// <param name="userId">The ID of the user marking the notification as read.</param>
        /// <returns>A string indicating the result of the operation.</returns>
        Task<string> MarkAsRead(Guid notificationId, Guid userId);

        /// <summary>
        /// Marks all notifications for a user as read.
        /// </summary>
        /// <param name="userId">The ID of the user whose notifications should be marked as read.</param>
        /// <returns>A string indicating the result of the operation.</returns>
        Task<string> MarkAllAsRead(Guid userId);

        /// <summary>
        /// Gets the count of unread notifications for a user.
        /// </summary>
        /// <param name="userId">The ID of the user to get unread count for.</param>
        /// <returns>The number of unread notifications.</returns>
        Task<int> GetUnreadCount(Guid userId);
    }
}