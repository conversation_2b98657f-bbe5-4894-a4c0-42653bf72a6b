using SmartBoat.API.Types;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for managing role operations
    /// </summary>
    public interface IRoleService
    {
        /// <summary>
        /// Creates a new role
        /// </summary>
        /// <param name="request">Role creation data transfer object</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Created role data transfer object</returns>
        Task<RoleDto> Create(CreateRoleDto request, Guid userId);

        /// <summary>
        /// Retrieves a specific role by ID
        /// </summary>
        /// <param name="id">ID of the role to retrieve</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Requested role data transfer object</returns>
        Task<RoleDto> Get(Guid id, Guid userId);

        /// <summary>
        /// Retrieves a list of roles based on filter criteria
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Paginated list of role data transfer objects</returns>
        Task<ReturnListRoleDto> GetList(ListRoleRequestDto request, Guid userId);

        /// <summary>
        /// Updates an existing role
        /// </summary>
        /// <param name="request">Role update data transfer object</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Updated role data transfer object</returns>
        Task<RoleDto> Update(UpdateRoleDto request, Guid userId);

        /// <summary>
        /// Deletes a role by ID
        /// </summary>
        /// <param name="id">ID of the role to delete</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Boolean indicating success of the operation</returns>
        Task<bool> Delete(Guid id, Guid userId);
    }
}