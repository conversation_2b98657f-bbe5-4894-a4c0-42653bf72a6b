using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for managing vessel operations
    /// </summary>
    public interface IVesselService
    {
        /// <summary>
        /// Creates a new vessel
        /// </summary>
        /// <param name="request">DTO containing vessel creation details</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Success message string</returns>
        Task<string> Create(CreateVesselDto request, Guid userId);

        /// <summary>
        /// Retrieves a specific vessel by its identifier
        /// </summary>
        /// <param name="request">DTO containing vessel identification details</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Vessel details DTO</returns>
        Task<VesselDto> Get(DeleteVesselDto request, Guid userId);

        /// <summary>
        /// Updates an existing vessel
        /// </summary>
        /// <param name="request">DTO containing vessel update details</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Success message string</returns>
        Task<string> Update(UpdateVesselDto request, Guid userId);

        /// <summary>
        /// Deletes a specific vessel
        /// </summary>
        /// <param name="request">DTO containing vessel identification details</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Boolean indicating success of the operation</returns>
        Task<bool> Delete(DeleteVesselDto request, Guid userId);

        /// <summary>
        /// Retrieves a paginated list of vessels based on filter criteria
        /// </summary>
        /// <param name="request">DTO containing filtering and pagination parameters</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>Paginated list of vessels with metadata</returns>
        Task<ReturnListVesselDto> GetList(ListVesselRequestDto request, Guid userId);
    }
}