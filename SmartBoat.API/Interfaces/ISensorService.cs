using SmartBoat.API.Types;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for sensor management operations
    /// </summary>
    public interface ISensorService
    {
        /// <summary>
        /// Creates a new sensor
        /// </summary>
        /// <param name="request">Sensor creation data</param>
        /// <param name="userId">ID of the user creating the sensor</param>
        /// <returns>Operation result message</returns>
        Task<string> Create(CreateSensorDto request, Guid userId);

        /// <summary>
        /// Retrieves a single sensor's details
        /// </summary>
        /// <param name="request">Sensor request parameters</param>
        /// <param name="userId">ID of the requesting user</param>
        /// <returns>Sensor data transfer object</returns>
        Task<SensorDto> Get(SensorRequestDto request, Guid userId);

        /// <summary>
        /// Updates an existing sensor
        /// </summary>
        /// <param name="request">Sensor update data</param>
        /// <param name="userId">ID of the user updating the sensor</param>
        /// <returns>Operation result message</returns>
        Task<string> Update(UpdateSensorDto request, Guid userId);

        /// <summary>
        /// Deletes a sensor
        /// </summary>
        /// <param name="request">Sensor deletion data</param>
        /// <param name="userId">ID of the user deleting the sensor</param>
        /// <returns>True if deletion was successful</returns>
        Task<bool> Delete(DeleteSensorDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of sensors with pagination support
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <param name="userId">ID of the requesting user</param>
        /// <returns>Paginated list of sensors</returns>
        Task<ReturnListSensorDto> GetList(ListSensorRequestDto request, Guid userId);
        /// <summary>
        /// Retrieves a list of sensors for a specific vessel
        /// </summary>
        /// <param name="vesselId">ID of the vessel</param>
        /// <returns>List of SensorDto</returns>
        Task<List<SensorDto>> GetListByVessel(Guid vesselId);
    }
}