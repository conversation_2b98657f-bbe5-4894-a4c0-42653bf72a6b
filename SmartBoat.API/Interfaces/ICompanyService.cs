using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for managing company-related operations.
    /// </summary>
    public interface ICompanyService
    {
        /// <summary>
        /// Creates a new company based on the provided data.
        /// </summary>
        /// <param name="request">The data required to create a company.</param>
        /// <param name="userId">The ID of the user creating the company.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the created company details.</returns>
        Task<CompanyDto> Create(CreateCompanyDto request, Guid userId);

        /// <summary>
        /// Retrieves the details of a specific company.
        /// </summary>
        /// <param name="request">The data required to identify the company.</param>
        /// <param name="userId">The ID of the user requesting the company details.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the company details.</returns>
        Task<CompanyDto> Get(CompanyRequestDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of companies based on the provided criteria.
        /// </summary>
        /// <param name="request">The criteria for filtering and paginating the list of companies.</param>
        /// <param name="userId">The ID of the user requesting the list of companies.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of companies and related metadata.</returns>
        Task<ReturnListCompanyDto> GetList(ListCompanyRequestDto request, Guid userId);

        /// <summary>
        /// Updates the details of an existing company.
        /// </summary>
        /// <param name="request">The data required to update the company.</param>
        /// <param name="userId">The ID of the user updating the company.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the updated company details.</returns>
        Task<CompanyDto> Update(UpdateCompanyDto request, Guid userId);

        /// <summary>
        /// Deactivates a specific company.
        /// </summary>
        /// <param name="request">The data required to identify the company to deactivate.</param>
        /// <param name="userId">The ID of the user deactivating the company.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the deactivated company details.</returns>
        Task<CompanyDto> Deactivate(CompanyRequestDto request, Guid userId);

        /// <summary>
        /// Deletes a specific company and all its related data.
        /// </summary>
        /// <param name="request">The data required to identify the company to delete.</param>
        /// <param name="userId">The ID of the user deleting the company.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if deletion was successful.</returns>
        Task<bool> Delete(DeleteCompanyDto request, Guid userId);
    }
}