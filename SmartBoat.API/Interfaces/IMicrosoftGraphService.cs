using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface IMicrosoftGraphService
    {
        Task<Response<List<EmailDto>>> GetEmailsAsync(Guid userId, int daysBack = 7);
        Task<Response<byte[]>> DownloadAttachmentAsync(string emailId, string attachmentId, Guid userId);
        Task<Response<bool>> TestConnectionAsync(Guid userId);
    }
}