using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for managing notification preferences.
    /// </summary>
    public interface INotificationPreferenceService
    {
        /// <summary>
        /// Retrieves a specific notification preference by its ID.
        /// </summary>
        /// <param name="request">The data required to retrieve the notification preference.</param>
        /// <param name="userId">The ID of the user requesting the preference.</param>
        /// <returns>The requested notification preference details.</returns>
        Task<NotificationPreferenceDto> Get(NotificationPreferenceRequestDto request, System.Guid userId);
    }
}