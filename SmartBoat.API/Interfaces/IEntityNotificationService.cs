using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for handling entity-related notifications.
    /// </summary>
    public interface IEntityNotificationService
    {
        /// <summary>
        /// Sends notifications when an entity is created.
        /// </summary>
        /// <param name="entityType">The type of entity (Company, Vessel, Sensor, Customer).</param>
        /// <param name="entityId">The ID of the created entity.</param>
        /// <param name="entityName">The name or identifier of the entity.</param>
        /// <param name="companyId">The company ID associated with the entity.</param>
        /// <param name="createdByUserId">The ID of the user who created the entity.</param>
        Task NotifyEntityCreated(string entityType, Guid? entityId, string entityName, Guid? companyId, Guid createdByUserId);

        /// <summary>
        /// Sends notifications when an entity is updated.
        /// </summary>
        /// <param name="entityType">The type of entity (Company, Vessel, Sensor, Customer).</param>
        /// <param name="entityId">The ID of the updated entity.</param>
        /// <param name="entityName">The name or identifier of the entity.</param>
        /// <param name="companyId">The company ID associated with the entity.</param>
        /// <param name="updatedByUserId">The ID of the user who updated the entity.</param>
        Task NotifyEntityUpdated(string entityType, Guid? entityId, string entityName, Guid? companyId, Guid updatedByUserId);

        /// <summary>
        /// Sends notifications when an entity is deleted.
        /// </summary>
        /// <param name="entityType">The type of entity (Company, Vessel, Sensor, Customer).</param>
        /// <param name="entityId">The ID of the deleted entity.</param>
        /// <param name="entityName">The name or identifier of the entity.</param>
        /// <param name="companyId">The company ID associated with the entity.</param>
        /// <param name="deletedByUserId">The ID of the user who deleted the entity.</param>
        Task NotifyEntityDeleted(string entityType, Guid? entityId, string entityName, Guid? companyId, Guid deletedByUserId);
    }
}