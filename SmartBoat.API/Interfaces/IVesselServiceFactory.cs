using SmartBoat.API.Interfaces;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Factory interface for creating IVesselService instances
    /// Used to break circular dependency between SensorService and VesselService
    /// </summary>
    public interface IVesselServiceFactory
    {
        /// <summary>
        /// Creates an instance of IVesselService
        /// </summary>
        /// <returns>IVesselService instance</returns>
        IVesselService CreateVesselService();
    }
}