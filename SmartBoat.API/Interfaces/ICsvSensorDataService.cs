using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface ICsvSensorDataService
    {
        Task<Response<string>> CreateCsvSensorDataAsync(CsvSensorData sensorData, Guid userId);
        Task<Response<List<CsvSensorData>>> CreateBulkCsvSensorDataAsync(List<CsvSensorData> sensorDataList, Guid userId);
        Task<Response<List<CsvSensorData>>> GetCsvSensorDataAsync(string vesselName, DateTime? startDate, DateTime? endDate, Guid userId);
        Task<Response<List<string>>> GetVesselNamesAsync(Guid userId);
        Task<Response<ProcessingSummaryDto>> GetSummaryAsync(Guid userId);
    }
}