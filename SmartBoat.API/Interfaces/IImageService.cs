using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface IImageService
    {
        Task<string> Create(CreateImageDto createImageDto);
        Task<List<ImageDto>> GetByEntityId(Guid entityId);
        Task<Image> GetImageData(Guid imageId);
        Task<bool> Delete(Guid imageId, Guid userId);
    }
}