using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface ITokenStorageService
    {
        Task<Response<string>> SaveTokenAsync(SaveTokenDto tokenDto, Guid userId);
        Task<Response<SaveTokenDto>> ExchangeTokenAsync(ExchangeTokenDto exchangeDto, Guid userId);
        Task<Response<TokenStatusDto>> GetTokenStatusAsync(Guid userId);
        Task<Response<string>> GetValidAccessTokenAsync(Guid userId);
        Task<Response<bool>> RefreshTokenIfNeededAsync(Guid userId);
        Task<Response<bool>> DeleteTokenAsync(Guid userId);
    }
}