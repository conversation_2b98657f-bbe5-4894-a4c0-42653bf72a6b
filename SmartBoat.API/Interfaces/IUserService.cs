using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for managing user-related operations.
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// Creates a new user based on the provided request data.
        /// </summary>
        /// <param name="request">The data required to create a user.</param>
        /// <param name="userId">The unique identifier of the user performing the action.</param>
        /// <returns>A string indicating the result of the operation.</returns>
        Task<string> Create(CreateUserDto request, Guid userId);

        /// <summary>
        /// Retrieves user details based on the provided request data.
        /// </summary>
        /// <param name="request">The data required to retrieve a user.</param>
        /// <param name="userId">The unique identifier of the user performing the action.</param>
        /// <returns>A UserDto containing the user details.</returns>
        Task<UserDto> Get(UserRequestDto request, Guid userId);

        /// <summary>
        /// Updates an existing user based on the provided request data.
        /// </summary>
        /// <param name="request">The data required to update a user.</param>
        /// <param name="userId">The unique identifier of the user performing the action.</param>
        /// <returns>A string indicating the result of the operation.</returns>
        Task<string> Update(UpdateUserDto request, Guid userId);

        /// <summary>
        /// Deletes a user based on the provided request data.
        /// </summary>
        /// <param name="request">The data required to delete a user.</param>
        /// <param name="userId">The unique identifier of the user performing the action.</param>
        /// <returns>A boolean indicating the success of the operation.</returns>
        Task<bool> Delete(DeleteUserDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of users based on the provided request data.
        /// </summary>
        /// <param name="request">The data required to retrieve a list of users.</param>
        /// <param name="userId">The unique identifier of the user performing the action.</param>
        /// <returns>A ReturnListUserDto containing the list of users.</returns>
        Task<ReturnListUserDto> GetList(ListUserRequestDto request, Guid userId);

        /// <summary>
        /// Changes the password of a user based on the provided request data.
        /// </summary>
        /// <param name="request">The data required to change the password.</param>
        /// <param name="userId">The unique identifier of the user performing the action.</param>
        /// <returns>A boolean indicating the success of the operation.</returns>
        Task<bool> ChangePassword(ChangePasswordDto request, Guid userId);

        /// <summary>
        /// Resets the password of a user based on the provided request data.
        /// </summary>
        /// <param name="request">The data required to reset the password.</param>
        /// <returns>A boolean indicating the success of the operation.</returns>
        Task<bool> ResetPassword(ResetPasswordDto request);
    }
}