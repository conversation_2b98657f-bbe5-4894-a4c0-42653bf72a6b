using System;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface IFeedbackService
    {
        Task<Feedback> CreateFeedback(FeedbackRequest request);
        Task<Feedback> SelectFeedbackAsync(Guid feedbackId);
        Task<Feedback> UpdateFeedback(UpdateFeedbackRequest request);
        Task DeleteFeedbackAsync(Guid feedbackId, Guid userId);
    }
}