using SmartBoat.API.Types;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Interfaces
{
    /// <summary>
    /// Interface for managing support requests
    /// </summary>
    public interface ISupportRequestService
    {
        /// <summary>
        /// Creates a new support request
        /// </summary>
        /// <param name="request">DTO containing support request creation details</param>
        /// <param name="userId">ID of the user creating the request</param>
        /// <returns>Confirmation message of the created request</returns>
        Task<string> Create(CreateSupportRequestDto request, Guid userId);

        /// <summary>
        /// Retrieves a specific support request
        /// </summary>
        /// <param name="request">DTO containing support request retrieval parameters</param>
        /// <param name="userId">ID of the user requesting the information</param>
        /// <returns>DTO containing the support request details</returns>
        Task<SupportRequestDto> Get(SupportRequestRequestDto request, Guid userId);

        /// <summary>
        /// Updates an existing support request
        /// </summary>
        /// <param name="request">DTO containing support request update details</param>
        /// <param name="userId">ID of the user updating the request</param>
        /// <returns>Confirmation message of the updated request</returns>
        Task<string> Update(UpdateSupportRequestDto request, Guid userId);

        /// <summary>
        /// Deletes a support request
        /// </summary>
        /// <param name="request">DTO containing support request deletion parameters</param>
        /// <param name="userId">ID of the user deleting the request</param>
        /// <returns>Boolean indicating success of the deletion</returns>
        Task<bool> Delete(DeleteSupportRequestDto request, Guid userId);

        /// <summary>
        /// Retrieves a list of support requests based on filter criteria
        /// </summary>
        /// <param name="request">DTO containing list request parameters</param>
        /// <param name="userId">ID of the user requesting the list</param>
        /// <returns>DTO containing the list of support requests and pagination information</returns>
        Task<ReturnListSupportRequestDto> GetList(ListSupportRequestRequestDto request, Guid userId);
    }
}