using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    /// <summary>
    /// Entity class for inserting SensorDataPoints without computed columns
    /// This class is used specifically for INSERT operations to avoid computed column conflicts
    /// </summary>
    [Table("SensorDataPoints")]
    public class SensorDataPointInsert
    {
        [Key]
        public Guid? Id { get; set; }

        [Required]
        public Guid SensorId { get; set; }

        [Required]
        public DateTime Timestamp { get; set; }

        [Required]
        public long TimestampUnix { get; set; }

        [Required]
        public string Measurements { get; set; } = string.Empty;

        public float QualityScore { get; set; } = 1.0f;

        public string Source { get; set; } = "API";

        public DateTime Created { get; set; } = DateTime.Now;
    }
}
