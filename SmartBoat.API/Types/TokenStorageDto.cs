using System;
using System.Collections.Generic;

namespace SmartBoat.API.Types
{
    public class SaveTokenDto
    {
        public string AccessToken { get; set; } = string.Empty;
        public string? RefreshToken { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string? Scope { get; set; }
    }

    public class ExchangeTokenDto
    {
        public string AuthorizationCode { get; set; } = string.Empty;
        public string CodeVerifier { get; set; } = string.Empty;
        public string RedirectUri { get; set; } = string.Empty;
    }

    public class TokenStatusDto
    {
        public bool HasToken { get; set; }
        public bool IsExpired { get; set; }
        public bool HasRefreshToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public DateTime? RefreshTokenIssuedAt { get; set; }
        public int? DaysUntilRefreshExpiry { get; set; }
        public bool RequiresReAuthentication { get; set; }
    }

    public class ProcessEmailsDto
    {
        public bool ForceReprocess { get; set; } = false;
        public int? DaysBack { get; set; } = 7;
    }

    public class ProcessingStatusDto
    {
        public bool IsProcessing { get; set; }
        public string? CurrentStatus { get; set; }
        public DateTime? LastProcessed { get; set; }
        public int? TotalEmails { get; set; }
        public int? ProcessedEmails { get; set; }
    }

    public class ProcessingSummaryDto
    {
        public int TotalRecords { get; set; }
        public int RecentRecords { get; set; }
        public List<VesselSummaryDto> VesselSummaries { get; set; } = new();
        public List<RecentProcessingDto> RecentProcessing { get; set; } = new();
    }

    public class VesselSummaryDto
    {
        public string VesselName { get; set; } = string.Empty;
        public int RecordCount { get; set; }
        public DateTime? LatestRecord { get; set; }
        public DateTime? OldestRecord { get; set; }
    }

    public class RecentProcessingDto
    {
        public DateTime ProcessedAt { get; set; }
        public string ProcessingStatus { get; set; } = string.Empty;
        public int RecordsProcessed { get; set; }
        public int VesselsFound { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class EmailDto
    {
        public string Id { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public DateTime ReceivedDateTime { get; set; }
        public string? FromEmail { get; set; }
        public string? Body { get; set; }
        public List<AttachmentDto> Attachments { get; set; } = new();
        public List<string> ZipDownloadUrls { get; set; } = new();
    }

    public class AttachmentDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long Size { get; set; }
        public bool IsZipFile => ContentType == "application/zip" || Name.EndsWith(".zip", StringComparison.OrdinalIgnoreCase);
    }

    public class ExtractedFileDto
    {
        public string FileName { get; set; } = string.Empty;
        public byte[] Content { get; set; } = Array.Empty<byte>();
        public bool IsCsvFile => FileName.EndsWith(".csv", StringComparison.OrdinalIgnoreCase);
    }
}