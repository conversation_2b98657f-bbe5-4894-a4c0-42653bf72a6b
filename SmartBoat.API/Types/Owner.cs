// File: Owner.cs
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Owners")]
    public class Owner
    {
        public Guid? Id { get; set; }
        
        [Required(ErrorMessage = "Owner name is required")]
        public string? Name { get; set; }
        
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
        
        [Required(ErrorMessage = "Company is required")]
        public Guid? CompanyId { get; set; }
        
        public string? Status { get; set; }
        public DateTime? LastUpdated { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}