using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Customer")]
    public class Customer
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? ContactPerson { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Status { get; set; }
        public DateTime? LastActive { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}