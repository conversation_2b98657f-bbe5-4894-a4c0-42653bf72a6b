using System;
using System.Collections.Generic;

namespace SmartBoat.API.Types
{
    public class SensorDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? Type { get; set; }
        public VesselDto? Vessel { get; set; }
        public string? Location { get; set; }
        public string? Status { get; set; }
        public DateTime? LastReading { get; set; }
        public DateTime? LastUpdated { get; set; }
        public string? AlertThreshold { get; set; }
        public List<SensorDataPoint>? DataPoints { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}