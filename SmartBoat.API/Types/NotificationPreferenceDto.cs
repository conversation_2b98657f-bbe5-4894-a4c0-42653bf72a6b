using System;

namespace SmartBoat.API.Types
{
    public class NotificationPreferenceDto
    {
        public Guid? Id { get; set; }
        public UserDto? User { get; set; }
        public string? EventType { get; set; }
        public string? Channel { get; set; }
        public bool? Enabled { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}