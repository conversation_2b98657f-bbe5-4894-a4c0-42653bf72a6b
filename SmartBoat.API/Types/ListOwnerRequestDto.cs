// File: ListOwnerRequestDto.cs
using System;

namespace SmartBoat.API.Types
{
    public class ListOwnerRequestDto
    {
        public int? PageLimit { get; set; }
        public int? PageOffset { get; set; }
        public string? SortField { get; set; }
        public string? SortOrder { get; set; }
        public string? SearchTerm { get; set; }
        public Guid? CompanyId { get; set; }
        public string? Status { get; set; }
        public string? Name { get; set; }
    }
}