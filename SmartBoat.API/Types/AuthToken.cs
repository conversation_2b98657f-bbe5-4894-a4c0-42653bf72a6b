using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("AuthTokens")]
    public class AuthToken
    {
        public Guid Id { get; set; }
        public string AccessToken { get; set; } = string.Empty;
        public string? RefreshToken { get; set; }
        public DateTime ExpiresAt { get; set; }
        public DateTime? RefreshTokenIssuedAt { get; set; }
        public string TokenType { get; set; } = "Bearer";
        public string? Scope { get; set; } = "User.Read Mail.Read offline_access";
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}