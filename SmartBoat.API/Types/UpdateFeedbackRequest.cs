// File: UpdateFeedbackRequest.cs
using System;

namespace SmartBoat.API.Types
{
    public class UpdateFeedbackRequest
    {
        public FeedbackHeader? Header { get; set; }
        public UpdateFeedbackPayload? Payload { get; set; }
    }

    public class UpdateFeedbackPayload
    {
        public Guid? FeedbackId { get; set; }
        public string? Type { get; set; }
        public string? Content { get; set; }
        public string? Status { get; set; }
    }
}