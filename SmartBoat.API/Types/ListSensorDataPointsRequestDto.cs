using System;

namespace SmartBoat.API.Types
{
    public class ListSensorDataPointsRequestDto
    {
        public Guid? SensorId { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        
        // Pagination
        public int? PageLimit { get; set; } = 100;
        public int? PageOffset { get; set; } = 0;
        
        // Filtering by measurement types
        public string[] MeasurementTypes { get; set; } // e.g., ["temperature", "rpm", "speed"]
        
        // Data quality filtering
        public float? MinQualityScore { get; set; }
        
        // Source filtering
        public string Source { get; set; }
        
        // Sorting
        public string SortField { get; set; } = "Timestamp";
        public string SortOrder { get; set; } = "desc"; // "asc" or "desc"
    }
}