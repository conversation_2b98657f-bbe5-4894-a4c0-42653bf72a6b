// File: VesselPathPoint.cs
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("VesselPathPoints")]
    public class VesselPathPoint
    {
        public Guid? Id { get; set; }
        public Guid? VesselId { get; set; }
        public float? Lat { get; set; }
        public float? Lng { get; set; }
        public DateTime? Timestamp { get; set; }
        public string? Location { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}