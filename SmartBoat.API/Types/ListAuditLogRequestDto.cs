using System;

namespace SmartBoat.API.Types
{
    public class ListAuditLogRequestDto
    {
        public int? PageLimit { get; set; }
        public int? PageOffset { get; set; }
        public string? SortField { get; set; }
        public string? SortOrder { get; set; }
        public Guid? UserId { get; set; }
        public string? EntityType { get; set; }
        public Guid? EntityId { get; set; }
        public string? Action { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }
}