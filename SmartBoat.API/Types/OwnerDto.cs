// File: OwnerDto.cs
using System;

namespace SmartBoat.API.Types
{
    public class OwnerDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
        public Guid? CompanyId { get; set; }
        public CompanyDto? Company { get; set; }
        public string? Status { get; set; }
        public DateTime? LastUpdated { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}