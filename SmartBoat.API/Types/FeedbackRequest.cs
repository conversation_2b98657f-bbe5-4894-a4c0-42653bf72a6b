// File: FeedbackRequest.cs
using System;

namespace SmartBoat.API.Types
{
    public class FeedbackRequest
    {
        public FeedbackHeader? Header { get; set; }
        public CreateFeedbackDto? Payload { get; set; }
    }

    public class FeedbackHeader
    {
        public Guid? Id { get; set; }
        public Guid? Application { get; set; }
        public string? Bank { get; set; }
        public Guid? UserId { get; set; }
    }
}