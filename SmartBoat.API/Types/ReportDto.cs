namespace SmartBoat.API.Types
{
    public class ReportDto
    {
        public int? Id { get; set; }
        public string? Type { get; set; }
        public string? Criteria { get; set; }
        public int? GeneratedBy { get; set; }
        public string? GeneratedAt { get; set; }
        public string? Status { get; set; }
        public string? DeliveryChannel { get; set; }
        public string? DeliveryStatus { get; set; }
    }
}