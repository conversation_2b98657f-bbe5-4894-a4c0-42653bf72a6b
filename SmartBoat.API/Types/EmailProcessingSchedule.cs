using System;
using System.ComponentModel.DataAnnotations;

namespace SmartBoat.API.Types
{
    /// <summary>
    /// Database model for email processing schedule configuration
    /// </summary>
    public class EmailProcessingSchedule
    {
        [Key]
        public Guid Id { get; set; }

        /// <summary>
        /// User ID for user-specific schedules, or NULL for global schedule
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Whether the schedule is enabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Type of schedule: 'Daily', 'Weekly', 'Custom'
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string ScheduleType { get; set; } = "Daily";

        /// <summary>
        /// Cron expression for the schedule
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CronExpression { get; set; } = "0 0 0 * * ?";

        /// <summary>
        /// Timezone for the schedule execution
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string TimeZone { get; set; } = "Europe/Athens";

        /// <summary>
        /// How many days back to look for emails during scheduled processing
        /// </summary>
        public int DaysBack { get; set; } = 7;

        /// <summary>
        /// Whether to force reprocess already processed emails
        /// </summary>
        public bool ForceReprocess { get; set; } = false;

        /// <summary>
        /// Description of the schedule
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// When the schedule was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the schedule was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Who created the schedule
        /// </summary>
        [MaxLength(255)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// Who last updated the schedule
        /// </summary>
        [MaxLength(255)]
        public string? UpdatedBy { get; set; }
    }

    /// <summary>
    /// DTO for schedule status responses
    /// </summary>
    public class ScheduleStatusDto
    {
        public bool IsEnabled { get; set; }
        public string ScheduleType { get; set; } = "Daily";
        public string Description { get; set; } = "";
        public DateTime? NextRunTime { get; set; }
        public string TimeZone { get; set; } = "Europe/Athens";
        public int DaysBack { get; set; } = 7;
    }

    /// <summary>
    /// DTO for updating schedule settings
    /// </summary>
    public class UpdateScheduleDto
    {
        public bool IsEnabled { get; set; }
        public int? DaysBack { get; set; }
        public bool? ForceReprocess { get; set; }
        public string? Description { get; set; }
    }
}