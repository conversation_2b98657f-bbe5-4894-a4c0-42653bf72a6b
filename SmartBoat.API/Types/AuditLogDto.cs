using System;

namespace SmartBoat.API.Types
{
    public class AuditLogDto
    {
        public Guid? Id { get; set; }
        public Guid? UserId { get; set; }
        public string? EntityType { get; set; }
        public Guid? EntityId { get; set; }
        public string? Action { get; set; }
        public string? Details { get; set; }
        public DateTime? Timestamp { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}