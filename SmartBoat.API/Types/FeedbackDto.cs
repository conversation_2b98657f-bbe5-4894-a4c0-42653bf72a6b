// File: FeedbackDto.cs
using System;

namespace SmartBoat.API.Types
{
    public class FeedbackDto
    {
        public Guid? Id { get; set; }
        public UserDto? User { get; set; }
        public string? Type { get; set; }
        public string? Content { get; set; }
        public DateTime? SubmittedAt { get; set; }
        public string? Status { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}