using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Alert")]
    public class Alert
    {
        public Guid? Id { get; set; }
        public string? Type { get; set; }
        public Guid? EntityId { get; set; }
        public string? EntityType { get; set; }
        public string? Value { get; set; }
        public string? Threshold { get; set; }
        public string? Status { get; set; }
        public string? Message { get; set; }
        public DateTime? Timestamp { get; set; }
        public string? DeliveryStatus { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}