using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Images")]
    public class Image
    {
        public Guid Id { get; set; }
        public Guid EntityId { get; set; }
        public byte[] ImageData { get; set; }
        public string FileName { get; set; }
        public string ContentType { get; set; }
        public DateTime UploadedDate { get; set; }
        public Guid UploadedBy { get; set; }
    }
}