// File: CreateSubscriptionDto.cs
using System;
using System.Collections.Generic;

namespace SmartBoat.API.Types
{
    public class CreateSubscriptionDto
    {
        public string? Name { get; set; }
        public string? Type { get; set; }
        public Guid? VesselId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal? Price { get; set; }
        public string? BillingFrequency { get; set; }
        public string? Status { get; set; }
        public int? SensorLimit { get; set; }
        public List<string>? Features { get; set; }
    }
}