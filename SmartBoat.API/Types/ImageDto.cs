using System;
using Microsoft.AspNetCore.Http;

namespace SmartBoat.API.Types
{
    public class ImageDto
    {
        public Guid Id { get; set; }
        public Guid EntityId { get; set; }
        public string FileName { get; set; }
        public string ContentType { get; set; }
        public DateTime UploadedDate { get; set; }
        public Guid UploadedBy { get; set; }
    }

    public class CreateImageDto
    {
        public Guid EntityId { get; set; }
        public IFormFile ImageFile { get; set; }
        public Guid UploadedBy { get; set; }
    }
}