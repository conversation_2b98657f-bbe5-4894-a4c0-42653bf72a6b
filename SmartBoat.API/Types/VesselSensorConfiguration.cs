using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("VesselSensorConfigurations")]
    public class VesselSensorConfiguration
    {
        public Guid Id { get; set; }
        public Guid VesselId { get; set; }
        public string VesselName { get; set; } = string.Empty;
        public Guid PrimarySensorId { get; set; }
        public Guid? PortEngineSensorId { get; set; }
        public Guid? StarboardEngineSensorId { get; set; }
        public string EngineConfiguration { get; set; } = "single"; // "single", "dual"
        public DateTime Created { get; set; } = DateTime.UtcNow;
        public DateTime? Updated { get; set; }
    }
}