using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("EmailProcessingLog")]
    public class EmailProcessingLog
    {
        public Guid Id { get; set; }
        public string? EmailId { get; set; } // Microsoft Graph email ID for duplicate detection
        public string? EmailSubject { get; set; }
        public DateTime EmailDate { get; set; }
        public string ProcessingStatus { get; set; } = string.Empty; // 'Success', 'Failed', 'Processing', 'Skipped'
        public int RecordsProcessed { get; set; }
        public int VesselsFound { get; set; }
        public string? AttachmentIds { get; set; } // JSON array of processed attachment IDs
        public int AttachmentCount { get; set; } // Total number of ZIP attachments in email
        public string? ErrorMessage { get; set; }
        public int? ProcessingDuration { get; set; } // in milliseconds
        public DateTime ProcessedAt { get; set; }
    }
}