using System;

namespace SmartBoat.API.Types
{
    public class NotificationDto
    {
        public Guid? Id { get; set; }
        public UserDto? User { get; set; }
        public string? EventType { get; set; }
        public string? Content { get; set; }
        public string? Channel { get; set; }
        public string? Status { get; set; }
        public DateTime? Timestamp { get; set; }
        public NotificationPreferenceDto? Preference { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}