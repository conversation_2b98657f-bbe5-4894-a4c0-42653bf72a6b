namespace SmartBoat.API.Types
{
    public class ListReportRequestDto
    {
        public int? PageLimit { get; set; }
        public int? PageOffset { get; set; }
        public string? SortField { get; set; }
        public string? SortOrder { get; set; }
        public string? SearchTerm { get; set; }
        public string? Type { get; set; }
        public string? Status { get; set; }
        public int? GeneratedBy { get; set; }
    }
}