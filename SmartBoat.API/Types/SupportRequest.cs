// File: SupportRequest.cs
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("SupportRequests")]
    public class SupportRequest
    {
        public Guid? Id { get; set; }
        public Guid? UserId { get; set; }
        public string? Subject { get; set; }
        public string? Description { get; set; }
        public string? Status { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public string? Resolution { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}