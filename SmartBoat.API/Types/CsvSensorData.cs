using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("CsvSensorData")]
    public class CsvSensorData
    {
        public Guid Id { get; set; }
        public string VesselName { get; set; } = string.Empty;
        public string? GroupCoordinates { get; set; }
        public string? Location { get; set; }
        public DateTime SensorTime { get; set; }
        public decimal? Speed { get; set; }
        public decimal? SeaDepth { get; set; }
        public decimal? RPM { get; set; }
        public decimal? WindAngle { get; set; }
        public decimal? WaterTemperature { get; set; }
        public decimal? WindSpeed { get; set; }
        public decimal? OilPressure { get; set; }
        public decimal? Rudder { get; set; }
        public decimal? EngineLoad { get; set; }
        public decimal? COG { get; set; }
        public decimal? FuelRate { get; set; }
        public decimal? TotalNM { get; set; }
        public decimal? EngineHours { get; set; }
        public decimal? PowerSupply { get; set; }
        public string? EngineAlarmCode { get; set; }
        public decimal? SmartBoatBattery { get; set; }
        public string? EngineWarningCode { get; set; }
        public string? GSMEngineRunningIndicator { get; set; }
        public string? SourceEmail { get; set; }
        public DateTime ProcessedDate { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}