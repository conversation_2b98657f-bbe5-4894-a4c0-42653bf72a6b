using System;
using System.Collections.Generic;

namespace SmartBoat.API.Types
{
    public class CustomerDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? ContactPerson { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Status { get; set; }
        public DateTime? LastActive { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
        public List<CompanyDto>? Companies { get; set; }
        public List<SubscriptionDto>? Subscriptions { get; set; }
    }
}