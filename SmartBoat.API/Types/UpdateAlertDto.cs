using System;

namespace SmartBoat.API.Types
{
    public class UpdateAlertDto
    {
        public Guid? Id { get; set; }
        public string? Type { get; set; }
        public Guid? EntityId { get; set; }
        public string? EntityType { get; set; }
        public string? Value { get; set; }
        public string? Threshold { get; set; }
        public string? Status { get; set; }
        public string? Message { get; set; }
        public DateTime? Timestamp { get; set; }
        public string? DeliveryStatus { get; set; }
    }
}