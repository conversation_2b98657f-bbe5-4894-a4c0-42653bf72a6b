using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("CsvColumnMappings")]
    public class CsvColumnMapping
    {
        public Guid Id { get; set; }
        public string VesselName { get; set; } = string.Empty;
        public string ColumnName { get; set; } = string.Empty;
        public string MeasurementKey { get; set; } = string.Empty;
        public string DataType { get; set; } = "string"; // "string", "decimal", "datetime", "boolean"
        public string Category { get; set; } = string.Empty; // "navigation", "engine_port", "engine_stbd", "environmental", "systems"
        public bool IsActive { get; set; } = true;
        public DateTime Created { get; set; } = DateTime.UtcNow;
        public DateTime? Updated { get; set; }
    }

    public class CsvMappingResult
    {
        public Dictionary<string, object> Measurements { get; set; } = new();
        public List<string> UnmappedColumns { get; set; } = new();
        public List<string> MappingErrors { get; set; } = new();
        public string VesselEngineConfiguration { get; set; } = "single"; // "single", "dual", "unknown"
        public float QualityScore { get; set; } = 1.0f;
    }


    public class DefaultColumnMappings
    {
        public static readonly Dictionary<string, (string measurementKey, string dataType, string category)> Mappings =
            new Dictionary<string, (string, string, string)>(StringComparer.OrdinalIgnoreCase)
            {
                // Vessel identification
                { "vessel", ("vessel_name", "string", "identification") },
                { "vesselname", ("vessel_name", "string", "identification") },
                { "vessel_name", ("vessel_name", "string", "identification") },
                { "boat", ("vessel_name", "string", "identification") },
                { "boatname", ("vessel_name", "string", "identification") },

                // Navigation
                { "speed", ("speed", "decimal", "navigation") },
                { "velocity", ("speed", "decimal", "navigation") },
                { "spd", ("speed", "decimal", "navigation") },
                { "cog", ("course", "decimal", "navigation") },
                { "course", ("course", "decimal", "navigation") },
                { "heading", ("course", "decimal", "navigation") },
                { "coordinates", ("coordinates", "string", "navigation") },
                { "groupcoordinates", ("coordinates", "string", "navigation") },
                { "group_coordinates", ("coordinates", "string", "navigation") },
                { "position", ("coordinates", "string", "navigation") },
                { "location", ("location", "string", "navigation") },
                { "address", ("location", "string", "navigation") },

                // Environmental
                { "seadepth", ("sea_depth", "decimal", "environmental") },
                { "sea_depth", ("sea_depth", "decimal", "environmental") },
                { "depth", ("sea_depth", "decimal", "environmental") },
                { "water_depth", ("sea_depth", "decimal", "environmental") },
                { "watertemperature", ("water_temperature", "decimal", "environmental") },
                { "water_temperature", ("water_temperature", "decimal", "environmental") },
                { "temp", ("water_temperature", "decimal", "environmental") },
                { "windangle", ("wind_angle", "decimal", "environmental") },
                { "wind_angle", ("wind_angle", "decimal", "environmental") },
                { "windspeed", ("wind_speed", "decimal", "environmental") },
                { "wind_speed", ("wind_speed", "decimal", "environmental") },

                // Engine - General
                { "rpm", ("rpm", "decimal", "engine_general") },
                { "engine_rpm", ("rpm", "decimal", "engine_general") },
                { "enginehours", ("engine_hours", "decimal", "engine_general") },
                { "engine_hours", ("engine_hours", "decimal", "engine_general") },
                { "engineload", ("engine_load", "decimal", "engine_general") },
                { "engine_load", ("engine_load", "decimal", "engine_general") },
                { "oilpressure", ("oil_pressure", "decimal", "engine_general") },
                { "oil_pressure", ("oil_pressure", "decimal", "engine_general") },
                { "fuelrate", ("fuel_rate", "decimal", "engine_general") },
                { "fuel_rate", ("fuel_rate", "decimal", "engine_general") },
                { "fuel_consumption", ("fuel_rate", "decimal", "engine_general") },

                // Engine - Port
                { "port_rpm", ("port_rpm", "decimal", "engine_port") },
                { "portrpm", ("port_rpm", "decimal", "engine_port") },
                { "rpmport", ("port_rpm", "decimal", "engine_port") },
                { "port_engine_rpm", ("port_rpm", "decimal", "engine_port") },
                { "port_engine_hours", ("port_engine_hours", "decimal", "engine_port") },
                { "portenghours", ("port_engine_hours", "decimal", "engine_port") },
                { "port_oil_pressure", ("port_oil_pressure", "decimal", "engine_port") },
                { "portoilpressure", ("port_oil_pressure", "decimal", "engine_port") },
                { "portengoilpress", ("port_oil_pressure", "decimal", "engine_port") },
                { "port_fuel_rate", ("port_fuel_rate", "decimal", "engine_port") },
                { "portfuelrate", ("port_fuel_rate", "decimal", "engine_port") },
                { "portengfuelrate", ("port_fuel_rate", "decimal", "engine_port") },
                { "portengwatertemp", ("port_water_temperature", "decimal", "engine_port") },
                { "portwatertemperature", ("port_water_temperature", "decimal", "engine_port") },
                { "portengalarmcode", ("port_alarm_code", "string", "engine_port") },
                { "portalarmcode", ("port_alarm_code", "string", "engine_port") },
                { "portengwarningcode", ("port_warning_code", "string", "engine_port") },
                { "portwarningcode", ("port_warning_code", "string", "engine_port") },
                { "portenginerunning", ("port_engine_running", "string", "engine_port") },

                // Engine - Starboard
                { "stbd_rpm", ("stbd_rpm", "decimal", "engine_stbd") },
                { "stbdrpm", ("stbd_rpm", "decimal", "engine_stbd") },
                { "rpmstbd", ("stbd_rpm", "decimal", "engine_stbd") },
                { "starboard_rpm", ("stbd_rpm", "decimal", "engine_stbd") },
                { "stbd_engine_rpm", ("stbd_rpm", "decimal", "engine_stbd") },
                { "starboard_engine_rpm", ("stbd_rpm", "decimal", "engine_stbd") },
                { "stbd_engine_hours", ("stbd_engine_hours", "decimal", "engine_stbd") },
                { "stbdenghours", ("stbd_engine_hours", "decimal", "engine_stbd") },
                { "stbd_oil_pressure", ("stbd_oil_pressure", "decimal", "engine_stbd") },
                { "stbdoilpressure", ("stbd_oil_pressure", "decimal", "engine_stbd") },
                { "stbdengoilpress", ("stbd_oil_pressure", "decimal", "engine_stbd") },
                { "stbd_fuel_rate", ("stbd_fuel_rate", "decimal", "engine_stbd") },
                { "stbdfuelrate", ("stbd_fuel_rate", "decimal", "engine_stbd") },
                { "stbdengfuelrate", ("stbd_fuel_rate", "decimal", "engine_stbd") },
                { "stbdengwatertemp", ("stbd_water_temperature", "decimal", "engine_stbd") },
                { "stbdwatertemperature", ("stbd_water_temperature", "decimal", "engine_stbd") },
                { "stbdengalarmcode", ("stbd_alarm_code", "string", "engine_stbd") },
                { "stbdalarmcode", ("stbd_alarm_code", "string", "engine_stbd") },
                { "stbdengwarningcode", ("stbd_warning_code", "string", "engine_stbd") },
                { "stbdwarningcode", ("stbd_warning_code", "string", "engine_stbd") },

                // Systems
                { "powersupply", ("power_supply", "decimal", "systems") },
                { "power_supply", ("power_supply", "decimal", "systems") },
                { "voltage", ("power_supply", "decimal", "systems") },
                { "smartboatbattery", ("battery", "decimal", "systems") },
                { "smartboat_battery", ("battery", "decimal", "systems") },
                { "battery", ("battery", "decimal", "systems") },
                { "rudder", ("rudder", "decimal", "systems") },
                { "rudder_angle", ("rudder", "decimal", "systems") },
                { "totalnm", ("total_nm", "decimal", "systems") },
                { "total_nm", ("total_nm", "decimal", "systems") },
                { "nautical_miles", ("total_nm", "decimal", "systems") },
                { "gsm", ("gsm_signal", "decimal", "systems") },
                { "gsmsignal", ("gsm_signal", "decimal", "systems") },
                { "gsm_signal", ("gsm_signal", "decimal", "systems") },
                { "sensor", ("sensor_id", "string", "metadata") },
                { "sensorid", ("sensor_id", "string", "metadata") },
                { "sensor_id", ("sensor_id", "string", "metadata") },

                // Status and Alarms
                { "enginealarmcode", ("engine_alarm_code", "string", "systems") },
                { "engine_alarm_code", ("engine_alarm_code", "string", "systems") },
                { "alarm_code", ("engine_alarm_code", "string", "systems") },
                { "enginewarningcode", ("engine_warning_code", "string", "systems") },
                { "engine_warning_code", ("engine_warning_code", "string", "systems") },
                { "warning_code", ("engine_warning_code", "string", "systems") },
                { "gsmenginerunningindicator", ("engine_running", "string", "systems") },
                { "gsm_engine_running_indicator", ("engine_running", "string", "systems") },
                { "engine_running", ("engine_running", "string", "systems") },

                // Timestamps
                { "datetime", ("timestamp", "datetime", "metadata") },
                { "timestamp", ("timestamp", "datetime", "metadata") },
                { "sensortime", ("timestamp", "datetime", "metadata") },
                { "sensor_time", ("timestamp", "datetime", "metadata") },
                { "time", ("timestamp", "datetime", "metadata") },

                // Greek Column Names
                { "πλοίο", ("vessel_name", "string", "identification") },
                { "όχημα", ("vessel_name", "string", "identification") },
                { "ημερομηνία", ("timestamp", "datetime", "metadata") },
                { "χρόνος", ("timestamp", "datetime", "metadata") },
                { "ταχύτητα", ("speed", "decimal", "navigation") },
                { "στροφές", ("rpm", "decimal", "engine_general") },
                { "μηχανήαριστερά", ("port_rpm", "decimal", "engine_port") },
                { "μηχανήδεξιά", ("stbd_rpm", "decimal", "engine_stbd") },
                { "θερμοκρασίανερού", ("water_temperature", "decimal", "environmental") },
                { "βάθοςθάλασσας", ("sea_depth", "decimal", "environmental") },
                { "ταχύτηταανέμου", ("wind_speed", "decimal", "environmental") },
                { "γωνίαανέμου", ("wind_angle", "decimal", "environmental") },
                { "ομαδοποίηση", ("grouping", "string", "metadata") },
                { "συντεταγμένες", ("coordinates", "string", "navigation") },
                { "τοποθεσία", ("location", "string", "navigation") },
                { "αισθητήρας", ("sensor_id", "string", "metadata") }
            };
    }
}