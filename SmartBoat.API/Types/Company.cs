// File: Company.cs
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Company")]
    public class Company
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? Location { get; set; }
        public string? Industry { get; set; }
        public string? Status { get; set; }
        public Guid? CustomerId { get; set; }
        public DateTime? LastUpdated { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}