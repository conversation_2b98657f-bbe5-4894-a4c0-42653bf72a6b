using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SmartBoat.API.Types
{
    public class CreateSensorDataPointDto
    {
        [Required]
        public Guid SensorId { get; set; }
        
        public DateTime? Timestamp { get; set; } // Optional, defaults to now
        
        [Required]
        public Dictionary<string, object> Measurements { get; set; } = new();
        
        public float? QualityScore { get; set; } = 1.0f;
        public string Source { get; set; }
        
        // Helper methods for common measurements
        public void AddTemperature(float temperature) => Measurements["temperature"] = temperature;
        public void AddHumidity(float humidity) => Measurements["humidity"] = humidity;
        public void AddSpeed(float speed) => Measurements["speed"] = speed;
        public void AddRPM(float rpm) => Measurements["rpm"] = rpm;
        public void AddFuelLevel(float fuelLevel) => Measurements["fuel_level"] = fuelLevel;
        public void AddBatteryVoltage(float voltage) => Measurements["battery_voltage"] = voltage;
        
        // GPS coordinates
        public void AddPosition(float latitude, float longitude)
        {
            Measurements["gps"] = new Dictionary<string, object>
            {
                ["latitude"] = latitude,
                ["longitude"] = longitude
            };
        }
        
        // Wind data
        public void AddWind(float speed, float direction)
        {
            Measurements["wind"] = new Dictionary<string, object>
            {
                ["speed"] = speed,
                ["direction"] = direction
            };
        }
    }
}