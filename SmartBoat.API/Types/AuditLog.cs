using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("AuditLog")]
    public class AuditLog
    {
        public Guid? Id { get; set; }
        public Guid? UserId { get; set; }
        public string? EntityType { get; set; }
        public Guid? EntityId { get; set; }
        public string? Action { get; set; }
        public string? Details { get; set; }
        public DateTime? Timestamp { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}