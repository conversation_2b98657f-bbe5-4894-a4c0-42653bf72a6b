// File: VesselDto.cs
using System;
using System.Collections.Generic;

namespace SmartBoat.API.Types
{
    public class VesselDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? Number { get; set; }
        public VesselType? Type { get; set; }
        public string? Location { get; set; }
        public string? Status { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Image { get; set; }
        public int? Onsigners { get; set; }
        public int? Offsigners { get; set; }
        public Guid? CompanyId { get; set; }
        public CompanyDto? Company { get; set; }
        public Guid? OwnerId { get; set; }
        public OwnerDto? Owner { get; set; }
        public List<SensorDto>? Sensors { get; set; }
        public List<VesselPathPointDto>? PathPoints { get; set; }
        public DateTime? LastUpdated { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}