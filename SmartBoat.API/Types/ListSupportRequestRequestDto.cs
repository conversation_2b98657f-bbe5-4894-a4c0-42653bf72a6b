// File: ListSupportRequestRequestDto.cs
using System;

namespace SmartBoat.API.Types
{
    public class ListSupportRequestRequestDto
    {
        public int? PageLimit { get; set; }
        public int? PageOffset { get; set; }
        public string? SortField { get; set; }
        public string? SortOrder { get; set; }
        public string? SearchTerm { get; set; }
        public string? Subject { get; set; }
        public Guid? UserId { get; set; }
        public string? Status { get; set; }
    }
}