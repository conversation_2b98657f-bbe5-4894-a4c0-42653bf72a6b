using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Users")]
    public class User
    {
        public Guid? Id { get; set; }
        public string? Username { get; set; }
        public string? Email { get; set; }
        public string? PasswordHash { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public Guid? RoleId { get; set; }
        public string? Role { get; set; }
        public string? Status { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
        public DateTime? LastLogin { get; set; }
        public bool? TwoFactorEnabled { get; set; }
        public string? Avatar { get; set; }
        public DateTime? Joined { get; set; }
        public string? Company { get; set; }
        public Guid? CompanyId { get; set; }
        public string? Department { get; set; }
        public string? Phone { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Timezone { get; set; }
        public string? Language { get; set; }
        public string? Bio { get; set; }
        public bool? IsDeleted { get; set; }
    }
}