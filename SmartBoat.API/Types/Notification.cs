using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Notifications")]
    public class Notification
    {
        public Guid? Id { get; set; }
        public Guid? UserId { get; set; }
        public string? EventType { get; set; }
        public string? Content { get; set; }
        public string? Channel { get; set; }
        public string? Status { get; set; }
        public DateTime? Timestamp { get; set; }
        public Guid? PreferenceId { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}