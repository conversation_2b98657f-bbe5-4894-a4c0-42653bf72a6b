using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Sensors")]
    public class Sensor
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? Type { get; set; }
        public Guid? VesselId { get; set; }
        public string? Location { get; set; }
        public string? Status { get; set; }
        public DateTime? LastReading { get; set; }
        public DateTime? LastUpdated { get; set; }
        public string? AlertThreshold { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}