// File: Feedback.cs
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Feedback")]
    public class Feedback
    {
        public Guid? Id { get; set; }
        public Guid? UserId { get; set; }
        public string? Type { get; set; }
        public string? Content { get; set; }
        public DateTime? SubmittedAt { get; set; }
        public string? Status { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}