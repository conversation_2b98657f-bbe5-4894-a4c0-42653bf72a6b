using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("UnmappedCsvColumns")]
    public class UnmappedCsvColumns
    {
        public Guid Id { get; set; }
        public string VesselName { get; set; } = string.Empty;
        public string ColumnName { get; set; } = string.Empty;
        public string? SampleValue { get; set; }
        public DateTime FirstSeen { get; set; }
        public DateTime LastSeen { get; set; }
        public int OccurrenceCount { get; set; }
        public string Status { get; set; } = "pending";
        public string? AdminNotes { get; set; }
    }
}