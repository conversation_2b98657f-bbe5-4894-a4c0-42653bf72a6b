// File: CompanyDto.cs
using System;

namespace SmartBoat.API.Types
{
    public class CompanyDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? Location { get; set; }
        public string? Industry { get; set; }
        public string? Status { get; set; }
        public Guid? CustomerId { get; set; }
        public DateTime? LastUpdated { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}