using System;

namespace SmartBoat.API.Types
{
    public class ListAlertRequestDto
    {
        public int? PageLimit { get; set; }
        public int? PageOffset { get; set; }
        public string? SortField { get; set; }
        public string? SortOrder { get; set; }
        public string? SearchTerm { get; set; }
        public string? Type { get; set; }
        public Guid? EntityId { get; set; }
        public string? Status { get; set; }
        public string? DeliveryStatus { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
    }
}