using System;

namespace SmartBoat.API.Types
{
    public class UpdateNotificationDto
    {
        public Guid? Id { get; set; }
        public Guid? UserId { get; set; }
        public string? EventType { get; set; }
        public string? Content { get; set; }
        public string? Channel { get; set; }
        public string? Status { get; set; }
        public Guid? PreferenceId { get; set; }
    }
}