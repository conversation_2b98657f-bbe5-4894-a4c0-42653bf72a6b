using System;

namespace SmartBoat.API.Types
{
    public class ListNotificationRequestDto
    {
        public int? PageLimit { get; set; }
        public int? PageOffset { get; set; }
        public string? SortField { get; set; }
        public string? SortOrder { get; set; }
        public string? SearchTerm { get; set; }
        public Guid? UserId { get; set; }
        public string? Status { get; set; }
        public string? EventType { get; set; }
        public string? Channel { get; set; }
    }
}