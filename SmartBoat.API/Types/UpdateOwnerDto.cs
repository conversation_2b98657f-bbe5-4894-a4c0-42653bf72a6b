// File: UpdateOwnerDto.cs
using System;
using System.ComponentModel.DataAnnotations;

namespace SmartBoat.API.Types
{
    public class UpdateOwnerDto
    {
        [Required(ErrorMessage = "Owner ID is required")]
        public Guid? Id { get; set; }
        
        [Required(ErrorMessage = "Owner name is required")]
        public string? Name { get; set; }
        
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
        
        [Required(ErrorMessage = "Company is required")]
        public Guid? CompanyId { get; set; }
        
        public string? Status { get; set; }
    }
}