// File: SupportRequestDto.cs
using System;

namespace SmartBoat.API.Types
{
    public class SupportRequestDto
    {
        public Guid? Id { get; set; }
        public UserDto? User { get; set; }
        public string? Subject { get; set; }
        public string? Description { get; set; }
        public string? Status { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public string? Resolution { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}