using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("NotificationPreferences")]
    public class NotificationPreference
    {
        public Guid? Id { get; set; }
        public Guid? UserId { get; set; }
        public string? EventType { get; set; }
        public string? Channel { get; set; }
        public bool? Enabled { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}