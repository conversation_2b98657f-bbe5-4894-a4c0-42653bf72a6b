using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace SmartBoat.API.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class JwtAuthorizeAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            // Skip if already authorized or if endpoint allows anonymous
            if (context.HttpContext.User.Identity?.IsAuthenticated == true)
                return;

            // Check for AllowAnonymous attribute
            if (context.ActionDescriptor.EndpointMetadata.OfType<AllowAnonymousAttribute>().Any())
                return;

            // Extract token from Authorization header
            var authHeader = context.HttpContext.Request.Headers["Authorization"].FirstOrDefault();
            
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                context.Result = new UnauthorizedObjectResult(new { message = "Authorization token is required" });
                return;
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            
            try
            {
                var handler = new JwtSecurityTokenHandler();
                
                // Basic token validation (detailed validation is done by JWT middleware)
                if (!handler.CanReadToken(token))
                {
                    context.Result = new UnauthorizedObjectResult(new { message = "Invalid token format" });
                    return;
                }

                var jsonToken = handler.ReadJwtToken(token);
                
                // Check if token is expired
                if (jsonToken.ValidTo < DateTime.UtcNow)
                {
                    context.Result = new UnauthorizedObjectResult(new { message = "Token has expired" });
                    return;
                }

                // Extract user ID from token
                var userIdClaim = jsonToken.Claims.FirstOrDefault(x => 
                    x.Type == "sub" || 
                    x.Type == "userId" || 
                    x.Type == ClaimTypes.NameIdentifier ||
                    x.Type == JwtRegisteredClaimNames.Sub);
                
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    context.Result = new UnauthorizedObjectResult(new { message = "Invalid user information in token" });
                    return;
                }

                // Create claims identity for the current request
                var claims = new List<Claim>
                {
                    new(ClaimTypes.NameIdentifier, userId.ToString()),
                    new("userId", userId.ToString())
                };

                // Add other claims from token
                foreach (var claim in jsonToken.Claims.Where(c => c.Type != "sub" && c.Type != "userId"))
                {
                    claims.Add(new Claim(claim.Type, claim.Value));
                }

                var identity = new ClaimsIdentity(claims, "jwt");
                var principal = new ClaimsPrincipal(identity);
                
                context.HttpContext.User = principal;
            }
            catch (Exception)
            {
                context.Result = new UnauthorizedObjectResult(new { message = "Invalid or malformed token" });
                return;
            }
        }
    }
}