<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>SmartBoat.API</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Nbg.AspNetCore.Dapper" Version="8.0.4.1" />
    <PackageReference Include="Nbg.NetCore.AutocodeDbOperations" Version="1.1.0-ci-20250409-144812" />
    <PackageReference Include="Nbg.NetCore.Data" Version="8.0.5.2" />
    <PackageReference Include="Nbg.NetCore.DatabaseService" Version="8.1.5" />
    <PackageReference Include="NLog" Version="5.4.0" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.4.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.15" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Nbg.NetCore.Utilities" Version="8.0.4" />
    <PackageReference Include="Nbg.NetCore.Configuration.Extensions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Graph" Version="5.56.0" />
    <PackageReference Include="Microsoft.Graph.Auth" Version="1.0.0-preview.7" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.11.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="BCryptGenerator\obj\BCryptGenerator.csproj.nuget.dgspec.json" />
    <_ContentIncludedByDefault Remove="BCryptGenerator\obj\project.assets.json" />
  </ItemGroup>

</Project>