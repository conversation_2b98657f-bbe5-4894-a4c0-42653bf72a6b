using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class SubscriptionController : ControllerBase
    {
        private readonly ISubscriptionService _subscriptionService;

        public SubscriptionController(ISubscriptionService subscriptionService)
        {
            _subscriptionService = subscriptionService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<SubscriptionDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Create([FromBody] Request<CreateSubscriptionDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _subscriptionService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<SubscriptionDto> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<SubscriptionDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get([FromBody] Request<SubscriptionRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _subscriptionService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<SubscriptionDto> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListSubscriptionDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetList([FromBody] Request<ListSubscriptionRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _subscriptionService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListSubscriptionDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<SubscriptionDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Update([FromBody] Request<UpdateSubscriptionDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _subscriptionService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<SubscriptionDto> { Payload = result });
            });
        }

        [HttpPost("deactivate")]
        [ProducesResponseType(200, Type = typeof(Response<SubscriptionDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Deactivate([FromBody] Request<DeleteSubscriptionDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _subscriptionService.Deactivate(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<SubscriptionDto> { Payload = result });
            });
        }
    }
}