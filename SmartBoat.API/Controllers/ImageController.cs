using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.IdentityModel.Tokens.Jwt;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class ImageController : ControllerBase
    {
        private readonly IImageService _imageService;

        public ImageController(IImageService imageService)
        {
            _imageService = imageService;
        }

        [HttpPost("upload/{entityId}")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Upload(Guid entityId, [FromForm] IFormFile imageFile)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                // Extract userId from JWT token
                var userId = GetUserIdFromToken();
                
                var createImageDto = new CreateImageDto
                {
                    EntityId = entityId,
                    ImageFile = imageFile,
                    UploadedBy = userId
                };

                var result = await _imageService.Create(createImageDto);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpGet("entity/{entityId}")]
        [ProducesResponseType(200, Type = typeof(Response<List<ImageDto>>))]
        public async Task<IActionResult> GetByEntityId(Guid entityId)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _imageService.GetByEntityId(entityId);
                return Ok(new Response<List<ImageDto>> { Payload = result });
            });
        }

        [HttpGet("{imageId}")]
        public async Task<IActionResult> GetImage(Guid imageId)
        {
            try
            {
                var image = await _imageService.GetImageData(imageId);
                return File(image.ImageData, image.ContentType, image.FileName);
            }
            catch (Exception ex)
            {
                return NotFound(new Response<object>
                {
                    Exception = new ResponseException
                    {
                        Code = "DP-404",
                        Description = "Image not found",
                        Category = "Technical"
                    }
                });
            }
        }

        [HttpDelete("{imageId}")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> Delete(Guid imageId)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                // Extract userId from JWT token
                var userId = GetUserIdFromToken();
                
                var result = await _imageService.Delete(imageId, userId);
                return Ok(new Response<bool> { Payload = result });
            });
        }

        private Guid GetUserIdFromToken()
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (authHeader != null && authHeader.StartsWith("Bearer "))
            {
                var token = authHeader.Substring("Bearer ".Length).Trim();
                
                try
                {
                    var handler = new JwtSecurityTokenHandler();
                    var jsonToken = handler.ReadJwtToken(token);
                    
                    // Extract user ID from token claims
                    var userIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "sub" || x.Type == "userId" || x.Type == ClaimTypes.NameIdentifier);
                    
                    if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                    {
                        return userId;
                    }
                }
                catch (Exception)
                {
                    // Invalid token
                }
            }
            
            throw new UnauthorizedAccessException("User not authenticated");
        }
    }
}