using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class SensorController : ControllerBase
    {
        private readonly ISensorService _sensorService;

        public SensorController(ISensorService sensorService)
        {
            _sensorService = sensorService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Create([FromBody] Request<CreateSensorDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<SensorDto>))]
        public async Task<IActionResult> Get([FromBody] Request<SensorRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<SensorDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Update([FromBody] Request<UpdateSensorDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("delete")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> Delete([FromBody] Request<DeleteSensorDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListSensorDto>))]
        public async Task<IActionResult> GetList([FromBody] Request<ListSensorRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListSensorDto> { Payload = result });
            });
        }
    }
}