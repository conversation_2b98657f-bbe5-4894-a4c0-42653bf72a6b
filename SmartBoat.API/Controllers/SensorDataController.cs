using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class SensorDataController : ControllerBase
    {
        private readonly ICsvSensorDataService _csvSensorDataService;

        public SensorDataController(ICsvSensorDataService csvSensorDataService)
        {
            _csvSensorDataService = csvSensorDataService;
        }

        [HttpPost("vessels")]
        [ProducesResponseType(200, Type = typeof(Response<System.Collections.Generic.List<string>>))]
        public async Task<IActionResult> GetVesselNames([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _csvSensorDataService.GetVesselNamesAsync(request.Header.UserId.Value);
                return Ok(result);
            });
        }

        [HttpPost("summary")]
        [ProducesResponseType(200, Type = typeof(Response<ProcessingSummaryDto>))]
        public async Task<IActionResult> GetSummary([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _csvSensorDataService.GetSummaryAsync(request.Header.UserId.Value);
                return Ok(result);
            });
        }
    }
}