using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Data;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly ILogger<HealthController> _logger;
        private readonly IDbConnection? _dbConnection;

        public HealthController(ILogger<HealthController> logger, IDbConnection? dbConnection = null)
        {
            _logger = logger;
            _dbConnection = dbConnection;
        }

        [HttpGet]
        public IActionResult Get()
        {
            try
            {
                var health = new
                {
                    Status = "Healthy",
                    Timestamp = DateTime.UtcNow,
                    Version = "1.0.0",
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
                };

                return Ok(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                return StatusCode(500, new { Status = "Unhealthy", Error = ex.Message });
            }
        }

        [HttpGet("database")]
        public async Task<IActionResult> Database()
        {
            try
            {
                if (_dbConnection == null)
                {
                    return StatusCode(500, new { Status = "Database Unavailable", Error = "No database connection available" });
                }

                // Simple database connectivity check
                var command = _dbConnection.CreateCommand();
                command.CommandText = "SELECT 1";
                var result = await Task.FromResult(command.ExecuteScalar());

                return Ok(new
                {
                    Status = "Database Connected",
                    Timestamp = DateTime.UtcNow,
                    Result = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database health check failed");
                return StatusCode(500, new { Status = "Database Unhealthy", Error = ex.Message });
            }
        }
    }
}