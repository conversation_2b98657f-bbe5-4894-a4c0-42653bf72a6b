using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService _notificationService;

        public NotificationController(INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Create([FromBody] Request<CreateNotificationDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _notificationService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result, Exception = null });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<NotificationDto>))]
        public async Task<IActionResult> Get([FromBody] Request<NotificationRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _notificationService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<NotificationDto> { Payload = result, Exception = null });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Update([FromBody] Request<UpdateNotificationDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _notificationService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result, Exception = null });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListNotificationDto>))]
        public async Task<IActionResult> GetList([FromBody] Request<ListNotificationRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _notificationService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListNotificationDto> { Payload = result, Exception = null });
            });
        }

        [HttpPost("{id}/mark-read")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> MarkAsRead(Guid id, [FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _notificationService.MarkAsRead(id, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result, Exception = null });
            });
        }

        [HttpPost("mark-all-read")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> MarkAllAsRead([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _notificationService.MarkAllAsRead(request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result, Exception = null });
            });
        }

        [HttpPost("unread-count")]
        [ProducesResponseType(200, Type = typeof(Response<int>))]
        public async Task<IActionResult> GetUnreadCount([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _notificationService.GetUnreadCount(request.Header.UserId.Value);
                return Ok(new Response<int> { Payload = result, Exception = null });
            });
        }
    }
}