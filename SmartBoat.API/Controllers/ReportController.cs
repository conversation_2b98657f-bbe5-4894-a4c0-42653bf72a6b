using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class ReportController : ControllerBase
    {
        private readonly IReportService _reportService;

        public ReportController(IReportService reportService)
        {
            _reportService = reportService;
        }

        [HttpPost("generate")]
        [ProducesResponseType(200, Type = typeof(Response<ReportDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Generate([FromBody] Request<GenerateReportDto> request)
        {
            if (request?.Header?.UserId == null)
                return BadRequest("UserId is required.");
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var reportId = await _reportService.Generate(request.Payload, request.Header.UserId.Value);
                // Assuming reportId is the string representation of the new report's Id (int)
                if (!int.TryParse(reportId, out var id))
                    return BadRequest("Failed to generate report: invalid report ID.");
                var reportDto = await _reportService.Get(new GetReportRequestDto { Id = id }, request.Header.UserId.Value);
                return Ok(new Response<ReportDto> { Payload = reportDto });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<ReportDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get([FromBody] Request<GetReportRequestDto> request)
        {
            if (request?.Header?.UserId == null)
                return BadRequest("UserId is required.");
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _reportService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReportDto> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListReportDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetList([FromBody] Request<ListReportRequestDto> request)
        {
            if (request?.Header?.UserId == null)
                return BadRequest("UserId is required.");
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _reportService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListReportDto> { Payload = result });
            });
        }
    }
}