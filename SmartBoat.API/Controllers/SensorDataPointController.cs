using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class SensorDataPointController : ControllerBase
    {
        private readonly ISensorDataPointService _sensorDataPointService;

        public SensorDataPointController(ISensorDataPointService sensorDataPointService)
        {
            _sensorDataPointService = sensorDataPointService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Create([FromBody] Request<CreateSensorDataPointDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorDataPointService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<SensorDataPointDto>))]
        public async Task<IActionResult> Get([FromBody] Request<SensorDataPointRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorDataPointService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<SensorDataPointDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Update([FromBody] Request<UpdateSensorDataPointDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorDataPointService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("delete")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> Delete([FromBody] Request<DeleteSensorDataPointDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorDataPointService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListSensorDataPointDto>))]
        public async Task<IActionResult> GetList([FromBody] Request<ListSensorDataPointRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorDataPointService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListSensorDataPointDto> { Payload = result });
            });
        }

        [HttpPost("latest")]
        [ProducesResponseType(200, Type = typeof(Response<List<SensorDataPointDto>>))]
        public async Task<IActionResult> GetLatest([FromBody] Request<LatestSensorDataPointRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorDataPointService.GetLatest(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<List<SensorDataPointDto>> { Payload = result });
            });
        }

        [HttpPost("by-sensor")]
        [ProducesResponseType(200, Type = typeof(Response<List<SensorDataPointDto>>))]
        public async Task<IActionResult> GetBySensor([FromBody] Request<SensorDataPointRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _sensorDataPointService.GetBySensor(request.Payload.Id, request.Header.UserId.Value);
                return Ok(new Response<List<SensorDataPointDto>> { Payload = result });
            });
        }
    }
}