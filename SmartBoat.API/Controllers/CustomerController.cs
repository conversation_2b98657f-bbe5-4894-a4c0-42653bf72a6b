using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class CustomerController : ControllerBase
    {
        private readonly ICustomerService _customerService;

        public CustomerController(ICustomerService customerService)
        {
            _customerService = customerService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<CustomerDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Create([FromBody] Request<CreateCustomerDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _customerService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<CustomerDto> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<CustomerDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get([FromBody] Request<CustomerRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _customerService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<CustomerDto> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListCustomerDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetList([FromBody] Request<ListCustomerRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _customerService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListCustomerDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<CustomerDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Update([FromBody] Request<UpdateCustomerDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _customerService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<CustomerDto> { Payload = result });
            });
        }

        [HttpPost("deactivate")]
        [ProducesResponseType(200, Type = typeof(Response<CustomerDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete([FromBody] Request<DeleteCustomerDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _customerService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<CustomerDto> { Payload = result });
            });
        }
    }
}