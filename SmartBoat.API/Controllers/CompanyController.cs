using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class CompanyController : ControllerBase
    {
        private readonly ICompanyService _companyService;

        public CompanyController(ICompanyService companyService)
        {
            _companyService = companyService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<CompanyDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Create([FromBody] Request<CreateCompanyDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _companyService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<CompanyDto> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<CompanyDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get([FromBody] Request<CompanyRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _companyService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<CompanyDto> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListCompanyDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetList([FromBody] Request<ListCompanyRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _companyService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListCompanyDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<CompanyDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Update([FromBody] Request<UpdateCompanyDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _companyService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<CompanyDto> { Payload = result });
            });
        }

        [HttpPost("deactivate")]
        [ProducesResponseType(200, Type = typeof(Response<CompanyDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Deactivate([FromBody] Request<CompanyRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _companyService.Deactivate(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<CompanyDto> { Payload = result });
            });
        }

        [HttpPost("delete")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete([FromBody] Request<DeleteCompanyDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _companyService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }
    }
}