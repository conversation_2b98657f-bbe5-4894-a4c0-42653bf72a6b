using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class OwnerController : ControllerBase
    {
        private readonly IOwnerService _ownerService;

        public OwnerController(IOwnerService ownerService)
        {
            _ownerService = ownerService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<OwnerDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Create([FromBody] Request<CreateOwnerDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _ownerService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<OwnerDto> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<OwnerDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get([FromBody] Request<DeleteOwnerDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _ownerService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<OwnerDto> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListOwnerDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetList([FromBody] Request<ListOwnerRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _ownerService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListOwnerDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<OwnerDto>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Update([FromBody] Request<UpdateOwnerDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _ownerService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<OwnerDto> { Payload = result });
            });
        }

        [HttpPost("delete")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete([FromBody] Request<DeleteOwnerDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _ownerService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }
    }
}