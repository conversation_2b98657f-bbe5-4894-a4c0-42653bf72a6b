using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class SupportRequestController : ControllerBase
    {
        private readonly ISupportRequestService _supportRequestService;

        public SupportRequestController(ISupportRequestService supportRequestService)
        {
            _supportRequestService = supportRequestService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Create([FromBody] Request<CreateSupportRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                if (request.Header.UserId == null)
                    return BadRequest("UserId is required.");
                var result = await _supportRequestService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<SupportRequestDto>))]
        public async Task<IActionResult> Get([FromBody] Request<SupportRequestRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                if (request.Header.UserId == null)
                    return BadRequest("UserId is required.");
                var result = await _supportRequestService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<SupportRequestDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Update([FromBody] Request<UpdateSupportRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                if (request.Header.UserId == null)
                    return BadRequest("UserId is required.");
                var result = await _supportRequestService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("delete")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> Delete([FromBody] Request<DeleteSupportRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                if (request.Header.UserId == null)
                    return BadRequest("UserId is required.");
                var result = await _supportRequestService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListSupportRequestDto>))]
        public async Task<IActionResult> GetList([FromBody] Request<ListSupportRequestRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                if (request.Header.UserId == null)
                    return BadRequest("UserId is required.");
                var result = await _supportRequestService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListSupportRequestDto> { Payload = result });
            });
        }
    }
}