using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class AuditLogController : ControllerBase
    {
        private readonly IAuditLogService _auditLogService;

        public AuditLogController(IAuditLogService auditLogService)
        {
            _auditLogService = auditLogService;
        }

        [HttpPost("CreateAuditLog")]
        [ProducesResponseType(200, Type = typeof(Response<AuditLog>))]
        public async Task<IActionResult> CreateAuditLog([FromBody] Request<AuditLogPayload> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _auditLogService.CreateAuditLog(request.Payload);
                return Ok(result);
            });
        }

        [HttpGet("GetAuditLog/{id}")]
        [ProducesResponseType(200, Type = typeof(Response<AuditLog>))]
        public async Task<IActionResult> GetAuditLog(Guid id)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _auditLogService.GetAuditLogById(id.ToString());
                return Ok(result);
            });
        }

        [HttpPut("UpdateAuditLog/{id}")]
        [ProducesResponseType(200, Type = typeof(Response<AuditLog>))]
        public async Task<IActionResult> UpdateAuditLog([FromBody] Request<AuditLogPayload> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _auditLogService.UpdateAuditLog(request.Payload);
                return Ok(result);
            });
        }

        [HttpDelete("DeleteAuditLog/{id}")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> DeleteAuditLog(Guid id)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _auditLogService.DeleteAuditLog(id.ToString());
                return Ok(result);
            });
        }
    }
}