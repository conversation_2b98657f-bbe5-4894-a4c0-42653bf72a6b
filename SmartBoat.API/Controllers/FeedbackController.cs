using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System.Threading.Tasks;
using System;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class FeedbackController : ControllerBase
    {
        private readonly IFeedbackService _feedbackService;

        public FeedbackController(IFeedbackService feedbackService)
        {
            _feedbackService = feedbackService;
        }

        [HttpPost("CreateFeedback")]
        [ProducesResponseType(200, Type = typeof(Response<Feedback>))]
        public async Task<IActionResult> CreateFeedback([FromBody] Request<FeedbackRequest> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _feedbackService.CreateFeedback(request.Payload);
                return Ok(new Response<Feedback> { Payload = result });
            });
        }

        [HttpGet("GetFeedback/{feedbackId}")]
        [ProducesResponseType(200, Type = typeof(Response<Feedback>))]
        public async Task<IActionResult> GetFeedback(Guid feedbackId)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _feedbackService.SelectFeedbackAsync(feedbackId);
                return Ok(new Response<Feedback> { Payload = result });
            });
        }

        [HttpPut("UpdateFeedback")]
        [ProducesResponseType(200, Type = typeof(Response<Feedback>))]
        public async Task<IActionResult> UpdateFeedback([FromBody] Request<UpdateFeedbackRequest> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _feedbackService.UpdateFeedback(request.Payload);
                return Ok(new Response<Feedback> { Payload = result });
            });
        }

        [HttpDelete("DeleteFeedback/{feedbackId}")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> DeleteFeedback(Guid feedbackId)
        {
            // TODO: Replace with actual userId extraction from claims or context
            Guid userId = Guid.Empty; // Placeholder, replace with real user id

            return await SafeExecutor.ExecuteAsync(async () =>
            {
                await _feedbackService.DeleteFeedbackAsync(feedbackId, userId);
                return Ok(new Response<bool> { Payload = true });
            });
        }
    }
}