using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class AlertController : ControllerBase
    {
        private readonly IAlertService _alertService;

        public AlertController(IAlertService alertService)
        {
            _alertService = alertService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Create([FromBody] Request<CreateAlertDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _alertService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<AlertDto>))]
        public async Task<IActionResult> Get([FromBody] Request<Guid> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _alertService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<AlertDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Update([FromBody] Request<UpdateAlertDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _alertService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("delete")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> Delete([FromBody] Request<DeleteAlertDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _alertService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListAlertDto>))]
        public async Task<IActionResult> GetList([FromBody] Request<ListAlertRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _alertService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListAlertDto> { Payload = result });
            });
        }
    }
}