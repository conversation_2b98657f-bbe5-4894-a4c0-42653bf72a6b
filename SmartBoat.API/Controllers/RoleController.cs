using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class RoleController : ControllerBase
    {
        private readonly IRoleService _roleService;

        public RoleController(IRoleService roleService)
        {
            _roleService = roleService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<RoleDto>))]
        public async Task<IActionResult> Create([FromBody] Request<CreateRoleDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _roleService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<RoleDto> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<RoleDto>))]
        public async Task<IActionResult> Get([FromBody] Request<Guid> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _roleService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<RoleDto> { Payload = result });
            });
        }

        [HttpGet("list")]
        [ProducesResponseType(200, Type = typeof(Response<List<RoleDto>>))]
        public async Task<IActionResult> GetList([FromQuery] Request<ListRoleRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _roleService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListRoleDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<RoleDto>))]
        public async Task<IActionResult> Update([FromBody] Request<UpdateRoleDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _roleService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<RoleDto> { Payload = result });
            });
        }

        [HttpPost("delete")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> Delete([FromBody] Request<Guid> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _roleService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }
    }
}