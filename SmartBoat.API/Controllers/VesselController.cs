using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class VesselController : ControllerBase
    {
        private readonly IVesselService _vesselService;

        public VesselController(IVesselService vesselService)
        {
            _vesselService = vesselService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Create([FromBody] Request<CreateVesselDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _vesselService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<VesselDto>))]
        public async Task<IActionResult> Get([FromBody] Request<DeleteVesselDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _vesselService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<VesselDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Update([FromBody] Request<UpdateVesselDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _vesselService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("delete")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> Delete([FromBody] Request<DeleteVesselDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _vesselService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListVesselDto>))]
        public async Task<IActionResult> GetList([FromBody] Request<ListVesselRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _vesselService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListVesselDto> { Payload = result });
            });
        }
    }
}