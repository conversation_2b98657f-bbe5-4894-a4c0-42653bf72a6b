using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpPost("create")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Create([FromBody] Request<CreateUserDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _userService.Create(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("get")]
        [ProducesResponseType(200, Type = typeof(Response<UserDto>))]
        public async Task<IActionResult> Get([FromBody] Request<UserRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _userService.Get(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<UserDto> { Payload = result });
            });
        }

        [HttpPost("update")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> Update([FromBody] Request<UpdateUserDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _userService.Update(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<string> { Payload = result });
            });
        }

        [HttpPost("delete")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> Delete([FromBody] Request<DeleteUserDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _userService.Delete(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }

        [HttpPost("list")]
        [ProducesResponseType(200, Type = typeof(Response<ReturnListUserDto>))]
        public async Task<IActionResult> GetList([FromBody] Request<ListUserRequestDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _userService.GetList(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<ReturnListUserDto> { Payload = result });
            });
        }

        [HttpPost("change-password")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> ChangePassword([FromBody] Request<ChangePasswordDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _userService.ChangePassword(request.Payload, request.Header.UserId.Value);
                return Ok(new Response<bool> { Payload = result });
            });
        }

        [HttpPost("reset-password")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> ResetPassword([FromBody] Request<ResetPasswordDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _userService.ResetPassword(request.Payload);
                return Ok(new Response<bool> { Payload = result });
            });
        }
    }
}