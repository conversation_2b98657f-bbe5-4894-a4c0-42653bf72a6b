# CSV to SensorDataPoints Migration System

## Overview

This document describes the implementation of a flexible CSV column mapping system that transforms IoT sensor data from emails directly into the production `SensorDataPoints` table, replacing the temporary `CsvSensorData` table approach.

## Architecture

### Previous Flow (Deprecated)
```
Emails → ZIP → CSV → CsvSensorData (temporary table) → Manual processing
```

### New Flow (Production)
```
Emails → ZIP → CSV → Column Mapping → JSON Measurements → SensorDataPoints
```

## Core Components

### 1. Column Mapping System

#### CsvColumnMapper Service (`Services/CsvColumnMapper.cs`)
- **Purpose**: Intelligent column mapping with multi-language support
- **Features**:
  - 80+ predefined column mappings
  - Fuzzy matching with normalization
  - Custom vessel-specific mappings
  - Data type conversion (decimal, datetime, boolean, string)
  - Quality scoring for mapping confidence

#### Default Mappings (`Types/CsvColumnMapping.cs`)
Supports common IoT sensor data patterns:
- **Navigation**: Speed, coordinates, course, distance
- **Environmental**: Sea depth, wind data, water temperature
- **Engine General**: RPM, hours, load, oil pressure, fuel rate
- **Engine Port/Starboard**: Dual-engine vessel support
- **Systems**: Power supply, battery, rudder, alarms
- **Multi-language**: English and Greek column variations

### 2. Vessel & Sensor Management

#### VesselSensorManager Service (`Services/VesselSensorManager.cs`)
- **Auto Vessel Creation**: Creates vessels when not found in database
- **Multi-Sensor Strategy**: 
  - Primary Sensor: Navigation, environmental, general systems
  - Port Engine Sensor: PORT engine measurements (dual-engine vessels)
  - Starboard Engine Sensor: STBD engine measurements (dual-engine vessels)
- **Engine Configuration Detection**: Automatically identifies single vs dual-engine setups

### 3. Database Schema

#### Core Tables
```sql
-- Column mapping configurations
CsvColumnMappings (Id, VesselName, ColumnName, MeasurementKey, DataType, Category, IsActive)

-- Vessel sensor configurations
VesselSensorConfigurations (Id, VesselId, VesselName, PrimarySensorId, PortEngineSensorId, StarboardEngineSensorId, EngineConfiguration)

-- Unknown column tracking
UnmappedCsvColumns (Id, VesselName, ColumnName, SampleValue, FirstSeen, LastSeen, OccurrenceCount, Status)
```

#### Production Table
```sql
-- Time-series sensor data with JSON measurements
SensorDataPoints (Id, SensorId, Timestamp, TimestampUnix, Measurements, QualityScore, Source)
```

## Key Features

### 1. Flexible Column Mapping
- **Default Mappings**: Covers 80+ common column variations
- **Custom Mappings**: Vessel-specific overrides stored in database
- **Normalization**: Handles spacing, casing, special characters
- **Multi-language**: Support for Greek and English column names

### 2. Multi-Engine Detection
```csharp
// Automatic detection patterns
PORT/STBD engines → Creates separate sensors
ENGINE1/ENGINE2 → Creates separate sensors
Single engine → Uses primary sensor
```

### 3. Data Quality Management
- **Quality Scoring**: Percentage of successfully mapped columns
- **Unknown Column Tracking**: Logs unmapped columns for admin review
- **Validation**: Ensures required fields (vessel name, timestamp)
- **Error Handling**: Graceful handling of conversion failures

### 4. JSON Measurements Structure
```json
{
  "speed": 12.5,
  "rpm": 1800,
  "water_temperature": 18.2,
  "coordinates": "37.9755°N 23.7348°E",
  "engine_running": "true",
  "port_rpm": 1750,
  "stbd_rpm": 1850
}
```

## Configuration

### Service Registration (`Program.cs`)
```csharp
// CSV Column Mapping Services
builder.Services.AddScoped<ICsvColumnMapper, CsvColumnMapper>();
builder.Services.AddScoped<IVesselSensorManager, VesselSensorManager>();
```

### Database Setup
```bash
# Run the migration scripts
./Database/setup-database.sh

# Include the new tables
sqlcmd -i Database/CsvColumnMappings.sql
```

## Usage

### Email Processing Integration
The system integrates seamlessly with the existing email processing pipeline:

```csharp
// In EmailProcessingService
var csvResponse = await _csvProcessingService.ProcessCsvToSensorDataPointsAsync(
    extractedFile.Content, 
    extractedFile.FileName, 
    email.FromEmail ?? "", 
    userId
);

// Returns List<Guid> of created SensorDataPoint IDs
```

### Column Mapping Process
1. **Load Custom Mappings**: Check for vessel-specific mappings
2. **Apply Default Mappings**: Use predefined column patterns
3. **Normalize Column Names**: Handle spacing, casing variations
4. **Convert Data Types**: Transform strings to appropriate types
5. **Group by Category**: Separate measurements by sensor type
6. **Create Data Points**: Bulk insert to SensorDataPoints table

### Multi-Engine Handling
```csharp
// Engine configuration detection
var engineConfig = await _csvColumnMapper.DetectVesselEngineConfigurationAsync(headers);

// Results in:
// "single" - One engine, use primary sensor
// "dual" - Two engines, create port/starboard sensors
// "unknown" - Cannot determine, use primary sensor
```

## Administration

### Unknown Column Management
Administrators can review unmapped columns:

```sql
SELECT VesselName, ColumnName, SampleValue, OccurrenceCount, FirstSeen 
FROM UnmappedCsvColumns 
WHERE Status = 'pending'
ORDER BY OccurrenceCount DESC;
```

### Custom Mapping Creation
```sql
INSERT INTO CsvColumnMappings (VesselName, ColumnName, MeasurementKey, DataType, Category)
VALUES ('Amazing Elli', 'ΜΗΧΑΝΗ_ΣΤΡΟΦΕΣ', 'rpm', 'decimal', 'engine_general');
```

### Vessel Sensor Configuration
```sql
SELECT v.Name, vsc.EngineConfiguration, 
       s1.Name as PrimarySensor,
       s2.Name as PortSensor,
       s3.Name as StbdSensor
FROM VesselSensorConfigurations vsc
JOIN Vessels v ON vsc.VesselId = v.Id
JOIN Sensors s1 ON vsc.PrimarySensorId = s1.Id
LEFT JOIN Sensors s2 ON vsc.PortEngineSensorId = s2.Id
LEFT JOIN Sensors s3 ON vsc.StarboardEngineSensorId = s3.Id;
```

## Performance Considerations

### Optimizations Implemented
- **Bulk Processing**: Batch sensor data point creation
- **Caching**: Vessel configurations cached per processing session
- **Efficient Queries**: Indexed database lookups
- **Memory Management**: Streaming CSV processing for large files

### Monitoring Metrics
- **Mapping Quality Score**: Percentage of successfully mapped columns
- **Processing Speed**: Records processed per second
- **Unknown Columns**: Rate of unmapped column discovery
- **Error Rate**: Failed conversions or validations

## Error Handling

### Common Scenarios
1. **Unknown Vessel**: Auto-creates vessel with default owner
2. **Missing Sensors**: Auto-creates required sensors for vessel
3. **Invalid Data Types**: Logs warning, continues processing
4. **Unknown Columns**: Tracks for admin review
5. **Duplicate Timestamps**: Handled by unique constraints

### Logging Levels
- **Info**: Successful processing, new vessel/sensor creation
- **Warning**: Data conversion failures, unknown columns
- **Error**: Critical failures, database connection issues

## Migration Strategy

### Phase 1: Core Implementation ✅
- [x] Column mapping infrastructure
- [x] Vessel-sensor management
- [x] Direct SensorDataPoints integration
- [x] Multi-engine detection

### Phase 2: Enhanced Features ✅
- [x] Unknown column tracking
- [x] Quality scoring
- [x] Admin configuration tables
- [x] Backwards compatibility

### Phase 3: Future Enhancements
- [ ] Web UI for mapping management
- [ ] Advanced pattern recognition
- [ ] Machine learning column suggestions
- [ ] Real-time processing metrics dashboard

## Testing

### Test Scenarios
1. **Single Engine Vessel**: Standard sensor data processing
2. **Dual Engine Vessel**: PORT/STBD engine data separation
3. **Unknown Columns**: Proper logging and tracking
4. **Multi-language Headers**: Greek/English column processing
5. **Data Type Conversion**: Decimal separators, date formats
6. **New Vessel Creation**: Auto-vessel and sensor setup

### Sample Data Patterns
```csv
# English headers
Vessel,DateTime,Speed,RPM,PORT_RPM,STBD_RPM,WaterTemp
"Amazing Elli","2025-01-15 10:30:00",12.5,1800,1750,1850,18.2

# Greek headers  
Πλοίο,Ημερομηνία,Ταχύτητα,Στροφές,ΜΗΧΑΝΗ_ΑΡΙΣΤΕΡΑ,ΜΗΧΑΝΗ_ΔΕΞΙΑ
"Amazing Elli","2025-01-15 10:30:00","12,5",1800,1750,1850

# Mixed patterns
vessel_name,sensor_time,spd,engine_rpm,port_engine_rpm,starboard_engine_rpm
```

## Security Considerations

### Data Protection
- **Input Validation**: All CSV data validated before processing
- **SQL Injection Prevention**: Parameterized queries only
- **Access Control**: Admin-only access to mapping configurations
- **Audit Trail**: All mapping changes logged with user ID

### Performance Limits
- **File Size Limits**: Large CSV files processed in chunks
- **Rate Limiting**: Prevent excessive API calls
- **Memory Management**: Streaming processing for large datasets
- **Timeout Protection**: Processing timeout limits

## Troubleshooting

### Common Issues

#### 1. Mapping Quality Score Low
**Symptoms**: Quality score < 0.7
**Solutions**:
- Review unknown columns in `UnmappedCsvColumns`
- Add custom mappings for vessel-specific columns
- Check for typos or encoding issues in CSV headers

#### 2. Dual Engine Not Detected
**Symptoms**: All engine data goes to primary sensor
**Solutions**:
- Verify column headers contain "PORT"/"STBD" or "ENGINE1"/"ENGINE2"
- Check column normalization is working correctly
- Review detection logic in `DetectVesselEngineConfigurationAsync`

#### 3. New Vessels Not Created
**Symptoms**: Processing fails with vessel not found errors
**Solutions**:
- Ensure at least one Owner exists in database
- Check vessel name extraction from CSV or filename
- Verify database permissions for vessel creation

#### 4. Data Type Conversion Failures
**Symptoms**: High number of conversion errors in logs
**Solutions**:
- Check decimal separator handling (comma vs period)
- Verify date format patterns match CSV data
- Review boolean value mappings

### Debug Commands

```sql
-- Check mapping coverage
SELECT 
    COUNT(*) as TotalColumns,
    COUNT(CASE WHEN cm.Id IS NOT NULL THEN 1 END) as MappedColumns,
    (COUNT(CASE WHEN cm.Id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*)) as CoveragePercent
FROM UnmappedCsvColumns ucc
LEFT JOIN CsvColumnMappings cm ON ucc.VesselName = cm.VesselName AND ucc.ColumnName = cm.ColumnName;

-- Recent processing stats
SELECT 
    VesselName,
    COUNT(*) as ProcessingSessions,
    AVG(QualityScore) as AvgQuality,
    MAX(ProcessedDate) as LastProcessed
FROM EmailProcessingLog 
WHERE ProcessedDate > DATEADD(day, -7, GETDATE())
GROUP BY VesselName;
```

## Support

### Documentation
- **API Documentation**: Available in Swagger UI
- **Database Schema**: See `Database/` folder for table definitions
- **Service Interfaces**: Check `Interfaces/` for method signatures

### Contact
- **Development Team**: SmartBoat Platform Team
- **Issue Reporting**: Create tickets for bugs or feature requests
- **Documentation Updates**: Submit pull requests for improvements

---

*Last Updated: January 2025*
*Version: 1.0*
*Status: Production Ready*