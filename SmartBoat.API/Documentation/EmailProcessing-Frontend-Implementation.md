# Email Sensor Data Processing System
## Frontend Implementation Guide

**Version:** 1.0  
**Date:** July 2025  
**Project:** SmartBoat Platform  
**Authors: <AUTHORS>

---

## Table of Contents

1. [Executive Summary](#executive-summary)
2. [Architecture Overview](#architecture-overview)
3. [Component Structure](#component-structure)
4. [Microsoft Authentication](#microsoft-authentication)
5. [API Integration](#api-integration)
6. [UI/UX Implementation](#uiux-implementation)
7. [State Management](#state-management)
8. [Implementation Guide](#implementation-guide)
9. [Code Examples](#code-examples)
10. [Testing & Validation](#testing--validation)

---

## Executive Summary

### Overview
This document provides comprehensive guidance for implementing the frontend components that integrate with the Email Sensor Data Processing System backend. The frontend enables administrators to authenticate with Microsoft Graph API, monitor token status, and track email processing synchronization.

### Key Features
- **Microsoft Graph Authentication**: OAuth2 PKCE flow with account selection
- **Token Lifecycle Management**: Real-time status monitoring and expiration alerts
- **Sync Status Tracking**: Last sync time and next scheduled sync information
- **Incognito Mode Support**: Fallback storage and user guidance
- **Error Handling**: Comprehensive error messages and recovery guidance
- **Responsive Design**: Mobile-friendly interface following SmartBoat patterns

### Integration Points
- **Profile Page**: New "Email Processing" tab for Administrator users
- **Service Layer**: API client integration with React Query
- **Authentication Flow**: Secure OAuth2 implementation with PKCE
- **Error Boundaries**: Consistent error handling across components

---

## Architecture Overview

### High-Level Frontend Architecture

```mermaid
graph TB
    subgraph "React Application"
        PROFILE[Profile Page]
        NAV[Profile Navigation]
        EMAIL[Email Processing Settings]
    end
    
    subgraph "Authentication Layer"
        AUTH[Microsoft Auth Utility]
        TOKEN[Token Management]
        CALLBACK[Auth Callback Handler]
    end
    
    subgraph "Data Layer"
        HOOKS[React Query Hooks]
        SERVICE[Email Processing Service]
        CLIENT[API Client]
    end
    
    subgraph "Backend API"
        ENDPOINTS[Email Processing Endpoints]
        GRAPH[Microsoft Graph Integration]
    end
    
    PROFILE --> NAV
    NAV --> EMAIL
    EMAIL --> AUTH
    EMAIL --> HOOKS
    AUTH --> TOKEN
    AUTH --> CALLBACK
    HOOKS --> SERVICE
    SERVICE --> CLIENT
    CLIENT --> ENDPOINTS
    ENDPOINTS --> GRAPH
```

### Component Hierarchy

```
UserProfilePage
├── ProfileProvider (Context)
├── ProfileHeader
├── ProfileNavigation
│   └── Email Processing Tab (Admin only)
└── Profile Content
    └── EmailProcessingSettings
        ├── Microsoft Login Section
        │   ├── Connection Status
        │   ├── Authentication Buttons
        │   └── Private Mode Warning
        ├── Token Status Section
        │   ├── Expiration Date
        │   ├── Days Remaining
        │   └── Re-authentication Alert
        └── Sync Information Section
            ├── Last Sync Status
            ├── Next Sync Schedule
            └── Processing History
```

### Technology Stack Integration

- **React 19.1.0**: Component framework
- **TypeScript**: Type safety for components
- **React Query**: Data fetching and state management
- **React Router**: Navigation and callback handling
- **Tailwind CSS**: Styling and responsive design
- **React i18next**: Internationalization support

---

## Component Structure

### 1. ProfileTab Type Extension

**File**: `src/components/features/profile/types.ts`

```typescript
// Extended ProfileTab union type
export type ProfileTab = 'subscriptions' | 'security' | 'notifications' | 'language' | 'emailProcessing';

// Component interfaces remain the same
export interface ProfileNavigationProps {
  activeTab: ProfileTab;
  setActiveTab: (tab: ProfileTab) => void;
  userRole: UserRole;
}
```

### 2. Profile Navigation Enhancement

**File**: `src/components/features/profile/ProfileNavigation.tsx`

```typescript
// Tab configuration with role-based access
const tabs: { id: ProfileTab; label: string; showFor: string[] }[] = [
  { id: 'subscriptions', label: t('common.subscriptions'), showFor: ['Customer', 'Manager', 'Technician', 'Viewer'] },
  { id: 'security', label: t('profile.accountSettings'), showFor: ['Administrator', 'Customer', 'Manager', 'Technician', 'Viewer'] },
  { id: 'notifications', label: t('profile.notifications'), showFor: ['Administrator', 'Customer', 'Manager', 'Technician', 'Viewer'] },
  { id: 'language', label: t('profile.language'), showFor: ['Administrator', 'Customer', 'Manager', 'Technician', 'Viewer'] },
  { id: 'emailProcessing', label: t('emailProcessing.title'), showFor: ['Administrator'] }
];
```

### 3. Main Email Processing Component

**File**: `src/components/features/profile/EmailProcessingSettings.tsx`

```typescript
interface EmailProcessingSettingsProps {
  userData: User;
  isEditing: boolean;
}

const EmailProcessingSettings: React.FC<EmailProcessingSettingsProps> = ({
  userData,
  isEditing
}) => {
  // State management
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const [isPrivateModeDetected, setIsPrivateModeDetected] = useState(false);

  // React Query hooks
  const { data: tokenStatus, isLoading: tokenLoading } = useTokenStatus();
  const { data: scheduleStatus } = useScheduleStatus();
  const { data: processingSummary } = useProcessingSummary();
  const saveTokenMutation = useSaveToken();

  // Authentication handlers
  const handleMicrosoftLogin = async (forceLogin = false) => {
    // Implementation details...
  };

  // Render logic with three main sections...
};
```

---

## Microsoft Authentication

### OAuth2 PKCE Flow Implementation

**File**: `src/utils/microsoftAuth.js`

```javascript
// Microsoft Graph configuration
const MICROSOFT_CONFIG = {
  clientId: '4d59239e-ff4d-4bd9-b825-e61d87a1a6e9',
  tenantId: '72d7af18-45b8-4c9a-ac32-d056c6bda0f5',
  redirectUri: `${window.location.origin}/smtp-redirect`,
  scopes: ['User.Read', 'Mail.Read', 'offline_access']
};

// PKCE code generation
const generatePKCE = () => {
  const codeVerifier = btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(32))))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return crypto.subtle.digest('SHA256', new TextEncoder().encode(codeVerifier))
    .then(hashBuffer => {
      const hashArray = new Uint8Array(hashBuffer);
      const codeChallenge = btoa(String.fromCharCode(...hashArray))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
      
      return { codeVerifier, codeChallenge };
    });
};
```

### Authentication Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant C as React Component
    participant A as Auth Utility
    participant M as Microsoft
    participant B as Backend API
    
    U->>C: Click "Connect to Microsoft"
    C->>A: startMicrosoftAuth()
    A->>A: Generate PKCE parameters
    A->>A: Store in sessionStorage/fallback
    A->>M: Redirect to Microsoft login
    M->>U: Show login page
    U->>M: Enter credentials
    M->>A: Redirect to /smtp-redirect
    A->>A: Extract auth code
    A->>M: Exchange code for tokens
    M-->>A: Return access + refresh tokens
    A->>C: Return token data
    C->>B: Save tokens via API
    B-->>C: Confirm token storage
    C->>C: Update UI status
```

### Incognito Mode Handling

```javascript
// Fallback storage for incognito mode
const handleStorageFailure = (codeVerifier, state) => {
  console.warn('SessionStorage not available (incognito mode?), using fallback storage');
  
  window._msauth_temp_storage = {
    codeVerifier,
    state,
    timestamp: Date.now()
  };
};

// Detection utility
const isIncognitoMode = () => {
  try {
    const test = '__incognito_test__';
    sessionStorage.setItem(test, 'test');
    sessionStorage.removeItem(test);
    return false;
  } catch (e) {
    return true;
  }
};
```

### Account Selection Parameters

```javascript
// Enhanced OAuth parameters for better account handling
const buildAuthUrl = (options = {}) => {
  const { forceLogin = false, selectAccount = true } = options;
  
  const authUrl = new URL(`https://login.microsoftonline.com/${MICROSOFT_CONFIG.tenantId}/oauth2/v2.0/authorize`);
  
  // Standard OAuth2 parameters
  authUrl.searchParams.set('client_id', MICROSOFT_CONFIG.clientId);
  authUrl.searchParams.set('response_type', 'code');
  authUrl.searchParams.set('redirect_uri', MICROSOFT_CONFIG.redirectUri);
  authUrl.searchParams.set('scope', MICROSOFT_CONFIG.scopes.join(' '));
  
  // PKCE parameters
  authUrl.searchParams.set('code_challenge', codeChallenge);
  authUrl.searchParams.set('code_challenge_method', 'S256');
  
  // Enhanced prompts for account selection
  const prompts = [];
  if (selectAccount) prompts.push('select_account');
  if (forceLogin) prompts.push('login');
  prompts.push('consent'); // Ensure Mail.Read permission
  
  authUrl.searchParams.set('prompt', prompts.join(' '));
  authUrl.searchParams.set('domain_hint', 'organizations');
  
  return authUrl.toString();
};
```

---

## API Integration

### Service Layer Implementation

**File**: `src/services/emailProcessingService.js`

```javascript
const BASE_ENDPOINT = '/api/emailprocessing';

const emailProcessingService = {
  // Token management
  saveToken: async (tokenData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/save-token`, tokenData, options);
  },

  getTokenStatus: async (options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/token-status`, {}, options);
  },

  // Processing status
  getScheduleStatus: async (options = {}) => {
    return apiClient.get(`${BASE_ENDPOINT}/schedule-status`, options);
  },

  getProcessingSummary: async (options = {}) => {
    return apiClient.get(`${BASE_ENDPOINT}/summary`, options);
  }
};
```

### React Query Hooks

**File**: `src/hooks/queries/useEmailProcessingQueries.js`

```javascript
// Query key factory
export const emailProcessingKeys = {
  all: ['email-processing'],
  tokenStatus: () => [...emailProcessingKeys.all, 'token-status'],
  scheduleStatus: () => [...emailProcessingKeys.all, 'schedule-status'],
  summary: () => [...emailProcessingKeys.all, 'summary'],
};

// Token status hook with real-time updates
export const useTokenStatus = (options = {}) => {
  return useQuery({
    queryKey: emailProcessingKeys.tokenStatus(),
    queryFn: () => emailProcessingService.getTokenStatus(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 60 * 1000, // 1 minute for token monitoring
    retry: 2,
    ...options,
  });
};

// Token save mutation with cache invalidation
export const useSaveToken = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (tokenData) => emailProcessingService.saveToken(tokenData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.tokenStatus() });
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.summary() });
    },
    onError: (error) => {
      console.error('Failed to save Microsoft Graph token:', error);
    },
  });
};

// Processing summary with longer cache time
export const useProcessingSummary = (options = {}) => {
  return useQuery({
    queryKey: emailProcessingKeys.summary(),
    queryFn: () => emailProcessingService.getProcessingSummary(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    ...options,
  });
};
```

### Error Handling Patterns

```javascript
// API error handling with user-friendly messages
const handleApiError = (error, operation) => {
  console.error(`${operation} failed:`, error);
  
  // Map technical errors to user-friendly messages
  const errorMappings = {
    'browser storage unavailable': 'Authentication failed due to browser storage restrictions. Please try using a regular browser window instead of incognito mode.',
    'session expired': 'Authentication session expired. Please try again.',
    'CSRF attack': 'Security validation failed. Please try again.',
    'TOKEN-401': 'Authentication token expired. Re-authentication required.',
    'TOKEN-500': 'Technical error occurred. Please try again later.'
  };
  
  for (const [key, message] of Object.entries(errorMappings)) {
    if (error.message.includes(key)) {
      return message;
    }
  }
  
  return error.message || 'An unexpected error occurred. Please try again.';
};
```

---

## UI/UX Implementation

### Component Layout Structure

```typescript
// Main component layout
const EmailProcessingSettings: React.FC<EmailProcessingSettingsProps> = () => {
  return (
    <div className="space-y-6">
      {/* Microsoft Login Section */}
      <AuthenticationSection />
      
      {/* Token Status Section - Only show if authenticated */}
      {isConnected && <TokenStatusSection />}
      
      {/* Sync Information Section */}
      <SyncInformationSection />
    </div>
  );
};
```

### Authentication Section UI

```typescript
const AuthenticationSection = () => (
  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
      {t('emailProcessing.microsoftLogin')}
    </h3>
    
    <div className="space-y-4">
      {/* Status and Controls Row */}
      <div className="flex items-center justify-between">
        <StatusIndicators />
        <AuthenticationButtons />
      </div>
      
      {/* Warning Messages */}
      <WarningMessages />
      
      {/* Error Display */}
      <ErrorDisplay />
    </div>
  </div>
);
```

### Status Badge System

```typescript
// Dynamic status badge coloring
const getStatusBadgeColor = (isConnected: boolean, daysUntilExpiry: number | null) => {
  if (!isConnected) 
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    
  if (daysUntilExpiry !== null && daysUntilExpiry <= 5) 
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    
  return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
};

// Status indicator components
const StatusIndicators = () => (
  <div className="flex items-center space-x-3">
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(isConnected, daysUntilExpiry)}`}>
      {isConnected ? t('emailProcessing.connected') : t('emailProcessing.notConnected')}
    </span>
    
    {/* Expiration warning */}
    {isConnected && daysUntilExpiry !== null && daysUntilExpiry <= 5 && (
      <span className="text-sm text-yellow-600 dark:text-yellow-400">
        {t('emailProcessing.reAuthRequired')}
      </span>
    )}
    
    {/* Private mode indicator */}
    {isPrivateModeDetected && (
      <span className="text-sm text-orange-600 dark:text-orange-400">
        Private mode detected
      </span>
    )}
  </div>
);
```

### Authentication Buttons

```typescript
const AuthenticationButtons = () => (
  <div className="flex space-x-2">
    {/* Primary authentication button */}
    <button
      onClick={() => handleMicrosoftLogin(false)}
      disabled={isAuthenticating || saveTokenMutation.isPending}
      className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
    >
      {isAuthenticating ? <LoadingSpinner /> : (
        isConnected ? 'Re-authenticate' : t('emailProcessing.connectToMicrosoft')
      )}
    </button>
    
    {/* Secondary "Use Different Account" button */}
    <button
      onClick={() => handleMicrosoftLogin(true)}
      disabled={isAuthenticating || saveTokenMutation.isPending}
      className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
    >
      Use Different Account
    </button>
  </div>
);
```

### Warning and Error Messages

```typescript
// Private mode warning
const PrivateModeWarning = () => (
  <div className="p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-md">
    <div className="flex">
      <WarningIcon />
      <div className="ml-3">
        <p className="text-sm text-orange-800 dark:text-orange-200">
          <strong>Private/Incognito Mode Detected:</strong> Authentication may be limited due to browser storage restrictions. 
          If you experience issues, please try using a regular browser window.
        </p>
      </div>
    </div>
  </div>
);

// Error message display
const ErrorDisplay = () => (
  authError && (
    <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
      <div className="flex">
        <ErrorIcon />
        <div className="ml-3">
          <p className="text-sm text-red-800 dark:text-red-200">
            <strong>Authentication Error:</strong> {authError}
          </p>
          <button
            onClick={() => setAuthError(null)}
            className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
          >
            Dismiss
          </button>
        </div>
      </div>
    </div>
  )
);
```

### Token Status Display

```typescript
const TokenStatusSection = () => (
  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
      {t('emailProcessing.tokenStatus')}
    </h3>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <TokenExpirationInfo />
      <DaysRemainingCounter />
    </div>
  </div>
);

const TokenExpirationInfo = () => (
  <div>
    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
      {t('emailProcessing.tokenExpires')}
    </dt>
    <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
      {formatDate(tokenStatus?.expiresAt)}
    </dd>
  </div>
);

const DaysRemainingCounter = () => (
  daysUntilExpiry !== null && (
    <div>
      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
        Days Remaining
      </dt>
      <dd className={`mt-1 text-sm font-medium ${
        daysUntilExpiry <= 5 
          ? 'text-red-600 dark:text-red-400' 
          : 'text-green-600 dark:text-green-400'
      }`}>
        {daysUntilExpiry} days
      </dd>
    </div>
  )
);
```

### Sync Information Display

```typescript
const SyncInformationSection = () => (
  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
      Sync Information
    </h3>
    
    {summaryLoading || scheduleLoading ? (
      <LoadingSkeleton />
    ) : (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <LastSyncInfo />
        <NextSyncInfo />
      </div>
    )}
  </div>
);

const LastSyncInfo = () => (
  <div>
    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
      {t('emailProcessing.lastSync')}
    </dt>
    <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
      {formatDate(processingSummary?.recentProcessing?.[0]?.processedAt)}
    </dd>
    {processingSummary?.recentProcessing?.[0] && (
      <dd className="mt-1">
        <StatusBadge 
          status={processingSummary.recentProcessing[0].processingStatus}
          success="Success"
          failure="Failed"
        />
      </dd>
    )}
  </div>
);

const NextSyncInfo = () => (
  <div>
    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
      {t('emailProcessing.nextSync')}
    </dt>
    <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
      {scheduleStatus ? 'Daily at 2:00 AM' : 'Not scheduled'}
    </dd>
    <dd className="mt-1">
      <StatusBadge 
        status={scheduleStatus ? 'Enabled' : 'Disabled'}
        colors={{
          'Enabled': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
          'Disabled': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
        }}
      />
    </dd>
  </div>
);
```

---

## State Management

### Component State Structure

```typescript
// Local component state
interface EmailProcessingState {
  isAuthenticating: boolean;
  authError: string | null;
  isPrivateModeDetected: boolean;
}

// React Query state (cached)
interface QueryState {
  tokenStatus: TokenStatusResponse | undefined;
  scheduleStatus: boolean | undefined;
  processingSummary: ProcessingSummaryResponse | undefined;
}

// Derived state
interface DerivedState {
  isConnected: boolean;
  daysUntilExpiry: number | null;
  lastSyncStatus: 'Success' | 'Failed' | null;
  nextSyncTime: string | null;
}
```

### State Management Patterns

```typescript
// State initialization
const [isAuthenticating, setIsAuthenticating] = useState(false);
const [authError, setAuthError] = useState<string | null>(null);
const [isPrivateModeDetected, setIsPrivateModeDetected] = useState(false);

// Derived state calculations
const isConnected = useMemo(() => 
  tokenStatus?.hasToken && !tokenStatus?.isExpired, 
  [tokenStatus]
);

const daysUntilExpiry = useMemo(() => {
  if (!tokenStatus?.expiresAt) return null;
  const now = new Date();
  const expiry = new Date(tokenStatus.expiresAt);
  const diffTime = expiry.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}, [tokenStatus?.expiresAt]);

// Error state management
const handleAuthError = useCallback((error: Error) => {
  const friendlyMessage = mapErrorToUserMessage(error);
  setAuthError(friendlyMessage);
  setIsAuthenticating(false);
}, []);

// Success state management
const handleAuthSuccess = useCallback(() => {
  setAuthError(null);
  setIsAuthenticating(false);
}, []);
```

### Cache Management

```typescript
// React Query cache invalidation strategy
const invalidateQueries = useCallback(() => {
  queryClient.invalidateQueries({ queryKey: emailProcessingKeys.tokenStatus() });
  queryClient.invalidateQueries({ queryKey: emailProcessingKeys.summary() });
}, [queryClient]);

// Optimistic updates
const saveTokenMutation = useMutation({
  mutationFn: (tokenData) => emailProcessingService.saveToken(tokenData),
  onMutate: async (tokenData) => {
    // Cancel outgoing queries
    await queryClient.cancelQueries({ queryKey: emailProcessingKeys.tokenStatus() });
    
    // Snapshot previous value
    const previousStatus = queryClient.getQueryData(emailProcessingKeys.tokenStatus());
    
    // Optimistically update
    queryClient.setQueryData(emailProcessingKeys.tokenStatus(), {
      hasToken: true,
      isExpired: false,
      expiresAt: tokenData.expiresAt
    });
    
    return { previousStatus };
  },
  onError: (err, tokenData, context) => {
    // Rollback on error
    if (context?.previousStatus) {
      queryClient.setQueryData(emailProcessingKeys.tokenStatus(), context.previousStatus);
    }
  },
  onSettled: () => {
    // Always refetch after mutation
    queryClient.invalidateQueries({ queryKey: emailProcessingKeys.tokenStatus() });
  }
});
```

---

## Implementation Guide

### Step 1: Profile Page Integration

1. **Extend ProfileTab Type**
   ```typescript
   // Add 'emailProcessing' to ProfileTab union type
   export type ProfileTab = 'subscriptions' | 'security' | 'notifications' | 'language' | 'emailProcessing';
   ```

2. **Update Profile Navigation**
   ```typescript
   // Add new tab configuration (Administrator only)
   { id: 'emailProcessing', label: t('emailProcessing.title'), showFor: ['Administrator'] }
   ```

3. **Add Route Handling**
   ```typescript
   // Update UserProfilePage to handle new tab
   {activeTab === 'emailProcessing' && (
     <EmailProcessingSettings userData={userData} isEditing={isEditing} />
   )}
   ```

### Step 2: Service Layer Setup

1. **Create Email Processing Service**
   ```bash
   touch src/services/emailProcessingService.js
   ```

2. **Implement API Methods**
   - `saveToken()` - Store Microsoft Graph tokens
   - `getTokenStatus()` - Retrieve token status and expiration
   - `getScheduleStatus()` - Get sync schedule information
   - `getProcessingSummary()` - Get processing history and statistics

3. **Create React Query Hooks**
   ```bash
   touch src/hooks/queries/useEmailProcessingQueries.js
   ```

### Step 3: Authentication Implementation

1. **Create Microsoft Auth Utility**
   ```bash
   touch src/utils/microsoftAuth.js
   ```

2. **Implement PKCE Flow**
   - Code verifier/challenge generation
   - OAuth2 URL building with proper parameters
   - Token exchange handling
   - Incognito mode fallback storage

3. **Add Auth Callback Handling**
   ```bash
   touch src/components/pages/AuthCallbackPage.tsx
   ```

### Step 4: Component Development

1. **Create Main Settings Component**
   ```bash
   touch src/components/features/profile/EmailProcessingSettings.tsx
   ```

2. **Implement UI Sections**
   - Microsoft Login Section
   - Token Status Section
   - Sync Information Section

3. **Add Error Handling**
   - Private mode detection and warnings
   - Authentication error display
   - Recovery guidance

### Step 5: Internationalization

1. **Add Translation Keys**
   ```json
   // src/i18n/locales/en.json
   "emailProcessing": {
     "title": "Email Processing",
     "microsoftLogin": "Microsoft Login",
     "tokenStatus": "Token Status",
     "lastSync": "Last Sync",
     "nextSync": "Next Sync",
     // ... additional keys
   }
   ```

2. **Translate to All Languages**
   - English (en.json)
   - Greek (el.json)  
   - French (fr.json)

### Step 6: Routing Configuration

1. **Add Auth Callback Route**
   ```typescript
   // App.jsx
   <Route path="/smtp-redirect" element={<AuthCallbackPage />} />
   ```

2. **Configure Vite Development Server**
   ```javascript
   // vite.config.js
   server: {
     port: 3000, // Match Azure AD redirect URI
   }
   ```

### Step 7: Testing and Validation

1. **Unit Testing**
   ```bash
   # Test authentication utility
   npm test src/utils/microsoftAuth.test.js
   
   # Test React Query hooks
   npm test src/hooks/queries/useEmailProcessingQueries.test.js
   ```

2. **Integration Testing**
   ```bash
   # Test complete authentication flow
   npm test src/components/features/profile/EmailProcessingSettings.test.tsx
   ```

3. **Manual Testing Scenarios**
   - Normal browser authentication
   - Incognito mode authentication
   - Account switching functionality
   - Error recovery flows
   - Token expiration handling

---

## Code Examples

### Complete Component Implementation

```typescript
// EmailProcessingSettings.tsx - Complete Implementation
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  useTokenStatus, 
  useSaveToken, 
  useScheduleStatus, 
  useProcessingSummary 
} from '../../../hooks/queries/useEmailProcessingQueries';
import { startMicrosoftAuth, handleAuthCallback, isAuthCallback, isPrivateMode } from '../../../utils/microsoftAuth';

interface EmailProcessingSettingsProps {
  userData: User;
  isEditing: boolean;
}

const EmailProcessingSettings: React.FC<EmailProcessingSettingsProps> = ({
  userData,
  isEditing
}) => {
  const { t } = useTranslation();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const [isPrivateModeDetected, setIsPrivateModeDetected] = useState(false);

  // React Query hooks
  const { data: tokenStatus, isLoading: tokenLoading, error: tokenError } = useTokenStatus();
  const { data: scheduleStatus, isLoading: scheduleLoading } = useScheduleStatus();
  const { data: processingSummary, isLoading: summaryLoading } = useProcessingSummary();
  const saveTokenMutation = useSaveToken();

  // Detect private/incognito mode on component mount
  useEffect(() => {
    setIsPrivateModeDetected(isPrivateMode());
  }, []);

  // Handle authentication callback on component mount
  useEffect(() => {
    const handleCallback = async () => {
      if (isAuthCallback()) {
        setIsAuthenticating(true);
        setAuthError(null);
        try {
          const urlParams = new URLSearchParams(window.location.search);
          const tokenData = await handleAuthCallback(urlParams);
          
          // Save tokens to backend
          await saveTokenMutation.mutateAsync(tokenData);
          
          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname + window.location.hash);
          
          setAuthError(null);
        } catch (error) {
          console.error('Authentication callback failed:', error);
          
          let errorMessage = error.message;
          if (error.message.includes('browser storage unavailable')) {
            errorMessage = 'Authentication failed due to browser storage restrictions. Please try using a regular browser window instead of incognito mode.';
          } else if (error.message.includes('session expired')) {
            errorMessage = 'Authentication session expired. Please try again.';
          } else if (error.message.includes('CSRF attack')) {
            errorMessage = 'Security validation failed. Please try again.';
          }
          
          setAuthError(errorMessage);
        } finally {
          setIsAuthenticating(false);
        }
      }
    };

    handleCallback();
  }, [saveTokenMutation]);

  // Microsoft authentication handler
  const handleMicrosoftLogin = async (forceLogin = false) => {
    setIsAuthenticating(true);
    setAuthError(null);
    try {
      await startMicrosoftAuth({ 
        forceLogin,
        selectAccount: true 
      });
    } catch (error) {
      console.error('Microsoft authentication failed:', error);
      
      let errorMessage = error.message;
      if (error.message.includes('Browser storage not available')) {
        errorMessage = 'Browser storage is not available. Please try using a regular browser window instead of incognito mode, or enable cookies and local storage.';
      }
      
      setAuthError(errorMessage);
      setIsAuthenticating(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return t('emailProcessing.never');
    return new Date(dateString).toLocaleString();
  };

  // Calculate days until expiration
  const getDaysUntilExpiration = (expirationDate: string | null) => {
    if (!expirationDate) return null;
    const now = new Date();
    const expiry = new Date(expirationDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Get status badge color
  const getStatusBadgeColor = (isConnected: boolean, daysUntilExpiry: number | null) => {
    if (!isConnected) return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    if (daysUntilExpiry !== null && daysUntilExpiry <= 5) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
  };

  const isConnected = tokenStatus?.hasToken && !tokenStatus?.isExpired;
  const daysUntilExpiry = tokenStatus?.expiresAt ? getDaysUntilExpiration(tokenStatus.expiresAt) : null;

  if (tokenLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Microsoft Login Section */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          {t('emailProcessing.microsoftLogin')}
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(isConnected, daysUntilExpiry)}`}
              >
                {isConnected ? t('emailProcessing.connected') : t('emailProcessing.notConnected')}
              </span>
              
              {isConnected && daysUntilExpiry !== null && daysUntilExpiry <= 5 && (
                <span className="text-sm text-yellow-600 dark:text-yellow-400">
                  {t('emailProcessing.reAuthRequired')}
                </span>
              )}
              
              {isPrivateModeDetected && (
                <span className="text-sm text-orange-600 dark:text-orange-400">
                  Private mode detected
                </span>
              )}
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => handleMicrosoftLogin(false)}
                disabled={isAuthenticating || saveTokenMutation.isPending}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              >
                {isAuthenticating || saveTokenMutation.isPending ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    <span>Connecting...</span>
                  </div>
                ) : (
                  isConnected ? 'Re-authenticate' : t('emailProcessing.connectToMicrosoft')
                )}
              </button>
              
              <button
                onClick={() => handleMicrosoftLogin(true)}
                disabled={isAuthenticating || saveTokenMutation.isPending}
                className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              >
                Use Different Account
              </button>
            </div>
          </div>

          {/* Private mode warning */}
          {isPrivateModeDetected && (
            <div className="p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-orange-800 dark:text-orange-200">
                    <strong>Private/Incognito Mode Detected:</strong> Authentication may be limited due to browser storage restrictions. 
                    If you experience issues, please try using a regular browser window.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Authentication error display */}
          {authError && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800 dark:text-red-200">
                    <strong>Authentication Error:</strong> {authError}
                  </p>
                  <button
                    onClick={() => setAuthError(null)}
                    className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {tokenError && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <p className="text-sm text-red-800 dark:text-red-200">
              Error loading token status. Please try refreshing the page.
            </p>
          </div>
        )}
      </div>

      {/* Token Status Section */}
      {isConnected && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            {t('emailProcessing.tokenStatus')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('emailProcessing.tokenExpires')}
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {formatDate(tokenStatus?.expiresAt)}
              </dd>
            </div>
            
            {daysUntilExpiry !== null && (
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Days Remaining
                </dt>
                <dd className={`mt-1 text-sm font-medium ${
                  daysUntilExpiry <= 5 
                    ? 'text-red-600 dark:text-red-400' 
                    : 'text-green-600 dark:text-green-400'
                }`}>
                  {daysUntilExpiry} days
                </dd>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Sync Information Section */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Sync Information
        </h3>
        
        {summaryLoading || scheduleLoading ? (
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('emailProcessing.lastSync')}
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {formatDate(processingSummary?.recentProcessing?.[0]?.processedAt)}
              </dd>
              {processingSummary?.recentProcessing?.[0] && (
                <dd className="mt-1">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                    processingSummary.recentProcessing[0].processingStatus === 'Success'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {processingSummary.recentProcessing[0].processingStatus === 'Success' 
                      ? t('emailProcessing.syncSuccessful')
                      : t('emailProcessing.syncFailed')
                    }
                  </span>
                </dd>
              )}
            </div>
            
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('emailProcessing.nextSync')}
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {scheduleStatus ? 'Daily at 2:00 AM' : 'Not scheduled'}
              </dd>
              <dd className="mt-1">
                <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                  scheduleStatus 
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}>
                  {scheduleStatus ? 'Enabled' : 'Disabled'}
                </span>
              </dd>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailProcessingSettings;
```

### Microsoft Authentication Utility

```javascript
// microsoftAuth.js - Complete Implementation
/**
 * Microsoft Graph Authentication Utility
 * Handles OAuth2 PKCE flow for Microsoft Graph API integration
 */

// Microsoft Graph configuration
const MICROSOFT_CONFIG = {
  clientId: '4d59239e-ff4d-4bd9-b825-e61d87a1a6e9',
  tenantId: '72d7af18-45b8-4c9a-ac32-d056c6bda0f5',
  redirectUri: `${window.location.origin}/smtp-redirect`,
  scopes: ['User.Read', 'Mail.Read', 'offline_access']
};

// Generate code verifier and challenge for PKCE
const generatePKCE = () => {
  const codeVerifier = btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(32))))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return crypto.subtle.digest('SHA256', new TextEncoder().encode(codeVerifier))
    .then(hashBuffer => {
      const hashArray = new Uint8Array(hashBuffer);
      const codeChallenge = btoa(String.fromCharCode(...hashArray))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
      
      return { codeVerifier, codeChallenge };
    });
};

// Generate random state parameter
const generateState = () => {
  return btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(16))))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

// Detect if browser is in incognito/private mode
const isIncognitoMode = () => {
  try {
    const test = '__incognito_test__';
    sessionStorage.setItem(test, 'test');
    sessionStorage.removeItem(test);
    return false;
  } catch (e) {
    return true;
  }
};

/**
 * Start Microsoft authentication flow
 * @param {Object} options - Authentication options
 * @param {boolean} options.forceLogin - Force fresh login
 * @param {boolean} options.selectAccount - Force account selection dialog
 */
export const startMicrosoftAuth = async (options = {}) => {
  try {
    const { forceLogin = false, selectAccount = true } = options;
    
    const { codeVerifier, codeChallenge } = await generatePKCE();
    const state = generateState();

    // Try sessionStorage with fallback for incognito mode
    try {
      sessionStorage.setItem('msauth_code_verifier', codeVerifier);
      sessionStorage.setItem('msauth_state', state);
      
      const testVerifier = sessionStorage.getItem('msauth_code_verifier');
      const testState = sessionStorage.getItem('msauth_state');
      
      if (!testVerifier || !testState) {
        throw new Error('SessionStorage test failed');
      }
    } catch (storageError) {
      console.warn('SessionStorage not available (incognito mode?), using fallback storage');
      
      window._msauth_temp_storage = {
        codeVerifier,
        state,
        timestamp: Date.now()
      };
    }

    // Build authorization URL
    const authUrl = new URL(`https://login.microsoftonline.com/${MICROSOFT_CONFIG.tenantId}/oauth2/v2.0/authorize`);
    authUrl.searchParams.set('client_id', MICROSOFT_CONFIG.clientId);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('redirect_uri', MICROSOFT_CONFIG.redirectUri);
    authUrl.searchParams.set('scope', MICROSOFT_CONFIG.scopes.join(' '));
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');
    authUrl.searchParams.set('response_mode', 'query');
    
    // Add prompt parameters for better account handling
    const prompts = [];
    if (selectAccount) prompts.push('select_account');
    if (forceLogin) prompts.push('login');
    prompts.push('consent'); // Ensure Mail.Read permission
    if (prompts.length > 0) {
      authUrl.searchParams.set('prompt', prompts.join(' '));
    }
    
    authUrl.searchParams.set('domain_hint', 'organizations');

    console.log('Starting Microsoft authentication with options:', { forceLogin, selectAccount });
    
    // Redirect to Microsoft login
    window.location.href = authUrl.toString();
  } catch (error) {
    console.error('Failed to start Microsoft authentication:', error);
    
    if (error.message.includes('sessionStorage') || error.message.includes('SessionStorage')) {
      throw new Error('Browser storage not available. Please ensure cookies and local storage are enabled, or try a regular browser window instead of incognito mode.');
    }
    
    throw new Error(`Failed to initialize authentication: ${error.message}`);
  }
};

/**
 * Handle authentication callback
 * @param {URLSearchParams} urlParams - URL parameters from callback
 */
export const handleAuthCallback = async (urlParams) => {
  try {
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');
    const errorDescription = urlParams.get('error_description');

    // Check for OAuth errors
    if (error) {
      throw new Error(`Authentication failed: ${error} - ${errorDescription}`);
    }

    // Verify state parameter (try sessionStorage first, then fallback)
    let storedState = null;
    try {
      storedState = sessionStorage.getItem('msauth_state');
    } catch (e) {
      console.warn('SessionStorage access failed for state verification');
    }
    
    if (!storedState && window._msauth_temp_storage) {
      const tempStorage = window._msauth_temp_storage;
      if (Date.now() - tempStorage.timestamp < 10 * 60 * 1000) {
        storedState = tempStorage.state;
        console.log('Using fallback storage for state verification');
      }
    }
    
    if (!state || state !== storedState) {
      throw new Error('Invalid state parameter - possible CSRF attack or browser storage unavailable');
    }

    // Get stored PKCE verifier
    let codeVerifier = null;
    try {
      codeVerifier = sessionStorage.getItem('msauth_code_verifier');
    } catch (e) {
      console.warn('SessionStorage access failed, trying fallback storage');
    }
    
    if (!codeVerifier && window._msauth_temp_storage) {
      const tempStorage = window._msauth_temp_storage;
      if (Date.now() - tempStorage.timestamp < 10 * 60 * 1000) {
        codeVerifier = tempStorage.codeVerifier;
        console.log('Using fallback storage for code verifier');
      } else {
        console.warn('Fallback storage expired');
      }
    }
    
    if (!codeVerifier) {
      throw new Error('Missing code verifier - authentication session expired or browser storage unavailable. Please try again in a regular browser window.');
    }

    // Exchange code for tokens
    const tokenResponse = await fetch(`https://login.microsoftonline.com/${MICROSOFT_CONFIG.tenantId}/oauth2/v2.0/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: MICROSOFT_CONFIG.clientId,
        code: code,
        redirect_uri: MICROSOFT_CONFIG.redirectUri,
        grant_type: 'authorization_code',
        code_verifier: codeVerifier,
      }),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json();
      throw new Error(`Token exchange failed: ${errorData.error_description || errorData.error}`);
    }

    const tokenData = await tokenResponse.json();

    // Clean up storage
    try {
      sessionStorage.removeItem('msauth_code_verifier');
      sessionStorage.removeItem('msauth_state');
    } catch (e) {
      console.warn('Could not clean sessionStorage (incognito mode?)');
    }
    
    if (window._msauth_temp_storage) {
      delete window._msauth_temp_storage;
    }

    // Format token data for backend
    return {
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token,
      expiresAt: new Date(Date.now() + tokenData.expires_in * 1000).toISOString(),
      scope: tokenData.scope || MICROSOFT_CONFIG.scopes.join(' ')
    };
  } catch (error) {
    // Clean up storage on error
    try {
      sessionStorage.removeItem('msauth_code_verifier');
      sessionStorage.removeItem('msauth_state');
    } catch (e) {
      console.warn('Could not clean sessionStorage on error (incognito mode?)');
    }
    
    if (window._msauth_temp_storage) {
      delete window._msauth_temp_storage;
    }
    
    console.error('Authentication callback failed:', error);
    throw error;
  }
};

/**
 * Check if we're currently in an authentication callback
 */
export const isAuthCallback = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.has('code') || urlParams.has('error');
};

/**
 * Get authentication status from URL
 */
export const getAuthStatus = () => {
  const urlParams = new URLSearchParams(window.location.search);
  
  if (urlParams.has('error')) {
    return {
      success: false,
      error: urlParams.get('error'),
      errorDescription: urlParams.get('error_description')
    };
  }
  
  if (urlParams.has('code')) {
    return {
      success: true,
      code: urlParams.get('code'),
      state: urlParams.get('state')
    };
  }
  
  return null;
};

/**
 * Check if browser is in incognito/private mode
 */
export const isPrivateMode = isIncognitoMode;
```

### React Query Hooks Implementation

```javascript
// useEmailProcessingQueries.js - Complete Implementation
/**
 * React Query hooks for Email Processing functionality
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import emailProcessingService from '../../services/emailProcessingService';

// Query key factory for email processing
export const emailProcessingKeys = {
  all: ['email-processing'],
  tokenStatus: () => [...emailProcessingKeys.all, 'token-status'],
  scheduleStatus: () => [...emailProcessingKeys.all, 'schedule-status'],
  summary: () => [...emailProcessingKeys.all, 'summary'],
};

/**
 * Hook to get Microsoft Graph token status and sync information
 */
export const useTokenStatus = (options = {}) => {
  return useQuery({
    queryKey: emailProcessingKeys.tokenStatus(),
    queryFn: () => emailProcessingService.getTokenStatus(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 60 * 1000, // 1 minute
    retry: 2,
    ...options,
  });
};

/**
 * Hook to save Microsoft Graph authentication tokens
 */
export const useSaveToken = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (tokenData) => emailProcessingService.saveToken(tokenData),
    onSuccess: () => {
      // Invalidate and refetch token status after saving
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.tokenStatus() });
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.summary() });
    },
    onError: (error) => {
      console.error('Failed to save Microsoft Graph token:', error);
    },
  });
};

/**
 * Hook to get schedule status and next sync time
 */
export const useScheduleStatus = (options = {}) => {
  return useQuery({
    queryKey: emailProcessingKeys.scheduleStatus(),
    queryFn: () => emailProcessingService.getScheduleStatus(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    ...options,
  });
};

/**
 * Hook to get processing summary with sync history
 */
export const useProcessingSummary = (options = {}) => {
  return useQuery({
    queryKey: emailProcessingKeys.summary(),
    queryFn: () => emailProcessingService.getProcessingSummary(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    ...options,
  });
};
```

---

## Testing & Validation

### Unit Testing Strategy

#### 1. Authentication Utility Tests

```javascript
// microsoftAuth.test.js
describe('Microsoft Authentication Utility', () => {
  describe('PKCE Generation', () => {
    test('should generate valid code verifier and challenge', async () => {
      const { codeVerifier, codeChallenge } = await generatePKCE();
      
      expect(codeVerifier).toHaveLength(43);
      expect(codeChallenge).toHaveLength(43);
      expect(codeVerifier).not.toEqual(codeChallenge);
    });
  });

  describe('Incognito Mode Detection', () => {
    test('should detect incognito mode when sessionStorage fails', () => {
      // Mock sessionStorage failure
      const originalSessionStorage = window.sessionStorage;
      delete window.sessionStorage;
      
      expect(isPrivateMode()).toBe(true);
      
      window.sessionStorage = originalSessionStorage;
    });
  });

  describe('Authentication URL Building', () => {
    test('should build correct OAuth URL with all parameters', async () => {
      const mockWindow = { location: { origin: 'http://localhost:3000' } };
      global.window = mockWindow;
      
      // Test URL building logic
      const authUrl = buildAuthUrl({ forceLogin: true, selectAccount: true });
      
      expect(authUrl).toContain('login.microsoftonline.com');
      expect(authUrl).toContain('client_id=4d59239e');
      expect(authUrl).toContain('prompt=select_account%20login%20consent');
    });
  });
});
```

#### 2. React Query Hooks Tests

```javascript
// useEmailProcessingQueries.test.js
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useTokenStatus, useSaveToken } from './useEmailProcessingQueries';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false }, mutations: { retry: false } }
  });
  
  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Email Processing Queries', () => {
  test('useTokenStatus should fetch token status', async () => {
    const { result } = renderHook(() => useTokenStatus(), {
      wrapper: createWrapper()
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
  });

  test('useSaveToken should invalidate queries on success', async () => {
    const { result } = renderHook(() => useSaveToken(), {
      wrapper: createWrapper()
    });

    // Test mutation behavior
    await waitFor(() => {
      expect(result.current.mutate).toBeDefined();
    });
  });
});
```

#### 3. Component Integration Tests

```javascript
// EmailProcessingSettings.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import EmailProcessingSettings from './EmailProcessingSettings';

const mockUser = {
  id: '123',
  role: 'Administrator',
  name: 'Test User'
};

const renderComponent = (props = {}) => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <EmailProcessingSettings 
        userData={mockUser} 
        isEditing={false} 
        {...props} 
      />
    </QueryClientProvider>
  );
};

describe('EmailProcessingSettings Component', () => {
  test('should render authentication section', () => {
    renderComponent();
    
    expect(screen.getByText('Microsoft Login')).toBeInTheDocument();
    expect(screen.getByText('Connect to Microsoft')).toBeInTheDocument();
  });

  test('should show private mode warning when detected', () => {
    // Mock private mode detection
    jest.spyOn(require('../../../utils/microsoftAuth'), 'isPrivateMode')
      .mockReturnValue(true);
    
    renderComponent();
    
    expect(screen.getByText(/Private\/Incognito Mode Detected/)).toBeInTheDocument();
  });

  test('should handle authentication button click', async () => {
    const mockStartAuth = jest.fn();
    jest.spyOn(require('../../../utils/microsoftAuth'), 'startMicrosoftAuth')
      .mockImplementation(mockStartAuth);
    
    renderComponent();
    
    fireEvent.click(screen.getByText('Connect to Microsoft'));
    
    await waitFor(() => {
      expect(mockStartAuth).toHaveBeenCalledWith({
        forceLogin: false,
        selectAccount: true
      });
    });
  });

  test('should display token status when connected', () => {
    // Mock connected state
    jest.spyOn(require('../../../hooks/queries/useEmailProcessingQueries'), 'useTokenStatus')
      .mockReturnValue({
        data: {
          hasToken: true,
          isExpired: false,
          expiresAt: '2025-08-31T14:30:00Z'
        },
        isLoading: false
      });
    
    renderComponent();
    
    expect(screen.getByText('Token Status')).toBeInTheDocument();
    expect(screen.getByText('Connected')).toBeInTheDocument();
  });
});
```

### Integration Testing Scenarios

#### 1. Complete Authentication Flow

```javascript
// authenticationFlow.integration.test.js
describe('Complete Authentication Flow', () => {
  test('should handle full OAuth flow from start to finish', async () => {
    // 1. Start authentication
    const { result: authResult } = renderHook(() => useAuthFlow());
    
    act(() => {
      authResult.current.startAuth();
    });
    
    // 2. Simulate redirect callback
    const mockUrlParams = new URLSearchParams('?code=test-code&state=test-state');
    
    // 3. Handle callback
    await act(async () => {
      await authResult.current.handleCallback(mockUrlParams);
    });
    
    // 4. Verify token saved
    expect(authResult.current.isAuthenticated).toBe(true);
  });
});
```

#### 2. Error Recovery Testing

```javascript
// errorRecovery.test.js
describe('Error Recovery Scenarios', () => {
  test('should recover from network errors', async () => {
    // Mock network failure then success
    const mockService = jest.spyOn(emailProcessingService, 'saveToken')
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce({ success: true });
    
    const { result } = renderHook(() => useSaveToken(), {
      wrapper: createWrapper()
    });
    
    // First attempt fails
    await act(async () => {
      try {
        await result.current.mutateAsync(mockTokenData);
      } catch (error) {
        expect(error.message).toBe('Network error');
      }
    });
    
    // Second attempt succeeds
    await act(async () => {
      const response = await result.current.mutateAsync(mockTokenData);
      expect(response.success).toBe(true);
    });
  });
});
```

### Manual Testing Checklist

#### Authentication Flow Testing

- [ ] **Normal Browser Authentication**
  - [ ] Click "Connect to Microsoft" opens Microsoft login
  - [ ] Account selection dialog appears
  - [ ] Successful authentication redirects back to profile
  - [ ] Token status updates to "Connected"
  - [ ] Expiration date displays correctly

- [ ] **Incognito Mode Testing**
  - [ ] Private mode warning appears
  - [ ] Authentication still works with fallback storage
  - [ ] Appropriate error messages for storage issues
  - [ ] Recovery guidance displayed

- [ ] **Account Switching**
  - [ ] "Use Different Account" forces fresh login
  - [ ] Can switch between different Microsoft accounts
  - [ ] Previous account sessions don't interfere

#### Error Scenarios Testing

- [ ] **Network Issues**
  - [ ] Graceful handling of API failures
  - [ ] Retry mechanisms work correctly
  - [ ] User-friendly error messages

- [ ] **Storage Issues**
  - [ ] Fallback storage works in incognito mode
  - [ ] Error messages explain storage problems
  - [ ] Recovery instructions are clear

- [ ] **Token Expiration**
  - [ ] Expiration warnings appear at 5 days
  - [ ] Re-authentication prompts work
  - [ ] Expired tokens handled gracefully

#### UI/UX Testing

- [ ] **Responsive Design**
  - [ ] Works on mobile devices
  - [ ] Tablet layout appropriate
  - [ ] Desktop experience optimal

- [ ] **Dark Mode Support**
  - [ ] All components render correctly in dark mode
  - [ ] Color contrast meets accessibility standards
  - [ ] Theme switching works properly

- [ ] **Internationalization**
  - [ ] All text is properly translated
  - [ ] Language switching works
  - [ ] RTL languages supported if needed

#### Performance Testing

- [ ] **Loading States**
  - [ ] Skeleton loaders display during API calls
  - [ ] Loading spinners work correctly
  - [ ] No layout shifts during loading

- [ ] **Data Fetching**
  - [ ] React Query caching works efficiently
  - [ ] Stale-while-revalidate strategy effective
  - [ ] Background refetching doesn't impact UX

### Deployment Testing

#### Production Readiness

- [ ] **Build Process**
  - [ ] Production build completes without errors
  - [ ] Bundle size is optimized
  - [ ] Source maps generated correctly

- [ ] **Environment Configuration**
  - [ ] Production API endpoints configured
  - [ ] Microsoft Graph production app registration
  - [ ] HTTPS redirect URIs working

- [ ] **Security Testing**
  - [ ] PKCE flow secure in production
  - [ ] No sensitive data in console logs
  - [ ] CSP headers compatible

---

## Conclusion

This comprehensive frontend implementation guide provides all the necessary components, patterns, and best practices to successfully integrate the Email Sensor Data Processing System with the SmartBoat platform frontend.

### Key Implementation Benefits

- **Seamless Integration**: Follows existing SmartBoat patterns and conventions
- **Robust Authentication**: OAuth2 PKCE with incognito mode support
- **User-Friendly Experience**: Clear status indicators and error handling
- **Responsive Design**: Works across all device types and screen sizes
- **Accessibility**: Meets WCAG guidelines for inclusive design
- **Internationalization**: Multi-language support with proper translations
- **Performance Optimized**: React Query for efficient data fetching
- **Error Resilient**: Comprehensive error handling and recovery

### Next Steps

1. **Implement Core Components**: Start with the basic profile tab integration
2. **Add Authentication Flow**: Implement Microsoft Graph OAuth2 integration
3. **Enhance Error Handling**: Add comprehensive error messages and recovery
4. **Test Thoroughly**: Run through all testing scenarios
5. **Deploy and Monitor**: Deploy to production and monitor performance

The implementation provides a solid foundation that can be extended with additional features like manual processing triggers, detailed processing logs, and advanced configuration options as the system evolves.

---

**Document Version:** 1.0  
**Last Updated:** July 31, 2025  
**Next Review:** October 31, 2025  

*This document should be updated whenever frontend components are modified or new features are added to the Email Sensor Data Processing System.*