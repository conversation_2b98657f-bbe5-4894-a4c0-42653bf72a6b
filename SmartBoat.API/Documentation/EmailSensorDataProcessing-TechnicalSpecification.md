# Email Sensor Data Processing System
## Technical Documentation & Implementation Guide

**Version:** 2.0  
**Date:** September 2025  
**Project:** SmartBoat Platform  
**Authors: <AUTHORS>

---

## Implementation Status (September 2025)

### Phase 1: Authentication & Infrastructure ✅ COMPLETED
- **Microsoft Graph OAuth2 PKCE authentication** - ✅ Working perfectly
- **Token storage and refresh mechanism** - ✅ Working with 90-day cycle
- **Database schema** (AuthTokens, CsvSensorData, EmailProcessingLog) - ✅ Created and tested
- **API endpoints** (token-status, exchange-token, summary, schedule-status) - ✅ All functional
- **Frontend UI integration** - ✅ Working with Greek interface
- **Server-side token exchange** - ✅ Implemented to resolve CORS issues
- **Error handling and logging** - ✅ Comprehensive error handling added

### Phase 2: Email Processing ✅ COMPLETED
- **Microsoft Graph email fetching** - ✅ **IMPLEMENTED** - GetEmailsAsync fully functional
- **ZIP download and CSV parsing** - ✅ **IMPLEMENTED** - Complete file processing pipeline
- **Sensor data storage** - ✅ **IMPLEMENTED** - Bulk insert operations working
- **Processing summary and logging** - ✅ **IMPLEMENTED** - Full audit trail and statistics
- **Duplicate detection and handling** - ✅ **IMPLEMENTED** - VesselName + SensorTime uniqueness
- **Email attachment processing** - ✅ **IMPLEMENTED** - ZIP extraction and CSV parsing
- **Vessel discovery** - ✅ **IMPLEMENTED** - Dynamic vessel detection from folder structure
- **Processing status tracking** - ✅ **IMPLEMENTED** - Real-time status and progress reporting

### Phase 3: Scheduled Processing ⏳ PENDING
- **Quartz.NET integration** - ⏳ Framework ready but not implemented
- **Automated daily processing** - ⏳ Manual processing fully working
- **Background job scheduling** - ⏳ Service architecture ready

### Current State (September 2025)
- **Authentication Status**: Microsoft Graph integration fully operational
- **Processing Status**: Manual email sync fully functional - processes emails, downloads ZIPs, parses CSV, stores sensor data
- **Frontend Status**: Complete UI with real-time sync, status monitoring, and error handling
- **Database Status**: All tables operational with proper indexing and constraints
- **Production Ready**: Core email processing system ready for production use
- **Next Steps**: Implement Quartz.NET for automated scheduled processing

---

## Table of Contents

1. [Executive Summary](#executive-summary)
2. [System Architecture](#system-architecture)
3. [Database Schema](#database-schema)
4. [API Endpoints](#api-endpoints)
5. [Security & Authentication](#security--authentication)
6. [Processing Workflow](#processing-workflow)
7. [User Interface](#user-interface)
8. [Configuration](#configuration)
9. [Error Handling](#error-handling)
10. [Monitoring & Maintenance](#monitoring--maintenance)
11. [Appendices](#appendices)

---

## Executive Summary

### Overview
The Email Sensor Data Processing System automatically processes sensor data emails from IoT platforms, downloads ZIP files containing CSV data, and stores the parsed sensor information in the SmartBoat database. The system provides a complete solution for automating maritime sensor data ingestion with comprehensive error handling and monitoring capabilities.

### Business Value
- **Automated Data Ingestion**: ✅ **IMPLEMENTED** - Eliminates manual CSV processing, reducing operational overhead
- **Manual Processing**: ✅ **IMPLEMENTED** - On-demand processing via UI with full control
- **Data Integrity**: ✅ **IMPLEMENTED** - Comprehensive duplicate prevention and validation
- **Administrative Control**: ✅ **IMPLEMENTED** - Full token lifecycle management with 90-day refresh cycle  
- **Monitoring & Status**: ✅ **IMPLEMENTED** - Complete processing status tracking and error notification
- **Scheduled Processing**: ⏳ **PLANNED** - Automated daily processing (Quartz.NET integration pending)

### Key Features - Implementation Status
- ✅ **Microsoft Graph API integration** - Full OAuth2 PKCE authentication and email fetching
- ✅ **Automated ZIP file download and extraction** - Complete file processing pipeline  
- ✅ **Dynamic vessel discovery and CSV parsing** - Smart folder structure detection and data parsing
- ✅ **Token lifecycle management** - Automatic token refresh and 90-day cycle management
- ✅ **Comprehensive frontend interface** - React UI with real-time status, manual sync, and error handling
- ✅ **Processing status and summary reporting** - Full audit trail and statistics tracking
- ✅ **Duplicate detection and prevention** - VesselName + SensorTime uniqueness constraints
- ✅ **Bulk data operations** - Optimized database insertions with transaction management
- ✅ **Error handling and recovery** - Comprehensive error logging and graceful failure handling
- ⏳ **Scheduled processing** - Quartz.NET integration planned for automated daily processing

---

## System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "External Systems"
        IOT[IoT Platform Email]
        GRAPH[Microsoft Graph API]
        AZURE[Azure AD Authentication]
    end
    
    subgraph "SmartBoat Frontend"
        ADMIN[Admin Interface]
        PROFILE[Profile Page]
    end
    
    subgraph "SmartBoat API"
        AUTH[Authentication Layer]
        CONTROLLERS[API Controllers]
        SERVICES[Business Services]
        JOBS[Scheduled Jobs]
    end
    
    subgraph "Data Layer"
        DB[(SQL Server Database)]
        FILES[Temporary File Storage]
    end
    
    IOT -->|Daily emails with ZIP| GRAPH
    AZURE -->|OAuth2 tokens| SERVICES
    ADMIN -->|API calls| CONTROLLERS
    PROFILE -->|Token management| CONTROLLERS
    CONTROLLERS -->|Business logic| SERVICES
    SERVICES -->|Data operations| DB
    SERVICES -->|File processing| FILES
    JOBS -->|Scheduled processing| SERVICES
    GRAPH -->|Email data| SERVICES
```

### Component Architecture

#### Backend Services
- **TokenStorageService**: Manages Microsoft Graph authentication tokens
- **EmailProcessingService**: Orchestrates email processing workflow
- **CsvSensorDataService**: Handles sensor data CRUD operations
- **ScheduledProcessingJob**: Quartz.NET job for automated processing

#### API Controllers
- **EmailProcessingController**: REST endpoints for email processing operations
- **SensorDataController**: REST endpoints for sensor data access

#### Data Models
- **CsvSensorData**: Primary sensor data entity
- **AuthToken**: Microsoft Graph token storage
- **EmailProcessingLog**: Processing history and audit trail

---

## Database Schema

### New Tables

#### CsvSensorData
Primary table for storing processed sensor data from CSV files.

```sql
CREATE TABLE CsvSensorData (
    Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    VesselName nvarchar(255) NOT NULL,
    GroupCoordinates nvarchar(500) NULL,
    Location nvarchar(1000) NULL,
    SensorTime datetime2(7) NOT NULL,
    Speed decimal(18,2) NULL,
    SeaDepth decimal(18,2) NULL,
    RPM decimal(18,2) NULL,
    WindAngle decimal(18,2) NULL,
    WaterTemperature decimal(18,2) NULL,
    WindSpeed decimal(18,2) NULL,
    OilPressure decimal(18,2) NULL,
    Rudder decimal(18,2) NULL,
    EngineLoad decimal(18,2) NULL,
    COG decimal(18,2) NULL,
    FuelRate decimal(18,2) NULL,
    TotalNM decimal(18,2) NULL,
    EngineHours decimal(18,2) NULL,
    PowerSupply decimal(18,2) NULL,
    EngineAlarmCode nvarchar(50) NULL,
    SmartBoatBattery decimal(18,2) NULL,
    EngineWarningCode nvarchar(50) NULL,
    GSMEngineRunningIndicator nvarchar(50) NULL,
    SourceEmail nvarchar(500) NULL,
    ProcessedDate datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    CreatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE()
);
```

**Indexes:**
- `IX_CsvSensorData_VesselName_SensorTime` (UNIQUE) - Duplicate prevention
- `IX_CsvSensorData_VesselName_SensorTime_Desc` - Vessel queries optimization
- `IX_CsvSensorData_SensorTime` - Time-based queries optimization

#### AuthTokens
Manages Microsoft Graph API authentication tokens with 90-day lifecycle.

```sql
CREATE TABLE AuthTokens (
    Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    AccessToken nvarchar(max) NOT NULL,
    RefreshToken nvarchar(max) NULL,
    ExpiresAt datetime2(7) NOT NULL,
    RefreshTokenIssuedAt datetime2(7) NULL,
    TokenType nvarchar(50) NOT NULL DEFAULT 'Bearer',
    Scope nvarchar(500) NULL DEFAULT 'User.Read Mail.Read offline_access',
    CreatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE()
);
```

#### EmailProcessingLog
Tracks processing history, errors, and performance metrics.

```sql
CREATE TABLE EmailProcessingLog (
    Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    EmailSubject nvarchar(500) NULL,
    EmailDate datetime2(7) NOT NULL,
    ProcessingStatus nvarchar(50) NOT NULL, -- 'Success', 'Failed', 'Processing'
    RecordsProcessed int NOT NULL DEFAULT 0,
    VesselsFound int NOT NULL DEFAULT 0,
    ErrorMessage nvarchar(max) NULL,
    ProcessingDuration int NULL, -- in milliseconds
    ProcessedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE()
);
```

### Data Relationships

```mermaid
erDiagram
    CsvSensorData {
        uniqueidentifier Id PK
        nvarchar VesselName
        datetime2 SensorTime
        decimal Speed
        decimal RPM
        nvarchar SourceEmail
        datetime2 ProcessedDate
    }
    
    AuthTokens {
        uniqueidentifier Id PK
        nvarchar AccessToken
        nvarchar RefreshToken
        datetime2 ExpiresAt
        datetime2 RefreshTokenIssuedAt
    }
    
    EmailProcessingLog {
        uniqueidentifier Id PK
        nvarchar EmailSubject
        datetime2 EmailDate
        nvarchar ProcessingStatus
        int RecordsProcessed
        int VesselsFound
    }
    
    CsvSensorData ||--o{ EmailProcessingLog : "processed_from"
```

---

## API Endpoints

### EmailProcessingController

Base URL: `/api/emailprocessing`

#### POST /save-token
Saves Microsoft Graph authentication tokens for email processing.

**Request:**
```json
{
  "header": {
    "userId": "12345678-1234-1234-1234-123456789012"
  },
  "payload": {
    "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIs...",
    "refreshToken": "0.ARoA6KAB-Spbv0GRgdPiJnwMGMHxG9rS...",
    "expiresAt": "2025-07-31T14:30:00Z",
    "scope": "User.Read Mail.Read offline_access"
  }
}
```

**Response:**
```json
{
  "payload": "12345678-1234-1234-1234-123456789012",
  "exception": null
}
```

#### GET /token-status
Retrieves current token status and expiration information.

**Request:**
```json
{
  "header": {
    "userId": "12345678-1234-1234-1234-123456789012"
  },
  "payload": {}
}
```

**Response:**
```json
{
  "payload": {
    "hasToken": true,
    "isExpired": false,
    "hasRefreshToken": true,
    "expiresAt": "2025-07-31T14:30:00Z",
    "refreshTokenIssuedAt": "2025-07-30T12:00:00Z",
    "daysUntilRefreshExpiry": 85,
    "requiresReAuthentication": false
  },
  "exception": null
}
```

#### POST /process-emails
Triggers manual email processing with optional parameters.

**Request:**
```json
{
  "header": {
    "userId": "12345678-1234-1234-1234-123456789012"
  },
  "payload": {
    "forceReprocess": false,
    "daysBack": 7
  }
}
```

**Response:**
```json
{
  "payload": {
    "isProcessing": true,
    "currentStatus": "Processing emails from the last 7 days",
    "lastProcessed": "2025-07-30T10:00:00Z",
    "totalEmails": 5,
    "processedEmails": 2
  },
  "exception": null
}
```

#### GET /processing-status
Gets current processing status and progress.

**Response:**
```json
{
  "payload": {
    "isProcessing": false,
    "currentStatus": "Ready",
    "lastProcessed": "2025-07-31T02:00:00Z",
    "totalEmails": null,
    "processedEmails": null
  },
  "exception": null
}
```

#### GET /summary
Retrieves processing summary with statistics and recent activity.

**Response:**
```json
{
  "payload": {
    "totalRecords": 15420,
    "recentRecords": 342,
    "vesselSummaries": [
      {
        "vesselName": "Sea Explorer",
        "recordCount": 5840,
        "latestRecord": "2025-07-31T01:45:00Z",
        "oldestRecord": "2025-06-01T00:00:00Z"
      }
    ],
    "recentProcessing": [
      {
        "processedAt": "2025-07-31T02:00:00Z",
        "processingStatus": "Success",
        "recordsProcessed": 342,
        "vesselsFound": 3,
        "errorMessage": null
      }
    ]
  },
  "exception": null
}
```

#### POST /schedule
Enables or disables scheduled processing.

**Request:**
```json
{
  "header": {
    "userId": "12345678-1234-1234-1234-123456789012"
  },
  "payload": true
}
```

**Response:**
```json
{
  "payload": true,
  "exception": null
}
```

#### GET /schedule-status
Gets current scheduled processing status.

**Response:**
```json
{
  "payload": true,
  "exception": null
}
```

### SensorDataController

Base URL: `/api/sensordata`

#### POST /vessels
Gets list of vessel names with available sensor data.

**Response:**
```json
{
  "payload": [
    "Sea Explorer",
    "Ocean Pioneer",
    "Marine Voyager"
  ],
  "exception": null
}
```

#### POST /summary
Gets sensor data summary statistics.

**Response:**
```json
{
  "payload": {
    "totalRecords": 15420,
    "recentRecords": 342,
    "vesselSummaries": [
      {
        "vesselName": "Sea Explorer",
        "recordCount": 5840,
        "latestRecord": "2025-07-31T01:45:00Z",
        "oldestRecord": "2025-06-01T00:00:00Z"
      }
    ],
    "recentProcessing": []
  },
  "exception": null
}
```

---

## Security & Authentication

### Microsoft Graph Integration

#### Authentication Flow
1. **Frontend Authentication**: React app initiates PKCE OAuth2 flow
2. **Token Exchange**: Frontend receives access and refresh tokens
3. **Token Storage**: Backend securely stores tokens in encrypted format
4. **Automatic Refresh**: Backend automatically refreshes expired access tokens

#### Configuration
```json
{
  "MicrosoftGraph": {
    "TenantId": "72d7af18-45b8-4c9a-ac32-d056c6bda0f5",
    "ClientId": "4d59239e-ff4d-4bd9-b825-e61d87a1a6e9",
    "RedirectUri": "http://localhost:3000/smtp-redirect"
  }
}
```

#### Required Scopes
- `User.Read` - Basic user profile information
- `Mail.Read` - Access to read emails
- `offline_access` - Refresh token capability

#### Token Lifecycle Management

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant G as Microsoft Graph
    
    U->>F: Initiate login
    F->>G: OAuth2 PKCE flow
    G-->>F: Access + Refresh tokens
    F->>A: Store tokens
    A->>A: Save to AuthTokens table
    
    Note over A: Daily Processing
    A->>A: Check token expiry
    alt Token expired
        A->>G: Refresh token request
        G-->>A: New access token
        A->>A: Update stored tokens
    end
    
    A->>G: Access emails
    G-->>A: Email data
```

#### Security Considerations
- **Token Encryption**: Sensitive tokens stored with encryption at rest
- **90-Day Limit**: Refresh tokens expire after 90 days requiring re-authentication
- **Scope Limitation**: Minimal required permissions requested
- **Audit Trail**: All token operations logged for security monitoring

---

## Processing Workflow

### Email Processing Pipeline

```mermaid
flowchart TD
    A[Manual/Scheduled Trigger] --> B[ProcessEmailsAsync Called]
    B --> C[Get Valid Access Token]
    C --> D{Token Valid?}
    D -->|No| E[Refresh Token via TokenStorageService]
    E --> F{Refresh Success?}
    F -->|No| G[Return Error Response]
    F -->|Yes| H[Fetch Emails via Microsoft Graph]
    D -->|Yes| H
    
    H --> I[GetEmailsAsync - Search ZIP Attachments]
    I --> J{Emails with ZIPs Found?}
    J -->|No| K[Log 'No Emails Found' & Complete]
    J -->|Yes| L[Process Each Email]
    
    L --> M[Check Email Already Processed]
    M --> N{Already Processed & !ForceReprocess?}
    N -->|Yes| O[Skip Email - Log as Skipped]
    N -->|No| P[Download ZIP from URLs]
    
    P --> Q[Extract ZIP via FileManagementService]
    Q --> R[Filter CSV Files Only]
    R --> S[Process Each CSV File]
    S --> T[Parse CSV via CsvProcessingService]
    T --> U[Bulk Insert via CsvSensorDataService]
    U --> V[Track Vessels Found]
    V --> W[Log Email Processing Result]
    
    O --> X[Next Email]
    W --> X
    X --> Y{More Emails?}
    Y -->|Yes| L
    Y -->|No| Z[Cleanup Temp Files]
    Z --> AA[Log Batch Summary]
    AA --> BB[Return Success Response]
    
    G --> CC[End with Error]
    K --> BB
    BB --> DD[Complete Successfully]
```

### File Structure Processing

The system processes ZIP files from IoT platform emails with the following structure:

```
Daily_Data_report_2025-07-30_23-41/
├── [Vessel Name 1]/
│   └── csv/
│       ├── [Vessel Name 1]_Daily_Data_...41_Sensor tracing.csv
│       └── [Vessel Name 1]_Daily_Data_...23-41_Στατιστικά.csv
├── [Vessel Name 2]/
│   └── csv/
│       ├── [Vessel Name 2]_Daily_D...41_Sensor tracing.csv
│       └── [Vessel Name 2]_Daily_D...23-41_Στατιστικά.csv
└── [Vessel Name N]/
    └── csv/
        ├── [Vessel Name N]_Daily_Data_...41_Sensor tracing.csv
        └── [Vessel Name N]_Daily_Data_...23-41_Στατιστικά.csv
```

**Processing Rules:**
- Only process "Sensor tracing.csv" files
- Ignore "Στατιστικά.csv" files (statistics files)
- Dynamically discover vessel names from folder structure
- Handle Greek characters in location data
- Parse datetime format: `29.07.2025 00:01:32`

### CSV Data Mapping

#### CSV Column Structure
Expected CSV columns mapped to database fields:

| CSV Column | Database Field | Data Type | Notes |
|------------|----------------|-----------|-------|
| Group Coordinates | GroupCoordinates | nvarchar(500) | GPS coordinates |
| Location | Location | nvarchar(1000) | May contain Greek text |
| Sensor Time | SensorTime | datetime2(7) | Primary key for duplicates |
| Speed | Speed | decimal(18,2) | Knots |
| Sea Depth | SeaDepth | decimal(18,2) | Meters |
| RPM | RPM | decimal(18,2) | Engine RPM |
| Wind Angle | WindAngle | decimal(18,2) | Degrees |
| Water Temperature | WaterTemperature | decimal(18,2) | Celsius |
| Wind Speed | WindSpeed | decimal(18,2) | Knots |
| Oil Pressure | OilPressure | decimal(18,2) | Bar |
| Rudder | Rudder | decimal(18,2) | Degrees |
| Engine Load | EngineLoad | decimal(18,2) | Percentage |
| COG | COG | decimal(18,2) | Course over ground |
| Fuel Rate | FuelRate | decimal(18,2) | Liters/hour |
| Total NM | TotalNM | decimal(18,2) | Total nautical miles |
| Engine Hours | EngineHours | decimal(18,2) | Operating hours |
| Power Supply | PowerSupply | decimal(18,2) | Volts |
| Engine Alarm Code | EngineAlarmCode | nvarchar(50) | Error codes |
| SmartBoat Battery | SmartBoatBattery | decimal(18,2) | Battery level |
| Engine Warning Code | EngineWarningCode | nvarchar(50) | Warning codes |
| GSM Engine Running Indicator | GSMEngineRunningIndicator | nvarchar(50) | Status indicator |

### Scheduled Processing

#### Current Status: Manual Processing Only
**Status**: ⏳ **NOT YET IMPLEMENTED** - Scheduled processing with Quartz.NET is planned but not implemented

**Current Implementation**:
- `GetScheduleStatusAsync()` - Returns static `false` (placeholder method)
- `ScheduleProcessingAsync()` - Returns input value without actual scheduling
- Manual processing via `/api/emailprocessing/process-emails` endpoint fully functional

#### Planned Quartz.NET Configuration
```csharp
services.AddQuartz(q =>
{
    q.UseMicrosoftDependencyInjection();
    q.AddJob<DailyEmailProcessingJob>(opts => opts.WithIdentity("email-processing-job"));
    q.AddTrigger(opts => opts
        .ForJob("email-processing-job")
        .WithIdentity("email-processing-trigger")
        .WithCronSchedule("0 0 2 * * ?")); // Daily at 2:00 AM
});
```

#### Manual Processing Logic (Currently Implemented)
1. **Authentication Check**: Verify token validity via `TokenStorageService.GetValidAccessTokenAsync()`
2. **Email Fetching**: `MicrosoftGraphService.GetEmailsAsync()` searches for emails with ZIP attachments
3. **File Processing**: Downloads ZIP files, extracts CSV files via `FileManagementService`
4. **Data Parsing**: `CsvProcessingService.ProcessCsvFileAsync()` parses sensor data
5. **Duplicate Prevention**: Checks `EmailProcessingLog` for already processed emails
6. **Database Operations**: `CsvSensorDataService.CreateBulkCsvSensorDataAsync()` for bulk inserts
7. **Cleanup & Logging**: Remove temporary files and log processing results
8. **Error Handling**: Comprehensive error logging and graceful failure handling

#### Future Job Execution Logic (When Implemented)
The scheduled job will call the same `ProcessEmailsAsync()` method that manual processing uses, ensuring consistent behavior between manual and automated processing.

---

## User Interface

### Frontend Integration

#### Profile Page Extension
New "Email Processing" tab added to existing Profile page with:

- **Token Management**: Azure AD authentication status and re-authentication
- **Processing Controls**: Manual trigger and schedule management
- **Status Dashboard**: Real-time processing status and progress
- **Summary Reports**: Processing history and data statistics
- **Error Monitoring**: Failed processing alerts and resolution guidance

#### React Component Structure
```
Profile Page
├── EmailProcessingTab
│   ├── TokenStatusCard
│   ├── ProcessingControlsCard
│   ├── StatusDashboard
│   ├── SummaryReports
│   └── ErrorAlerts
```

#### Service Integration
```typescript
// React Query hooks for data fetching
const { data: tokenStatus } = useTokenStatus();
const { data: processingSummary } = useProcessingSummary();
const { mutate: processEmails } = useProcessEmails();
const { mutate: saveToken } = useSaveToken();
```

#### UI/UX Features
- **Token Expiration Alerts**: Visual indicators for token expiry (90-day limit)
- **Processing Progress**: Real-time updates during email processing
- **Data Visualization**: Charts showing processing trends and vessel data
- **Error Recovery**: Clear instructions for resolving authentication issues
- **Responsive Design**: Mobile-friendly interface following existing patterns

---

## Configuration

### Backend Configuration

#### appsettings.Development.json
```json
{
  "MicrosoftGraph": {
    "TenantId": "72d7af18-45b8-4c9a-ac32-d056c6bda0f5",
    "ClientId": "4d59239e-ff4d-4bd9-b825-e61d87a1a6e9",
    "RedirectUri": "http://localhost:3000/smtp-redirect"
  },
  "EmailProcessing": {
    "ScheduleEnabled": true,
    "DefaultDaysBack": 7,
    "MaxFileSize": 52428800,
    "TempFileCleanupHours": 24,
    "ProcessingTimeoutMinutes": 30,
    "BulkInsertBatchSize": 1000
  }
}
```

#### Service Registration
```csharp
// Program.cs
builder.Services.AddScoped<ITokenStorageService, TokenStorageService>();
builder.Services.AddScoped<ICsvSensorDataService, CsvSensorDataService>();
builder.Services.AddScoped<IEmailProcessingService, EmailProcessingService>();

// Quartz.NET for scheduled processing
builder.Services.AddQuartz(q =>
{
    q.UseMicrosoftDependencyInjection();
    q.AddJob<DailyEmailProcessingJob>(opts => opts.WithIdentity("email-processing-job"));
    q.AddTrigger(opts => opts
        .ForJob("email-processing-job")
        .WithIdentity("email-processing-trigger")
        .WithCronSchedule("0 0 2 * * ?"));
});
```

### Database Setup

#### Migration Scripts
Database tables are automatically created by the existing setup script:
```bash
./Database/setup-database.sh
```

The following files are processed:
- `CsvSensorData.sql` - Primary sensor data table
- `AuthTokens.sql` - Token storage table
- `EmailProcessingLog.sql` - Processing audit table

### Environment Requirements

#### Production Deployment
- **.NET 8.0.410** or higher
- **SQL Server** 2019 or Azure SQL Database
- **Azure AD** application registration
- **Microsoft Graph** API permissions
- **Quartz.NET** for background processing
- **File system** access for temporary ZIP processing

#### Azure AD Application Setup
1. **Register Application** in Azure AD tenant
2. **Configure Redirect URIs** for frontend callback
3. **Grant Permissions**: `User.Read`, `Mail.Read`, `offline_access`
4. **Enable Public Client** flows for PKCE
5. **Configure Token Lifetime** policies if needed

---

## Error Handling

### Comprehensive Error Scenarios

#### Authentication Errors

**TOKEN-404: No Token Found**
- **Cause**: No stored authentication token
- **Resolution**: User must authenticate via frontend
- **Admin Action**: Guide user through authentication process

**TOKEN-401: Refresh Token Expired**
- **Cause**: 90-day refresh token limit reached
- **Resolution**: Complete re-authentication required
- **Admin Action**: Schedule quarterly re-authentication reminders

**TOKEN-500: Token Refresh Failed**
- **Cause**: Microsoft Graph API error or network issues
- **Resolution**: Automatic retry with exponential backoff
- **Admin Action**: Monitor Graph API status and network connectivity

#### Processing Errors

**EMAIL-404: No Emails Found**
- **Cause**: No emails from IoT platform in specified time range
- **Resolution**: Extend search timeframe or check email filters
- **Admin Action**: Verify IoT platform email configuration

**FILE-500: ZIP Download Failed**
- **Cause**: Network issues or invalid ZIP file links
- **Resolution**: Automatic retry with different endpoints
- **Admin Action**: Check IoT platform file hosting status

**CSV-422: Invalid CSV Format**
- **Cause**: Unexpected CSV structure or encoding issues
- **Resolution**: Log error details and skip problematic files
- **Admin Action**: Review CSV format changes with IoT platform

**DB-409: Duplicate Data Conflict**
- **Cause**: Attempting to insert duplicate sensor time records
- **Resolution**: Skip duplicates and continue processing
- **Admin Action**: Review duplicate detection logic if needed

#### Service Errors

**SCHED-500: Scheduled Job Failed**
- **Cause**: Quartz.NET job execution failure
- **Resolution**: Automatic retry with exponential backoff
- **Admin Action**: Review job configuration and dependencies

**NET-408: Network Timeout**
- **Cause**: Microsoft Graph API or file download timeouts
- **Resolution**: Increase timeout values and retry
- **Admin Action**: Monitor network latency and API response times

### Error Response Format

All API endpoints return standardized error responses:

```json
{
  "payload": null,
  "exception": {
    "id": "12345678-1234-1234-1234-123456789012",
    "code": "TOKEN-401",
    "description": "Refresh token expired. Re-authentication required.",
    "category": "Unauthorized"
  }
}
```

### Logging Strategy

#### Log Levels
- **Information**: Successful operations, processing statistics
- **Warning**: Recoverable errors, token expiration alerts
- **Error**: Failed operations requiring admin attention
- **Critical**: System-level failures affecting service availability

#### Log Categories
```csharp
// Token management
_logger.LogInformation($"TOKEN-200: Token refreshed successfully. UserId: {userId}");
_logger.LogWarning($"TOKEN-401: Refresh token expires in {daysLeft} days. UserId: {userId}");

// Email processing
_logger.LogInformation($"EMAIL-200: Processed {emailCount} emails, {recordCount} records. Duration: {duration}ms");
_logger.LogError($"EMAIL-500: Failed to process email {emailId}. Error: {error}");

// Data operations
_logger.LogInformation($"CSV-200: Inserted {recordCount} sensor records for vessel {vesselName}");
_logger.LogWarning($"CSV-409: Skipped {duplicateCount} duplicate records");
```

---

## Monitoring & Maintenance

### Operational Monitoring

#### Key Performance Indicators (KPIs)
- **Processing Success Rate**: Percentage of successful email processing runs
- **Token Health**: Days until refresh token expiration
- **Data Volume**: Records processed per day/week/month
- **Processing Duration**: Average time for email processing cycles
- **Error Rate**: Failed processing attempts per time period

#### Monitoring Dashboard
```sql
-- Processing success rate (last 30 days)
SELECT 
    COUNT(*) as TotalRuns,
    SUM(CASE WHEN ProcessingStatus = 'Success' THEN 1 ELSE 0 END) as SuccessfulRuns,
    (SUM(CASE WHEN ProcessingStatus = 'Success' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as SuccessRate
FROM EmailProcessingLog 
WHERE ProcessedAt >= DATEADD(DAY, -30, GETUTCDATE());

-- Data volume trends
SELECT 
    CAST(ProcessedDate AS DATE) as ProcessingDate,
    COUNT(*) as RecordsProcessed,
    COUNT(DISTINCT VesselName) as VesselsProcessed
FROM CsvSensorData 
WHERE ProcessedDate >= DATEADD(DAY, -30, GETUTCDATE())
GROUP BY CAST(ProcessedDate AS DATE)
ORDER BY ProcessingDate DESC;

-- Token expiration monitoring
SELECT 
    DATEDIFF(DAY, GETUTCDATE(), ExpiresAt) as DaysUntilAccessExpiry,
    DATEDIFF(DAY, RefreshTokenIssuedAt, GETUTCDATE()) as RefreshTokenAge,
    CASE 
        WHEN DATEDIFF(DAY, RefreshTokenIssuedAt, GETUTCDATE()) >= 85 THEN 'Critical'
        WHEN DATEDIFF(DAY, RefreshTokenIssuedAt, GETUTCDATE()) >= 75 THEN 'Warning'
        ELSE 'OK'
    END as TokenStatus
FROM AuthTokens;
```

### Maintenance Procedures

#### Daily Operations
1. **Monitor Processing Status**: Check EmailProcessingLog for failures
2. **Token Health Check**: Verify token expiration status
3. **Data Volume Validation**: Ensure expected data volume processed
4. **Error Log Review**: Address any processing errors or warnings

#### Weekly Operations
1. **Performance Analysis**: Review processing duration trends
2. **Storage Management**: Archive old processing logs if needed
3. **Security Audit**: Review authentication logs and token usage
4. **Capacity Planning**: Monitor database growth and storage usage

#### Monthly Operations
1. **Token Rotation Planning**: Schedule re-authentication for 90-day cycle
2. **Data Retention**: Archive old sensor data based on retention policies
3. **Performance Optimization**: Review query performance and index usage
4. **Disaster Recovery Testing**: Verify backup and recovery procedures

#### Quarterly Operations
1. **Re-authentication**: Complete Microsoft Graph token renewal (90-day limit)
2. **Security Review**: Audit permissions and access controls
3. **Performance Tuning**: Optimize database queries and indexes
4. **Documentation Updates**: Update operational procedures and configurations

### Backup and Recovery

#### Database Backup Strategy
```sql
-- Full backup (daily)
BACKUP DATABASE Smartboat 
TO DISK = 'C:\Backups\Smartboat_Full_YYYY-MM-DD.bak'
WITH FORMAT, INIT, NAME = 'Smartboat Full Backup';

-- Differential backup (every 6 hours)
BACKUP DATABASE Smartboat 
TO DISK = 'C:\Backups\Smartboat_Diff_YYYY-MM-DD_HH.bak'
WITH DIFFERENTIAL, FORMAT, INIT, NAME = 'Smartboat Differential Backup';

-- Transaction log backup (every 15 minutes)
BACKUP LOG Smartboat 
TO DISK = 'C:\Backups\Smartboat_Log_YYYY-MM-DD_HHMM.trn'
WITH FORMAT, INIT, NAME = 'Smartboat Log Backup';
```

#### Recovery Procedures
1. **Point-in-time Recovery**: Restore from full + differential + log backups
2. **Token Recovery**: Re-authenticate with Microsoft Graph if tokens are lost
3. **Data Validation**: Verify data integrity after recovery
4. **Service Restart**: Restart scheduled processing after recovery

### Alerts and Notifications

#### Critical Alerts
- **Token Expiration**: 5 days before refresh token expires
- **Processing Failures**: 3 consecutive failed processing attempts
- **Database Issues**: Connection failures or constraint violations
- **Service Outages**: Quartz.NET job failures or API unavailability

#### Alert Configuration
```csharp
// Token expiration alert
if (daysUntilRefreshExpiry <= 5)
{
    await _notificationService.SendAdminAlert(
        "Email Processing Token Expiration Warning",
        $"Microsoft Graph token expires in {daysUntilRefreshExpiry} days. Re-authentication required."
    );
}

// Processing failure alert
if (consecutiveFailures >= 3)
{
    await _notificationService.SendAdminAlert(
        "Email Processing Failures",
        $"{consecutiveFailures} consecutive processing failures. Manual intervention required."
    );
}
```

---

## Appendices

### Appendix A: Code Examples

#### TokenStorageService Implementation
```csharp
public async Task<Response<string>> SaveTokenAsync(SaveTokenDto tokenDto, Guid userId)
{
    try
    {
        if (tokenDto == null || string.IsNullOrWhiteSpace(tokenDto.AccessToken))
        {
            return new Response<string>
            {
                Payload = null,
                Exception = new ResponseException
                {
                    Id = Guid.NewGuid(),
                    Code = "TOKEN-422",
                    Description = "Access token is required",
                    Category = "Client Error"
                }
            };
        }

        // Delete existing token (only one should exist)
        var existingToken = (await _databaseService.SelectAsync<AuthToken>(new { })).FirstOrDefault();
        if (existingToken != null)
        {
            await _databaseService.DeleteAsync<AuthToken>(new { Id = existingToken.Id });
        }

        var authToken = new AuthToken
        {
            Id = Guid.NewGuid(),
            AccessToken = tokenDto.AccessToken,
            RefreshToken = tokenDto.RefreshToken,
            ExpiresAt = tokenDto.ExpiresAt,
            RefreshTokenIssuedAt = DateTime.UtcNow,
            TokenType = "Bearer",
            Scope = tokenDto.Scope ?? "User.Read Mail.Read offline_access",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _databaseService.InsertAsync<AuthToken>(authToken);

        return new Response<string>
        {
            Payload = authToken.Id.ToString(),
            Exception = null
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"TOKEN-500: Technical Error. Error while saving token. UserId: {userId}");
        return new Response<string>
        {
            Payload = null,
            Exception = new ResponseException
            {
                Id = Guid.NewGuid(),
                Code = "TOKEN-500",
                Description = "Technical Error",
                Category = "Technical Error"
            }
        };
    }
}
```

#### Frontend React Hook
```typescript
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { emailProcessingService } from '../../services';

export const useTokenStatus = () => {
  return useQuery({
    queryKey: ['email-processing', 'token-status'],
    queryFn: () => emailProcessingService.getTokenStatus(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 60 * 1000, // 1 minute
  });
};

export const useSaveToken = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (tokenData) => emailProcessingService.saveToken(tokenData),
    onSuccess: () => {
      queryClient.invalidateQueries(['email-processing', 'token-status']);
    },
  });
};

export const useProcessEmails = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (options) => emailProcessingService.processEmails(options),
    onSuccess: () => {
      queryClient.invalidateQueries(['email-processing']);
    },
  });
};
```

### Appendix B: Troubleshooting Guide

#### Common Issues and Solutions

**Issue: "No emails found for processing"**
- **Check**: Email sender address matches `<EMAIL>`
- **Check**: Email subject contains `Data_History_Every_day`
- **Check**: Time range includes recent emails (default 7 days)
- **Solution**: Extend `daysBack` parameter or verify IoT platform configuration

**Issue: "Token refresh failed"**
- **Check**: Internet connectivity to Microsoft Graph API
- **Check**: Azure AD application permissions and configuration
- **Check**: Refresh token hasn't exceeded 90-day limit
- **Solution**: Complete re-authentication or check Azure AD status

**Issue: "CSV parsing errors"**
- **Check**: ZIP file structure matches expected format
- **Check**: CSV files contain expected columns and data types
- **Check**: Character encoding for Greek text (UTF-8)
- **Solution**: Update CSV mapping or contact IoT platform for format changes

**Issue: "Duplicate key violations"**
- **Check**: `VesselName` and `SensorTime` combination uniqueness
- **Check**: Multiple processing runs for same time period
- **Solution**: Enable `ForceReprocess` flag or check duplicate detection logic

**Issue: "Scheduled processing not running"**
- **Status**: ⏳ **SCHEDULED PROCESSING NOT YET IMPLEMENTED**
- **Current State**: Only manual processing via API endpoints is available
- **Solution**: Implement Quartz.NET integration as planned in Phase 3
- **Workaround**: Use manual processing via `/api/emailprocessing/process-emails` endpoint

### Appendix C: Future Enhancements

#### Planned Features
1. **Real-time Processing**: WebSocket integration for live processing updates
2. **Data Visualization**: Interactive charts for sensor data analysis
3. **Advanced Filtering**: Custom date ranges and vessel-specific processing
4. **Export Functionality**: CSV/Excel export of processed sensor data
5. **API Rate Limiting**: Implement throttling for Microsoft Graph requests
6. **Multi-tenant Support**: Separate token management per customer/organization

#### Scalability Considerations
1. **Horizontal Scaling**: Support for multiple processing instances
2. **Load Balancing**: Distribute processing across multiple servers
3. **Caching Strategy**: Redis cache for frequently accessed data
4. **Database Partitioning**: Partition sensor data by date or vessel
5. **Message Queues**: Async processing with Azure Service Bus or RabbitMQ

#### Security Enhancements
1. **Token Encryption**: Enhanced encryption for stored tokens
2. **Audit Logging**: Comprehensive security audit trail
3. **Role-based Access**: Granular permissions for different user roles
4. **Certificate Pinning**: Enhanced security for API communications
5. **Compliance**: GDPR and maritime industry compliance features

---

**Document Version:** 2.0  
**Last Updated:** September 7, 2025  
**Next Review:** December 7, 2025  

## Version History
- **v1.0** (July 2025): Initial documentation with Phase 1 completed, Phase 2 planned
- **v2.0** (September 2025): Updated to reflect Phase 2 completion - full email processing implementation

*This document is maintained by the SmartBoat development team and should be updated with any system changes or enhancements.*