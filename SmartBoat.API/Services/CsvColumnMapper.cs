using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using Microsoft.Extensions.Logging;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public interface ICsvColumnMapper
    {
        Task<CsvMappingResult> MapCsvRecordAsync(IDictionary<string, object> recordDict, string vesselName);
        Task<string> DetectVesselEngineConfigurationAsync(string[] headers);
        Task LogUnmappedColumnAsync(string vesselName, string columnName, string? sampleValue);
    }

    public class CsvColumnMapper : ICsvColumnMapper
    {
        private readonly ILogger<CsvColumnMapper> _logger;
        private readonly IDatabaseService _databaseService;

        public CsvColumnMapper(ILogger<CsvColumnMapper> logger, IDatabaseService databaseService)
        {
            _logger = logger;
            _databaseService = databaseService;
        }

        public async Task<CsvMappingResult> MapCsvRecordAsync(IDictionary<string, object> recordDict, string vesselName)
        {
            var result = new CsvMappingResult
            {
                VesselEngineConfiguration = await DetectVesselEngineConfigurationAsync(recordDict.Keys.ToArray())
            };

            var mappingErrors = new List<string>();
            var unmappedColumns = new List<string>();
            var mappedColumnCount = 0;
            var totalColumnCount = 0;

            // Get vessel-specific mappings if they exist
            var customMappings = await GetVesselMappingsAsync(vesselName);

            foreach (var kvp in recordDict)
            {
                var columnName = kvp.Key?.ToString()?.Trim() ?? "";
                var value = kvp.Value?.ToString()?.Trim();

                totalColumnCount++;

                if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(columnName))
                    continue;

                var normalizedColumnName = NormalizeColumnName(columnName);
                
                // Try custom mapping first, then default mapping
                var mappingInfo = TryGetMapping(normalizedColumnName, customMappings) ?? 
                                 TryGetDefaultMapping(normalizedColumnName);

                if (mappingInfo.HasValue)
                {
                    var (measurementKey, dataType, category) = mappingInfo.Value;
                    
                    try
                    {
                        var convertedValue = ConvertValue(value, dataType);
                        if (convertedValue != null)
                        {
                            result.Measurements[measurementKey] = convertedValue;
                            mappedColumnCount++;
                        }
                        else
                        {
                            mappingErrors.Add($"Failed to convert '{columnName}' value '{value}' to {dataType}");
                        }
                    }
                    catch (Exception ex)
                    {
                        mappingErrors.Add($"Error converting '{columnName}': {ex.Message}");
                        _logger.LogWarning(ex, "Error converting column {ColumnName} with value {Value} to {DataType}", 
                            columnName, value, dataType);
                    }
                }
                else
                {
                    unmappedColumns.Add(columnName);
                    await LogUnmappedColumnAsync(vesselName, columnName, value);
                }
            }

            result.UnmappedColumns = unmappedColumns;
            result.MappingErrors = mappingErrors;
            result.QualityScore = totalColumnCount > 0 ? (float)mappedColumnCount / totalColumnCount : 0f;

            _logger.LogInformation("Mapped {MappedCount}/{TotalCount} columns for vessel {VesselName}. Quality score: {QualityScore:F2}", 
                mappedColumnCount, totalColumnCount, vesselName, result.QualityScore);

            return result;
        }

        public async Task<string> DetectVesselEngineConfigurationAsync(string[] headers)
        {
            var normalizedHeaders = headers.Select(NormalizeColumnName).ToArray();
            
            var hasPortEngine = normalizedHeaders.Any(h => h.Contains("port") && (h.Contains("rpm") || h.Contains("engine")));
            var hasStbdEngine = normalizedHeaders.Any(h => (h.Contains("stbd") || h.Contains("starboard")) && (h.Contains("rpm") || h.Contains("engine")));
            var hasEngine1 = normalizedHeaders.Any(h => h.Contains("engine1") || h.Contains("engine_1"));
            var hasEngine2 = normalizedHeaders.Any(h => h.Contains("engine2") || h.Contains("engine_2"));

            if ((hasPortEngine && hasStbdEngine) || (hasEngine1 && hasEngine2))
            {
                return "dual";
            }
            
            var hasGeneralEngine = normalizedHeaders.Any(h => h.Contains("rpm") || h.Contains("engine"));
            return hasGeneralEngine ? "single" : "unknown";
        }

        public async Task LogUnmappedColumnAsync(string vesselName, string columnName, string? sampleValue)
        {
            try
            {
                // Check if this unmapped column already exists
                var existing = await _databaseService.SelectAsync<UnmappedCsvColumns>(new { VesselName = vesselName, ColumnName = columnName });
                var existingRecord = existing?.FirstOrDefault();

                if (existingRecord != null)
                {
                    // Update existing record
                    existingRecord.LastSeen = DateTime.UtcNow;
                    existingRecord.OccurrenceCount++;
                    if (!string.IsNullOrEmpty(sampleValue))
                    {
                        existingRecord.SampleValue = sampleValue;
                    }
                    await _databaseService.UpdateAsync<UnmappedCsvColumns>(existingRecord, new { Id = existingRecord.Id });
                }
                else
                {
                    // Create new record
                    var newRecord = new UnmappedCsvColumns
                    {
                        Id = Guid.NewGuid(),
                        VesselName = vesselName,
                        ColumnName = columnName,
                        SampleValue = sampleValue,
                        FirstSeen = DateTime.UtcNow,
                        LastSeen = DateTime.UtcNow,
                        OccurrenceCount = 1,
                        Status = "pending"
                    };
                    await _databaseService.InsertAsync<UnmappedCsvColumns>(newRecord);
                    
                    _logger.LogInformation("New unmapped column discovered: '{ColumnName}' for vessel '{VesselName}' with sample value: '{SampleValue}'", 
                        columnName, vesselName, sampleValue);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log unmapped column {ColumnName} for vessel {VesselName}", columnName, vesselName);
            }
        }

        private async Task<Dictionary<string, (string measurementKey, string dataType, string category)>> GetVesselMappingsAsync(string vesselName)
        {
            try
            {
                var mappings = await _databaseService.SelectAsync<CsvColumnMapping>(new { VesselName = vesselName, IsActive = true });
                if (mappings == null) return new Dictionary<string, (string, string, string)>();

                return mappings.ToDictionary(
                    m => NormalizeColumnName(m.ColumnName),
                    m => (m.MeasurementKey, m.DataType, m.Category),
                    StringComparer.OrdinalIgnoreCase
                );
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load custom mappings for vessel {VesselName}", vesselName);
                return new Dictionary<string, (string, string, string)>();
            }
        }

        private (string measurementKey, string dataType, string category)? TryGetMapping(
            string normalizedColumnName, 
            Dictionary<string, (string measurementKey, string dataType, string category)> customMappings)
        {
            if (customMappings.TryGetValue(normalizedColumnName, out var mapping))
            {
                return mapping;
            }
            return null;
        }

        private (string measurementKey, string dataType, string category)? TryGetDefaultMapping(string normalizedColumnName)
        {
            if (DefaultColumnMappings.Mappings.TryGetValue(normalizedColumnName, out var mapping))
            {
                return mapping;
            }
            return null;
        }

        private string NormalizeColumnName(string columnName)
        {
            if (string.IsNullOrEmpty(columnName)) return "";

            return columnName.Trim()
                           .ToLowerInvariant()
                           .Replace(" ", "")
                           .Replace("_", "")
                           .Replace("-", "")
                           .Replace("(", "")
                           .Replace(")", "");
        }

        private object? ConvertValue(string value, string dataType)
        {
            if (string.IsNullOrEmpty(value)) return null;

            try
            {
                return dataType.ToLowerInvariant() switch
                {
                    "decimal" or "float" => ConvertToDecimal(value),
                    "datetime" => ConvertToDateTime(value),
                    "boolean" or "bool" => ConvertToBoolean(value),
                    "string" or _ => value
                };
            }
            catch
            {
                return null;
            }
        }

        private decimal? ConvertToDecimal(string value)
        {
            if (string.IsNullOrEmpty(value)) return null;

            // Handle different decimal separators (both comma and period)
            var normalizedValue = value.Replace(",", ".");
            
            if (decimal.TryParse(normalizedValue, NumberStyles.Float, CultureInfo.InvariantCulture, out var result))
            {
                return result;
            }

            return null;
        }

        private DateTime? ConvertToDateTime(string value)
        {
            if (string.IsNullOrEmpty(value)) return null;

            // Try multiple datetime formats
            var formats = new[]
            {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-ddTHH:mm:ss",
                "yyyy-MM-ddTHH:mm:ssZ",
                "yyyy-MM-dd HH:mm:ss.fff",
                "dd/MM/yyyy HH:mm:ss",
                "MM/dd/yyyy HH:mm:ss",
                "yyyy-MM-dd",
                "dd/MM/yyyy",
                "MM/dd/yyyy"
            };

            foreach (var format in formats)
            {
                if (DateTime.TryParseExact(value, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var result))
                {
                    return result;
                }
            }

            // Fallback to general parsing
            if (DateTime.TryParse(value, out var fallbackResult))
            {
                return fallbackResult;
            }

            return null;
        }

        private bool? ConvertToBoolean(string value)
        {
            if (string.IsNullOrEmpty(value)) return null;

            var normalizedValue = value.ToLowerInvariant().Trim();

            return normalizedValue switch
            {
                "true" or "1" or "yes" or "on" or "running" or "active" => true,
                "false" or "0" or "no" or "off" or "not running" or "inactive" => false,
                _ => null
            };
        }
    }

}