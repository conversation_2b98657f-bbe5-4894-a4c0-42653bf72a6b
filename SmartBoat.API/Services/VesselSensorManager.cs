using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using Microsoft.Extensions.Logging;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public interface IVesselSensorManager
    {
        Task<VesselSensorConfiguration> GetOrCreateVesselSensorConfigurationAsync(string vesselName, string engineConfiguration = "single");
        Task<Guid> GetSensorIdForMeasurementAsync(string vesselName, string category, string engineConfiguration);
        Task<List<Guid>> GetAllSensorIdsForVesselAsync(string vesselName);
    }

    public class VesselSensorManager : IVesselSensorManager
    {
        private readonly ILogger<VesselSensorManager> _logger;
        private readonly IDatabaseService _databaseService;

        public VesselSensorManager(ILogger<VesselSensorManager> logger, IDatabaseService databaseService)
        {
            _logger = logger;
            _databaseService = databaseService;
        }

        public async Task<VesselSensorConfiguration> GetOrCreateVesselSensorConfigurationAsync(string vesselName, string engineConfiguration = "single")
        {
            try
            {
                // First, try to find existing configuration
                var existingConfigs = await _databaseService.SelectAsync<VesselSensorConfiguration>(new { VesselName = vesselName });
                var existingConfig = existingConfigs?.FirstOrDefault();

                if (existingConfig != null)
                {
                    // Update engine configuration if it has changed
                    if (existingConfig.EngineConfiguration != engineConfiguration)
                    {
                        existingConfig.EngineConfiguration = engineConfiguration;
                        existingConfig.Updated = DateTime.UtcNow;

                        // Create additional sensors if upgrading to dual-engine
                        if (engineConfiguration == "dual" && (existingConfig.PortEngineSensorId == null || existingConfig.StarboardEngineSensorId == null))
                        {
                            var vesselForUpdate = await GetOrCreateVesselAsync(vesselName);
                            await CreateDualEngineSensorsAsync(existingConfig, vesselForUpdate);
                        }

                        await _databaseService.UpdateAsync<VesselSensorConfiguration>(existingConfig, new { Id = existingConfig.Id });
                        _logger.LogInformation("Updated engine configuration for vessel {VesselName} to {EngineConfiguration}", 
                            vesselName, engineConfiguration);
                    }

                    return existingConfig;
                }

                // Create new configuration
                var vessel = await GetOrCreateVesselAsync(vesselName);
                var config = await CreateVesselSensorConfigurationAsync(vessel, engineConfiguration);

                _logger.LogInformation("Created new sensor configuration for vessel {VesselName} with engine configuration {EngineConfiguration}", 
                    vesselName, engineConfiguration);

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting or creating vessel sensor configuration for {VesselName}", vesselName);
                throw;
            }
        }

        public async Task<Guid> GetSensorIdForMeasurementAsync(string vesselName, string category, string engineConfiguration)
        {
            var config = await GetOrCreateVesselSensorConfigurationAsync(vesselName, engineConfiguration);

            return category switch
            {
                "engine_port" when config.PortEngineSensorId.HasValue => config.PortEngineSensorId.Value,
                "engine_stbd" when config.StarboardEngineSensorId.HasValue => config.StarboardEngineSensorId.Value,
                _ => config.PrimarySensorId
            };
        }

        public async Task<List<Guid>> GetAllSensorIdsForVesselAsync(string vesselName)
        {
            var config = await GetOrCreateVesselSensorConfigurationAsync(vesselName);
            var sensorIds = new List<Guid> { config.PrimarySensorId };

            if (config.PortEngineSensorId.HasValue)
                sensorIds.Add(config.PortEngineSensorId.Value);

            if (config.StarboardEngineSensorId.HasValue)
                sensorIds.Add(config.StarboardEngineSensorId.Value);

            return sensorIds;
        }

        private async Task<Vessel> GetOrCreateVesselAsync(string vesselName)
        {
            try
            {
                // Try to find existing vessel
                var existingVessels = await _databaseService.SelectAsync<Vessel>(new { Name = vesselName });
                var existingVessel = existingVessels?.FirstOrDefault();

                if (existingVessel != null)
                {
                    return existingVessel;
                }

                // Create new vessel - we need to find an owner to assign it to
                var owners = await _databaseService.SelectAsync<Owner>(null);
                var defaultOwner = owners?.FirstOrDefault();

                if (defaultOwner == null)
                {
                    throw new InvalidOperationException("No owners found in database. Cannot create vessel without an owner.");
                }

                var newVessel = new Vessel
                {
                    Id = Guid.NewGuid(),
                    Name = vesselName,
                    Type = VesselType.Mechanical, // Default to mechanical for IoT sensor data
                    OwnerId = defaultOwner.Id,
                    Status = "Active",
                    Created = DateTime.UtcNow
                };

                await _databaseService.InsertAsync<Vessel>(newVessel);
                _logger.LogInformation("Created new vessel {VesselName} with Id {VesselId}", vesselName, newVessel.Id);

                return newVessel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating vessel {VesselName}", vesselName);
                throw;
            }
        }

        private async Task<VesselSensorConfiguration> CreateVesselSensorConfigurationAsync(Vessel vessel, string engineConfiguration)
        {
            var config = new VesselSensorConfiguration
            {
                Id = Guid.NewGuid(),
                VesselId = vessel.Id ?? Guid.NewGuid(),
                VesselName = vessel.Name ?? "Unknown",
                EngineConfiguration = engineConfiguration,
                Created = DateTime.UtcNow
            };

            // Create primary sensor
            config.PrimarySensorId = await CreateSensorAsync(vessel, "Primary Sensor", "Environmental");

            // Create engine-specific sensors if dual-engine
            if (engineConfiguration == "dual")
            {
                await CreateDualEngineSensorsAsync(config, vessel);
            }

            await _databaseService.InsertAsync<VesselSensorConfiguration>(config);
            return config;
        }

        private async Task CreateDualEngineSensorsAsync(VesselSensorConfiguration config, Vessel vessel)
        {
            if (config.PortEngineSensorId == null)
            {
                config.PortEngineSensorId = await CreateSensorAsync(vessel, "Port Engine", "Engine");
            }

            if (config.StarboardEngineSensorId == null)
            {
                config.StarboardEngineSensorId = await CreateSensorAsync(vessel, "Starboard Engine", "Engine");
            }
        }

        private async Task<Guid> CreateSensorAsync(Vessel vessel, string sensorName, string sensorType)
        {
            var sensor = new Sensor
            {
                Id = Guid.NewGuid(),
                Name = $"{vessel.Name ?? "Unknown"} - {sensorName}",
                Type = sensorType,
                VesselId = vessel.Id ?? Guid.NewGuid(),
                Status = "Active",
                Created = DateTime.UtcNow
            };

            await _databaseService.InsertAsync<Sensor>(sensor);
            _logger.LogInformation("Created sensor {SensorName} for vessel {VesselName}", sensor.Name, vessel.Name);

            return sensor.Id ?? Guid.NewGuid();
        }
    }

}