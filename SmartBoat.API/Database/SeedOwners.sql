-- Seed data for Owners table
-- This script inserts sample owner data for testing purposes

-- First, let's get some company IDs to use as references
DECLARE @Company1Id uniqueidentifier = (SELECT TOP 1 Id FROM Company WHERE Name LIKE '%Ocean%' OR Name LIKE '%Marine%');
DECLARE @Company2Id uniqueidentifier = (SELECT TOP 1 Id FROM Company WHERE Name LIKE '%Shipping%' OR Name LIKE '%Vessel%');
DECLARE @Company3Id uniqueidentifier = (SELECT TOP 1 Id FROM Company WHERE Id != @Company1Id AND Id != @Company2Id);

-- If no specific companies found, get the first three companies
IF @Company1Id IS NULL SET @Company1Id = (SELECT TOP 1 Id FROM Company ORDER BY Created);
IF @Company2Id IS NULL SET @Company2Id = (SELECT TOP 1 Id FROM Company WHERE Id != @Company1Id ORDER BY Created);
IF @Company3Id IS NULL SET @Company3Id = (SELECT TOP 1 Id FROM Company WHERE Id != @Company1Id AND Id != @Company2Id ORDER BY Created);

-- Insert sample owners
INSERT INTO Owners (Id, Name, Email, Phone, Address, CompanyId, Status, Created, LastUpdated)
VALUES
    (NEWID(), 'Captain John Mitchell', '<EMAIL>', '+30-210-1234567', 'Athens, Greece', @Company1Id, 'Active', GETDATE(), GETDATE()),
    (NEWID(), 'Maria Papadopoulos', '<EMAIL>', '+30-210-2345678', 'Piraeus, Greece', @Company1Id, 'Active', GETDATE(), GETDATE()),
    (NEWID(), 'Dimitris Konstantinou', '<EMAIL>', '+30-210-3456789', 'Thessaloniki, Greece', @Company2Id, 'Active', GETDATE(), GETDATE()),
    (NEWID(), 'Elena Georgiou', '<EMAIL>', '+30-210-4567890', 'Patras, Greece', @Company2Id, 'Active', GETDATE(), GETDATE()),
    (NEWID(), 'Andreas Nikolas', '<EMAIL>', '+30-210-5678901', 'Heraklion, Greece', @Company3Id, 'Active', GETDATE(), GETDATE());

-- Update some vessels to have these owners (if vessels exist)
DECLARE @Owner1Id uniqueidentifier = (SELECT TOP 1 Id FROM Owners WHERE Name = 'Captain John Mitchell');
DECLARE @Owner2Id uniqueidentifier = (SELECT TOP 1 Id FROM Owners WHERE Name = 'Maria Papadopoulos');
DECLARE @Owner3Id uniqueidentifier = (SELECT TOP 1 Id FROM Owners WHERE Name = 'Dimitris Konstantinou');

-- Assign vessels to owners (assuming some vessels exist)
UPDATE TOP (2) Vessels 
SET OwnerId = @Owner1Id, LastUpdated = GETDATE()
WHERE CompanyId = @Company1Id AND OwnerId IS NULL;

UPDATE TOP (1) Vessels 
SET OwnerId = @Owner2Id, LastUpdated = GETDATE()
WHERE CompanyId = @Company1Id AND OwnerId IS NULL;

UPDATE TOP (2) Vessels 
SET OwnerId = @Owner3Id, LastUpdated = GETDATE()
WHERE CompanyId = @Company2Id AND OwnerId IS NULL;

PRINT 'Sample owners and vessel assignments created successfully.';