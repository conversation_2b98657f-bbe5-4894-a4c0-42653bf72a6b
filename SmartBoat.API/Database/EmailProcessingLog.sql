CREATE TABLE EmailProcessingLog (
    Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    EmailId nvarchar(255) NULL, -- Microsoft Graph email ID for duplicate detection
    EmailSubject nvarchar(500) NULL,
    EmailDate datetime2(7) NOT NULL,
    ProcessingStatus nvarchar(50) NOT NULL, -- 'Success', 'Failed', 'Processing', 'Skipped'
    RecordsProcessed int NOT NULL DEFAULT 0,
    VesselsFound int NOT NULL DEFAULT 0,
    AttachmentIds nvarchar(max) NULL, -- JSON array of processed attachment IDs
    AttachmentCount int NOT NULL DEFAULT 0, -- Total number of ZIP attachments in email
    ErrorMessage nvarchar(max) NULL,
    ProcessingDuration int NULL, -- in milliseconds
    ProcessedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE()
);

-- Index for email ID duplicate detection
CREATE UNIQUE INDEX IX_EmailProcessingLog_EmailId ON EmailProcessingLog(EmailId) WHERE EmailId IS NOT NULL AND ProcessingStatus = 'Success';

-- Index for email date duplicates check
CREATE INDEX IX_EmailProcessingLog_EmailDate ON EmailProcessingLog(EmailDate DESC);

-- Index for status monitoring
CREATE INDEX IX_EmailProcessingLog_ProcessingStatus ON EmailProcessingLog(ProcessingStatus, ProcessedAt DESC);