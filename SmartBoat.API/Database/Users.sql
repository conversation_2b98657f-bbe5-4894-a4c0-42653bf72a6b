CREATE TABLE Users (
    Id uniqueidentifier NOT NULL UNIQUE,
    Username nvarchar(100) NOT NULL UNIQUE,
    Email nvarchar(200) NOT NULL UNIQUE,
    PasswordHash nvarchar(256) NOT NULL,
    FirstName nvarchar(100) NOT NULL,
    LastName nvarchar(100) NOT NULL,
    RoleId uniqueidentifier NOT NULL,
    Role nvarchar(50) NOT NULL,
    Status nvarchar(50) NOT NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    LastLogin datetime2(7) NULL,
    TwoFactorEnabled bit NOT NULL,
    Avatar nvarchar(500) NULL,
    Joined datetime2(7) NOT NULL,
    Company nvarchar(200) NULL,
    CompanyId uniqueidentifier NULL,
    Department nvarchar(100) NULL,
    Phone nvarchar(50) NULL,
    PhoneNumber nvarchar(50) NULL,
    Timezone nvarchar(50) NULL,
    Language nvarchar(10) NULL,
    Bio nvarchar(500) NULL,
    IsDeleted bit NOT NULL
);