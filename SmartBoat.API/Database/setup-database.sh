#!/bin/bash

# Bash script to setup Smartboat database using Docker
CONFIG_FILE="${1:-appsettings.Development.json}"

# Parse connection string without jq (using sed/grep)
CONNECTION_STRING=$(grep -o '"DefaultConnection"[[:space:]]*:[[:space:]]*"[^"]*"' "$CONFIG_FILE" | sed 's/.*"DefaultConnection"[[:space:]]*:[[:space:]]*"//' | sed 's/".*//')

# Parse connection string components
SERVER=$(echo "$CONNECTION_STRING" | grep -o 'Server=[^;]*' | cut -d'=' -f2)
USER_ID=$(echo "$CONNECTION_STRING" | grep -o 'User ID=[^;]*' | cut -d'=' -f2)
PASSWORD=$(echo "$CONNECTION_STRING" | grep -o 'Password=[^;]*' | cut -d'=' -f2)

echo "Connecting to server: $SERVER"
echo "Using User ID: $USER_ID"

# Create database if it doesn't exist
CREATE_DB_SQL="IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'Smartboat')
BEGIN
    CREATE DATABASE Smartboat;
    PRINT 'Database Smartboat created successfully!';
END
ELSE
BEGIN
    PRINT 'Database Smartboat already exists.';
END"

# Use Docker to run sqlcmd
echo "Creating database..."
docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -Q "$CREATE_DB_SQL"

if [ $? -ne 0 ]; then
    echo "Failed to create database"
    exit 1
fi

# Execute Authorization module setup FIRST (creates Permissions table)
if [ -f "Authorization/Database/setup-authorization.sql" ]; then
    echo "Setting up Authorization module..."
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat < "Authorization/Database/setup-authorization.sql"

    if [ $? -ne 0 ]; then
        echo "Failed to setup Authorization module"
        exit 1
    fi
else
    echo "Authorization module setup script not found, skipping..."
fi

# Execute all SQL files in Database folder (sorted by name)
# Skip specific files that have dedicated execution order or are deprecated
for sql_file in Database/*.sql; do
    filename=$(basename "$sql_file")
    if [ -f "$sql_file" ] && [ "$filename" != "EntityPermissions.sql" ] && [ "$filename" != "SeedUsersAndRoles.sql" ] && [ "$filename" != "SeedRolePermissions.sql" ] && [ "$filename" != "SeedVessels.sql" ] && [ "$filename" != "SeedSensors.sql" ] && [ "$filename" != "RolePermission.sql" ] && [ "$filename" != "VesselTypeMigration.sql" ]; then
        echo "Executing $(basename "$sql_file")..."
        docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat < "$sql_file"

        if [ $? -ne 0 ]; then
            echo "Failed to execute $(basename "$sql_file")"
            exit 1
        fi
    fi
done

# Execute EntityPermissions.sql (after Authorization module is set up)
if [ -f "Database/EntityPermissions.sql" ]; then
    echo "Executing EntityPermissions.sql..."
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat < "Database/EntityPermissions.sql"

    if [ $? -ne 0 ]; then
        echo "Failed to execute EntityPermissions.sql"
        exit 1
    fi
fi

# Execute Users and Roles seed script
if [ -f "Database/SeedUsersAndRoles.sql" ]; then
    echo "Deleting existing users and roles..."

    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U SA -P YourStrong@Passw0rd -C -d Smartboat -Q "DELETE FROM Users"
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U SA -P YourStrong@Passw0rd -C -d Smartboat -Q "DELETE FROM Role"

    echo "Executing SeedUsersAndRoles.sql..."
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat < "Database/SeedUsersAndRoles.sql"

    if [ $? -ne 0 ]; then
        echo "Failed to execute SeedUsersAndRoles.sql"
        exit 1
    fi
fi

# Execute Vessel Type Migration (before seeding vessels)
if [ -f "Database/VesselTypeMigration.sql" ]; then
    echo "Executing VesselTypeMigration.sql..."
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat < "Database/VesselTypeMigration.sql"

    if [ $? -ne 0 ]; then
        echo "Failed to execute VesselTypeMigration.sql"
        exit 1
    fi
fi

# Execute Vessels seed script (after companies are created)
if [ -f "Database/SeedVessels.sql" ]; then
    echo "Executing SeedVessels.sql..."
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat < "Database/SeedVessels.sql"

    if [ $? -ne 0 ]; then
        echo "Failed to execute SeedVessels.sql"
        exit 1
    fi
fi

# Execute Sensors seed script (after vessels are created)
if [ -f "Database/SeedSensors.sql" ]; then
    echo "Executing SeedSensors.sql..."
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat < "Database/SeedSensors.sql"

    if [ $? -ne 0 ]; then
        echo "Failed to execute SeedSensors.sql"
        exit 1
    fi
fi

# Execute Vessel Path Points seed script (after vessels are created)
if [ -f "Database/SeedVesselPathPoints.sql" ]; then
    echo "Executing SeedVesselPathPoints.sql..."
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat < "Database/SeedVesselPathPoints.sql"

    if [ $? -ne 0 ]; then
        echo "Failed to execute SeedVesselPathPoints.sql"
        exit 1
    fi
fi

# Execute Role-Permission mappings (must be last, after all permissions and roles exist)
if [ -f "Database/SeedRolePermissions.sql" ]; then
    echo "Executing SeedRolePermissions.sql..."
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat < "Database/SeedRolePermissions.sql"

    if [ $? -ne 0 ]; then
        echo "Failed to execute SeedRolePermissions.sql"
        exit 1
    fi
fi

echo "Database setup completed successfully!"