-- ============================================================================
-- Add Missing Computed Columns to SensorDataPoints Table
-- ============================================================================
-- This script adds only the missing computed columns that are causing errors

USE Smartboat;
GO

-- Set proper options for computed columns
SET QUOTED_IDENTIFIER ON;
SET ANSI_NULLS ON;
GO

PRINT 'Adding missing computed columns to SensorDataPoints table...';

-- Add the missing computed columns that are causing the API errors
-- Only add if they don't already exist

-- Check and add Temperature
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'Temperature')
BEGIN
    ALTER TABLE SensorDataPoints ADD Temperature AS CAST(JSON_VALUE(Measurements, '$.temperature') AS FLOAT);
    PRINT 'Added Temperature computed column';
END
ELSE
BEGIN
    PRINT 'Temperature column already exists';
END

-- Check and add Humidity
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'Humidity')
BEGIN
    ALTER TABLE SensorDataPoints ADD Humidity AS CAST(JSON_VALUE(Measurements, '$.humidity') AS FLOAT);
    PRINT 'Added Humidity computed column';
END
ELSE
BEGIN
    PRINT 'Humidity column already exists';
END

-- Check and add Speed
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'Speed')
BEGIN
    ALTER TABLE SensorDataPoints ADD Speed AS CAST(JSON_VALUE(Measurements, '$.speed') AS FLOAT);
    PRINT 'Added Speed computed column';
END
ELSE
BEGIN
    PRINT 'Speed column already exists';
END

-- Check and add RPM
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'RPM')
BEGIN
    ALTER TABLE SensorDataPoints ADD RPM AS CAST(JSON_VALUE(Measurements, '$.rpm') AS FLOAT);
    PRINT 'Added RPM computed column';
END
ELSE
BEGIN
    PRINT 'RPM column already exists';
END

-- Check and add PortRPM
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'PortRPM')
BEGIN
    ALTER TABLE SensorDataPoints ADD PortRPM AS CAST(JSON_VALUE(Measurements, '$.port_rpm') AS FLOAT);
    PRINT 'Added PortRPM computed column';
END
ELSE
BEGIN
    PRINT 'PortRPM column already exists';
END

-- Check and add StbdRPM
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'StbdRPM')
BEGIN
    ALTER TABLE SensorDataPoints ADD StbdRPM AS CAST(JSON_VALUE(Measurements, '$.stbd_rpm') AS FLOAT);
    PRINT 'Added StbdRPM computed column';
END
ELSE
BEGIN
    PRINT 'StbdRPM column already exists';
END

-- Check and add WindSpeed
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'WindSpeed')
BEGIN
    ALTER TABLE SensorDataPoints ADD WindSpeed AS CAST(JSON_VALUE(Measurements, '$.wind_speed') AS FLOAT);
    PRINT 'Added WindSpeed computed column';
END
ELSE
BEGIN
    PRINT 'WindSpeed column already exists';
END

-- Check and add WindAngle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'WindAngle')
BEGIN
    ALTER TABLE SensorDataPoints ADD WindAngle AS CAST(JSON_VALUE(Measurements, '$.wind_angle') AS FLOAT);
    PRINT 'Added WindAngle computed column';
END
ELSE
BEGIN
    PRINT 'WindAngle column already exists';
END

-- Check and add SeaDepth
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'SeaDepth')
BEGIN
    ALTER TABLE SensorDataPoints ADD SeaDepth AS CAST(JSON_VALUE(Measurements, '$.sea_depth') AS FLOAT);
    PRINT 'Added SeaDepth computed column';
END
ELSE
BEGIN
    PRINT 'SeaDepth column already exists';
END

-- Check and add Coordinates
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'Coordinates')
BEGIN
    ALTER TABLE SensorDataPoints ADD Coordinates AS JSON_VALUE(Measurements, '$.coordinates');
    PRINT 'Added Coordinates computed column';
END
ELSE
BEGIN
    PRINT 'Coordinates column already exists';
END

-- Verify the columns were added
DECLARE @AddedColumns INT;
SELECT @AddedColumns = COUNT(*) 
FROM sys.columns 
WHERE object_id = OBJECT_ID('SensorDataPoints') 
AND name IN ('Temperature', 'Humidity', 'Speed', 'RPM', 'PortRPM', 'StbdRPM', 'WindSpeed', 'WindAngle', 'SeaDepth', 'Coordinates');

PRINT '============================================================================';
PRINT 'Missing Computed Columns Fix Complete';
PRINT '============================================================================';
PRINT 'Total computed columns now present: ' + CAST(@AddedColumns AS NVARCHAR(10)) + ' out of 10 required';
PRINT 'The SensorDataPointService should now work without column errors';
PRINT '============================================================================';
