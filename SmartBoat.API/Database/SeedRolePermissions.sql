-- ============================================================================
-- SmartBoat Role-Permission Mapping Seed Script
-- ============================================================================
-- This script maps the 102 existing permissions to the 4 core roles
-- Based on business logic and security requirements

USE SmartBoat;
GO

PRINT 'Seeding role-permission mappings...';

-- Get role IDs
DECLARE @SuperAdminRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Super Admin');
DECLARE @PlatformAdminRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Platform Admin');
DECLARE @CustomerAdminRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Customer Admin');
DECLARE @CustomerEmployeeRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Customer Employee');

-- Clear existing mappings for these roles (development cleanup)
DELETE FROM UserRolePermissions 
WHERE UserRoleId IN (@SuperAdminRoleId, @PlatformAdminRoleId, @CustomerAdminRoleId, @CustomerEmployeeRoleId);

-- ============================================================================
-- 1. SUPER ADMIN PERMISSIONS (Complete Access)
-- ============================================================================

PRINT 'Assigning Super Admin permissions...';

-- Super Admin gets ALL permissions (core + entity-specific)
INSERT INTO UserRolePermissions (Id, UserRoleId, PermissionId)
SELECT NEWID(), @SuperAdminRoleId, p.Id
FROM Permissions p;

DECLARE @SuperAdminPermCount int = @@ROWCOUNT;

-- ============================================================================
-- 2. PLATFORM ADMIN PERMISSIONS (Cross-Customer Support)
-- ============================================================================

PRINT 'Assigning Platform Admin permissions...';

-- Core authorization permissions
INSERT INTO UserRolePermissions (Id, UserRoleId, PermissionId)
SELECT NEWID(), @PlatformAdminRoleId, p.Id
FROM Permissions p
WHERE p.MachineName IN (
    'CreatePermission', 'UpdatePermission', 'ListPermissions', 'DeletePermission',
    'ManagePermissionCache', 'ManageCache'
);

-- Entity permissions for cross-customer support
INSERT INTO UserRolePermissions (Id, UserRoleId, PermissionId)
SELECT NEWID(), @PlatformAdminRoleId, p.Id
FROM Permissions p
WHERE p.MachineName IN (
    -- User Management (can manage all users for support)
    'User.CreateGeneric', 'User.ReadAny', 'User.UpdateAny', 'User.DeleteAny',
    
    -- Role Management (read access for support)
    'Role.ReadAny',
    
    -- Company Management (can manage companies for onboarding/support)
    'Company.CreateGeneric', 'Company.ReadAny', 'Company.UpdateAny', 'Company.DeleteAny',
    
    -- Customer Management (can manage customers for support)
    'Customer.CreateGeneric', 'Customer.ReadAny', 'Customer.UpdateAny', 'Customer.DeleteAny',
    
    -- Vessel Management (read-only for support troubleshooting)
    'Vessel.ReadAny',
    
    -- Sensor Management (read-only for technical support)
    'Sensor.ReadAny',
    
    -- Alert Management (can view and update for support)
    'Alert.ReadAny', 'Alert.UpdateAny',
    
    -- Audit Log (full read access for compliance and troubleshooting)
    'AuditLog.ReadAny',
    
    -- Notification Management (can manage for support)
    'Notification.CreateGeneric', 'Notification.ReadAny', 'Notification.UpdateAny', 'Notification.DeleteAny',
    
    -- Report Management (can generate platform-wide reports)
    'Report.CreateGeneric', 'Report.ReadAny', 'Report.UpdateAny', 'Report.DeleteAny',
    
    -- Support Request Management (full access for handling support)
    'SupportRequest.CreateGeneric', 'SupportRequest.ReadAny', 'SupportRequest.UpdateAny', 'SupportRequest.DeleteAny',
    
    -- Feedback Management (can view and manage all feedback)
    'Feedback.ReadAny', 'Feedback.UpdateAny', 'Feedback.DeleteAny'
);

DECLARE @PlatformAdminPermCount int = @@ROWCOUNT + 6; -- +6 for core permissions

-- ============================================================================
-- 3. CUSTOMER ADMIN PERMISSIONS (Company Management)
-- ============================================================================

PRINT 'Assigning Customer Admin permissions...';

-- Customer Admin: Full control over their company data
INSERT INTO UserRolePermissions (Id, UserRoleId, PermissionId)
SELECT NEWID(), @CustomerAdminRoleId, p.Id
FROM Permissions p
WHERE p.MachineName IN (
    -- User Management (can manage company employees)
    'User.CreateGeneric', 'User.ReadOwn', 'User.ReadGroup', 'User.UpdateOwn', 'User.DeleteOwn',
    
    -- Role Management (read own role info)
    'Role.ReadOwn',
    
    -- Company Management (can update own company)
    'Company.ReadOwn', 'Company.UpdateOwn',
    
    -- Customer Management (can update own customer record)
    'Customer.ReadOwn', 'Customer.UpdateOwn',
    
    -- Vessel Management (full control over company vessels)
    'Vessel.CreateGeneric', 'Vessel.ReadOwn', 'Vessel.ReadGroup', 'Vessel.UpdateOwn', 'Vessel.DeleteOwn',
    
    -- Sensor Management (full control over company sensors)
    'Sensor.CreateGeneric', 'Sensor.ReadOwn', 'Sensor.ReadGroup', 'Sensor.UpdateOwn', 'Sensor.DeleteOwn',
    
    -- Alert Management (full control over company alerts)
    'Alert.CreateGeneric', 'Alert.ReadOwn', 'Alert.ReadGroup', 'Alert.UpdateOwn', 'Alert.DeleteOwn',
    
    -- Audit Log (can view company audit trail)
    'AuditLog.ReadOwn',
    
    -- Notification Management (full control over company notifications)
    'Notification.CreateGeneric', 'Notification.ReadOwn', 'Notification.ReadGroup', 'Notification.UpdateOwn', 'Notification.DeleteOwn',
    
    -- Report Management (full control over company reports)
    'Report.CreateGeneric', 'Report.ReadOwn', 'Report.ReadGroup', 'Report.UpdateOwn', 'Report.DeleteOwn',
    
    -- Support Request Management (full control over company support requests)
    'SupportRequest.CreateGeneric', 'SupportRequest.ReadOwn', 'SupportRequest.ReadGroup', 'SupportRequest.UpdateOwn', 'SupportRequest.DeleteOwn',
    
    -- Feedback Management (full control over company feedback)
    'Feedback.CreateGeneric', 'Feedback.ReadOwn', 'Feedback.ReadGroup', 'Feedback.UpdateOwn', 'Feedback.DeleteOwn'
);

DECLARE @CustomerAdminPermCount int = @@ROWCOUNT;

-- ============================================================================
-- 4. CUSTOMER EMPLOYEE PERMISSIONS (Operational Access)
-- ============================================================================

PRINT 'Assigning Customer Employee permissions...';

-- Customer Employee: Limited operational access
INSERT INTO UserRolePermissions (Id, UserRoleId, PermissionId)
SELECT NEWID(), @CustomerEmployeeRoleId, p.Id
FROM Permissions p
WHERE p.MachineName IN (
    -- User Management (can view company colleagues)
    'User.ReadGroup',
    
    -- Role Management (can view own role)
    'Role.ReadOwn',
    
    -- Company Management (can view own company)
    'Company.ReadOwn',
    
    -- Customer Management (can view own customer info)
    'Customer.ReadOwn',
    
    -- Vessel Management (can view assigned vessels)
    'Vessel.ReadOwn', 'Vessel.ReadGroup',
    
    -- Sensor Management (can view and update assigned sensors)
    'Sensor.ReadOwn', 'Sensor.ReadGroup', 'Sensor.UpdateOwn',
    
    -- Alert Management (can view and respond to assigned alerts)
    'Alert.ReadOwn', 'Alert.ReadGroup', 'Alert.UpdateOwn',
    
    -- Notification Management (can view assigned notifications)
    'Notification.ReadOwn', 'Notification.ReadGroup',
    
    -- Report Management (can view assigned reports)
    'Report.ReadOwn', 'Report.ReadGroup',
    
    -- Support Request Management (can create and manage own support requests)
    'SupportRequest.CreateGeneric', 'SupportRequest.ReadOwn', 'SupportRequest.UpdateOwn',
    
    -- Feedback Management (can create and manage own feedback)
    'Feedback.CreateGeneric', 'Feedback.ReadOwn', 'Feedback.UpdateOwn'
);

DECLARE @CustomerEmployeePermCount int = @@ROWCOUNT;

-- ============================================================================
-- 5. SUMMARY AND VALIDATION
-- ============================================================================

-- Validate role assignments
DECLARE @TotalMappings int = (SELECT COUNT(*) FROM UserRolePermissions);
DECLARE @TotalPermissions int = (SELECT COUNT(*) FROM Permissions);

PRINT '============================================================================';
PRINT 'Role-Permission Mapping Complete';
PRINT '============================================================================';
PRINT 'Total Available Permissions: ' + CAST(@TotalPermissions AS nvarchar(10));
PRINT 'Total Permission Mappings Created: ' + CAST(@TotalMappings AS nvarchar(10));
PRINT '';
PRINT 'Permission Distribution by Role:';
PRINT '- Super Admin: ' + CAST(@SuperAdminPermCount AS nvarchar(10)) + ' permissions (ALL)';
PRINT '- Platform Admin: ' + CAST(@PlatformAdminPermCount AS nvarchar(10)) + ' permissions (Cross-customer support)';
PRINT '- Customer Admin: ' + CAST(@CustomerAdminPermCount AS nvarchar(10)) + ' permissions (Company management)';
PRINT '- Customer Employee: ' + CAST(@CustomerEmployeePermCount AS nvarchar(10)) + ' permissions (Operational access)';
PRINT '';

-- Show permission breakdown by category
PRINT 'Permission Categories Assigned:';
PRINT '';

-- Core Authorization Permissions
DECLARE @CorePerms int = (
    SELECT COUNT(DISTINCT p.MachineName) 
    FROM UserRolePermissions urp 
    JOIN Permissions p ON urp.PermissionId = p.Id 
    WHERE p.Entity IN ('Permission', 'Cache')
);
PRINT 'Core Authorization: ' + CAST(@CorePerms AS nvarchar(10)) + ' permissions';

-- Entity-based Permissions
DECLARE @EntityPerms int = (
    SELECT COUNT(DISTINCT p.MachineName) 
    FROM UserRolePermissions urp 
    JOIN Permissions p ON urp.PermissionId = p.Id 
    WHERE p.Entity NOT IN ('Permission', 'Cache')
);
PRINT 'Entity-based: ' + CAST(@EntityPerms AS nvarchar(10)) + ' permissions';

PRINT '';
PRINT 'Key Security Rules Applied:';
PRINT '- Super Admin: Complete platform access for system administration';
PRINT '- Platform Admin: Cross-customer support with read/update access';
PRINT '- Customer Admin: Full control over company data and employees';
PRINT '- Customer Employee: Limited to assigned vessels and operational tasks';
PRINT '- All roles: Can create support requests and feedback';
PRINT '- Data isolation: Customer roles cannot access other companies'' data';
PRINT '============================================================================';