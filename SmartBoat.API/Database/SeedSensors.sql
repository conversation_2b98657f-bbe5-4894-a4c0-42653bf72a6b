-- ============================================================================
-- SmartBoat Sensors Seed Script
-- ============================================================================
-- This script seeds sample sensors for all vessels
-- Executed by setup-database.sh after vessels are created

USE SmartBoat;
GO

PRINT 'Seeding sample sensors...';

-- Get all vessel IDs and names for sensor assignment
DECLARE @VesselCursor CURSOR;
DECLARE @VesselId uniqueidentifier;
DECLARE @VesselName nvarchar(200);
DECLARE @VesselType nvarchar(100);

SET @VesselCursor = CURSOR FOR
SELECT Id, Name, Type FROM Vessels;

OPEN @VesselCursor;
FETCH NEXT FROM @VesselCursor INTO @VesselId, @VesselName, @VesselType;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- Engine sensors (all vessels)
    INSERT INTO Sensors (Id, Name, Type, VesselId, Location, Status, LastReading, LastUpdated, AlertThreshold, Created, Changed)
    VALUES
    (NEWID(), 'Engine Temperature', 'Temperature', @VesselId, 'Engine Room', 'Active', DATEADD(MINUTE, -RAND()*120, GETUTCDATE()), GETUTCDATE(), '85°C', GETUTCDATE(), NULL),
    (NEWID(), 'Engine Oil Pressure', 'Pressure', @VesselId, 'Engine Room', 'Active', DATEADD(MINUTE, -RAND()*120, GETUTCDATE()), GETUTCDATE(), '2.5 bar', GETUTCDATE(), NULL),
    (NEWID(), 'Engine RPM', 'Speed', @VesselId, 'Engine Room', 'Active', DATEADD(MINUTE, -RAND()*120, GETUTCDATE()), GETUTCDATE(), '2500 RPM', GETUTCDATE(), NULL);

    -- Navigation sensors (all vessels)
    INSERT INTO Sensors (Id, Name, Type, VesselId, Location, Status, LastReading, LastUpdated, AlertThreshold, Created, Changed)
    VALUES
    (NEWID(), 'GPS Position', 'GPS', @VesselId, 'Bridge', 'Active', DATEADD(MINUTE, -RAND()*30, GETUTCDATE()), GETUTCDATE(), NULL, GETUTCDATE(), NULL),
    (NEWID(), 'Compass Heading', 'Direction', @VesselId, 'Bridge', 'Active', DATEADD(MINUTE, -RAND()*30, GETUTCDATE()), GETUTCDATE(), NULL, GETUTCDATE(), NULL),
    (NEWID(), 'Speed Over Ground', 'Speed', @VesselId, 'Bridge', 'Active', DATEADD(MINUTE, -RAND()*30, GETUTCDATE()), GETUTCDATE(), '25 knots', GETUTCDATE(), NULL);

    -- Hull sensors (all vessels)
    INSERT INTO Sensors (Id, Name, Type, VesselId, Location, Status, LastReading, LastUpdated, AlertThreshold, Created, Changed)
    VALUES
    (NEWID(), 'Hull Temperature', 'Temperature', @VesselId, 'Hull', 'Active', DATEADD(MINUTE, -RAND()*60, GETUTCDATE()), GETUTCDATE(), '50°C', GETUTCDATE(), NULL),
    (NEWID(), 'Bilge Water Level', 'Level', @VesselId, 'Bilge', 'Active', DATEADD(MINUTE, -RAND()*60, GETUTCDATE()), GETUTCDATE(), '30cm', GETUTCDATE(), NULL);

    -- Type-specific sensors
    IF @VesselType IN ('Container Ship', 'Bulk Carrier', 'Tanker', 'General Cargo', 'Factory Ship')
    BEGIN
        -- Commercial vessel specific sensors
        INSERT INTO Sensors (Id, Name, Type, VesselId, Location, Status, LastReading, LastUpdated, AlertThreshold, Created, Changed)
        VALUES
        (NEWID(), 'Cargo Hold Temperature', 'Temperature', @VesselId, 'Cargo Hold', 'Active', DATEADD(MINUTE, -RAND()*120, GETUTCDATE()), GETUTCDATE(), '25°C', GETUTCDATE(), NULL),
        (NEWID(), 'Fuel Level', 'Level', @VesselId, 'Fuel Tank', 'Active', DATEADD(MINUTE, -RAND()*180, GETUTCDATE()), GETUTCDATE(), '20%', GETUTCDATE(), NULL),
        (NEWID(), 'Generator Load', 'Power', @VesselId, 'Engine Room', 'Active', DATEADD(MINUTE, -RAND()*60, GETUTCDATE()), GETUTCDATE(), '90%', GETUTCDATE(), NULL);
    END

    IF @VesselType IN ('Luxury Yacht', 'Charter Boat', 'Tourist Vessel', 'Sport Fishing')
    BEGIN
        -- Charter/recreational vessel specific sensors
        INSERT INTO Sensors (Id, Name, Type, VesselId, Location, Status, LastReading, LastUpdated, AlertThreshold, Created, Changed)
        VALUES
        (NEWID(), 'Cabin Temperature', 'Temperature', @VesselId, 'Main Cabin', 'Active', DATEADD(MINUTE, -RAND()*90, GETUTCDATE()), GETUTCDATE(), '28°C', GETUTCDATE(), NULL),
        (NEWID(), 'Fresh Water Level', 'Level', @VesselId, 'Water Tank', 'Active', DATEADD(MINUTE, -RAND()*180, GETUTCDATE()), GETUTCDATE(), '15%', GETUTCDATE(), NULL),
        (NEWID(), 'Battery Voltage', 'Voltage', @VesselId, 'Battery Bank', 'Active', DATEADD(MINUTE, -RAND()*60, GETUTCDATE()), GETUTCDATE(), '11.5V', GETUTCDATE(), NULL);
    END

    IF @VesselType IN ('Fishing Trawler', 'Purse Seiner', 'Longline Vessel')
    BEGIN
        -- Fishing vessel specific sensors
        INSERT INTO Sensors (Id, Name, Type, VesselId, Location, Status, LastReading, LastUpdated, AlertThreshold, Created, Changed)
        VALUES
        (NEWID(), 'Fish Hold Temperature', 'Temperature', @VesselId, 'Fish Hold', 'Active', DATEADD(MINUTE, -RAND()*120, GETUTCDATE()), GETUTCDATE(), '4°C', GETUTCDATE(), NULL),
        (NEWID(), 'Net Tension', 'Tension', @VesselId, 'Deck', 'Active', DATEADD(MINUTE, -RAND()*90, GETUTCDATE()), GETUTCDATE(), '2000kg', GETUTCDATE(), NULL),
        (NEWID(), 'Winch Motor Current', 'Current', @VesselId, 'Deck', 'Active', DATEADD(MINUTE, -RAND()*60, GETUTCDATE()), GETUTCDATE(), '50A', GETUTCDATE(), NULL),
        (NEWID(), 'Ice Machine Temperature', 'Temperature', @VesselId, 'Processing Area', 'Active', DATEADD(MINUTE, -RAND()*120, GETUTCDATE()), GETUTCDATE(), '-5°C', GETUTCDATE(), NULL);
    END

    FETCH NEXT FROM @VesselCursor INTO @VesselId, @VesselName, @VesselType;
END

CLOSE @VesselCursor;
DEALLOCATE @VesselCursor;

-- Get count of inserted sensors
DECLARE @SensorCount int = (SELECT COUNT(*) FROM Sensors);

PRINT '============================================================================';
PRINT 'Sensors Seeded Successfully';
PRINT '============================================================================';
PRINT 'Total Sensors Created: ' + CAST(@SensorCount AS nvarchar(10));
PRINT '';
PRINT 'Sensor Types by Vessel Category:';
PRINT '- All Vessels: Engine sensors, Navigation sensors, Hull sensors';
PRINT '- Commercial Ships: Cargo hold, Fuel level, Generator sensors';
PRINT '- Charter/Recreation: Cabin comfort, Water/Battery monitoring';
PRINT '- Fishing Vessels: Fish hold, Net equipment, Ice machine sensors';
PRINT '';
PRINT 'Sensor Locations: Engine Room, Bridge, Hull, Bilge, Cargo Hold,';
PRINT '                  Main Cabin, Deck, Fish Hold, Processing Area';
PRINT '============================================================================';