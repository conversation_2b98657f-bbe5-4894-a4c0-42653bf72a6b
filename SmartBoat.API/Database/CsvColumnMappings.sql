CREATE TABLE CsvColumnMappings (
    Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    VesselName nvarchar(255) NOT NULL,
    ColumnName nvarchar(255) NOT NULL,
    Measurement<PERSON>ey nvarchar(100) NOT NULL,
    DataType nvarchar(50) NOT NULL DEFAULT 'string',
    Category nvarchar(100) NOT NULL,
    IsActive bit NOT NULL DEFAULT 1,
    Created datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    Updated datetime2(7) NULL
);

-- Index for vessel-specific lookups
CREATE INDEX IX_CsvColumnMappings_VesselName_IsActive ON CsvColumnMappings(VesselName, IsActive);

-- Index for column name lookups
CREATE INDEX IX_CsvColumnMappings_ColumnName_IsActive ON CsvColumnMappings(ColumnName, IsActive);

-- Unique constraint to prevent duplicate mappings for same vessel/column
CREATE UNIQUE INDEX IX_CsvColumnMappings_Vessel_Column_Unique 
ON CsvColumnMappings(VesselName, ColumnName) 
WHERE IsActive = 1;

-- Table for vessel sensor configurations
CREATE TABLE VesselSensorConfigurations (
    Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    VesselId uniqueidentifier NOT NULL,
    VesselName nvarchar(255) NOT NULL,
    PrimarySensorId uniqueidentifier NOT NULL,
    PortEngineSensorId uniqueidentifier NULL,
    StarboardEngineSensorId uniqueidentifier NULL,
    EngineConfiguration nvarchar(50) NOT NULL DEFAULT 'single',
    Created datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    Updated datetime2(7) NULL,
    
    -- Foreign key constraints
    CONSTRAINT FK_VesselSensorConfigurations_Vessels FOREIGN KEY (VesselId) REFERENCES Vessels(Id) ON DELETE CASCADE,
    CONSTRAINT FK_VesselSensorConfigurations_PrimarySensor FOREIGN KEY (PrimarySensorId) REFERENCES Sensors(Id),
    CONSTRAINT FK_VesselSensorConfigurations_PortSensor FOREIGN KEY (PortEngineSensorId) REFERENCES Sensors(Id),
    CONSTRAINT FK_VesselSensorConfigurations_StbdSensor FOREIGN KEY (StarboardEngineSensorId) REFERENCES Sensors(Id)
);

-- Index for vessel lookups
CREATE UNIQUE INDEX IX_VesselSensorConfigurations_VesselId ON VesselSensorConfigurations(VesselId);
CREATE INDEX IX_VesselSensorConfigurations_VesselName ON VesselSensorConfigurations(VesselName);

-- Table to track unmapped columns for admin review
CREATE TABLE UnmappedCsvColumns (
    Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    VesselName nvarchar(255) NOT NULL,
    ColumnName nvarchar(255) NOT NULL,
    SampleValue nvarchar(500) NULL,
    FirstSeen datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    LastSeen datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    OccurrenceCount int NOT NULL DEFAULT 1,
    Status nvarchar(50) NOT NULL DEFAULT 'pending', -- 'pending', 'mapped', 'ignored'
    AdminNotes nvarchar(1000) NULL
);

-- Index for admin review queries
CREATE INDEX IX_UnmappedCsvColumns_Status ON UnmappedCsvColumns(Status);
CREATE INDEX IX_UnmappedCsvColumns_VesselName_Status ON UnmappedCsvColumns(VesselName, Status);

-- Unique constraint to prevent duplicates
CREATE UNIQUE INDEX IX_UnmappedCsvColumns_Vessel_Column_Unique 
ON UnmappedCsvColumns(VesselName, ColumnName);