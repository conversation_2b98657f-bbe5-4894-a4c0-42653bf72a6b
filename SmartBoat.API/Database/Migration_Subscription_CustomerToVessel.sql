-- Migration script to change Subscription table from CustomerId to VesselId
-- This script should be run carefully in production with proper backup

-- Step 1: Add the new VesselId column
ALTER TABLE Subscription 
ADD VesselId uniqueidentifier NULL;

-- Step 2: Update existing subscriptions with VesselId
-- Note: This assumes a relationship where customers own vessels
-- You may need to adjust this logic based on your business rules
-- For now, we'll set VesselId to the first vessel of each customer
UPDATE s
SET s.VesselId = v.Id
FROM Subscription s
INNER JOIN (
    SELECT 
        c.Id as CustomerId,
        MIN(v.Id) as Id
    FROM Customer c
    INNER JOIN Company comp ON c.CompanyId = comp.Id
    INNER JOIN Vessel v ON comp.Id = v.CompanyId
    GROUP BY c.Id
) v ON s.CustomerId = v.CustomerId;

-- Step 3: Handle subscriptions that couldn't be mapped to vessels
-- These will need manual intervention or business rules to determine vessel assignment
-- For now, we'll log them but not delete them
SELECT 
    s.Id as SubscriptionId,
    s.Name as SubscriptionName,
    s.CustomerId,
    'Subscription without vessel mapping' as Issue
FROM Subscription s
WHERE s.VesselId IS NULL;

-- Step 4: Make VesselId NOT NULL (only run after ensuring all records have VesselId)
-- ALTER TABLE Subscription 
-- ALTER COLUMN VesselId uniqueidentifier NOT NULL;

-- Step 5: Drop the old CustomerId column (only run after confirming all is working)
-- ALTER TABLE Subscription 
-- DROP COLUMN CustomerId;

-- Step 6: Create index on VesselId for performance
CREATE INDEX IX_Subscription_VesselId ON Subscription(VesselId);