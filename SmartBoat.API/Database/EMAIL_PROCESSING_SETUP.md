# Email Processing Database Setup

## Issue Description

If you're seeing "Technical Error" messages when accessing the Email Processing Settings in the SmartBoat platform, it's likely because the required database tables haven't been created yet.

The error typically appears as:
```
API Error [/api/emailprocessing/summary]: Error: Technical Error
```

## Quick Fix

### Option 1: Run the Email Processing Setup Script (Recommended)

Execute the dedicated setup script for email processing tables:

```bash
# From the SmartBoat.API directory
docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U SA -P YourStrong@Passw0rd -C -d Smartboat < Database/setup-email-processing-tables.sql
```

### Option 2: Run the Full Database Setup

If you haven't set up the database yet, run the complete setup:

```bash
# From the SmartBoat.API directory
./Database/setup-database.sh
```

### Option 3: Manual SQL Execution

Connect to your SQL Server and run the following files in order:

1. `Database/EmailProcessingLog.sql`
2. `Database/EmailProcessingSchedule.sql`

Or run the combined setup script:
- `Database/setup-email-processing-tables.sql`

## What These Scripts Do

### EmailProcessingLog Table
- Stores history of email processing operations
- Tracks success/failure status of email syncs
- Records processing statistics (emails processed, vessels found, etc.)
- Prevents duplicate processing of the same emails

### EmailProcessingSchedule Table
- Manages scheduled email processing configuration
- Stores timezone settings (default: Europe/Athens)
- Controls automatic daily processing at midnight
- Allows users to enable/disable scheduled processing

## Verification

After running the setup, you can verify the tables exist by connecting to your database and running:

```sql
USE Smartboat;
SELECT name FROM sys.tables WHERE name IN ('EmailProcessingLog', 'EmailProcessingSchedule');
```

You should see both tables listed.

## Expected Behavior After Setup

Once the tables are created:

1. ✅ The Email Processing Settings page will load without errors
2. ✅ You can view sync history (initially empty)
3. ✅ You can enable/disable scheduled processing
4. ✅ Manual sync operations will work
5. ✅ Processing history will be recorded and displayed

## Troubleshooting

### Still Getting Errors?

1. **Check database connection**: Ensure your API can connect to the SQL Server
2. **Verify table creation**: Run the verification SQL above
3. **Check logs**: Look at the API logs for more detailed error messages
4. **Restart API**: After creating tables, restart the SmartBoat API service

### Permission Issues

If you get permission errors, ensure your database user has:
- `CREATE TABLE` permissions
- `INSERT`, `UPDATE`, `DELETE` permissions on the created tables
- `CREATE INDEX` permissions

### Connection String

Verify your connection string in `appsettings.json` points to the correct database:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=Smartboat;User Id=SA;Password=YourStrong@Passw0rd;TrustServerCertificate=true;"
  }
}
```

## Development Notes

The API has been updated to handle missing tables gracefully:

- **Before**: API would return "Technical Error" and crash
- **After**: API returns empty data and logs warnings, allowing the UI to work

However, for full functionality, the database tables must be created using one of the methods above.

## Files Involved

- `Database/EmailProcessingLog.sql` - Creates the processing log table
- `Database/EmailProcessingSchedule.sql` - Creates the schedule configuration table
- `Database/setup-email-processing-tables.sql` - Combined setup script (recommended)
- `Database/setup-database.sh` - Full database setup script

## Support

If you continue to experience issues after following this guide, please:

1. Check the API logs for detailed error messages
2. Verify your database connection and permissions
3. Ensure you're using the correct database name (`Smartboat`)
4. Try running the setup scripts again

The email processing feature requires these database tables to function properly. Once set up, you'll have full access to email sync history, scheduling, and manual processing capabilities.
