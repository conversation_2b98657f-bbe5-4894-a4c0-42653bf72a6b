-- ============================================================================
-- Email Processing Tables Setup Script
-- ============================================================================
-- This script ensures that the required tables for email processing exist
-- Can be run multiple times safely (idempotent)

USE Smartboat;
GO

PRINT 'Setting up Email Processing tables...';

-- ============================================================================
-- 1. CREATE EmailProcessingLog TABLE
-- ============================================================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'EmailProcessingLog')
BEGIN
    CREATE TABLE EmailProcessingLog (
        Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
        EmailId nvarchar(255) NULL, -- Microsoft Graph email ID for duplicate detection
        EmailSubject nvarchar(500) NULL,
        EmailDate datetime2(7) NOT NULL,
        ProcessingStatus nvarchar(50) NOT NULL, -- 'Success', 'Failed', 'Processing', 'Skipped'
        RecordsProcessed int NOT NULL DEFAULT 0,
        VesselsFound int NOT NULL DEFAULT 0,
        AttachmentIds nvarchar(max) NULL, -- JSON array of processed attachment IDs
        AttachmentCount int NOT NULL DEFAULT 0, -- Total number of ZIP attachments in email
        ErrorMessage nvarchar(max) NULL,
        ProcessingDuration int NULL, -- in milliseconds
        ProcessedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE()
    );

    -- Index for email ID duplicate detection
    CREATE UNIQUE INDEX IX_EmailProcessingLog_EmailId ON EmailProcessingLog(EmailId) 
    WHERE EmailId IS NOT NULL AND ProcessingStatus = 'Success';

    -- Index for email date duplicates check
    CREATE INDEX IX_EmailProcessingLog_EmailDate ON EmailProcessingLog(EmailDate DESC);

    -- Index for status monitoring
    CREATE INDEX IX_EmailProcessingLog_ProcessingStatus ON EmailProcessingLog(ProcessingStatus, ProcessedAt DESC);

    PRINT 'EmailProcessingLog table created successfully with indexes';
END
ELSE
BEGIN
    PRINT 'EmailProcessingLog table already exists';
    
    -- Check if new columns exist and add them if missing
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'EmailId')
    BEGIN
        ALTER TABLE EmailProcessingLog ADD EmailId nvarchar(255) NULL;
        PRINT 'Added EmailId column to EmailProcessingLog table';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'AttachmentIds')
    BEGIN
        ALTER TABLE EmailProcessingLog ADD AttachmentIds nvarchar(max) NULL;
        PRINT 'Added AttachmentIds column to EmailProcessingLog table';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'AttachmentCount')
    BEGIN
        ALTER TABLE EmailProcessingLog ADD AttachmentCount int NOT NULL DEFAULT 0;
        PRINT 'Added AttachmentCount column to EmailProcessingLog table';
    END
END

-- ============================================================================
-- 2. CREATE EmailProcessingSchedule TABLE
-- ============================================================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'EmailProcessingSchedule')
BEGIN
    CREATE TABLE EmailProcessingSchedule (
        Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
        UserId uniqueidentifier NULL, -- NULL for global/system-wide schedule
        IsEnabled bit NOT NULL DEFAULT 0,
        ScheduleType nvarchar(50) NOT NULL DEFAULT 'Daily', -- Future: 'Daily', 'Weekly', 'Custom'
        CronExpression nvarchar(100) NOT NULL DEFAULT '0 0 0 * * ?', -- Daily at midnight
        TimeZone nvarchar(100) NOT NULL DEFAULT 'Europe/Athens',
        DaysBack int NOT NULL DEFAULT 7, -- How many days back to process emails
        ForceReprocess bit NOT NULL DEFAULT 0, -- Whether to reprocess already processed emails
        Description nvarchar(500) NULL,
        CreatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
        CreatedBy nvarchar(100) NULL,
        UpdatedBy nvarchar(100) NULL
    );

    -- Index for user-specific schedules
    CREATE INDEX IX_EmailProcessingSchedule_UserId ON EmailProcessingSchedule(UserId);

    -- Index for enabled schedules
    CREATE INDEX IX_EmailProcessingSchedule_IsEnabled ON EmailProcessingSchedule(IsEnabled, UpdatedAt DESC);

    -- Insert default global schedule (disabled by default)
    INSERT INTO EmailProcessingSchedule (
        Id,
        UserId,
        IsEnabled,
        ScheduleType,
        CronExpression,
        TimeZone,
        DaysBack,
        ForceReprocess,
        Description,
        CreatedAt,
        UpdatedAt,
        CreatedBy
    ) VALUES (
        NEWID(),
        NULL, -- Global schedule
        0, -- Disabled by default
        'Daily',
        '0 0 0 * * ?', -- Daily at midnight
        'Europe/Athens',
        7,
        0,
        'Default daily email processing schedule at midnight Greece time',
        GETUTCDATE(),
        GETUTCDATE(),
        'SYSTEM'
    );

    PRINT 'EmailProcessingSchedule table created successfully with default global schedule';
END
ELSE
BEGIN
    PRINT 'EmailProcessingSchedule table already exists';
    
    -- Ensure default global schedule exists
    IF NOT EXISTS (SELECT * FROM EmailProcessingSchedule WHERE UserId IS NULL)
    BEGIN
        INSERT INTO EmailProcessingSchedule (
            Id,
            UserId,
            IsEnabled,
            ScheduleType,
            CronExpression,
            TimeZone,
            DaysBack,
            ForceReprocess,
            Description,
            CreatedAt,
            UpdatedAt,
            CreatedBy
        ) VALUES (
            NEWID(),
            NULL, -- Global schedule
            0, -- Disabled by default
            'Daily',
            '0 0 0 * * ?', -- Daily at midnight
            'Europe/Athens',
            7,
            0,
            'Default daily email processing schedule at midnight Greece time',
            GETUTCDATE(),
            GETUTCDATE(),
            'SYSTEM'
        );
        PRINT 'Added default global schedule to existing EmailProcessingSchedule table';
    END
    ELSE
    BEGIN
        PRINT 'Default global schedule already exists';
    END
END

-- ============================================================================
-- 3. SUMMARY
-- ============================================================================

DECLARE @LogTableExists bit = CASE WHEN EXISTS (SELECT * FROM sys.tables WHERE name = 'EmailProcessingLog') THEN 1 ELSE 0 END;
DECLARE @ScheduleTableExists bit = CASE WHEN EXISTS (SELECT * FROM sys.tables WHERE name = 'EmailProcessingSchedule') THEN 1 ELSE 0 END;
DECLARE @GlobalScheduleExists bit = CASE WHEN EXISTS (SELECT * FROM EmailProcessingSchedule WHERE UserId IS NULL) THEN 1 ELSE 0 END;

PRINT '';
PRINT '============================================================================';
PRINT 'Email Processing Tables Setup Summary:';
PRINT '============================================================================';
PRINT 'EmailProcessingLog table exists: ' + CASE WHEN @LogTableExists = 1 THEN 'YES' ELSE 'NO' END;
PRINT 'EmailProcessingSchedule table exists: ' + CASE WHEN @ScheduleTableExists = 1 THEN 'YES' ELSE 'NO' END;
PRINT 'Default global schedule exists: ' + CASE WHEN @GlobalScheduleExists = 1 THEN 'YES' ELSE 'NO' END;
PRINT '';

IF @LogTableExists = 1 AND @ScheduleTableExists = 1 AND @GlobalScheduleExists = 1
BEGIN
    PRINT '✅ All email processing tables are set up correctly!';
    PRINT '   The API should now work without "Technical Error" messages.';
END
ELSE
BEGIN
    PRINT '❌ Some tables or data are missing. Please check the output above.';
END

PRINT '============================================================================';
