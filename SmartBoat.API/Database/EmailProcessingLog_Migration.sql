-- Migration script to add new columns to existing EmailProcessingLog table
-- This script can be run multiple times safely

USE Smartboat;
GO

-- Add EmailId column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'EmailId')
BEGIN
    ALTER TABLE EmailProcessingLog ADD EmailId nvarchar(255) NULL;
    PRINT 'Added EmailId column to EmailProcessingLog table';
END
ELSE
BEGIN
    PRINT 'EmailId column already exists in EmailProcessingLog table';
END

-- Add AttachmentIds column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'AttachmentIds')
BEGIN
    ALTER TABLE EmailProcessingLog ADD AttachmentIds nvarchar(max) NULL;
    PRINT 'Added AttachmentIds column to EmailProcessingLog table';
END
ELSE
BEGIN
    PRINT 'AttachmentIds column already exists in EmailProcessingLog table';
END

-- Add AttachmentCount column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'AttachmentCount')
BEGIN
    ALTER TABLE EmailProcessingLog ADD AttachmentCount int NOT NULL DEFAULT 0;
    PRINT 'Added AttachmentCount column to EmailProcessingLog table';
END
ELSE
BEGIN
    PRINT 'AttachmentCount column already exists in EmailProcessingLog table';
END

-- Update ProcessingStatus column to allow 'Skipped' status
-- This is safe to run multiple times
PRINT 'ProcessingStatus column can now store: Success, Failed, Processing, Skipped';

-- Add unique index for email ID duplicate detection if it doesn't exist
-- This must be done after the EmailId column is added
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'EmailId')
   AND NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'IX_EmailProcessingLog_EmailId')
BEGIN
    CREATE UNIQUE INDEX IX_EmailProcessingLog_EmailId ON EmailProcessingLog(EmailId) 
    WHERE EmailId IS NOT NULL AND ProcessingStatus = 'Success';
    PRINT 'Created unique index IX_EmailProcessingLog_EmailId for duplicate detection';
END
ELSE
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'EmailId')
        PRINT 'EmailId column does not exist - index will be created when column is added';
    ELSE
        PRINT 'Unique index IX_EmailProcessingLog_EmailId already exists';
END

PRINT 'EmailProcessingLog migration completed successfully';