CREATE TABLE CsvSensorData (
    Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    VesselName nvarchar(255) NOT NULL,
    GroupCoordinates nvarchar(500) NULL,
    Location nvarchar(1000) NULL,
    SensorTime datetime2(7) NOT NULL,
    Speed decimal(18,2) NULL,
    SeaDepth decimal(18,2) NULL,
    RPM decimal(18,2) NULL,
    WindAngle decimal(18,2) NULL,
    WaterTemperature decimal(18,2) NULL,
    WindSpeed decimal(18,2) NULL,
    OilPressure decimal(18,2) NULL,
    Rudder decimal(18,2) NULL,
    EngineLoad decimal(18,2) NULL,
    COG decimal(18,2) NULL,
    FuelRate decimal(18,2) NULL,
    TotalNM decimal(18,2) NULL,
    EngineHours decimal(18,2) NULL,
    PowerSupply decimal(18,2) NULL,
    EngineAlarmCode nvarchar(50) NULL,
    SmartBoatBattery decimal(18,2) NULL,
    EngineWarningCode nvarchar(50) NULL,
    GSMEngineRunningIndicator nvarchar(50) NULL,
    SourceEmail nvarchar(500) NULL,
    ProcessedDate datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    CreatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE()
);

-- Index for duplicate prevention (vessel + sensor time)
CREATE UNIQUE INDEX IX_CsvSensorData_VesselName_SensorTime ON CsvSensorData(VesselName, SensorTime);

-- Performance index for vessel queries
CREATE INDEX IX_CsvSensorData_VesselName_SensorTime_Desc ON CsvSensorData(VesselName, SensorTime DESC);

-- Index for time-based queries
CREATE INDEX IX_CsvSensorData_SensorTime ON CsvSensorData(SensorTime DESC);