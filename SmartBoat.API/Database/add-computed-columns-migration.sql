-- Migration: Add computed columns to SensorDataPoints table
-- This adds database-level computed columns that automatically extract values from JSON Measurements field

USE SmartBoatAPI;
GO

-- Phase 1: Add basic computed columns for existing fields
ALTER TABLE SensorDataPoints ADD 
    Temperature AS CAST(JSON_VALUE(Measurements, '$.temperature') AS FLOAT),
    Humidity AS CAST(JSON_VALUE(Measurements, '$.humidity') AS FLOAT),
    Speed AS CAST(JSON_VALUE(Measurements, '$.speed') AS FLOAT),
    RPM AS CAST(JSON_VALUE(Measurements, '$.rpm') AS FLOAT);
GO

-- Phase 2: Add missing essential computed columns based on CSV data analysis
ALTER TABLE SensorDataPoints ADD
    Coordinates AS JSON_VALUE(Measurements, '$.coordinates'),
    WindSpeed AS CAST(JSON_VALUE(Measurements, '$.wind_speed') AS FLOAT),
    WindAngle AS CAST(JSON_VALUE(Measurements, '$.wind_angle') AS FLOAT),
    SeaDepth AS CAST(JSON_VALUE(Measurements, '$.sea_depth') AS FLOAT),
    PortRPM AS CAST(JSON_VALUE(Measurements, '$.port_rpm') AS FLOAT),
    StbdRPM AS CAST(JSON_VALUE(Measurements, '$.stbd_rpm') AS FLOAT);
GO

-- Phase 3: Add engine-specific computed columns
ALTER TABLE SensorDataPoints ADD
    PortEngineHours AS CAST(JSON_VALUE(Measurements, '$.port_engine_hours') AS FLOAT),
    StbdEngineHours AS CAST(JSON_VALUE(Measurements, '$.stbd_engine_hours') AS FLOAT),
    PortOilPressure AS CAST(JSON_VALUE(Measurements, '$.port_oil_pressure') AS FLOAT),
    StbdOilPressure AS CAST(JSON_VALUE(Measurements, '$.stbd_oil_pressure') AS FLOAT),
    PortWaterTemp AS CAST(JSON_VALUE(Measurements, '$.port_water_temperature') AS FLOAT),
    StbdWaterTemp AS CAST(JSON_VALUE(Measurements, '$.stbd_water_temperature') AS FLOAT),
    PortFuelRate AS CAST(JSON_VALUE(Measurements, '$.port_fuel_rate') AS FLOAT),
    StbdFuelRate AS CAST(JSON_VALUE(Measurements, '$.stbd_fuel_rate') AS FLOAT);
GO

-- Phase 4: Add system and navigation computed columns
ALTER TABLE SensorDataPoints ADD
    GSMSignal AS CAST(JSON_VALUE(Measurements, '$.gsm_signal') AS FLOAT),
    SensorIdField AS JSON_VALUE(Measurements, '$.sensor_id'),
    EngineAlarmCode AS JSON_VALUE(Measurements, '$.engine_alarm_code'),
    EngineWarningCode AS JSON_VALUE(Measurements, '$.engine_warning_code'),
    PortAlarmCode AS JSON_VALUE(Measurements, '$.port_alarm_code'),
    StbdAlarmCode AS JSON_VALUE(Measurements, '$.stbd_alarm_code'),
    PortWarningCode AS JSON_VALUE(Measurements, '$.port_warning_code'),
    StbdWarningCode AS JSON_VALUE(Measurements, '$.stbd_warning_code');
GO

-- Phase 5: Add indexes on commonly queried computed columns for performance
CREATE INDEX IX_SensorDataPoints_Speed ON SensorDataPoints (Speed) WHERE Speed IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_RPM ON SensorDataPoints (RPM) WHERE RPM IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_PortRPM ON SensorDataPoints (PortRPM) WHERE PortRPM IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_StbdRPM ON SensorDataPoints (StbdRPM) WHERE StbdRPM IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_Temperature ON SensorDataPoints (Temperature) WHERE Temperature IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_WindSpeed ON SensorDataPoints (WindSpeed) WHERE WindSpeed IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_SeaDepth ON SensorDataPoints (SeaDepth) WHERE SeaDepth IS NOT NULL;
GO

PRINT 'Computed columns migration completed successfully!';
PRINT 'All computed columns will now auto-populate from JSON Measurements field';