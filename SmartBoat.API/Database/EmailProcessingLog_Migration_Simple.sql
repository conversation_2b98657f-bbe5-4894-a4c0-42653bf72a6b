-- Simple migration script to add new columns to existing EmailProcessingLog table
USE Smartboat;
GO

-- Add EmailId column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'EmailId')
BEGIN
    ALTER TABLE EmailProcessingLog ADD EmailId nvarchar(255) NULL;
    PRINT 'Added EmailId column to EmailProcessingLog table';
END
ELSE
BEGIN
    PRINT 'EmailId column already exists in EmailProcessingLog table';
END

-- Add AttachmentIds column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'AttachmentIds')
BEGIN
    ALTER TABLE EmailProcessingLog ADD AttachmentIds nvarchar(max) NULL;
    PRINT 'Added AttachmentIds column to EmailProcessingLog table';
END
ELSE
BEGIN
    PRINT 'AttachmentIds column already exists in EmailProcessingLog table';
END

-- Add AttachmentCount column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'AttachmentCount')
BEGIN
    ALTER TABLE EmailProcessingLog ADD AttachmentCount int NOT NULL DEFAULT 0;
    PRINT 'Added AttachmentCount column to EmailProcessingLog table';
END
ELSE
BEGIN
    PRINT 'AttachmentCount column already exists in EmailProcessingLog table';
END

PRINT 'Basic migration completed successfully';