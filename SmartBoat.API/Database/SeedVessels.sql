-- ============================================================================
-- SmartBoat Vessels Seed Script
-- ============================================================================
-- This script seeds sample vessels for the three demo companies
-- Executed by setup-database.sh after tables are created

USE SmartBoat;
GO

PRINT 'Seeding sample vessels...';

-- Get company IDs
DECLARE @AtlanticMaritimeId uniqueidentifier = (SELECT Id FROM Company WHERE Name = 'Atlantic Maritime Solutions - SmartBoat Demo');
DECLARE @PacificCharterId uniqueidentifier = (SELECT Id FROM Company WHERE Name = 'Pacific Charter Services - SmartBoat Demo');
DECLARE @NordicFishingId uniqueidentifier = (SELECT Id FROM Company WHERE Name = 'Nordic Fishing Fleet - SmartBoat Demo');

-- Insert sample vessels
INSERT INTO Vessels (Id, Name, Number, Type, Location, Status, StartDate, EndDate, Image, Onsigners, Offsigners, CompanyId, LastUpdated, Created, Changed)
VALUES
-- Atlantic Maritime Corp vessels (Commercial shipping) - All Mechanical (1)
(NEWID(), 'Atlantic Explorer', 'AMC-001', 1, 'Port of New York', 'Active', '2020-01-15', NULL, NULL, 25, 0, @AtlanticMaritimeId, GETUTCDATE(), GETUTCDATE(), NULL),
(NEWID(), 'Ocean Pioneer', 'AMC-002', 1, 'Port of Baltimore', 'Active', '2019-05-20', NULL, NULL, 22, 0, @AtlanticMaritimeId, GETUTCDATE(), GETUTCDATE(), NULL),
(NEWID(), 'Maritime Guardian', 'AMC-003', 1, 'Port of Miami', 'Maintenance', '2021-03-10', NULL, NULL, 18, 5, @AtlanticMaritimeId, GETUTCDATE(), GETUTCDATE(), NULL),
(NEWID(), 'Coastal Voyager', 'AMC-004', 1, 'Port of Charleston', 'Active', '2022-08-01', NULL, NULL, 20, 0, @AtlanticMaritimeId, GETUTCDATE(), GETUTCDATE(), NULL),

-- Pacific Charter Services vessels (Charter services) - Mix of Sailing (2) and Mechanical (1)
(NEWID(), 'Pacific Dream', 'PCS-101', 2, 'San Diego Marina', 'Active', '2018-06-15', NULL, NULL, 8, 0, @PacificCharterId, GETUTCDATE(), GETUTCDATE(), NULL),
(NEWID(), 'Sunset Cruiser', 'PCS-102', 1, 'Marina del Rey', 'Active', '2019-04-20', NULL, NULL, 6, 0, @PacificCharterId, GETUTCDATE(), GETUTCDATE(), NULL),
(NEWID(), 'Golden Gate', 'PCS-103', 1, 'San Francisco Bay', 'Active', '2020-09-12', NULL, NULL, 12, 0, @PacificCharterId, GETUTCDATE(), GETUTCDATE(), NULL),
(NEWID(), 'Wave Runner', 'PCS-104', 1, 'Long Beach Harbor', 'Maintenance', '2021-11-05', NULL, NULL, 4, 2, @PacificCharterId, GETUTCDATE(), GETUTCDATE(), NULL),

-- Nordic Fishing Fleet vessels (Commercial fishing) - All Mechanical (1)
(NEWID(), 'Nordic Harvester', 'NFF-201', 1, 'Bergen Harbor', 'Active', '2017-03-22', NULL, NULL, 15, 0, @NordicFishingId, GETUTCDATE(), GETUTCDATE(), NULL),
(NEWID(), 'Arctic Explorer', 'NFF-202', 1, 'Tromsø Port', 'Active', '2016-08-14', NULL, NULL, 35, 0, @NordicFishingId, GETUTCDATE(), GETUTCDATE(), NULL),
(NEWID(), 'Sea Hunter', 'NFF-203', 1, 'Stavanger Harbor', 'Active', '2019-12-03', NULL, NULL, 12, 0, @NordicFishingId, GETUTCDATE(), GETUTCDATE(), NULL),
(NEWID(), 'Northern Star', 'NFF-204', 1, 'Ålesund Port', 'Inactive', '2015-05-18', '2024-01-31', NULL, 0, 8, @NordicFishingId, GETUTCDATE(), GETUTCDATE(), NULL);

-- Get count of inserted vessels
DECLARE @VesselCount int = @@ROWCOUNT;

PRINT '============================================================================';
PRINT 'Vessels Seeded Successfully';
PRINT '============================================================================';
PRINT 'Total Vessels Created: ' + CAST(@VesselCount AS nvarchar(10));
PRINT '';
PRINT 'Vessels by Company:';
PRINT '- Atlantic Maritime Corp: 4 vessels (Commercial shipping)';
PRINT '- Pacific Charter Services: 4 vessels (Charter services)';
PRINT '- Nordic Fishing Fleet: 4 vessels (Commercial fishing)';
PRINT '';
PRINT 'Vessel Types: Mechanical (1) = 11 vessels, Sailing (2) = 1 vessel';
PRINT '              Pacific Dream is the only sailing vessel';
PRINT '============================================================================';