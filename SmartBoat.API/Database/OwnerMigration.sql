-- Owner Migration Script
-- This script adds the Owner entity and updates the Vessels table

-- Step 1: Create Owners table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Owners' AND xtype='U')
BEGIN
    CREATE TABLE Owners (
        Id uniqueidentifier NOT NULL PRIMARY KEY,
        Name nvarchar(200) NOT NULL,
        Email nvarchar(254) NULL,
        Phone nvarchar(20) NULL,
        Address nvarchar(500) NULL,
        CompanyId uniqueidentifier NOT NULL,
        Status nvarchar(50) NOT NULL DEFAULT 'Active',
        LastUpdated datetime2(7) NULL,
        Created datetime2(7) NOT NULL,
        Changed datetime2(7) NULL,
        FOREIGN KEY (CompanyId) REFERENCES Company(Id)
    );
    PRINT 'Owners table created successfully.';
END
ELSE
BEGIN
    PRINT 'Owners table already exists.';
END

-- Step 2: Add OwnerId column to Vessels table if it doesn't exist
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Vessels' AND COLUMN_NAME = 'OwnerId')
BEGIN
    ALTER TABLE Vessels
    ADD OwnerId uniqueidentifier NULL;
    
    -- Add foreign key constraint
    ALTER TABLE Vessels
    ADD CONSTRAINT FK_Vessels_Owners FOREIGN KEY (OwnerId) REFERENCES Owners(Id);
    
    PRINT 'OwnerId column added to Vessels table.';
END
ELSE
BEGIN
    PRINT 'OwnerId column already exists in Vessels table.';
END

-- Step 3: Create default owners for existing companies (optional)
-- This creates one owner per company with the company name as the owner name
INSERT INTO Owners (Id, Name, Email, Phone, Address, CompanyId, Status, Created)
SELECT 
    NEWID() as Id,
    c.Name + ' Owner' as Name,
    NULL as Email,
    NULL as Phone,
    c.Location as Address,
    c.Id as CompanyId,
    'Active' as Status,
    GETDATE() as Created
FROM Company c
WHERE NOT EXISTS (SELECT 1 FROM Owners o WHERE o.CompanyId = c.Id);

PRINT 'Default owners created for existing companies.';

-- Step 4: Optionally assign vessels to the first owner of their company
-- This updates existing vessels to have an owner
UPDATE v
SET OwnerId = (
    SELECT TOP 1 o.Id 
    FROM Owners o 
    WHERE o.CompanyId = v.CompanyId
)
FROM Vessels v
WHERE v.OwnerId IS NULL 
AND v.CompanyId IS NOT NULL
AND EXISTS (SELECT 1 FROM Owners o WHERE o.CompanyId = v.CompanyId);

PRINT 'Existing vessels assigned to owners.';

PRINT 'Owner migration completed successfully.';