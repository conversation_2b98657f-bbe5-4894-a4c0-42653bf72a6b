-- ============================================================================
-- Fix Missing Computed Columns in SensorDataPoints Table
-- ============================================================================
-- This script adds the missing computed columns that extract values from JSON Measurements field

USE Smartboat;
GO

PRINT 'Adding missing computed columns to SensorDataPoints table...';

-- Check if computed columns already exist and drop them if they do
IF EXISTS (SELECT * FROM sys.computed_columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'Temperature')
BEGIN
    PRINT 'Computed columns already exist, dropping them first...';
    ALTER TABLE SensorDataPoints DROP COLUMN Temperature, Humidity, Speed, RPM;
END

-- Phase 1: Add basic computed columns
ALTER TABLE SensorDataPoints ADD 
    Temperature AS CAST(JSON_VALUE(Measurements, '$.temperature') AS FLOAT) PERSISTED,
    Humidity AS CAST(JSON_VALUE(Measurements, '$.humidity') AS FLOAT) PERSISTED,
    Speed AS CAST(JSON_VALUE(Measurements, '$.speed') AS FLOAT) PERSISTED,
    RPM AS CAST(JSON_VALUE(Measurements, '$.rpm') AS FLOAT) PERSISTED;
PRINT 'Added basic computed columns: Temperature, Humidity, Speed, RPM';
GO

-- Phase 2: Add navigation computed columns
ALTER TABLE SensorDataPoints ADD
    Coordinates AS JSON_VALUE(Measurements, '$.coordinates') PERSISTED,
    WindSpeed AS CAST(JSON_VALUE(Measurements, '$.wind_speed') AS FLOAT) PERSISTED,
    WindAngle AS CAST(JSON_VALUE(Measurements, '$.wind_angle') AS FLOAT) PERSISTED,
    SeaDepth AS CAST(JSON_VALUE(Measurements, '$.sea_depth') AS FLOAT) PERSISTED,
    PortRPM AS CAST(JSON_VALUE(Measurements, '$.port_rpm') AS FLOAT) PERSISTED,
    StbdRPM AS CAST(JSON_VALUE(Measurements, '$.stbd_rpm') AS FLOAT) PERSISTED;
PRINT 'Added navigation computed columns: Coordinates, WindSpeed, WindAngle, SeaDepth, PortRPM, StbdRPM';
GO

-- Phase 3: Add engine computed columns
ALTER TABLE SensorDataPoints ADD
    PortEngineHours AS CAST(JSON_VALUE(Measurements, '$.port_engine_hours') AS FLOAT) PERSISTED,
    StbdEngineHours AS CAST(JSON_VALUE(Measurements, '$.stbd_engine_hours') AS FLOAT) PERSISTED,
    PortOilPressure AS CAST(JSON_VALUE(Measurements, '$.port_oil_pressure') AS FLOAT) PERSISTED,
    StbdOilPressure AS CAST(JSON_VALUE(Measurements, '$.stbd_oil_pressure') AS FLOAT) PERSISTED,
    PortWaterTemp AS CAST(JSON_VALUE(Measurements, '$.port_water_temperature') AS FLOAT) PERSISTED,
    StbdWaterTemp AS CAST(JSON_VALUE(Measurements, '$.stbd_water_temperature') AS FLOAT) PERSISTED,
    PortFuelRate AS CAST(JSON_VALUE(Measurements, '$.port_fuel_rate') AS FLOAT) PERSISTED,
    StbdFuelRate AS CAST(JSON_VALUE(Measurements, '$.stbd_fuel_rate') AS FLOAT) PERSISTED;
PRINT 'Added engine computed columns: PortEngineHours, StbdEngineHours, PortOilPressure, StbdOilPressure, PortWaterTemp, StbdWaterTemp, PortFuelRate, StbdFuelRate';
GO

-- Phase 4: Add system computed columns (but avoid duplicate GSMSignal)
-- Check if GSMSignal already exists as a regular column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'GSMSignal' AND is_computed = 0)
BEGIN
    ALTER TABLE SensorDataPoints ADD
        SensorIdField AS JSON_VALUE(Measurements, '$.sensor_id') PERSISTED,
        EngineAlarmCode AS JSON_VALUE(Measurements, '$.engine_alarm_code') PERSISTED,
        EngineWarningCode AS JSON_VALUE(Measurements, '$.engine_warning_code') PERSISTED,
        PortAlarmCode AS JSON_VALUE(Measurements, '$.port_alarm_code') PERSISTED,
        StbdAlarmCode AS JSON_VALUE(Measurements, '$.stbd_alarm_code') PERSISTED,
        PortWarningCode AS JSON_VALUE(Measurements, '$.port_warning_code') PERSISTED,
        StbdWarningCode AS JSON_VALUE(Measurements, '$.stbd_warning_code') PERSISTED;
    PRINT 'Added system computed columns (GSMSignal already exists as regular column)';
END
ELSE
BEGIN
    ALTER TABLE SensorDataPoints ADD
        GSMSignalComputed AS CAST(JSON_VALUE(Measurements, '$.gsm_signal') AS FLOAT) PERSISTED,
        SensorIdField AS JSON_VALUE(Measurements, '$.sensor_id') PERSISTED,
        EngineAlarmCode AS JSON_VALUE(Measurements, '$.engine_alarm_code') PERSISTED,
        EngineWarningCode AS JSON_VALUE(Measurements, '$.engine_warning_code') PERSISTED,
        PortAlarmCode AS JSON_VALUE(Measurements, '$.port_alarm_code') PERSISTED,
        StbdAlarmCode AS JSON_VALUE(Measurements, '$.stbd_alarm_code') PERSISTED,
        PortWarningCode AS JSON_VALUE(Measurements, '$.port_warning_code') PERSISTED,
        StbdWarningCode AS JSON_VALUE(Measurements, '$.stbd_warning_code') PERSISTED;
    PRINT 'Added all system computed columns including GSMSignalComputed';
END
GO

-- Phase 5: Add performance indexes on commonly queried computed columns
-- Only create indexes if they don't already exist
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'IX_SensorDataPoints_Speed_Computed')
BEGIN
    CREATE INDEX IX_SensorDataPoints_Speed_Computed ON SensorDataPoints (Speed) WHERE Speed IS NOT NULL;
    PRINT 'Created index on Speed';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'IX_SensorDataPoints_Temperature_Computed')
BEGIN
    CREATE INDEX IX_SensorDataPoints_Temperature_Computed ON SensorDataPoints (Temperature) WHERE Temperature IS NOT NULL;
    PRINT 'Created index on Temperature';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'IX_SensorDataPoints_WindSpeed_Computed')
BEGIN
    CREATE INDEX IX_SensorDataPoints_WindSpeed_Computed ON SensorDataPoints (WindSpeed) WHERE WindSpeed IS NOT NULL;
    PRINT 'Created index on WindSpeed';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'IX_SensorDataPoints_RPM_Computed')
BEGIN
    CREATE INDEX IX_SensorDataPoints_RPM_Computed ON SensorDataPoints (RPM) WHERE RPM IS NOT NULL;
    PRINT 'Created index on RPM';
END

PRINT '============================================================================';
PRINT 'Computed Columns Fix Complete';
PRINT '============================================================================';
PRINT 'All missing computed columns have been added to SensorDataPoints table';
PRINT 'Columns are PERSISTED for optimal performance';
PRINT 'Performance indexes created on commonly queried columns';
PRINT '============================================================================';
