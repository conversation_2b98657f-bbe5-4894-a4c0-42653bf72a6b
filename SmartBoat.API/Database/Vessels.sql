CREATE TABLE Vessels (
    Id uniqueidentifier NOT NULL PRIMARY KEY,
    Name nvarchar(200) NULL,
    Number nvarchar(100) NULL,
    Type int NULL,
    Location nvarchar(200) NULL,
    Status nvarchar(50) NULL,
    StartDate datetime2(7) NULL,
    EndDate datetime2(7) NULL,
    Image nvarchar(500) NULL,
    Onsigners int NULL,
    Offsigners int NULL,
    CompanyId uniqueidentifier NULL,
    OwnerId uniqueidentifier NULL,
    LastUpdated datetime2(7) NULL,
    Created datetime2(7) NOT NULL,
    Changed datetime2(7) NULL,
    FOREIGN KEY (CompanyId) REFERENCES Company(Id),
    FOREIGN KEY (OwnerId) REFERENCES Owners(Id)
);