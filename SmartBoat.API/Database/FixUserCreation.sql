-- ============================================================================
-- Fix User Creation Script
-- ============================================================================
-- This script creates users using existing customer and company data

USE Smartboat;
GO

PRINT 'Creating users with existing customer and company data...';

-- Get role IDs
DECLARE @SuperAdminRoleId uniqueidentifier = '549664C8-DB7A-404A-B2EC-D04D5F8E58D3';
DECLARE @PlatformAdminRoleId uniqueidentifier = '15BAF9D6-7C57-42AA-9459-6C7036567AC2';
DECLARE @CustomerAdminRoleId uniqueidentifier = '63CFBA87-C76C-4459-B8C3-287AF03A7AD4';
DECLARE @CustomerEmployeeRoleId uniqueidentifier = 'A9AE3CD9-9BAF-404A-8678-A951C668DDDC';

-- Get company IDs (using the first occurrence of each company)
DECLARE @Company1Id uniqueidentifier = (SELECT TOP 1 Id FROM Company WHERE Name LIKE 'Atlantic Maritime Solutions - SmartBoat Demo%');
DECLARE @Company2Id uniqueidentifier = (SELECT TOP 1 Id FROM Company WHERE Name LIKE 'Pacific Charter Services - SmartBoat Demo%');
DECLARE @Company3Id uniqueidentifier = (SELECT TOP 1 Id FROM Company WHERE Name LIKE 'Nordic Fishing Fleet - SmartBoat Demo%');

-- Clear existing demo users if any
DELETE FROM Users WHERE Email LIKE '%@smartboat.demo' OR Email LIKE '%@smartboat-demo.com';

-- Platform Users (SmartBoat Internal)
INSERT INTO Users (
    Id, Username, Email, PasswordHash, FirstName, LastName, RoleId, Role, Status, 
    Created, Changed, LastLogin, TwoFactorEnabled, Avatar, Joined, Company, CompanyId, 
    Department, Phone, PhoneNumber, Timezone, Language, Bio, IsDeleted
) VALUES 
-- Super Admin
(NEWID(), 'superadmin', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G', -- "admin123"
 'Alex', 'Rodriguez', @SuperAdminRoleId, 'Super Admin', 'Active', 
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'SmartBoat Inc', NULL,
 'Engineering', '******-0001', '******-0001', 'UTC', 'en', 'Platform founder and system administrator', 0),

-- Platform Admins
(NEWID(), 'support.manager', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Maria', 'Santos', @PlatformAdminRoleId, 'Platform Admin', 'Active', 
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'SmartBoat Inc', NULL,
 'Customer Success', '******-0002', '******-0002', 'EST', 'en', 'Customer success and platform operations manager', 0),

(NEWID(), 'tech.support', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'David', 'Kim', @PlatformAdminRoleId, 'Platform Admin', 'Active', 
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'SmartBoat Inc', NULL,
 'Technical Support', '******-0003', '******-0003', 'PST', 'en', 'Technical support specialist', 0),

-- Atlantic Maritime Solutions Users
(NEWID(), 'j.morrison', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'James', 'Morrison', @CustomerAdminRoleId, 'Customer Admin', 'Active', 
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'Atlantic Maritime Solutions', @Company1Id,
 'Fleet Management', '******-0101', '******-0101', 'EST', 'en', 'Fleet manager with 20+ years maritime experience', 0),

(NEWID(), 'r.thompson', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Robert', 'Thompson', @CustomerEmployeeRoleId, 'Customer Employee', 'Active', 
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'Atlantic Maritime Solutions', @Company1Id,
 'Vessel Operations', '******-0111', '******-0111', 'EST', 'en', 'Senior vessel operator and maintenance specialist', 0),

-- Pacific Charter Services Users
(NEWID(), 's.chen', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Sarah', 'Chen', @CustomerAdminRoleId, 'Customer Admin', 'Active', 
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'Pacific Charter Services', @Company2Id,
 'Business Operations', '******-0102', '******-0102', 'PST', 'en', 'Charter business owner and operations manager', 0),

(NEWID(), 'm.johnson', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Michael', 'Johnson', @CustomerEmployeeRoleId, 'Customer Employee', 'Active', 
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'Pacific Charter Services', @Company2Id,
 'Charter Operations', '******-0121', '******-0121', 'PST', 'en', 'Charter boat captain and tour guide', 0),

-- Nordic Fishing Fleet Users
(NEWID(), 'e.andersen', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Erik', 'Andersen', @CustomerAdminRoleId, 'Customer Admin', 'Active', 
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'Nordic Fishing Fleet', @Company3Id,
 'Fleet Management', '******-0103', '******-0103', 'PST', 'en', 'Fishing fleet owner with family maritime tradition', 0),

(NEWID(), 'o.hansen', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Olaf', 'Hansen', @CustomerEmployeeRoleId, 'Customer Employee', 'Active', 
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'Nordic Fishing Fleet', @Company3Id,
 'Fishing Operations', '******-0131', '******-0131', 'PST', 'en', 'Experienced fishing boat captain', 0);

DECLARE @UserCount int = (SELECT COUNT(*) FROM Users WHERE Email LIKE '%@smartboat.demo' OR Email LIKE '%@smartboat-demo.com');

PRINT '============================================================================';
PRINT 'Users Created Successfully';
PRINT '============================================================================';
PRINT 'Total Users Created: ' + CAST(@UserCount AS nvarchar(10));
PRINT '';
PRINT 'Test Credentials (all users): password = "admin123"';
PRINT '';
PRINT 'Sample Users:';
PRINT '- Super Admin: <EMAIL>';
PRINT '- Platform Admin: <EMAIL>';
PRINT '- Customer Admin: <EMAIL>';
PRINT '- Customer Employee: <EMAIL>';
PRINT '============================================================================';
