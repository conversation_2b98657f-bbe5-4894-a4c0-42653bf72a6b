-- ============================================================================
-- SmartBoat Entity-Specific Permissions Seed Script
-- ============================================================================
-- This script seeds entity-based permissions for the main project entities
-- Executed by setup-database.sh after core tables and authorization module

USE SmartBoat;
GO

PRINT 'Seeding entity-specific permissions...';

-- ============================================================================
-- ENTITY-BASED PERMISSIONS
-- ============================================================================

-- Main entities (from controller analysis)
DECLARE @Entities TABLE (EntityName nvarchar(200));
INSERT INTO @Entities VALUES 
('User'), ('Role'), ('Company'), ('Customer'), ('Vessel'), 
('Sensor'), ('Alert'), ('AuditLog'), ('Notification'), 
('Report'), ('SupportRequest'), ('Feedback');

-- Permission checkers available in authorization module
DECLARE @Checkers TABLE (
    CheckerName nvarchar(200),
    OperationActionId int,
    Suffix nvarchar(50),
    DisplaySuffix nvarchar(50)
);

INSERT INTO @Checkers VALUES
('CreateGenericChecker', 1, 'Create', 'Create'),
('ReadAnyGenericChecker', 2, 'ReadAny', 'Read Any'),
('ReadOwnGenericChecker', 2, 'ReadOwn', 'Read Own'),
('ReadGroupGenericChecker', 2, 'ReadGroup', 'Read Group'),
('UpdateAnyGenericChecker', 3, 'UpdateAny', 'Update Any'),
('UpdateOwnGenericChecker', 3, 'UpdateOwn', 'Update Own'),
('DeleteAnyGenericChecker', 4, 'DeleteAny', 'Delete Any'),
('DeleteOwnGenericChecker', 4, 'DeleteOwn', 'Delete Own');

-- Generate entity-based permissions
INSERT INTO Permissions (Id, Entity, CheckerName, MachineName, DisplayName, OperationActionId, Version, Created, CreatorId)
SELECT 
    NEWID(),
    e.EntityName,
    c.CheckerName,
    e.EntityName + '.' + c.Suffix,
    c.DisplaySuffix + ' ' + e.EntityName,
    c.OperationActionId,
    1,
    GETUTCDATE(),
    NULL
FROM @Entities e
CROSS JOIN @Checkers c
WHERE NOT EXISTS (
    SELECT 1 FROM Permissions p 
    WHERE p.MachineName = e.EntityName + '.' + c.Suffix
);

-- ============================================================================
-- SUMMARY
-- ============================================================================

DECLARE @EntityPermissions int = (
    SELECT COUNT(*) FROM Permissions 
    WHERE Entity NOT IN ('Permission', 'Cache')
);

PRINT '============================================================================';
PRINT 'Entity-Specific Permissions Seeded Successfully';
PRINT '============================================================================';
PRINT 'Entity Permissions Created: ' + CAST(@EntityPermissions AS nvarchar(10));
PRINT '';
PRINT 'Entities with permissions:';
PRINT '- User, Role, Company, Customer, Vessel';
PRINT '- Sensor, Alert, AuditLog, Notification';
PRINT '- Report, SupportRequest, Feedback';
PRINT '';
PRINT 'Each entity has 8 permission types (Create, ReadAny, ReadOwn, ReadGroup,';
PRINT 'UpdateAny, UpdateOwn, DeleteAny, DeleteOwn)';
PRINT '============================================================================';