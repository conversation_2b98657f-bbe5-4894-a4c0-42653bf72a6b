# 🚢 Flexible Sensor Data Examples

## 📊 Sample JSON Measurements

### Engine Sensors
```json
{
  "rpm": 1850,
  "oil_pressure": 45.2,
  "coolant_temp": 82.5,
  "fuel_consumption": 12.8,
  "engine_hours": 1247.5,
  "exhaust_temp": 320.1
}
```

### Environmental Sensors
```json
{
  "temperature": 23.5,
  "humidity": 65.2,
  "pressure": 1013.25,
  "wind": {
    "speed": 15.2,
    "direction": 245,
    "gust": 18.7
  },
  "uv_index": 6.8
}
```

### Navigation Sensors
```json
{
  "speed": 12.5,
  "heading": 245.7,
  "depth": 15.3,
  "gps": {
    "latitude": 37.7749,
    "longitude": -122.4194,
    "altitude": 5.2,
    "accuracy": 1.5
  },
  "course_over_ground": 248.2
}
```

### Electrical System
```json
{
  "battery_voltage": 12.4,
  "current": 8.7,
  "power_consumption": 107.88,
  "solar_panel_voltage": 18.5,
  "alternator_output": 14.2,
  "battery_temperature": 25.3
}
```

### Safety & Security
```json
{
  "bilge_water_level": 2.1,
  "fire_detected": false,
  "co2_level": 450,
  "motion_detected": true,
  "door_status": "closed",
  "anchor_dragging": false
}
```

### Custom/Future Sensors
```json
{
  "wave_height": 2.1,
  "water_quality": {
    "ph": 8.2,
    "dissolved_oxygen": 7.8,
    "turbidity": 12.5
  },
  "fish_finder": {
    "depth": 45.2,
    "fish_detected": true,
    "bottom_type": "rocky"
  },
  "cabin_co2": 420,
  "refrigerator_temp": 4.2,
  "water_tank_level": 75.8
}
```

## 🔧 API Usage Examples

### Create Sensor Data Point
```http
POST /api/sensordatapoint/create
{
  "sensorId": "123e4567-e89b-12d3-a456-426614174000",
  "measurements": {
    "temperature": 23.5,
    "humidity": 65.2,
    "rpm": 1850,
    "speed": 12.5,
    "gps": {
      "latitude": 37.7749,
      "longitude": -122.4194
    }
  },
  "qualityScore": 0.95,
  "source": "main_sensor_unit"
}
```

### Query Recent Engine Data
```http
POST /api/sensordatapoint/list
{
  "sensorId": "engine-sensor-id",
  "startTime": "2025-07-17T00:00:00Z",
  "measurementTypes": ["rpm", "oil_pressure", "coolant_temp"],
  "pageLimit": 50
}
```

### Query All Sensors for a Vessel
```sql
-- Raw SQL example for analytics
SELECT 
    s.Name as SensorName,
    sdp.Timestamp,
    JSON_VALUE(sdp.Measurements, '$.temperature') as Temperature,
    JSON_VALUE(sdp.Measurements, '$.rpm') as RPM,
    JSON_VALUE(sdp.Measurements, '$.speed') as Speed
FROM SensorDataPoints sdp
JOIN Sensors s ON sdp.SensorId = s.Id
WHERE s.VesselId = 'vessel-guid'
  AND sdp.Timestamp > DATEADD(hour, -24, GETUTCDATE())
ORDER BY sdp.Timestamp DESC
```

## 🚀 C# Code Examples

### Creating Data Points
```csharp
// Simple temperature reading
var tempReading = new CreateSensorDataPointDto
{
    SensorId = sensorId,
    Measurements = new Dictionary<string, object>
    {
        ["temperature"] = 23.5f,
        ["humidity"] = 65.2f
    }
};

// Complex engine data
var engineReading = new CreateSensorDataPointDto
{
    SensorId = engineSensorId,
    QualityScore = 0.98f,
    Source = "engine_control_unit"
};
engineReading.AddRPM(1850);
engineReading.AddTemperature(82.5f);
engineReading.Measurements["oil_pressure"] = 45.2f;
engineReading.Measurements["fuel_consumption"] = 12.8f;

// GPS + environmental data
var navReading = new CreateSensorDataPointDto
{
    SensorId = navSensorId
};
navReading.AddPosition(37.7749f, -122.4194f);
navReading.AddSpeed(12.5f);
navReading.AddWind(15.2f, 245f);
```

### Reading Data Points
```csharp
// Access common measurements via computed columns
var temperature = dataPoint.Temperature; // Fast, indexed
var speed = dataPoint.Speed; // Fast, indexed

// Access any measurement from JSON
var oilPressure = dataPoint.GetMeasurement<float>("oil_pressure");
var gpsData = dataPoint.GetPosition();
var windData = dataPoint.GetWind();

// Access complex nested data
if (dataPoint.Measurements.TryGetValue("engine", out var engineData))
{
    var engine = (Dictionary<string, object>)engineData;
    var hours = Convert.ToSingle(engine["hours"]);
}
```

## 🎯 Benefits of This Approach

✅ **Unlimited Flexibility**: Add any sensor type without schema changes  
✅ **Performance**: Common measurements are indexed for fast queries  
✅ **Backward Compatible**: Existing temperature/humidity queries still work  
✅ **Future-Proof**: Ready for IoT evolution and new sensor technologies  
✅ **Analytics-Ready**: JSON queries in SQL Server are powerful and efficient  
✅ **Type Safety**: Helper methods provide compile-time checking for common measurements