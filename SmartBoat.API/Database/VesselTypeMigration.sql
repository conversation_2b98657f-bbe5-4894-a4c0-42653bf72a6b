-- ============================================================================
-- Vessel Type Migration Script
-- ============================================================================
-- This script migrates existing string-based Type values to integer enum values
-- Enum Values: Mechanical = 1, Sailing = 2
-- Works for both new and existing deployments

PRINT 'Starting Vessel Type migration check...';

-- Check if Vessels table exists
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Vessels')
BEGIN
    PRINT 'Vessels table does not exist yet, skipping migration';
    RETURN;
END

-- Check if Type column is already int (migration already done or new deployment)
DECLARE @TypeDataType NVARCHAR(50);
SELECT @TypeDataType = DATA_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Vessels' AND COLUMN_NAME = 'Type';

IF @TypeDataType = 'int'
BEGIN
    PRINT 'Type column is already int, migration completed previously or this is a new deployment';
    RETURN;
END

-- Check if there are any vessels to migrate
DECLARE @VesselCount INT;
SELECT @VesselCount = COUNT(*) FROM Vessels;

IF @VesselCount = 0
BEGIN
    PRINT 'No vessels found, performing schema-only migration';
    -- Just change the column type for empty table
    ALTER TABLE Vessels ALTER COLUMN Type int NULL;
    PRINT 'Changed Type column to int for empty table';
    RETURN;
END

PRINT 'Found vessels with string Type values, performing data migration...';

-- Add temporary column to store the new integer values
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Vessels') AND name = 'TypeTemp')
BEGIN
    ALTER TABLE Vessels ADD TypeTemp int NULL;
    PRINT 'Added temporary TypeTemp column';
END

-- Map existing string values to enum integers
-- Most vessels are mechanical by default (1), sailing vessels get (2)
UPDATE Vessels 
SET TypeTemp = CASE 
    WHEN Type LIKE '%Sailing%' OR Type LIKE '%Sail%' OR Type LIKE '%Yacht%' THEN 2  -- Sailing
    ELSE 1  -- Mechanical (default for all others)
END;

PRINT 'Mapped existing Type values to enum integers';

-- Show the mapping results
PRINT 'Migration mapping results:';
SELECT 
    Type as OriginalType, 
    TypeTemp as NewEnumValue,
    CASE TypeTemp 
        WHEN 1 THEN 'Mechanical'
        WHEN 2 THEN 'Sailing'
        ELSE 'Unknown'
    END as EnumName,
    COUNT(*) as Count
FROM Vessels 
GROUP BY Type, TypeTemp
ORDER BY TypeTemp, Type;

-- Drop the old Type column
ALTER TABLE Vessels DROP COLUMN Type;
PRINT 'Dropped old Type column';

-- Rename TypeTemp to Type
EXEC sp_rename 'Vessels.TypeTemp', 'Type', 'COLUMN';
PRINT 'Renamed TypeTemp column to Type';

PRINT '============================================================================';
PRINT 'Vessel Type Migration Completed Successfully';
PRINT '============================================================================';