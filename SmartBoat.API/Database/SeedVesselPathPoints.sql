-- ============================================================================
-- SmartBoat Vessel Path Points Seed Script
-- ============================================================================
-- This script seeds sample path points for demo vessels
-- Executed by setup-database.sh after vessels are created

USE SmartBoat;
GO

PRINT 'Seeding sample vessel path points...';

-- Get vessel IDs for path point generation
DECLARE @VesselIds TABLE (Id uniqueidentifier, Name nvarchar(255), Type nvarchar(100));
INSERT INTO @VesselIds (Id, Name, Type)
SELECT Id, Name, Type FROM Vessels WHERE Name IN (
    'Atlantic Explorer', 'Ocean Pioneer', 'Maritime Guardian', 'Coastal Voyager',
    'Pacific Dream', 'Sunset Cruiser', 'Golden Gate', 'Wave Runner',
    'Nordic Harvester', 'Arctic Explorer', 'Sea Hunter', 'Northern Star'
);

-- Generate path points for each vessel
DECLARE @VesselId uniqueidentifier;
DECLARE @VesselName nvarchar(255);
DECLARE @VesselType nvarchar(100);
DECLARE @Counter int = 1;
DECLARE @PathCounter int;
DECLARE @BaseTime datetime = DATEADD(day, -7, GETUTCDATE()); -- Start from 7 days ago

DECLARE vessel_cursor CURSOR FOR
SELECT Id, Name, Type FROM @VesselIds;

OPEN vessel_cursor;
FETCH NEXT FROM vessel_cursor INTO @VesselId, @VesselName, @VesselType;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @PathCounter = 1;
    
    -- Generate different path patterns based on vessel type
    IF @VesselType IN ('Container Ship', 'Bulk Carrier', 'Tanker', 'General Cargo')
    BEGIN
        -- Commercial shipping route: New York to Miami
        INSERT INTO VesselPathPoints (Id, VesselId, Lat, Lng, Timestamp, Location, Created, Changed)
        VALUES
        (NEWID(), @VesselId, 40.7128, -74.0060, DATEADD(hour, @PathCounter * 6, @BaseTime), 'Port of New York', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 39.2904, -76.6122, DATEADD(hour, (@PathCounter + 1) * 6, @BaseTime), 'Chesapeake Bay', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 36.8485, -75.9779, DATEADD(hour, (@PathCounter + 2) * 6, @BaseTime), 'Virginia Beach', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 33.7490, -78.8707, DATEADD(hour, (@PathCounter + 3) * 6, @BaseTime), 'Off Cape Fear', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 32.0835, -81.0998, DATEADD(hour, (@PathCounter + 4) * 6, @BaseTime), 'Savannah Waters', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 30.3322, -81.6557, DATEADD(hour, (@PathCounter + 5) * 6, @BaseTime), 'Jacksonville', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 28.5383, -80.7821, DATEADD(hour, (@PathCounter + 6) * 6, @BaseTime), 'Cape Canaveral', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 25.7617, -80.1918, DATEADD(hour, (@PathCounter + 7) * 6, @BaseTime), 'Port of Miami', GETUTCDATE(), GETUTCDATE());
    END
    ELSE IF @VesselType IN ('Luxury Yacht', 'Charter Boat', 'Tourist Vessel', 'Sport Fishing')
    BEGIN
        -- Pacific coast leisure route: San Diego to San Francisco
        INSERT INTO VesselPathPoints (Id, VesselId, Lat, Lng, Timestamp, Location, Created, Changed)
        VALUES
        (NEWID(), @VesselId, 32.7157, -117.1611, DATEADD(hour, @PathCounter * 4, @BaseTime), 'San Diego Bay', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 33.4255, -117.6110, DATEADD(hour, (@PathCounter + 1) * 4, @BaseTime), 'Newport Beach', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 34.0522, -118.2437, DATEADD(hour, (@PathCounter + 2) * 4, @BaseTime), 'Los Angeles Harbor', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 34.4208, -119.6982, DATEADD(hour, (@PathCounter + 3) * 4, @BaseTime), 'Santa Barbara Channel', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 35.2828, -120.8597, DATEADD(hour, (@PathCounter + 4) * 4, @BaseTime), 'Morro Bay', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 36.6002, -121.8947, DATEADD(hour, (@PathCounter + 5) * 4, @BaseTime), 'Monterey Bay', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 37.7749, -122.4194, DATEADD(hour, (@PathCounter + 6) * 4, @BaseTime), 'San Francisco Bay', GETUTCDATE(), GETUTCDATE());
    END
    ELSE IF @VesselType IN ('Fishing Trawler', 'Factory Ship', 'Purse Seiner', 'Longline Vessel')
    BEGIN
        -- Nordic fishing route: Norwegian coast
        INSERT INTO VesselPathPoints (Id, VesselId, Lat, Lng, Timestamp, Location, Created, Changed)
        VALUES
        (NEWID(), @VesselId, 60.3913, 5.3221, DATEADD(hour, @PathCounter * 8, @BaseTime), 'Bergen Harbor', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 61.2181, 5.0332, DATEADD(hour, (@PathCounter + 1) * 8, @BaseTime), 'Sognefjord', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 62.4720, 6.1492, DATEADD(hour, (@PathCounter + 2) * 8, @BaseTime), 'Ålesund Waters', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 63.4305, 10.3951, DATEADD(hour, (@PathCounter + 3) * 8, @BaseTime), 'Trondheim Fjord', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 68.7984, 16.0405, DATEADD(hour, (@PathCounter + 4) * 8, @BaseTime), 'Lofoten Islands', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 69.6492, 18.9553, DATEADD(hour, (@PathCounter + 5) * 8, @BaseTime), 'Tromsø Port', GETUTCDATE(), GETUTCDATE()),
        (NEWID(), @VesselId, 70.6632, 23.6816, DATEADD(hour, (@PathCounter + 6) * 8, @BaseTime), 'Nordkapp', GETUTCDATE(), GETUTCDATE());
    END
    
    SET @Counter = @Counter + 1;
    FETCH NEXT FROM vessel_cursor INTO @VesselId, @VesselName, @VesselType;
END

CLOSE vessel_cursor;
DEALLOCATE vessel_cursor;

-- Get count of inserted path points
DECLARE @PathPointCount int = (SELECT COUNT(*) FROM VesselPathPoints);

PRINT '============================================================================';
PRINT 'Vessel Path Points Seeded Successfully';
PRINT '============================================================================';
PRINT 'Total Path Points Created: ' + CAST(@PathPointCount AS nvarchar(10));
PRINT '';
PRINT 'Path Points by Vessel Type:';
PRINT '- Commercial Ships: Trans-Atlantic shipping routes';
PRINT '- Charter/Leisure: Pacific coast recreational routes';
PRINT '- Fishing Vessels: Norwegian coastal fishing routes';
PRINT '';
PRINT 'Path Points represent realistic maritime routes with:';
PRINT '- GPS coordinates (Lat/Lng)';
PRINT '- Timestamps over 7-day period';
PRINT '- Location names for major waypoints';
PRINT '============================================================================';