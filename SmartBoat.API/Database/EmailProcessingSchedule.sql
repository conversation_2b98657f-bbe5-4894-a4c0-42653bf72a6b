-- Email Processing Schedule Configuration Table
-- Stores user preferences for scheduled email processing

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmailProcessingSchedule' AND xtype='U')
BEGIN
    CREATE TABLE EmailProcessingSchedule (
        Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
        UserId uniqueidentifier NULL, -- NULL for global/system-wide schedule
        IsEnabled bit NOT NULL DEFAULT 0,
        ScheduleType nvarchar(50) NOT NULL DEFAULT 'Daily', -- Future: 'Daily', 'Weekly', 'Custom'
        CronExpression nvarchar(100) NOT NULL DEFAULT '0 0 0 * * ?', -- Daily at midnight
        TimeZone nvarchar(100) NOT NULL DEFAULT 'Europe/Athens',
        DaysBack int NOT NULL DEFAULT 7, -- How many days back to process emails
        ForceReprocess bit NOT NULL DEFAULT 0, -- Whether to reprocess already processed emails
        Description nvarchar(500) NULL,
        CreatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
        CreatedBy nvarchar(255) NULL,
        UpdatedBy nvarchar(255) NULL
    );

    -- Create unique index to ensure one schedule per user (or one global schedule)
    CREATE UNIQUE INDEX IX_EmailProcessingSchedule_UserId 
    ON EmailProcessingSchedule (UserId) 
    WHERE UserId IS NOT NULL;

    -- Allow only one global schedule (where UserId IS NULL)
    CREATE UNIQUE INDEX IX_EmailProcessingSchedule_Global 
    ON EmailProcessingSchedule (IsEnabled) 
    WHERE UserId IS NULL;

    -- Insert default global schedule (disabled by default)
    INSERT INTO EmailProcessingSchedule (
        Id,
        UserId,
        IsEnabled,
        ScheduleType,
        CronExpression,
        TimeZone,
        DaysBack,
        ForceReprocess,
        Description,
        CreatedAt,
        UpdatedAt,
        CreatedBy
    ) VALUES (
        NEWID(),
        NULL, -- Global schedule
        0, -- Disabled by default
        'Daily',
        '0 0 0 * * ?', -- Daily at midnight
        'Europe/Athens',
        7,
        0,
        'Default daily email processing schedule at midnight Greece time',
        GETUTCDATE(),
        GETUTCDATE(),
        'SYSTEM'
    );

    PRINT 'EmailProcessingSchedule table created successfully with default global schedule'
END
ELSE
BEGIN
    PRINT 'EmailProcessingSchedule table already exists'
END