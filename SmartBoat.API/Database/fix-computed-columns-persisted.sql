-- Migration: Fix computed columns to be persisted for better performance and indexing
-- This recreates the computed columns as PERSISTED which stores the computed values physically

USE Smartboat;
GO

-- Drop existing computed columns first (they exist but are not persisted)
IF EXISTS (SELECT * FROM sys.computed_columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'Temperature')
BEGIN
    ALTER TABLE SensorDataPoints DROP COLUMN Temperature, Humidity, Speed, RPM;
    PRINT 'Dropped existing non-persisted basic computed columns';
END

IF EXISTS (SELECT * FROM sys.computed_columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'Coordinates')
BEGIN
    ALTER TABLE SensorDataPoints DROP COLUMN Coordinates, WindSpeed, WindAngle, SeaDepth, PortRPM, StbdRPM;
    PRINT 'Dropped existing non-persisted navigation computed columns';
END

IF EXISTS (SELECT * FROM sys.computed_columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'PortEngineHours')
BEGIN
    ALTER TABLE SensorDataPoints DROP COLUMN PortEngineHours, StbdEngineHours, PortOilPressure, StbdOilPressure, PortWaterTemp, StbdWaterTemp, PortFuelRate, StbdFuelRate;
    PRINT 'Dropped existing non-persisted engine computed columns';
END

IF EXISTS (SELECT * FROM sys.computed_columns WHERE object_id = OBJECT_ID('SensorDataPoints') AND name = 'GSMSignal')
BEGIN
    ALTER TABLE SensorDataPoints DROP COLUMN GSMSignal, SensorIdField, EngineAlarmCode, EngineWarningCode, PortAlarmCode, StbdAlarmCode, PortWarningCode, StbdWarningCode;
    PRINT 'Dropped existing non-persisted system computed columns';
END
GO

-- Re-create computed columns as PERSISTED for better performance
ALTER TABLE SensorDataPoints ADD 
    Temperature AS CAST(JSON_VALUE(Measurements, '$.temperature') AS FLOAT) PERSISTED,
    Humidity AS CAST(JSON_VALUE(Measurements, '$.humidity') AS FLOAT) PERSISTED,
    Speed AS CAST(JSON_VALUE(Measurements, '$.speed') AS FLOAT) PERSISTED,
    RPM AS CAST(JSON_VALUE(Measurements, '$.rpm') AS FLOAT) PERSISTED;
PRINT 'Added persisted basic computed columns';
GO

ALTER TABLE SensorDataPoints ADD
    Coordinates AS JSON_VALUE(Measurements, '$.coordinates') PERSISTED,
    WindSpeed AS CAST(JSON_VALUE(Measurements, '$.wind_speed') AS FLOAT) PERSISTED,
    WindAngle AS CAST(JSON_VALUE(Measurements, '$.wind_angle') AS FLOAT) PERSISTED,
    SeaDepth AS CAST(JSON_VALUE(Measurements, '$.sea_depth') AS FLOAT) PERSISTED,
    PortRPM AS CAST(JSON_VALUE(Measurements, '$.port_rpm') AS FLOAT) PERSISTED,
    StbdRPM AS CAST(JSON_VALUE(Measurements, '$.stbd_rpm') AS FLOAT) PERSISTED;
PRINT 'Added persisted navigation computed columns';
GO

ALTER TABLE SensorDataPoints ADD
    PortEngineHours AS CAST(JSON_VALUE(Measurements, '$.port_engine_hours') AS FLOAT) PERSISTED,
    StbdEngineHours AS CAST(JSON_VALUE(Measurements, '$.stbd_engine_hours') AS FLOAT) PERSISTED,
    PortOilPressure AS CAST(JSON_VALUE(Measurements, '$.port_oil_pressure') AS FLOAT) PERSISTED,
    StbdOilPressure AS CAST(JSON_VALUE(Measurements, '$.stbd_oil_pressure') AS FLOAT) PERSISTED,
    PortWaterTemp AS CAST(JSON_VALUE(Measurements, '$.port_water_temperature') AS FLOAT) PERSISTED,
    StbdWaterTemp AS CAST(JSON_VALUE(Measurements, '$.stbd_water_temperature') AS FLOAT) PERSISTED,
    PortFuelRate AS CAST(JSON_VALUE(Measurements, '$.port_fuel_rate') AS FLOAT) PERSISTED,
    StbdFuelRate AS CAST(JSON_VALUE(Measurements, '$.stbd_fuel_rate') AS FLOAT) PERSISTED;
PRINT 'Added persisted engine computed columns';
GO

ALTER TABLE SensorDataPoints ADD
    GSMSignal AS CAST(JSON_VALUE(Measurements, '$.gsm_signal') AS FLOAT) PERSISTED,
    SensorIdField AS JSON_VALUE(Measurements, '$.sensor_id') PERSISTED,
    EngineAlarmCode AS JSON_VALUE(Measurements, '$.engine_alarm_code') PERSISTED,
    EngineWarningCode AS JSON_VALUE(Measurements, '$.engine_warning_code') PERSISTED,
    PortAlarmCode AS JSON_VALUE(Measurements, '$.port_alarm_code') PERSISTED,
    StbdAlarmCode AS JSON_VALUE(Measurements, '$.stbd_alarm_code') PERSISTED,
    PortWarningCode AS JSON_VALUE(Measurements, '$.port_warning_code') PERSISTED,
    StbdWarningCode AS JSON_VALUE(Measurements, '$.stbd_warning_code') PERSISTED;
PRINT 'Added persisted system computed columns';
GO

-- Now add performance indexes on the persisted computed columns
CREATE INDEX IX_SensorDataPoints_Speed ON SensorDataPoints (Speed) WHERE Speed IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_WindSpeed ON SensorDataPoints (WindSpeed) WHERE WindSpeed IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_SeaDepth ON SensorDataPoints (SeaDepth) WHERE SeaDepth IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_PortRPM ON SensorDataPoints (PortRPM) WHERE PortRPM IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_Temperature ON SensorDataPoints (Temperature) WHERE Temperature IS NOT NULL;
CREATE INDEX IX_SensorDataPoints_Coordinates ON SensorDataPoints (Coordinates) WHERE Coordinates IS NOT NULL;
PRINT 'Created performance indexes on persisted computed columns';
GO

PRINT 'Persisted computed columns migration completed successfully!';
PRINT 'All computed columns are now stored physically and indexed for optimal performance';