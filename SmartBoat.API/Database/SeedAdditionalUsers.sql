-- ============================================================================
-- SmartBoat Additional Users Seed Script
-- ============================================================================
-- This script is for adding individual users without affecting demo data
-- Use this when you need to add users manually without destroying vessels/sensors

USE SmartBoat;
GO

PRINT 'Adding additional users to SmartBoat platform...';

-- Get existing role IDs
DECLARE @SuperAdminRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Super Admin');
DECLARE @PlatformAdminRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Platform Admin');
DECLARE @CustomerAdminRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Customer Admin');
DECLARE @CustomerEmployeeRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Customer Employee');

-- Check if roles exist
IF @SuperAdminRoleId IS NULL OR @PlatformAdminRoleId IS NULL OR @CustomerAdminRoleId IS NULL OR @CustomerEmployeeRoleId IS NULL
BEGIN
    PRINT 'ERROR: Required roles not found. Please run SeedUsersAndRoles.sql first to create the base roles.';
    RETURN;
END

-- ============================================================================
-- Add individual users here (replace with actual user data)
-- ============================================================================

-- Example: Add a new Platform Admin
-- Uncomment and modify as needed:
/*
INSERT INTO Users (
    Id, Username, Email, PasswordHash, FirstName, LastName, RoleId, Role, Status, 
    Created, Changed, LastLogin, TwoFactorEnabled, Avatar, Joined, Company, CompanyId, 
    Department, Phone, PhoneNumber, Timezone, Language, Bio, IsDeleted
) VALUES 
(NEWID(), 'new.admin', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G', -- "admin123"
 'New', 'Admin', @PlatformAdminRoleId, 'Platform Admin', 'Active', 
 GETUTCDATE(), NULL, NULL, 0, NULL, GETUTCDATE(), 'SmartBoat Inc', NULL,
 'Operations', '******-9999', '******-9999', 'UTC', 'en', 'Additional platform administrator', 0);
*/

-- Example: Add a new Customer (Company)
-- You'll need to create the customer and company first, then users
/*
-- 1. Create Customer
DECLARE @NewCustomerId uniqueidentifier = NEWID();
INSERT INTO Customer (Id, Name, ContactPerson, Email, Phone, Status, LastActive, Created, Changed) VALUES
(@NewCustomerId, 'New Maritime Company', 'John Doe', '<EMAIL>', '******-8888', 'Active', GETUTCDATE(), GETUTCDATE(), NULL);

-- 2. Create Company
DECLARE @NewCompanyId uniqueidentifier = NEWID();
INSERT INTO Company (Id, Name, Location, Industry, Status, CustomerId, Created, Changed) VALUES
(@NewCompanyId, 'New Maritime Company', 'New York, NY', 'Shipping', 'Active', @NewCustomerId, GETUTCDATE(), NULL);

-- 3. Create Customer Admin
INSERT INTO Users (
    Id, Username, Email, PasswordHash, FirstName, LastName, RoleId, Role, Status, 
    Created, Changed, LastLogin, TwoFactorEnabled, Avatar, Joined, Company, CompanyId, 
    Department, Phone, PhoneNumber, Timezone, Language, Bio, IsDeleted
) VALUES 
(NEWID(), 'j.doe', '<EMAIL>', 
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G', -- "admin123"
 'John', 'Doe', @CustomerAdminRoleId, 'Customer Admin', 'Active', 
 GETUTCDATE(), NULL, NULL, 1, NULL, GETUTCDATE(), 'New Maritime Company', @NewCompanyId,
 'Management', '******-8888', '******-8888', 'EST', 'en', 'Company owner and fleet manager', 0);
*/

PRINT 'Additional users script completed.';
PRINT 'NOTE: This is a template script. Uncomment and modify the examples above to add actual users.';
PRINT 'This script preserves existing demo data (vessels, sensors) while adding new users.';