CREATE TABLE AuthTokens (
    Id uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    AccessToken nvarchar(max) NOT NULL,
    RefreshToken nvarchar(max) NULL,
    ExpiresAt datetime2(7) NOT NULL,
    RefreshTokenIssuedAt datetime2(7) NULL,
    TokenType nvarchar(50) NOT NULL DEFAULT 'Bearer',
    Scope nvarchar(500) NULL DEFAULT 'User.Read Mail.Read offline_access',
    CreatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2(7) NOT NULL DEFAULT GETUTCDATE()
);

-- Only one token record should exist at a time
CREATE UNIQUE INDEX IX_AuthTokens_Single_Record ON AuthTokens(Id) WHERE Id IS NOT NULL;