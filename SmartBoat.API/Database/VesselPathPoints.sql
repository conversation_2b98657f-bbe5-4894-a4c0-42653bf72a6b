-- VesselPathPoints table for tracking vessel location history
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'VesselPathPoints')
BEGIN
    CREATE TABLE VesselPathPoints (
        Id UNIQUEIDENTIFIER DEFAULT NEWID() PRIMARY KEY,
        VesselId UNIQUEIDENTIFIER NULL,
        Lat REAL NULL,
        Lng REAL NULL,
        Timestamp DATETIME NULL,
        Location NVARCHAR(255) NULL,
        Created DATETIME DEFAULT GETDATE(),
        Changed DATETIME DEFAULT GETDATE(),
        CONSTRAINT FK_VesselPathPoints_Vessels FOREIGN KEY (VesselId) REFERENCES Vessels(Id) ON DELETE CASCADE
    );
END