-- Add unique index for EmailId duplicate detection
USE Smartboat;
GO

SET QUOTED_IDENTIFIER ON;
SET ANSI_NULLS ON;
GO

-- Add unique index for email ID duplicate detection if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('EmailProcessingLog') AND name = 'IX_EmailProcessingLog_EmailId')
BEGIN
    CREATE UNIQUE INDEX IX_EmailProcessingLog_EmailId ON EmailProcessingLog(EmailId) 
    WHERE EmailId IS NOT NULL AND ProcessingStatus = 'Success';
    PRINT 'Created unique index IX_EmailProcessingLog_EmailId for duplicate detection';
END
ELSE
BEGIN
    PRINT 'Unique index IX_EmailProcessingLog_EmailId already exists';
END

PRINT 'Index creation completed successfully';