-- ============================================================================
-- Test Computed Columns in SensorDataPoints Table
-- ============================================================================
-- This script tests that the computed columns are working correctly

USE Smartboat;
GO

PRINT 'Testing computed columns in SensorDataPoints table...';

-- Get a sensor ID to use for testing
DECLARE @TestSensorId uniqueidentifier = (SELECT TOP 1 Id FROM Sensors);

IF @TestSensorId IS NULL
BEGIN
    PRINT 'No sensors found in database. Cannot test computed columns.';
    RETURN;
END

-- Insert a test sensor data point with JSON measurements
DECLARE @TestId uniqueidentifier = NEWID();
DECLARE @TestMeasurements NVARCHAR(MAX) = N'{
    "temperature": 25.5,
    "humidity": 65.2,
    "speed": 12.8,
    "rpm": 1850,
    "port_rpm": 1820,
    "stbd_rpm": 1880,
    "wind_speed": 8.5,
    "wind_angle": 45.0,
    "sea_depth": 15.2,
    "coordinates": "40.7128,-74.0060"
}';

INSERT INTO SensorDataPoints (
    Id, SensorId, Timestamp, TimestampUnix, Measurements, QualityScore, Source, Created
) VALUES (
    @TestId, @TestSensorId, GETUTCDATE(), DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()), 
    @TestMeasurements, 1.0, 'Test', GETUTCDATE()
);

PRINT 'Inserted test sensor data point with JSON measurements';

-- Query the computed columns to verify they work
SELECT 
    Id,
    Temperature,
    Humidity,
    Speed,
    RPM,
    PortRPM,
    StbdRPM,
    WindSpeed,
    WindAngle,
    SeaDepth,
    Coordinates,
    Measurements
FROM SensorDataPoints 
WHERE Id = @TestId;

PRINT 'Computed columns test completed successfully!';
PRINT 'If you see values above (not NULL), the computed columns are working correctly.';

-- Clean up the test data
DELETE FROM SensorDataPoints WHERE Id = @TestId;
PRINT 'Test data cleaned up.';
