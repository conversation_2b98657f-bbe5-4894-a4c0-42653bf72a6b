CREATE TABLE SensorDataPoints (
    Id uniqueidentifier NOT NULL PRIMARY KEY,
    SensorId uniqueidentifier NOT NULL,
    Timestamp datetime2(7) NOT NULL,
    TimestampUnix bigint NOT NULL,
    
    -- JSON field for unlimited sensor measurements
    Measurements nvarchar(max) NOT NULL CHECK (ISJSON(Measurements) > 0),
    
    -- Computed columns for common queries (indexed for performance)
    Temperature AS CAST(JSON_VALUE(Measurements, '$.temperature') AS float),
    Humidity AS CAST(JSON_VALUE(Measurements, '$.humidity') AS float),
    Speed AS CAST(JSON_VALUE(Measurements, '$.speed') AS float),
    RPM AS CAST(JSON_VALUE(Measurements, '$.rpm') AS float),
    
    -- Data quality and metadata
    QualityScore float NULL DEFAULT 1.0,
    Source nvarchar(100) NULL,
    
    Created datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    Changed datetime2(7) NULL,
    
    -- Foreign key constraint
    CONSTRAINT FK_SensorDataPoints_Sensors FOREIGN KEY (SensorId) REFERENCES Sensors(Id) ON DELETE CASCADE
);

-- Performance indexes for time-series queries
CREATE INDEX IX_SensorDataPoints_SensorId_Timestamp ON SensorDataPoints(SensorId, Timestamp DESC);
CREATE INDEX IX_SensorDataPoints_Timestamp ON SensorDataPoints(Timestamp DESC);

-- Additional indexes can be created later based on query patterns
-- Note: SQL Server has limitations on filtered indexes with JSON functions
-- For optimal JSON querying, consider using computed columns with indexes if needed