-- Migration script to add unique constraints to Customer table
-- This will prevent duplicate names and emails at the database level

-- Add unique constraint for customer names
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Customer') AND name = 'UK_Customer_Name')
BEGIN
    ALTER TABLE Customer ADD CONSTRAINT UK_Customer_Name UNIQUE (Name);
    PRINT 'Added unique constraint UK_Customer_Name to Customer table';
END
ELSE
BEGIN
    PRINT 'Unique constraint UK_Customer_Name already exists';
END

-- Add unique constraint for customer emails
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Customer') AND name = 'UK_Customer_Email')
BEGIN
    ALTER TABLE Customer ADD CONSTRAINT UK_Customer_Email UNIQUE (Email);
    PRINT 'Added unique constraint UK_Customer_Email to Customer table';
END
ELSE
BEGIN
    PRINT 'Unique constraint UK_Customer_Email already exists';
END

-- Check for existing duplicate names before adding constraint
DECLARE @duplicateNames INT;
SELECT @duplicateNames = COUNT(*)
FROM (
    SELECT Name, COUNT(*) as cnt
    FROM Customer 
    WHERE Name IS NOT NULL
    GROUP BY Name
    HAVING COUNT(*) > 1
) duplicates;

IF @duplicateNames > 0
BEGIN
    PRINT 'WARNING: Found ' + CAST(@duplicateNames AS VARCHAR) + ' duplicate customer names. Please resolve these before applying unique constraints:';
    
    SELECT Name, COUNT(*) as DuplicateCount
    FROM Customer 
    WHERE Name IS NOT NULL
    GROUP BY Name
    HAVING COUNT(*) > 1
    ORDER BY DuplicateCount DESC;
END

-- Check for existing duplicate emails before adding constraint  
DECLARE @duplicateEmails INT;
SELECT @duplicateEmails = COUNT(*)
FROM (
    SELECT Email, COUNT(*) as cnt
    FROM Customer 
    WHERE Email IS NOT NULL
    GROUP BY Email
    HAVING COUNT(*) > 1
) duplicates;

IF @duplicateEmails > 0
BEGIN
    PRINT 'WARNING: Found ' + CAST(@duplicateEmails AS VARCHAR) + ' duplicate customer emails. Please resolve these before applying unique constraints:';
    
    SELECT Email, COUNT(*) as DuplicateCount
    FROM Customer 
    WHERE Email IS NOT NULL
    GROUP BY Email
    HAVING COUNT(*) > 1
    ORDER BY DuplicateCount DESC;
END