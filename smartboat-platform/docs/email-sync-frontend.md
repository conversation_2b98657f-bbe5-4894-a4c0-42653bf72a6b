# Email Sync Frontend Documentation

## Overview

The SmartBoat platform's email sync feature allows users to automatically process sensor data from email attachments using Microsoft Graph integration. This document covers the frontend implementation details, user interface components, and integration patterns.

## Architecture

### Component Structure

```
src/
├── components/features/profile/
│   └── EmailProcessingSettings.tsx     # Main UI component
├── services/
│   └── emailProcessingService.js       # API service layer
├── hooks/queries/
│   └── useEmailProcessingQueries.js    # React Query hooks
└── utils/
    └── microsoftAuth.js               # OAuth authentication utilities
```

### Key Technologies

- **React 18** - Component framework
- **TypeScript** - Type safety for main components
- **React Query (@tanstack/react-query)** - Data fetching and caching
- **Tailwind CSS** - Styling and responsive design
- **Microsoft Graph** - Email and attachment access

## Components

### EmailProcessingSettings Component

**File**: `src/components/features/profile/EmailProcessingSettings.tsx`

Main component that renders the complete email sync interface within the user profile page.

#### Props

```typescript
interface EmailProcessingSettingsProps {
  userData: any;      // Current user data
  isEditing: boolean; // Whether profile is in edit mode
}
```

#### Key Features

1. **Microsoft Authentication Status**
   - Connection status badge (Connected/Not Connected)
   - Token expiration warnings (≤5 days)
   - Private/incognito mode detection

2. **Authentication Controls**
   - Connect to Microsoft button
   - Re-authenticate option
   - Use Different Account button

3. **Manual Sync Interface**
   - Sync Now button (7 days back)
   - Force Sync button (reprocess duplicates)
   - Real-time processing status

4. **Sync Information Display**
   - Last sync timestamp and status
   - Next scheduled sync info
   - Processing history

#### State Management

```typescript
const [isAuthenticating, setIsAuthenticating] = useState(false);
const [authError, setAuthError] = useState<string | null>(null);
const [syncMessage, setSyncMessage] = useState<{type: 'success' | 'error'; message: string} | null>(null);
const [isPrivateModeDetected, setIsPrivateModeDetected] = useState(false);
```

## Service Layer

### emailProcessingService.js

**File**: `src/services/emailProcessingService.js`

Handles all API communication with the backend email processing endpoints.

#### Available Methods

```javascript
// Token Management
exchangeToken(exchangeData, options = {})    // Exchange OAuth code for tokens
saveToken(tokenData, options = {})           // Save Microsoft Graph tokens
getTokenStatus(options = {})                 // Get current token status

// Processing Operations
processEmails(processOptions, options = {})  // Trigger manual sync
getProcessingSummary(options = {})          // Get sync history
getScheduleStatus(options = {})             // Get schedule status
```

#### API Endpoints

All endpoints use POST method with request body containing user context:

- `/api/emailprocessing/exchange-token` - Token exchange
- `/api/emailprocessing/token-status` - Token validation
- `/api/emailprocessing/process-emails` - Manual processing
- `/api/emailprocessing/summary` - Processing history
- `/api/emailprocessing/schedule-status` - Schedule information

## Data Fetching Hooks

### useEmailProcessingQueries.js

**File**: `src/hooks/queries/useEmailProcessingQueries.js`

React Query hooks for data fetching, caching, and synchronization.

#### Available Hooks

```javascript
// Query Hooks
useTokenStatus(options = {})        // Token status with 1min refresh
useScheduleStatus(options = {})     // Schedule status  
useProcessingSummary(options = {})  // Processing history with 5min cache

// Mutation Hooks
useExchangeToken()                  // Exchange authorization code
useSaveToken()                      // Save tokens manually
useProcessEmails()                  // Trigger email processing
```

#### Caching Strategy

```javascript
// Token Status - Frequent updates for auth state
staleTime: 5 * 60 * 1000,  // 5 minutes
refetchInterval: 60 * 1000, // 1 minute

// Processing Summary - Less frequent updates
staleTime: 5 * 60 * 1000,  // 5 minutes
retry: 2,

// Schedule Status - Infrequent updates  
staleTime: 10 * 60 * 1000, // 10 minutes
retry: 2,
```

## Authentication Flow

### Microsoft Graph OAuth Integration

**File**: `src/utils/microsoftAuth.js`

#### OAuth Flow Steps

1. **Initiate Authentication**
   ```javascript
   await startMicrosoftAuth({ 
     forceLogin: false,
     selectAccount: true 
   });
   ```

2. **Handle Callback**
   ```javascript
   if (isAuthCallback()) {
     const exchangeData = await handleAuthCallback(urlParams);
     await exchangeTokenMutation.mutateAsync(exchangeData);
   }
   ```

3. **Token Storage**
   - Server-side secure storage
   - Automatic refresh handling
   - Expiration tracking

#### Error Handling

```javascript
// Browser Compatibility
'Browser storage not available' → Private mode warning
'session expired' → Re-authentication prompt
'CSRF attack' → Security validation failure
'already redeemed' → Duplicate code usage
```

## User Interface Patterns

### Status Indicators

#### Connection Status Badge
```jsx
<span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
  isConnected 
    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
}`}>
  {isConnected ? 'Connected' : 'Not Connected'}
</span>
```

#### Expiration Warnings
```jsx
{daysUntilExpiry <= 5 && (
  <span className="text-sm text-yellow-600 dark:text-yellow-400">
    Re-authentication required
  </span>
)}
```

### Loading States

#### Authentication Loading
```jsx
{isAuthenticating && (
  <div className="flex items-center space-x-2">
    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
    <span>Connecting...</span>
  </div>
)}
```

#### Processing Loading
```jsx
{processEmailsMutation.isPending && (
  <div className="flex items-center space-x-2">
    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
    <span>Syncing...</span>
  </div>
)}
```

### Error Display

#### Authentication Errors
```jsx
{authError && (
  <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
    <p className="text-sm text-red-800 dark:text-red-200">
      <strong>Authentication Error:</strong> {authError}
    </p>
    <button onClick={() => setAuthError(null)}>Dismiss</button>
  </div>
)}
```

#### Processing Messages
```jsx
{syncMessage && (
  <div className={`p-3 rounded-md ${
    syncMessage.type === 'success' 
      ? 'bg-green-50 border-green-200' 
      : 'bg-red-50 border-red-200'
  }`}>
    <p>{syncMessage.message}</p>
  </div>
)}
```

## Data Models

### Token Status
```typescript
interface TokenStatusDto {
  hasToken: boolean;
  isExpired: boolean;
  expiresAt: string | null;
  scope?: string;
}
```

### Processing Summary
```typescript
interface ProcessingSummaryDto {
  totalRecords: number;
  recentRecords: number;
  recentProcessing: RecentProcessingDto[];
}

interface RecentProcessingDto {
  processedAt: string;
  processingStatus: 'Success' | 'Failed' | 'Skipped';
  recordsProcessed: number;
  vesselsFound: number;
  errorMessage?: string;
}
```

### Processing Options
```javascript
// Manual sync options
{
  daysBack: 7,           // Days to look back for emails
  forceReprocess: false  // Whether to reprocess already handled emails
}
```

## Integration Points

### Profile Page Integration

The email sync component is embedded within the user profile page:

```jsx
// UserProfilePage.tsx
<TabPanel value={value} index={2}>
  <EmailProcessingSettings 
    userData={userData} 
    isEditing={isEditing} 
  />
</TabPanel>
```

### Context Dependencies

```jsx
// Required contexts
import { useTranslation } from 'react-i18next';  // i18n support
// Theme context automatically inherited via Tailwind dark: classes
```

## Best Practices

### Error Handling
- Always provide user-friendly error messages
- Auto-dismiss success messages (10s) and errors (15s)
- Graceful degradation for network issues

### Performance
- Use React Query for automatic caching and background updates  
- Debounce user actions to prevent duplicate requests
- Minimize re-renders with proper dependency arrays

### Accessibility
- Provide loading states with screen reader announcements
- Use semantic HTML with proper ARIA labels
- Ensure keyboard navigation support

### Security
- Never expose tokens in frontend code
- Use PKCE flow for OAuth authentication
- Validate all user inputs before API calls

## Testing Considerations

### Manual Testing Scenarios
1. **Authentication Flow**
   - First-time connection
   - Re-authentication with expired token
   - Different account selection
   - Private/incognito mode handling

2. **Sync Operations**
   - Manual sync with recent emails
   - Force sync with duplicate processing
   - Error handling during processing
   - Network interruption recovery

3. **UI States**
   - Loading states during operations
   - Success/error message display
   - Token expiration warnings
   - Responsive design on mobile

### Browser Compatibility
- Test OAuth flow across browsers
- Verify localStorage availability
- Check private mode detection
- Validate CORS handling

## Troubleshooting

### Common Issues

1. **"Browser storage not available"**
   - User in private/incognito mode
   - Cookies/localStorage disabled
   - Solution: Use regular browser window

2. **"Authentication session expired"**
   - OAuth state parameter mismatch
   - Solution: Clear browser cache, retry authentication

3. **"Sync failed"**
   - Token expired during processing
   - Network connectivity issues
   - Solution: Re-authenticate, check network

### Debug Information

Enable console logging to see detailed OAuth flow:
```javascript
// Authentication debugging
console.log('Auth callback processing:', exchangeData);
console.log('Token status:', tokenStatus);
console.log('Processing result:', result);
```

## Future Enhancements

### Planned Features
- Real-time sync progress updates via WebSocket
- Bulk email selection for processing
- Custom sync schedules (hourly, daily, weekly)
- Advanced filtering options for email processing
- Export functionality for processing history

### Performance Improvements
- Implement virtual scrolling for processing history
- Add offline support with sync queue
- Optimize bundle size with code splitting
- Add service worker for background processing notifications