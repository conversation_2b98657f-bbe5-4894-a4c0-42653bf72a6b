<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>SmartBoat Platform</title>
  <script>
    // SPA Fallback for 404 errors
    // This script redirects all 404 requests back to index.html
    // while preserving the original URL for client-side routing

    (function() {
      // Store the current URL path, search, and hash
      var path = window.location.pathname;
      var search = window.location.search;
      var hash = window.location.hash;

      // For Vite's static assets, we don't want to redirect
      if (path.includes('.')) {
        console.log('Static asset 404:', path);
        return; // Let the 404 happen for actual files
      }

      // For SPA routes, redirect to root preserving the path
      // We use history state to store the intended route
      var redirectURL = window.location.origin + '/';

      // Log for debugging
      console.log('404 handler - Redirecting to root, current path:', path);

      // Store the original path in sessionStorage for retrieval
      sessionStorage.setItem('redirectPath', path + (search || '') + (hash || ''));

      // Redirect to the root
      window.location.href = redirectURL;
    })();
  </script>
</head>
<body>
  <h2>Redirecting to SmartBoat Platform...</h2>
  <p>If you are not redirected automatically, <a href="/">click here</a>.</p>
</body>
</html>