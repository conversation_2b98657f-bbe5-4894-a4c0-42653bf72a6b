# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The SmartBoat Platform is a comprehensive web application for monitoring and managing vessels, sensors, companies, and customers in a maritime environment. It's built with React, TypeScript, and Tailwind CSS to provide a professional user interface for fleet management and IoT data visualization.

## Commands

### Development

- `npm run dev` - Start the development server (Vite)
- `npm run build` - Build for production
- `npm run lint` - Run ESLint to check code quality
- `npm run preview` - Preview the production build locally

## Architecture

### Application Structure

The application uses a feature-based architecture with the following key components:

1. **Navigation System**
   - Single-page application with client-side routing handled in `MainApp.tsx`
   - Navigation state is managed through React state with typed navigation states
   - Each page can navigate to detail views for specific entities

2. **Layout System**
   - `Layout.tsx` provides the main application layout with responsive design
   - Fixed header with sidebar navigation that can collapse on smaller screens
   - Responsive layout adjustments handled through CSS and state management

3. **Authentication & Authorization**
   - User authentication and context provided by `AuthContext.tsx`
   - Role-based access control through the `authUtils.ts` utilities
   - Permissions defined for different user roles (Administrator, Customer, etc.)
   - User profile management with role-specific UI adaptations
   - Subscription management for non-admin users in their profile
   - Admins manage all subscriptions through a dedicated Subscriptions page

4. **Data Management**
   - Currently uses mock data from `mockData.ts` for development
   - Data structures for entities (vessels, sensors, companies, etc.) are defined in `types/index.ts`
   - API data fetching implemented with React Query (@tanstack/react-query)
   - Services layer handles API communication with structured endpoints
   - Query hooks provide data fetching, caching, and state management

5. **Theming**
   - Light/dark mode using React Context (`ThemeContext.tsx`)
   - Theme preferences are stored in localStorage
   - System preference detection with `prefers-color-scheme` media query
   - Tailwind CSS for styling with responsive design
   - Dark mode implemented via Tailwind's dark class strategy
   - Toggle component (`ThemeToggle.tsx`) with contextual sun/moon icons
   - Consistent theming across all UI components with dark/light variants

6. **Internationalization (i18n)**
   - Multi-language support using react-i18next
   - Supports English, Greek, and French languages
   - Language selection via dropdown in header and login page
   - Translations stored in JSON files in `src/i18n/locales/`
   - Language preferences stored in localStorage

### Component Organization

- **Feature-based structure**
  - Core entities (vessels, sensors, customers, companies, subscriptions) are organized into feature folders
  - Each feature folder contains its own components and logic
  - Features expose their main component through an index.ts barrel file

- **Common Components**
  - Reusable components like `SensorChart`, `StatusBadge`, and `VesselMap` are in the common folder
  - These components are designed to be shared across multiple features

- **Pages**
  - Main application container is in `MainApp.tsx`
  - Detail pages for each entity (vessel, sensor, customer, company)
  - User profile page (`UserProfilePage.tsx`) with role-specific tabs and content
  - Navigation between pages is handled through state management

- **Context Providers**
  - `AuthContext.tsx` - Manages user authentication and permissions
  - `ThemeContext.tsx` - Manages theme preferences
  - `LanguageContext.tsx` - Manages language preferences and internationalization
  - Providers are integrated at the application root in `main.jsx`

### Data Flow

1. API requests are handled through service modules in `services/` directory
2. Each entity type has a dedicated service (vesselService, sensorService, etc.)
3. React Query hooks in `hooks/queries/` provide data fetching and caching
4. Consistent query keys defined in `queryClient.js` ensure proper cache management
5. Components use query hooks to access data with loading/error states
6. Mutations handle data updates with automatic cache invalidation
7. Mock data from `mockData.ts` serves as a fallback during development
8. Entity data is defined with TypeScript interfaces in `types/index.ts`
9. Theme settings are managed through React Context API
10. User state and permissions are managed through AuthContext

### Authentication Flow

1. User authentication is managed via the `AuthContext` provider
2. User roles and permissions are defined in `authUtils.ts`
3. Each user is associated with subscriptions via `subscriptionIds`
4. Permission checks are performed using the `hasPermission` utility function
5. The UI conditionally renders elements based on the user's permissions

## TypeScript Types

Key types and interfaces are defined in `types/index.ts`:

### Entity Types
- `Vessel` - Ship data including identification, location, and status
- `Sensor` - IoT device data with readings and alert thresholds
- `Customer` - Customer information including contact details and relationships
- `Company` - Business entities that own vessels
- `Subscription` - Service plans with billing information
- `SensorDataPoint` - Time-series data structure for sensor readings
- `VesselPathPoint` - Coordinates and location data for vessel tracking

### Authentication Types
- `User` - User entity with role and permissions
- `UserRole` - Union type of available roles: 'Administrator', 'Customer', 'Manager', 'Technician', 'Viewer'
- `RolePermissions` - Defines what actions each role can perform
- `NotificationPreferences` - User notification settings
- `SocialLinks` - User social media information

## Authorization System

The platform implements a role-based access control system:

1. **User Roles**
   - `Administrator` - Full access to all features
   - `Customer` - Access to assigned subscriptions with ability to modify their own
   - `Manager` - View-only access to all subscriptions and data
   - `Technician` - Access to sensor data for maintenance
   - `Viewer` - Limited read-only access

2. **Permission Structure**
   - Each role has pre-defined permissions in `rolePermissions` object
   - Permissions control subscription management, data visibility, and editing capabilities
   - Utility functions like `hasPermission` and `canAccessSubscription` enforce rules

3. **UI Integration**
   - Components conditionally render based on user permissions
   - Buttons and actions are enabled/disabled according to role
   - Navigation options are role-specific (admins see all tabs, customers see limited tabs)
   - Subscription management UI adapts to show appropriate options
   - Profile page tabs vary by user role (admin profiles don't show subscription tab)
   - Admin users manage all subscriptions through the main Subscriptions page

## Data Services & API Integration

### API Client

The application includes a robust API client in `apiClient.js` with the following features:

1. **Standardized Request Methods** - Consistent methods for GET, POST, PUT, PATCH, DELETE
2. **Authentication Management** - Automatic token inclusion in request headers
3. **Error Handling** - Consistent error parsing and handling
4. **Timeout Support** - Configurable request timeouts
5. **Mock Data Fallback** - Optional fallback to mock data during development
6. **Response Parsing** - Automatic JSON parsing with error handling

### Service Layer

Each entity type has a dedicated service module that encapsulates its API operations:

1. **authService** - Authentication operations (login, logout, register, profile)
2. **vesselService** - Vessel management and tracking
3. **sensorService** - Sensor data and alerting
4. **companyService** - Company management
5. **customerService** - Customer record operations
6. **subscriptionService** - Subscription plan management
7. **userService** - User account operations

Each service provides a clean interface that abstracts the underlying API details.

### React Query Integration

The application uses React Query (TanStack Query) for data fetching, caching, and state management:

1. **Query Client Configuration** - Centralized in `queryClient.js` with defaults
2. **Query Keys** - Structured key factory ensures consistent cache handling
3. **Custom Hooks** - Entity-specific query hooks in `hooks/queries/`
4. **Stale Time Management** - Configurable stale times for different data types
5. **Automatic Refetching** - Background refreshes based on window focus
6. **Mutation Support** - Data update operations with cache invalidation
7. **Error Handling** - Consistent error handling with optional fallbacks
8. **Optimistic Updates** - Support for optimistic UI updates with rollback

### Mock Data Structure

For development, the application uses mock data in `mockData.ts`:

1. **Mock Users** - Sample users with different roles and permissions
2. **Mock Subscriptions** - Subscription plans with features and pricing
3. **Mock Companies** - Business entities that own vessels
4. **Mock Vessels** - Ships with location and status information
5. **Mock Sensors** - IoT devices that report data from vessels
6. **Mock Customers** - Organizations that subscribe to the platform

Each mock object includes relationships to other entities through ID references.

## Internationalization Guidelines

### Translation Structure

The application uses the following translation structure in JSON files:

1. **Namespaces** - Top-level categories like "common", "auth", "dashboard", etc.
2. **Nested Keys** - Hierarchical structure with namespaces containing related translations
3. **Component-Specific Keys** - Translations are organized by feature/component

### Translation Files

- `src/i18n/locales/en.json` - English translations (default language)
- `src/i18n/locales/el.json` - Greek translations
- `src/i18n/locales/fr.json` - French translations

### Translation Rules

1. **Key Format**
   - Use camelCase for keys
   - Use dot notation for nested keys (e.g., `common.save`, `vessels.noVesselsFound`)
   - Group related translations under the same namespace

2. **Adding New Translations**
   - Always add translations to ALL language files
   - Match the structure across all language files
   - Add new keys in alphabetical order within their namespace

3. **Using Translations in Components**
   - Import the `useTranslation` hook from react-i18next
   - Use the `t` function to access translations: `t('common.save')`
   - For dynamic content, use template strings: `t('vessels.count', { count: 5 })`

4. **Language Switcher Components**
   - Main app: Use `LanguageSwitcher` component in Header
   - Login page: Use `LoginLanguageSwitcher` component

5. **Adding New Languages**
   - Create a new JSON file in `src/i18n/locales/`
   - Add the language to `i18n.ts` resources
   - Add the language code to the `Language` type in `LanguageContext.tsx`
   - Add the language to the languages array in language switcher components

## Theme System Guidelines

### Theme Implementation

The SmartBoat Platform implements a dark/light theme system using the following approach:

1. **Theme Context**
   - Defined in `src/context/ThemeContext.tsx`
   - Exposes theme state and toggle function via React Context
   - Theme types: `'light' | 'dark'`
   - Uses local storage to persist user preferences
   - Detects and respects system preferences via `prefers-color-scheme` media query

2. **Dark Mode Strategy**
   - Uses Tailwind CSS's class strategy (adding 'dark' class to html element)
   - Dark mode styles are defined using Tailwind's `dark:` prefix
   - Custom colors defined in tailwind.config.js for consistent theming
   - Custom gray-750 color (#2d3748) added for dark mode hover states
   - ThemeContext toggles the 'dark' class on the HTML element

3. **Theme Toggle Component**
   - Implemented in `src/components/common/ThemeToggle.tsx`
   - Provides an accessible button that changes theme
   - Shows contextual icons (sun for light mode, moon for dark mode)
   - Available in the app header for easy access

### Using Dark Mode in Components

When creating or modifying components, follow these guidelines for dark mode support:

1. **Color Pairing**
   - Always define both light and dark variants for:
     - Background colors: `bg-white dark:bg-gray-800`
     - Text colors: `text-gray-800 dark:text-gray-200`
     - Borders: `border-gray-200 dark:border-gray-700`
     - Shadows: `shadow-md dark:shadow-none` or custom dark shadows

2. **Interactive Elements**
   - Ensure hover/focus states are defined for both themes:
     - `hover:bg-gray-100 dark:hover:bg-gray-700`
     - `focus:ring-blue-500 dark:focus:ring-blue-400`

3. **Contrast & Accessibility**
   - Maintain WCAG AA contrast ratio in both themes
   - Test interactive elements in both modes
   - Ensure icons and UI elements are visible in both themes

4. **Images & Icons**
   - Use SVG icons with `currentColor` to inherit text color
   - Consider providing dark-specific assets for logos or complex graphics
   - Use `filter` utilities for SVGs when appropriate

5. **Charts & Data Visualization**
   - Define separate color schemes for light and dark modes
   - Use the theme context to detect current mode and apply appropriate colors

### Testing Dark Mode

Before committing changes:

1. Test components in both light and dark modes
2. Verify theme persistence across page refreshes
3. Check system preference integration by changing OS theme
4. Ensure smooth transitions between themes (no flickering)
5. Verify accessibility in both modes