erDiagram
    User {
        number id PK
        string name
        string email
        string role
        string avatar
        string joined
        string status
        string lastLogin
        boolean twoFactorEnabled
        string company
        string department
        string phone
        string timezone
        string language
        string bio
    }
    
    Customer {
        number id PK
        string name
        string contactPerson
        string email
        string phone
        number companies
        number vessels
        number sensors
        string status
        string lastActive
    }
    
    Company {
        number id PK
        string name
        string location
        string industry
        number vessels
        number sensors
        string status
        number customerId FK
        string lastUpdated
    }
    
    Vessel {
        number id PK
        string name
        string number
        string type
        string location
        string status
        string startDate
        string endDate
        string image
        number onsigners
        number offsigners
        number sensors
        string lastUpdated
        number companyId FK
    }
    
    Sensor {
        number id PK
        string name
        string type
        string vessel
        string location
        string status
        string lastReading
        string lastUpdated
        string alertThreshold
    }
    
    Subscription {
        number id PK
        string name
        string type
        number customerId FK
        string customerName
        string startDate
        string endDate
        number price
        string billingFrequency
        string status
        number sensorLimit
        array features
        string lastUpdated
    }
    
    SensorDataPoint {
        string time
        number timestamp
        number temperature
        number humidity
    }
    
    VesselPathPoint {
        number lat
        number lng
        string timestamp
        string location
    }
    
    User }|--o{ Subscription : "has access to"
    User ||--o| Customer : "may be associated with"
    Customer ||--o{ Company : "owns"
    Customer ||--o{ Subscription : "purchases"
    Company ||--o{ Vessel : "operates"
    Vessel ||--o{ Sensor : "equipped with"
    Sensor ||--o{ SensorDataPoint : "generates"
    Vessel ||--o{ VesselPathPoint : "records position as"