# API Migration Guide

This document provides instructions for migrating from mock data to the API service layer in the SmartBoat Platform application.

## Overview

We've implemented a complete API service layer with React Query to replace the existing mock data implementation. This change provides:

- Robust data fetching with caching, background updates, and optimistic UI
- Consistent error handling across the application
- Loading state management
- Mock data fallback for development
- Strong TypeScript typing and integration

## Migration Steps

Follow these steps to migrate a component from using mock data to using the API service:

### 1. Replace Mock Data Imports

Remove the mock data imports and add the necessary hooks:

```diff
- import { mockVesselData, mockCompanies } from '../../../utils/mockData';
+ import { useVessels, useCreateVessel } from '../../../hooks';
+ import { useApi } from '../../../providers/ApiProvider';
```

### 2. Remove Direct State Management for API Data

Replace direct state management for data with React Query hooks:

```diff
- const [vessels, setVessels] = useState<Vessel[]>([]);
-
- // Load vessels based on user role
- useEffect(() => {
-   if (currentUser) {
-     if (currentUser.role === 'Administrator' || currentUser.role === 'Manager') {
-       // Admins and managers see all vessels
-       setVessels(mockVesselData);
-     } else if (currentUser.role === 'Customer' && currentUser.customerId) {
-       // Filter logic
-       // ...
-       setVessels(customerVessels);
-     } else {
-       setVessels([]);
-     }
-   } else {
-     setVessels(mockVesselData);
-   }
- }, [currentUser]);
+ 
+ // Use React Query to fetch vessels
+ const queryOptions = {
+   // For customers, we'll filter server-side by company
+   ...(currentUser?.role === 'Customer' && currentUser.customerId && {
+     customerId: currentUser.customerId
+   })
+ };
+ 
+ // Fetch vessels with React Query
+ const { 
+   data: vessels = [], 
+   isLoading, 
+   isError 
+ } = useVessels(queryOptions);
```

### 3. Replace Direct Data Mutation

Replace direct data mutation with React Query mutation hooks:

```diff
- const handleAddVessel = (newVessel: Vessel) => {
-   setVessels(prevVessels => [...prevVessels, newVessel]);
-   setIsCreateModalOpen(false);
- };
+ 
+ // Mutation hook for creating vessels
+ const createVesselMutation = useCreateVessel({
+   onSuccess: () => {
+     setIsCreateModalOpen(false);
+   }
+ });
+ 
+ const handleAddVessel = (newVessel: Vessel) => {
+   createVesselMutation.mutate(newVessel);
+ };
```

### 4. Add Loading and Error States

Add handling for loading and error states:

```jsx
{/* Loading state */}
{isLoading && (
  <div className="bg-white rounded-lg shadow p-8 text-center">
    <div className="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
    <h3 className="text-lg font-medium text-gray-900 mb-2">{t('common.loading')}</h3>
  </div>
)}

{/* Error state */}
{isError && (
  <div className="bg-white rounded-lg shadow p-8 text-center">
    <div className="bg-red-100 text-red-700 p-4 rounded-lg mb-4">
      <h3 className="text-lg font-medium mb-2">{t('common.error')}</h3>
      <p>{t('vessels.errorLoadingVessels')}</p>
      {isMockMode() && (
        <p className="mt-2 text-sm">{t('common.usingMockData')}</p>
      )}
    </div>
  </div>
)}

{/* Data display - only show when not loading and no error */}
{!isLoading && !isError && (
  // Your existing display code here
)}
```

## API Services

We've implemented service modules for each entity type in the application. These modules provide:

- Methods for fetching, creating, updating and deleting data
- TypeScript typing for parameters and return values
- Consistent error handling
- Integration with React Query

### Available Services

The following services are available in the `src/services` directory:

- `authService.js` - Authentication and user profile
- `vesselService.js` - Vessel data and tracking
- `sensorService.js` - Sensor data and readings
- `companyService.js` - Company management
- `customerService.js` - Customer management
- `subscriptionService.js` - Subscription plans and user subscriptions
- `userService.js` - User management (admin only)

Each service is exported from `src/services/index.js`.

## React Query Hooks

For each service, we've created corresponding React Query hooks that simplify data fetching and mutation. These hooks are available in the `src/hooks/queries` directory:

- `useAuthQueries.js` - Authentication hooks (login, logout, etc.)
- `useVesselQueries.js` - Vessel data hooks
- `useSensorQueries.js` - Sensor data hooks
- `useCompanyQueries.js` - Company data hooks
- `useCustomerQueries.js` - Customer data hooks
- `useSubscriptionQueries.js` - Subscription data hooks
- `useUserQueries.js` - User data hooks

All hooks are exported from `src/hooks/index.js`.

### Example Usage

```jsx
import { useVessels, useVessel, useCreateVessel, useUpdateVessel, useDeleteVessel } from '../hooks';

// Fetch all vessels
const { data: vessels = [], isLoading, isError } = useVessels();

// Fetch a specific vessel
const { data: vessel } = useVessel(vesselId);

// Create a new vessel
const createVesselMutation = useCreateVessel({
  onSuccess: (data) => {
    console.log('Vessel created:', data);
  }
});
createVesselMutation.mutate(newVesselData);

// Update a vessel
const updateVesselMutation = useUpdateVessel();
updateVesselMutation.mutate({ id: vesselId, vesselData: updatedData });

// Delete a vessel
const deleteVesselMutation = useDeleteVessel();
deleteVesselMutation.mutate(vesselId);
```

## Environment Configuration

Environment-specific settings are now centralized in `src/config/envConfig.js`. Key configurations include:

- `API_BASE_URL` - Base URL for API requests
- `API_VERSION` - API version prefix
- `REQUEST_TIMEOUT` - Default timeout for requests
- `ENV_NAME` - Current environment name
- `FEATURES` - Feature flags, including mock data fallback
- `USE_MOCK_FALLBACK` - Whether to fall back to mock data if API calls fail

## API Provider

The API Provider (`src/providers/ApiProvider.jsx`) wraps the application and provides:

- Global loading state for API operations
- Error management
- Connection status
- Data refresh capabilities
- Mock mode detection

### Example Usage

```jsx
import { useApi } from '../providers/ApiProvider';

const MyComponent = () => {
  const { isLoading, addError, refreshData, isMockMode } = useApi();
  
  return (
    <div>
      {isLoading && <p>Loading...</p>}
      
      {isMockMode() && (
        <p className="text-yellow-600">Using mock data</p>
      )}
      
      <button onClick={refreshData}>
        Refresh Data
      </button>
    </div>
  );
};
```

## Mock Data Fallback

For development and testing, we've implemented a mock data fallback system. This allows the application to use mock data when:

1. The API is unavailable
2. The `USE_MOCK_FALLBACK` feature flag is enabled
3. During development

This provides a seamless experience during development and protects against API outages in production.

## Additional Components

We've added several utility components to enhance the UI:

- `LoadingOverlay.jsx` - Displays a loading overlay during API operations
- `ErrorNotification.jsx` - Shows error notifications
- `OfflineNotice.jsx` - Displays a notice when offline or using mock data

## Translation Updates

New translation keys have been added to support API-related UI:

- `common.loading` - Loading message
- `common.error` - Error title
- `common.usingMockData` - Mock data indicator
- Entity-specific error messages (e.g., `vessels.errorLoadingVessels`)

## Troubleshooting

### API Authentication Issues

If you encounter authentication issues:

1. Check that the `authService.login` method is being called correctly
2. Verify that the token is being stored and retrieved properly in `authUtils.js`
3. Check that the token is being included in request headers

### API Errors

React Query provides built-in error handling. You can access error information using:

```jsx
const { error, isError } = useVessels();

if (isError) {
  console.error('Error fetching vessels:', error);
}
```

### Cache Invalidation

To force a data refresh:

```jsx
const queryClient = useQueryClient();
queryClient.invalidateQueries(queryKeys.vessels.all);
```

## Next Steps

For future improvements, consider:

1. Implementing WebSocket support for real-time updates
2. Adding pagination for large data sets
3. Implementing more advanced caching strategies
4. Adding offline mode with localStorage persistence