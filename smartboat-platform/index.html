<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SmartBoat Platform</title>
    <script>
      // Check for saved theme preference
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme === 'dark') {
        document.documentElement.classList.add('dark');
      } else if (savedTheme === 'light') {
        document.documentElement.classList.remove('dark');
      } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.documentElement.classList.add('dark');
      }
    </script>
    <script>
      // This script helps with deep linking and refreshing pages with routes
      (function() {
        try {
          // IMPORTANT: Save original URL path in localStorage before any redirects happen
          // This is critical for handling page refreshes
          const path = window.location.pathname;
          const search = window.location.search;
          const hash = window.location.hash;

          // Don't save paths for actual files or the root path
          if (path !== '/' && !path.includes('.')) {
            console.log('Saving current path to localStorage:', path);
            localStorage.setItem('smartboat_last_path', path + search + hash);
            window.__ROUTE_PATH = path;
          }

          // Handle redirected paths from 404.html (SPA fallback)
          const redirectPath = sessionStorage.getItem('redirectPath');
          if (redirectPath && path === '/') {
            console.log('Handling redirect from 404.html to:', redirectPath);

            // Use history API to rewrite URL without page reload
            history.pushState(null, null, redirectPath);

            // Clear the redirect path to prevent infinite loops
            sessionStorage.removeItem('redirectPath');

            // Also save this path to localStorage for future refreshes
            localStorage.setItem('smartboat_last_path', redirectPath);
            window.__ROUTE_PATH = redirectPath;
          }
        } catch (error) {
          console.error('Error in path handling script:', error);
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>