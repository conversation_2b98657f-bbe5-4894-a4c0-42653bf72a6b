@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));

/* Global styles */
body {
  font-family: 'Inter', sans-serif;
  overflow: hidden;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for the entire app */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #2d3748;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Main content area adjustments for sidebar */
.main-content {
  transition: margin-left 0.3s ease;
}

/* Fixed header layout */
.fixed-header-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.fixed-header-layout header {
  position: sticky;
  top: 0;
  z-index: 40;
}

.fixed-header-layout main {
  flex: 1;
  overflow-y: auto;
  height: calc(100vh - 72px); /* Adjusted for 72px header height */
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
