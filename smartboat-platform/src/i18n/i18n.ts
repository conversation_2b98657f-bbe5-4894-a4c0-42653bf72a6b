import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enTranslation from './locales/en.json';
import elTranslation from './locales/el.json';
import frTranslation from './locales/fr.json';

// Configure i18next
i18n
  // Detect user language
  // Learn more: https://github.com/i18next/i18next-browser-languageDetector
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    debug: process.env.NODE_ENV === 'development',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false, // not needed for react as it escapes by default
    },
    resources: {
      en: {
        translation: enTranslation
      },
      el: {
        translation: elTranslation
      },
      fr: {
        translation: frTranslation
      }
    },
    // Store the language setting in localStorage
    detection: {
      order: ['localStorage', 'navigator'],
      lookupLocalStorage: 'smartboat_language',
      caches: ['localStorage'],
    },
  });

export default i18n;