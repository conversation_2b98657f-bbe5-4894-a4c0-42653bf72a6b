{"common": {"dashboard": "Dashboard", "vessels": "<PERSON><PERSON><PERSON>", "sensors": "Sensors", "customers": "Customers", "companies": "Companies", "owners": "Owners", "subscriptions": "Subscriptions", "profile": "Profile", "backToDashboard": "Back to Dashboard", "welcome": "Welcome to SmartBoat Platform", "logout": "Logout", "loading": "Loading...", "search": "Search...", "save": "Save", "cancel": "Cancel", "add": "Add", "edit": "Edit", "delete": "Delete", "deleting": "Deleting...", "view": "View", "configure": "Configure", "active": "Active", "inactive": "Inactive", "pending": "Pending", "status": "Status", "notFound": "Page Not Found", "dragToResize": "Drag to resize", "email": "Email", "phone": "Phone", "name": "Name", "contact": "Contact", "contactPerson": "Contact Person", "actions": "Actions", "close": "Close", "update": "Update", "create": "Create", "required": "* Required", "previous": "Previous", "next": "Next", "showing": "Showing", "to": "to", "of": "of", "itemsPerPage": "Items per page", "showingItems": "Showing {{start}} to {{end}} of {{total}} items", "previousPage": "Previous page", "nextPage": "Next page", "goToPage": "Go to page {{page}}", "all": "All", "download": "Download", "noResults": "No results found", "last24Hours": "Last 24 Hours", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "customRange": "Custom Range", "from": "From", "selectDate": "Select date", "searchPlaceholder": "Search by name, email, or contact person", "backTo": "Back to", "lastActive": "Last active", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "statistics": "Statistics", "details": "Details", "overview": "Overview", "billing": "Billing", "unknown": "Unknown", "blocks": "Blocks", "basicInformation": "Basic Information", "preview": "Preview", "fillAllRequired": "Please fill in all required fields", "companyId": "Company ID", "id": "ID", "type": "Type", "vessel": "<PERSON><PERSON><PERSON>", "location": "Location", "apply": "Apply", "saveChanges": "Save Changes", "role": "Role", "change": "Change", "company": "Company", "optional": "Optional", "loadingVessels": "Loading vessels..."}, "auth": {"signIn": "Sign in", "emailAddress": "Email address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot your password?", "signingIn": "Signing in...", "demoAccounts": "Demo Accounts", "adminUser": "Admin User", "customerUser": "Customer User", "enterEmailPassword": "Please enter both email and password.", "invalidCredentials": "Invalid credentials. Please try again.", "loginError": "An error occurred during login. Please try again.", "resetPassword": "Reset Your Password", "resetInstructions": "If an account with this email exists, you will receive instructions to reset your password shortly.", "enterEmail": "Enter your email address", "sendInstructions": "Send Reset Instructions", "sending": "Sending...", "backToLogin": "Back to Login", "sessionTimeout": "Session timeout warning:", "logoutIn": "You will be logged out in", "stayLoggedIn": "Stay Logged In"}, "dashboard": {"recentActivity": "Recent Activity", "expandAll": "Expand all", "showAll": "Show all", "minAgo": "minutes ago", "hourAgo": "hour ago", "hoursAgo": "hours ago", "newSensorAdded": "New sensor added to", "subscriptionRenewed": "Subscription renewed for", "alertAnomalyDetected": "Alert: Anomaly detected on", "temperatureSensor": "temperature sensor", "vesselSensors": "Sensors", "statCards": {"customers": "Customers", "companies": "Companies", "vessels": "<PERSON><PERSON><PERSON>", "activeSensors": "Active Sensors"}}, "vessels": {"name": "Name", "number": "Number", "type": "Type", "location": "Location", "startDate": "Start Date", "endDate": "End Date", "onsigners": "Onsigners", "offsigners": "Offsigners", "lastUpdated": "Last Updated", "details": "<PERSON><PERSON><PERSON>", "vesselInformation": "Vessel Information", "quickActions": "Quick Actions", "noVessels": "No vessels found", "vesselsList": "<PERSON><PERSON><PERSON>", "addNewVessel": "Add New Vessel", "editVessel": "<PERSON>", "createNewVessel": "Create <PERSON>", "searchVessels": "Search vessels by name, number, or location", "noVesselsFound": "No vessels found", "noVesselsMessage": "Try adjusting your search or filters, or add a new vessel.", "noVesselsMessageUser": "Try adjusting your search or filters.", "allStatuses": "All Statuses", "active": "Active", "maintenance": "Maintenance", "inactive": "Inactive", "vesselNumber": "Vessel Number", "currentLocation": "Current Location", "totalSensors": "Total Sensors", "backToVessels": "Back to Vessels", "vesselNotFound": "<PERSON><PERSON><PERSON> not found", "viewDetails": "View Details", "manageSensors": "Manage Sensors", "updateVessel": "Update <PERSON><PERSON><PERSON>", "createVessel": "C<PERSON> <PERSON><PERSON><PERSON>", "updateVesselInfo": "Update vessel information", "addNewVesselInfo": "Add a new vessel to the platform", "lastUpdate": "Last update", "sensorOverview": "Sensor Overview", "vesselSensors": "Vessel Sensors", "scheduleInteractive": "Interactive scheduling calendar will be available in future updates", "statUnavailable": "Statistics will be available after creating the vessel", "tabs": {"vesselInfo": "Vessel Info", "routeMap": "Route Map", "sensors": "Sensors"}, "blocks": {"basicInformation": "Basic Information", "schedule": "Schedule", "scheduleDetails": "Schedule Details", "statistics": "Statistics"}, "vesselTypes": {"mechanical": "Motor-powered", "sailing": "Sailing"}, "fillAllRequired": "Please fill in all required fields", "selectCompany": "Select a company", "selectOwner": "Select Owner", "noOwnerSelected": "No owner selected", "noOwnersForCompany": "No owners available for this company", "vesselName": "Vessel Name", "vesselType": "Vessel Type", "homePort": "Home Port", "length": "Length", "beam": "<PERSON><PERSON>", "draft": "Draft", "selectType": "Select vessel type", "nameRequired": "V<PERSON><PERSON> name is required", "createFailed": "Failed to create vessel. Please try again.", "addToCompany": "Add to company", "locationUnknown": "Location unknown", "ownerHasNoVessels": "{{owner<PERSON>ame}} has no vessels assigned", "types": {"cargoship": "Cargo Ship", "containership": "Container Ship", "tanker": "Tanker", "bulkcarrier": "Bulk Carrier", "fishingvessel": "Fishing Vessel", "passengership": "Passenger Ship", "yacht": "Yacht", "navalvessel": "Naval Vessel", "researchvessel": "Research Vessel", "other": "Other"}, "status": {"maintenance": "Maintenance", "atport": "At Port", "atsea": "At Sea"}, "confirmDelete": "Delete Vessel", "deleteWarning": "Are you sure you want to delete {{vesselName}}? This action cannot be undone.", "deleteFailed": "Failed to delete vessel. Please try again.", "deleteVessel": "Delete Vessel"}, "sensors": {"name": "Name", "type": "Type", "vessel": "<PERSON><PERSON><PERSON>", "selectVessel": "Select Vessel", "location": "Location", "lastReading": "Last Reading", "lastUpdated": "Last Updated", "alertThreshold": "<PERSON><PERSON>", "readings": "Readings", "average": "Average", "current": "Current", "minimum": "Minimum", "maximum": "Maximum", "sensorData": "Sensor Data", "sensorInformation": "Sensor Information", "sensorReadings": "Sensor Readings", "loadingSensorData": "Loading sensor data...", "fetchingSensorReadings": "Fetching sensor readings...", "errorLoadingSensorData": "Failed to load sensor data", "noDataAvailable": "No sensor data available", "time": "Time", "sensor": "Sensor", "temperature": "Temperature", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "pressure": "Pressure", "quickActions": "Quick Actions", "recalibrate": "Recalibrate", "diagnosticTest": "Diagnostic Test", "noSensors": "No sensors found", "sensorName": "Sensor Name", "vesselLocation": "Vessel / Location", "addNewSensor": "Add New Sensor", "editSensor": "<PERSON>", "createNewSensor": "Create New Sensor", "searchSensors": "Search sensors by name, vessel, or location", "noSensorsMessageAdmin": "Try adjusting your search or filters, or add a new sensor.", "noSensorsMessageUser": "Try adjusting your search or filters.", "allTypes": "All Types", "allStatuses": "All Statuses", "types": {"temperature": "Temperature", "pressure": "Pressure", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "fuellevel": "Fuel Level", "enginerpm": "Engine RPM", "speed": "Speed", "gps": "GPS", "depth": "De<PERSON><PERSON>", "windspeed": "Wind Speed", "winddirection": "Wind Direction", "batteryvoltage": "Battery Voltage", "current": "Current", "vibration": "Vibration", "flowrate": "Flow Rate", "other": "Other"}, "status": {"maintenance": "Maintenance", "calibrating": "Calibrating", "error": "Error", "warning": "Warning", "critical": "Critical"}, "configuration": "Configuration", "blocks": "Blocks", "basicInformation": "Basic Information", "updateSensorInfo": "Update sensor information", "addNewSensorInfo": "Add a new sensor to the platform", "readingsData": "Readings Data", "readingsUnavailable": "Readings data will be available after creating the sensor", "historicalReadingsUnavailable": "Historical readings chart will be available in future updates", "updateSensor": "Update Sensor", "createSensor": "Create Sensor", "fillAllRequired": "Please fill in all required fields", "sensorNotFound": "<PERSON><PERSON> not found", "backToSensors": "Back to Sensors", "history": "History", "settings": "Settings", "readingHistory": "Reading History", "dateRange": "Date Range", "resolution": "Resolution", "hourly": "Hourly", "daily": "Daily", "weekly": "Weekly", "last24Hours": "Last 24 hours", "last7Days": "Last 7 days", "last30Days": "Last 30 days", "customRange": "Custom range", "timestamp": "Timestamp", "value": "Value", "sensorSettings": "Sensor Settings", "generalSettings": "General Settings", "alertConfiguration": "Alert Configuration", "samplingRate": "Sampling Rate", "every5Minutes": "Every 5 minutes", "every15Minutes": "Every 15 minutes", "every30Minutes": "Every 30 minutes", "enableEmailAlerts": "Enable email alerts", "enableSmsAlerts": "Enable SMS alerts", "addSensor": "<PERSON><PERSON> Sensor", "needVesselFirst": "Add a vessel first", "needVesselMessage": "You need to add a vessel before you can add sensors.", "needCompanyAndVesselMessage": "You need to add a company and vessel before you can add sensors.", "unknownVessel": "Unknown <PERSON><PERSON>el", "noSensorsFound": "No sensors found", "noSensorsMessage": "This customer doesn't have any sensors yet.", "addToVessel": "Add to vessel", "locationPlaceholder": "e.g., Engine Room, Bridge, Deck", "unit": "Unit", "minValue": "Min Value", "maxValue": "Max Value", "selectType": "Select sensor type", "typeRequired": "Sensor type is required", "nameRequired": "Sensor name is required", "createFailed": "Failed to create sensor. Please try again.", "confirmDelete": "Delete Sensor", "deleteWarning": "Are you sure you want to delete {{sensorName}}? This action cannot be undone.", "deleteFailed": "Failed to delete sensor. Please try again.", "deleteSensor": "Delete Sensor", "errorLoadingSensors": "Failed to load sensors"}, "subscriptions": {"yourSubscriptions": "Your Subscriptions", "yourCurrentSubscription": "Your Current Subscription", "yourOtherSubscriptions": "Your Other Subscriptions", "manageSubscriptions": "Manage Subscriptions", "addNewSubscription": "Add New Subscription", "createNewSubscription": "Create New Subscription", "editSubscription": "Edit Subscription", "price": "Price", "validFrom": "<PERSON>id from", "validTo": "to", "plan": "Plan", "noSubscriptionsFound": "No Subscriptions Found", "noSubscriptionsMessage": "There are currently no active subscriptions in the system. Click the button below to create a new subscription.", "billingFrequency": "Billing Frequency", "startDate": "Start Date", "endDate": "End Date", "monthly": "Monthly", "quarterly": "Quarterly", "yearly": "Yearly", "status": {"active": "Active", "pending": "Pending", "expired": "Expired", "cancelled": "Cancelled"}, "subscriptionPeriod": "Subscription Period", "subscriptionNotFound": "Subscription not found", "subscriptionNotFoundMessage": "The subscription you're looking for doesn't exist or you don't have permission to view it.", "backToSubscriptions": "Back to Subscriptions", "errorLoadingSubscription": "Error loading subscription details", "features": "Features", "paymentInformation": "Payment Information", "nextBillingDate": "Next billing date", "paymentMethod": "Payment method", "updateSubscriptionDetails": "Update subscription details", "addNewSubscriptionInfo": "Add a new subscription plan for a customer", "billingDetails": "Billing Details", "planFeatures": "Plan Features", "subscriptionName": "Subscription Name", "subscriptionType": "Subscription Type", "selectCustomer": "Select Customer", "selectVessel": "Select Vessel", "errorFetchingVessels": "Failed to load vessels. Please try again.", "pricePerSensor": "Price Per Sensor", "sensorLimit": "Sensor Limit per V<PERSON>el", "unlimitedSensorsInfo": "Leave empty for unlimited sensors", "featuresPerLine": "Features (one per line)", "enterFeaturesPlaceholder": "Enter features, one per line", "featuresInstructionText": "List the features included in this subscription plan, with each feature on a new line.", "updateSubscription": "Update Subscription", "createSubscription": "Create Subscription", "planTypes": {"standard": "Standard", "professional": "Professional", "enterprise": "Enterprise", "custom": "Custom"}}, "notFound": {"title": "404", "subtitle": "Page Not Found", "message": "The page doesn't exist or you don't have permission to access it.", "backToDashboard": "Go to Dashboard", "goBack": "Go Back"}, "profile": {"userProfile": "User Profile", "personalInformation": "Personal Information", "accountSettings": "Account <PERSON><PERSON>", "notifications": "Notifications", "language": "Language", "english": "English", "greek": "Greek", "french": "French", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "saveChanges": "Save Changes", "editProfile": "Edit Profile", "joined": "Joined", "lastLogin": "Last login", "fullName": "Full Name", "bio": "Bio", "workInformation": "Work Information", "company": "Company", "department": "Department", "socialLinks": "Social Links", "linkedinUrl": "LinkedIn URL", "twitterUrl": "Twitter URL", "githubUrl": "GitHub URL", "notProvided": "Not provided", "securitySettings": "Security Settings", "twoFactorAuth": "Two-Factor Authentication", "twoFactorStatus": "Two-factor authentication is", "enabled": "enabled", "notEnabled": "not enabled", "forYourAccount": "for your account", "enable": "Enable", "disable": "Disable", "changeYourPassword": "Change your password", "updatePassword": "Update Password", "loginSessions": "<PERSON><PERSON>", "currentSession": "Current Session", "started": "Started", "signOutAllSessions": "Sign out of all other sessions", "accountActions": "Account Actions", "deactivateAccount": "Deactivate account", "languageSetEnglish": "The application language is set to English", "languageSetGreek": "The application language is set to Greek", "languageSetFrench": "The application language is set to French", "change": "Change", "role": "Role"}, "customers": {"customersList": "Customers", "addNewCustomer": "Add New Customer", "editCustomer": "Edit Customer", "createNewCustomer": "Create New Customer", "searchCustomers": "Search customers by name, contact person, or email", "noCustomersFound": "No customers found", "noCustomersMessage": "Try adjusting your search or add a new customer.", "customerDetails": "Customer Details", "companyInformation": "Company Information", "primaryContact": "Primary Contact", "subscriptionOverview": "Subscription Overview", "viewVessels": "View Vessels", "backToCustomers": "Back to Customers", "customerName": "Customer Name", "fillAllRequired": "Please fill in all required fields", "basicInformation": "Basic Information", "contactDetails": "Contact Details", "updateCustomerInfo": "Update customer information", "addNewCustomerInfo": "Add a new customer to the platform", "blocks": "BLOCKS", "statUnavailable": "Statistics will be available after creating the customer", "updateCustomer": "Update Customer", "createCustomer": "Create Customer", "customerNotFound": "Customer not found", "deleteCustomer": "Delete Customer", "confirmDelete": "Confirm Delete", "deleteWarning": "Are you sure you want to delete {{customerName}}? This action cannot be undone.", "deleteFailed": "Failed to delete customer. Please try again.", "recentActivity": "Recent Activity", "subscription": {"currentPlan": "Current Plan", "changePlan": "Change Plan", "enterprise": "Enterprise", "unlimited": "Unlimited", "allowed": "allowed", "billingInformation": "Billing Information", "updatePaymentMethod": "Update Payment Method", "billingHistory": "Billing History", "invoiceNumber": "Invoice Number", "date": "Date", "amount": "Amount", "paid": "Paid", "subscriptionAndBilling": "Subscription & Billing", "errorLoading": "Error loading subscriptions", "sensorLimit": "sensor limit", "noSubscriptions": "No subscriptions available", "subscriptionDetails": "Subscription Details", "noSubscriptionDetails": "No subscription details available"}, "tabs": {"overview": "Overview", "companies": "Companies", "vessels": "<PERSON><PERSON><PERSON>", "subscriptionBilling": "Subscription & Billing"}, "companies": {"addCompany": "Add Company", "noCompanies": "No companies", "noCompaniesMessage": "Get started by adding a company to this customer."}, "vessels": {"allVessels": "All Vessels (across companies)", "addVesselToCompany": "Add Vessel to Company", "unknownCompany": "Unknown Company", "noVesselsFound": "No vessels found", "noVesselsMessage": "None of this customer's companies have any vessels yet.", "viewDetails": "View Details", "needCompanyFirst": "Add a company first", "needCompanyMessage": "You need to add a company before you can add vessels.", "addCompanyFirst": "Add Company First", "addVesselFirst": "Add Vessel First"}, "errors": {"duplicateName": "A customer with this name already exists. Please choose a different name.", "duplicateEmail": "This email address is already in use by another customer. Please use a different email address.", "nameAlreadyExists": "This customer name is already taken. Please use a different name.", "technicalError": "A technical error occurred while processing your request. Please try again later.", "validationFailed": "The provided information is invalid. Please check your input and try again.", "operationFailed": "Operation failed. Please try again or contact support if the problem persists.", "nameRequired": "Customer name is required.", "nameTooShort": "Customer name must be at least 2 characters long."}}, "companies": {"companiesList": "Companies", "addNewCompany": "Add New Company", "editCompany": "Edit Company", "createNewCompany": "Create New Company", "searchCompanies": "Search companies by name or location", "noCompaniesFound": "No companies found", "noCompaniesMessage": "Try adjusting your search or filters, or add a new company.", "noCompaniesMessageUser": "Try adjusting your search or filters to find companies.", "companyInformation": "Company Information", "companyDetails": "Company Details", "companyName": "Company Name", "industry": "Industry", "location": "Location", "locationDetails": "Location Details", "updateCompanyInfo": "Update company information", "addNewCompanyInfo": "Add a new company to the platform", "statUnavailable": "Statistics will be available after creating the company", "updateCompany": "Update Company", "createCompany": "Create Company", "companyNotFound": "Company not found", "backToCompanies": "Back to Companies", "deleteCompany": "Delete Company", "confirmDelete": "Confirm Delete", "deleteWarning": "Are you sure you want to delete {{companyName}}? This action cannot be undone.", "deleteFailed": "Failed to delete company. Please try again.", "companyVessels": "Company Vessels", "noVesselsFound": "No vessels found", "noVesselsMessage": "This company doesn't have any vessels yet.", "addNewVessel": "Add New Vessel", "companyOwners": "Company Owners", "addNewOwner": "Add New Owner", "noOwnersFound": "No owners found", "noOwnersMessage": "This company has no owners yet.", "companyPerformance": "Company Performance", "quickActions": "Quick Actions", "generateReport": "Generate Report", "tabs": {"overview": "Overview", "vessels": "<PERSON><PERSON><PERSON>", "owners": "Owners", "contracts": "Contracts"}, "nameRequired": "Company name is required", "createFailed": "Failed to create company. Please try again.", "selectCustomer": "Select Customer", "assignToCustomer": "Assign to Customer", "customerRequired": "Customer selection is required", "assignedCustomer": "Assigned Customer", "stats": {"activeVessels": "Active Vessels", "totalVessels": "Total Vessels", "ofFleet": "of fleet", "inFleet": "in fleet", "sensorAlertRate": "Sensor Alert <PERSON>", "onTimePerformance": "On-time Performance", "maintenanceRate": "Maintenance Rate", "fromLastWeek": "from last week", "fromLastMonth": "from last month", "fromTarget": "from target"}, "contracts": {"contracts": "Contracts", "addNewContract": "Add New Contract", "validUntil": "Valid until", "active": "Active", "renewing": "Renewing"}}, "owners": {"ownersList": "Owners", "addNewOwner": "Add New Owner", "editOwner": "Edit Owner", "createNewOwner": "Create New Owner", "searchOwners": "Search owners by name, email or phone", "noOwnersFound": "No owners found", "noOwnersMessage": "Try adjusting your search or filters, or add a new owner.", "noOwnersMessageUser": "Try adjusting your search or filters to find owners.", "ownerInformation": "Owner Information", "ownerDetails": "Owner Details", "ownerName": "Owner Name", "basicInformation": "Basic Information", "contactDetails": "Contact Details", "companyAssignment": "Company Assignment", "email": "Email", "phone": "Phone", "address": "Address", "company": "Company", "updateOwnerInfo": "Update owner information", "addNewOwnerInfo": "Add a new owner to the platform", "updateOwner": "Update Owner", "createOwner": "Create Owner", "ownerNotFound": "Owner not found", "backToOwners": "Back to Owners", "deleteOwner": "Delete Owner", "confirmDelete": "Confirm Delete", "deleteWarning": "Are you sure you want to delete {{ownerName}}? This action cannot be undone.", "deleteFailed": "Failed to delete owner. Please try again.", "createFailed": "Failed to create owner. Please try again.", "updateFailed": "Failed to update owner. Please try again.", "nameRequired": "Owner name is required", "companyRequired": "Company selection is required", "selectCompany": "Select Company", "assignToCompany": "Assign to Company", "assignedCompany": "Assigned Company", "selectedCompany": "Selected Company", "enterOwnerName": "Enter owner name", "enterEmail": "Enter email address", "enterPhone": "Enter phone number", "enterAddress": "Enter address"}, "notifications": {"title": "Notifications", "noNotifications": "No notifications", "noNotificationsMessage": "You have no notifications at this time.", "markAllRead": "Mark all as read", "markingAllRead": "Marking all as read...", "viewAll": "View all notifications", "justNow": "just now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago", "unreadCount": "{{count}} unread of {{total}} total", "unreadCountOnly": "{{count}} unread", "allRead": "All notifications read", "types": {"info": "Information", "success": "Success", "warning": "Warning", "error": "Error", "alert": "<PERSON><PERSON>"}, "categories": {"system": "System", "sensor": "Sensor", "vessel": "<PERSON><PERSON><PERSON>", "subscription": "Subscription", "maintenance": "Maintenance"}}, "emailProcessing": {"title": "Email Processing", "microsoftLogin": "Microsoft Login", "tokenStatus": "Token Status", "lastSync": "Last Sync", "nextSync": "Next Sync", "connectToMicrosoft": "Connect to Microsoft", "connected": "Connected", "notConnected": "Not Connected", "tokenExpires": "Token expires", "reAuthRequired": "Re-authentication required", "syncSuccessful": "Successful", "syncFailed": "Failed", "syncPending": "Pending", "never": "Never"}}