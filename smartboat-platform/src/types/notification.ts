export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'alert';
  category: 'system' | 'sensor' | 'vessel' | 'subscription' | 'maintenance';
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  timestamp: string;
  userId?: string;
  customerId?: string;
  vesselId?: string;
  sensorId?: string;
  companyId?: string;
  actionUrl?: string;
  actionText?: string;
  metadata?: Record<string, any>;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  inApp: boolean;
  categories: {
    system: boolean;
    sensor: boolean;
    vessel: boolean;
    subscription: boolean;
    maintenance: boolean;
  };
}

export interface NotificationSettings {
  id: string;
  userId: string;
  preferences: NotificationPreferences;
  updatedAt: string;
}

export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<Notification['type'], number>;
  byCategory: Record<Notification['category'], number>;
}