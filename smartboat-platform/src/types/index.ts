// Define user roles as a string union type for better type safety
export type UserRole = 'Administrator' | 'Customer' | 'Manager' | 'Technician' | 'Viewer';

// Define permissions for role-based access control
export interface RolePermissions {
  viewAllSubscriptions: boolean;
  addSubscription: boolean;
  editSubscription: boolean;
  viewOwnSubscription: boolean;
  changeOwnSubscription: boolean;
  viewAllVessels: boolean;
  viewOwnVessels: boolean;
  viewAllSensors: boolean;
  viewOwnSensors: boolean;
  editSensors: boolean;
  viewAllCompanies: boolean;
  viewOwnCompanies: boolean;
  manageCompanies: boolean;
  manageCustomers: boolean;
  manageUsers: boolean;
}

// Notification preferences
export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
}

// Social media links
export interface SocialLinks {
  linkedin: string;
  twitter: string;
  github: string;
}

// User model
export interface User {
  id: string; // Converted from backend Guid (assuming users also use Guids)
  name: string;
  email: string;
  role: User<PERSON><PERSON>;
  avatar?: string;
  joined: string;
  status: string; // Active, Inactive, Suspended
  lastLogin: string;
  twoFactorEnabled: boolean;
  company?: string;
  department?: string;
  phone?: string;
  timezone: string;
  language: string;
  bio?: string;

  // References to related entities
  customerId?: string; // If user is associated with a customer - converted from backend Guid
  vesselId?: string; // If user is associated with a specific vessel - converted from backend Guid
  subscriptionIds: string[]; // IDs of subscriptions the user has access to - converted from backend Guids

  // Preferences and settings
  socialLinks: SocialLinks;
  notifications: NotificationPreferences;

  // Method to check if user has a specific permission based on their role
  // This would be implemented in the authentication service
  // hasPermission?(permission: keyof RolePermissions): boolean;
}

// Note: This interface is duplicated - the main definition is in ./vessel.ts

// Export all type definitions
export * from './sensor';
export * from './vessel';
export * from './company';
export * from './customer';
export * from './user';
export * from './dashboard';

// Note: This interface is duplicated - the main definition is in ./customer.ts

export interface SensorDataPoint {
  time: string;
  timestamp: number;
  temperature: number;
  humidity: number;
}

export interface VesselPathPoint {
  lat: number;
  lng: number;
  timestamp: string;
  location: string;
}

// Note: This interface is duplicated - the main definition is in ./company.ts

export interface Subscription {
  id: string; // Converted from backend Guid
  name: string;
  type: string; // Standard, Professional, Enterprise, Custom
  vesselId: string; // Converted from backend Guid
  vesselName?: string;
  startDate?: string; // Converted from backend DateTime
  endDate?: string; // Converted from backend DateTime
  price?: number; // Decimal in backend
  billingFrequency?: string; // Monthly, Quarterly, Annually
  status?: string; // Active, Expired, Canceled, Pending
  sensorLimit?: number;
  features?: string[];
  lastUpdated?: string; // Converted from backend DateTime
  created?: string; // Converted from backend DateTime - added from backend
  changed?: string; // Converted from backend DateTime - added from backend
}