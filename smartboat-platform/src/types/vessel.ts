import { Company } from './company';
import { Sensor } from './sensor';
import { Owner } from './owner';

// Define the allowed vessel types matching the backend enum
export type VesselType = 'Μηχανοκίνητο' | 'Ιστιοπλοϊκό';

export interface Vessel {
  id: string; // Converted from backend Guid
  name: string;
  number: string;
  type: VesselType;
  location: string;
  status: string;
  startDate?: string; // Converted from backend DateTime
  endDate?: string; // Converted from backend DateTime
  image?: string;
  onsigners?: number;
  offsigners?: number;
  companyId: string; // Converted from backend Guid
  ownerId?: string; // Converted from backend Guid
  company?: Company; // Only available in VesselDto responses
  owner?: Owner; // Only available in VesselDto responses
  sensors?: Sensor[]; // Only available in VesselDto responses
  lastUpdated?: string; // Converted from backend DateTime
  created?: string; // Converted from backend DateTime
  changed?: string; // Converted from backend DateTime
}