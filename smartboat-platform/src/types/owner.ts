import { Company } from './company';

export interface Owner {
  id: string; // Converted from backend Guid
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  companyId: string; // Converted from backend Guid
  company?: Company; // Only available in OwnerDto responses
  status: string;
  lastUpdated?: string; // Converted from backend DateTime
  created?: string; // Converted from backend DateTime
  changed?: string; // Converted from backend DateTime
}

// Request DTOs for API calls
export interface CreateOwnerRequest {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  companyId: string;
}

export interface UpdateOwnerRequest {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  companyId: string;
}

export interface DeleteOwnerRequest {
  id: string;
}

export interface GetOwnerRequest {
  id: string;
}

export interface ListOwnerRequest {
  pageLimit?: number;
  pageOffset?: number;
  searchTerm?: string;
}

// Response DTOs
export interface ListOwnerResponse {
  owners: Owner[];
  pageLimit: number;
  pageOffset: number;
  total: number;
}