export interface UserPreferences {
  language: string;
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'Administrator' | 'Manager' | 'User' | 'Customer';
  company: string;
  avatar?: string;
  lastLogin: string;
  twoFactorEnabled: boolean;
  preferences: UserPreferences;
}

export interface UserSubscription {
  id: string;
  name: string;
  type: string;
  status: 'Active' | 'Pending' | 'Expired';
  startDate: string;
  endDate: string;
  price: number;
  billingFrequency: 'Monthly' | 'Quarterly' | 'Annual';
  customerName: string;
  features?: string[];
}
