export interface DashboardStats {
  totalVessels: number;
  activeVessels: number;
  totalSensors: number;
  activeSensors: number;
  totalCompanies: number;
  totalCustomers: number;
  dataPoints: number;
  alertsToday: number;
}

export interface RecentActivity {
  id: string;
  type: 'alert' | 'status' | 'system';
  title: string;
  description: string;
  timestamp: string;
  vesselId?: string;
  vesselName?: string;
}

export interface VesselSummary {
  id: string;
  name: string;
  status: string;
  location: string;
  lastUpdated: string;
  sensors: number;
  activeSensors: number;
}

export interface SensorSummary {
  id: string;
  name: string;
  type: string;
  status: string;
  lastReading: string;
  lastUpdated: string;
  vesselId: string;
  vesselName: string;
}
