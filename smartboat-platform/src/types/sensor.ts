import { Vessel } from './vessel';

export interface Sensor {
  id: string; // Converted from backend Guid
  name: string;
  type: string; // Flexible string, not enum
  vesselId: string; // Converted from backend Guid
  location?: string;
  status?: string;
  lastReading?: string; // Converted from backend DateTime
  lastUpdated?: string; // Converted from backend DateTime
  alertThreshold?: string; // Single string field, not min/max
  vessel?: Vessel; // Only available in SensorDto responses
  dataPoints?: SensorDataPoint[]; // Only available in SensorDto responses
  created?: string; // Converted from backend DateTime
  changed?: string; // Converted from backend DateTime
}

export interface SensorDataPoint {
  timestamp: string;
  value: number;
  unit: string;
  sensorId: string;
}

// Keep these for backward compatibility with existing components
export interface SensorAlert {
  id: string;
  sensorId: string;
  type: 'threshold' | 'battery' | 'connection' | 'maintenance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

export interface SensorThreshold {
  id: string;
  sensorId: string;
  type: 'min' | 'max';
  value: number;
  enabled: boolean;
}