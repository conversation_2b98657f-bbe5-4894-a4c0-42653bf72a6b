import axios from 'axios';
import { MOCK_DATA_ENABLED } from './mockConfig';

// Import mock data services
import * as vesselsMock from './mockData/vesselsMockData';
import * as companiesMock from './mockData/companiesMockData';
import * as customersMock from './mockData/customersMockData';
import * as dashboardMock from './mockData/dashboardMockData';
import * as userMock from './mockData/userMockData';

// Create axios instance for real API calls
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// API service that conditionally uses mock or real data
const apiService = {
  // Vessels API
  vessels: {
    getAll: () => {
      if (MOCK_DATA_ENABLED) {
        return vesselsMock.getVessels();
      }
      return api.get('/vessels').then(response => response.data);
    },
    getById: (id: string) => {
      if (MOCK_DATA_ENABLED) {
        return vesselsMock.getVesselById(id);
      }
      return api.get(`/vessels/${id}`).then(response => response.data);
    },
    create: (vessel: any) => {
      if (MOCK_DATA_ENABLED) {
        return vesselsMock.createVessel(vessel);
      }
      return api.post('/vessels', vessel).then(response => response.data);
    },
    update: (id: string, vessel: any) => {
      if (MOCK_DATA_ENABLED) {
        return vesselsMock.updateVessel(id, vessel);
      }
      return api.put(`/vessels/${id}`, vessel).then(response => response.data);
    },
    delete: (id: string) => {
      if (MOCK_DATA_ENABLED) {
        return vesselsMock.deleteVessel(id);
      }
      return api.delete(`/vessels/${id}`).then(response => response.data);
    }
  },

  // Companies API
  companies: {
    getAll: () => {
      if (MOCK_DATA_ENABLED) {
        return companiesMock.getCompanies();
      }
      return api.get('/companies').then(response => response.data);
    },
    getById: (id: string) => {
      if (MOCK_DATA_ENABLED) {
        return companiesMock.getCompanyById(id);
      }
      return api.get(`/companies/${id}`).then(response => response.data);
    },
    create: (company: any) => {
      if (MOCK_DATA_ENABLED) {
        return companiesMock.createCompany(company);
      }
      return api.post('/companies', company).then(response => response.data);
    },
    update: (id: string, company: any) => {
      if (MOCK_DATA_ENABLED) {
        return companiesMock.updateCompany(id, company);
      }
      return api.put(`/companies/${id}`, company).then(response => response.data);
    },
    delete: (id: string) => {
      if (MOCK_DATA_ENABLED) {
        return companiesMock.deleteCompany(id);
      }
      return api.delete(`/companies/${id}`).then(response => response.data);
    }
  },

  // Customers API
  customers: {
    getAll: () => {
      if (MOCK_DATA_ENABLED) {
        return customersMock.getCustomers();
      }
      return api.get('/customers').then(response => response.data);
    },
    getById: (id: string) => {
      if (MOCK_DATA_ENABLED) {
        return customersMock.getCustomerById(id);
      }
      return api.get(`/customers/${id}`).then(response => response.data);
    },
    create: (customer: any) => {
      if (MOCK_DATA_ENABLED) {
        return customersMock.createCustomer(customer);
      }
      return api.post('/customers', customer).then(response => response.data);
    },
    update: (id: string, customer: any) => {
      if (MOCK_DATA_ENABLED) {
        return customersMock.updateCustomer(id, customer);
      }
      return api.put(`/customers/${id}`, customer).then(response => response.data);
    },
    delete: (id: string) => {
      if (MOCK_DATA_ENABLED) {
        return customersMock.deleteCustomer(id);
      }
      return api.delete(`/customers/${id}`).then(response => response.data);
    }
  },

  // Dashboard API
  dashboard: {
    getStats: () => {
      if (MOCK_DATA_ENABLED) {
        return dashboardMock.getDashboardStats();
      }
      return api.get('/dashboard/stats').then(response => response.data);
    },
    getRecentActivity: () => {
      if (MOCK_DATA_ENABLED) {
        return dashboardMock.getRecentActivity();
      }
      return api.get('/dashboard/activity').then(response => response.data);
    },
    getVesselSummaries: () => {
      if (MOCK_DATA_ENABLED) {
        return dashboardMock.getVesselSummaries();
      }
      return api.get('/dashboard/vessels').then(response => response.data);
    },
    getSensorSummaries: () => {
      if (MOCK_DATA_ENABLED) {
        return dashboardMock.getSensorSummaries();
      }
      return api.get('/dashboard/sensors').then(response => response.data);
    }
  },

  // User API
  user: {
    getCurrentUser: () => {
      if (MOCK_DATA_ENABLED) {
        return userMock.getCurrentUser();
      }
      return api.get('/user/profile').then(response => response.data);
    },
    updateProfile: (userData: any) => {
      if (MOCK_DATA_ENABLED) {
        return userMock.updateUserProfile(userData);
      }
      return api.put('/user/profile', userData).then(response => response.data);
    },
    updatePreferences: (preferences: any) => {
      if (MOCK_DATA_ENABLED) {
        return userMock.updateUserPreferences(preferences);
      }
      return api.put('/user/preferences', preferences).then(response => response.data);
    },
    getSubscriptions: () => {
      if (MOCK_DATA_ENABLED) {
        return userMock.getUserSubscriptions();
      }
      return api.get('/user/subscriptions').then(response => response.data);
    },
    updatePassword: (currentPassword: string, newPassword: string) => {
      if (MOCK_DATA_ENABLED) {
        return userMock.updateUserPassword(currentPassword, newPassword);
      }
      return api.put('/user/password', { currentPassword, newPassword }).then(response => response.data);
    },
    toggleTwoFactorAuth: (enabled: boolean) => {
      if (MOCK_DATA_ENABLED) {
        return userMock.toggleTwoFactorAuth(enabled);
      }
      return api.put('/user/2fa', { enabled }).then(response => response.data);
    }
  }
};

export default apiService;
