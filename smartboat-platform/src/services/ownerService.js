/**
 * Owner API Service
 * Handles API requests related to owners
 */

import apiClient from './apiClient';
import { FEATURES } from '../config/envConfig';

const BASE_ENDPOINT = '/api/owner';

const ownerService = {
  /**
   * Get all owners
   * @param {Object} options - Request options (pageLimit, pageOffset, searchTerm, etc.)
   * @returns {Promise<Array>} List of owners (data extracted from API response)
   */
  getAllOwners: async (options = {}) => {
    const listRequest = {
      pageLimit: options.pageLimit || 20,
      pageOffset: options.pageOffset || 0,
      searchTerm: options.searchTerm || null // Send null instead of empty string
    };
    
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, listRequest, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
      
      // Handle API response format: { data: OwnerDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      }
      
      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response;
      }
      
      console.warn('Unexpected owner list response format:', response);
      return [];
    } catch (error) {
      console.error('Error fetching owners:', error);
      throw error;
    }
  },

  /**
   * Get an owner by ID
   * @param {string} id - Owner ID (GUID)
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Owner data
   */
  getOwnerById: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/get`, { id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Create a new owner
   * @param {Object} ownerData - Owner data (name, email, phone, address, companyId)
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Created owner
   */
  createOwner: (ownerData, options = {}) => {
    const createRequest = {
      name: ownerData.name,
      email: ownerData.email || null,
      phone: ownerData.phone || null,
      address: ownerData.address || null,
      companyId: ownerData.companyId
    };
    return apiClient.post(`${BASE_ENDPOINT}/create`, createRequest, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Update an owner
   * @param {string} id - Owner ID (GUID)
   * @param {Object} ownerData - Updated owner data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated owner
   */
  updateOwner: (id, ownerData, options = {}) => {
    const updateRequest = {
      id,
      name: ownerData.name,
      email: ownerData.email || null,
      phone: ownerData.phone || null,
      address: ownerData.address || null,
      companyId: ownerData.companyId
    };
    return apiClient.post(`${BASE_ENDPOINT}/update`, updateRequest, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Delete an owner
   * @param {string} id - Owner ID (GUID)
   * @param {Object} options - Request options
   * @returns {Promise<boolean>} True if deletion was successful
   */
  deleteOwner: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/delete`, { id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Get owners by company
   * @param {string} companyId - Company ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Company owners
   */
  getOwnersByCompany: async (companyId, options = {}) => {
    console.log('🔍 OwnerService: Fetching owners for company:', companyId);
    console.log('🔍 OwnerService: Options:', options);
    console.log('🔍 OwnerService: USE_MOCK_FALLBACK:', FEATURES.USE_MOCK_FALLBACK);
    
    // Ensure all required fields are present and valid
    const listRequest = {
      pageLimit: options.pageLimit || 100,
      pageOffset: options.pageOffset || 0,
      searchTerm: options.searchTerm || null // Send null instead of empty string
    };
    
    try {
      console.log('🔍 OwnerService: Making API call to:', `${BASE_ENDPOINT}/list`);
      console.log('🔍 OwnerService: Request payload:', listRequest);
      
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, listRequest, { 
        ...options, 
        useMockFallback: false // Force API call, no mock fallback
      });
      
      console.log('🔍 OwnerService: Raw API response:', response);
      
      // Handle API response format: { data: OwnerDto[], metadata: MetadataDto }
      let owners = [];
      if (response && response.data && Array.isArray(response.data)) {
        owners = response.data;
        console.log('🔍 OwnerService: Extracted owners from response.data:', owners.length);
      } else if (Array.isArray(response)) {
        owners = response;
        console.log('🔍 OwnerService: Response is direct array:', owners.length);
      } else {
        console.warn('🔍 OwnerService: Unexpected response format:', response);
      }
      
      // Filter by company ID on frontend (since API doesn't support company filtering)
      // Backend returns CompanyId (Pascal case), frontend expects companyId (camel case)
      const filteredOwners = owners.filter(owner => 
        owner.CompanyId === companyId || owner.companyId === companyId
      );
      console.log('🔍 OwnerService: Filtered owners for company', companyId, ':', filteredOwners);
      
      return filteredOwners;
    } catch (error) {
      console.error('❌ OwnerService: Error fetching owners by company:', error);
      console.error('❌ OwnerService: Error details:', {
        message: error.message,
        status: error.status,
        data: error.data
      });
      throw error;
    }
  }
};

export default ownerService;