/**
 * User API Service
 * Handles API requests related to users
 */

import apiClient from './apiClient';
import { FEATURES } from '../config/envConfig';

const BASE_ENDPOINT = '/api/user';

const userService = {
  /**
   * Get all users (admin only)
   * @param {Object} options - Request options
   * @returns {Promise<Array>} List of users
   */
  getAllUsers: (options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/list`, { filter: {}, paging: { page: 1, size: 20 } }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Get a user by ID
   * @param {string} id - User ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} User data
   */
  getUserById: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/get`, { id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Create a new user (admin only)
   * @param {Object} userData - User data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Created user
   */
  createUser: (userData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/register`, userData, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Update a user
   * @param {string} id - User ID
   * @param {Object} userData - Updated user data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated user
   */
  updateUser: (id, userData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/update`, { ...userData, id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Delete a user (admin only)
   * @param {string} id - User ID
   * @param {Object} options - Request options
   * @returns {Promise<void>}
   */
  deleteUser: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/deactivate`, { id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Update user role (admin only)
   * @param {string} id - User ID
   * @param {string} role - New role
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated user
   */
  updateUserRole: (id, role, options = {}) => {
    return apiClient.patch(`${BASE_ENDPOINT}/${id}/role`, { role }, options);
  },

  /**
   * Get user notification settings
   * @param {string} id - User ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Notification settings
   */
  getUserNotificationSettings: (id, options = {}) => {
    return apiClient.get(`${BASE_ENDPOINT}/${id}/notifications`, options);
  },

  /**
   * Update user notification settings
   * @param {string} id - User ID
   * @param {Object} notificationSettings - Updated notification settings
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated notification settings
   */
  updateUserNotificationSettings: (id, notificationSettings, options = {}) => {
    return apiClient.put(`${BASE_ENDPOINT}/${id}/notifications`, notificationSettings, options);
  },
};

export default userService;