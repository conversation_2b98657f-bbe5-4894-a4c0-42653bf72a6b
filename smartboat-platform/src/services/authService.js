/**
 * Authentication API Service
 * Handles API requests related to user authentication
 */

import apiClient from './apiClient';
import { setAuthToken, clearAuthToken } from '../utils/authUtils';

const BASE_ENDPOINT = '/api/auth';

const authService = {
  /**
   * Login a user
   * @param {Object} credentials - User credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Login response with user and token
   */
  login: async (credentials, options = {}) => {
    const response = await apiClient.post(`${BASE_ENDPOINT}/login`, credentials, {
      ...options,
      useMockFallback: false, // Don't fallback to mock data for auth
    });
    
    if (response && response.token) {
      setAuthToken(response.token);
    }
    
    return response;
  },

  /**
   * Logout the current user
   * @returns {Promise<void>}
   */
  logout: async () => {
    try {
      // Call logout endpoint to invalidate token on server
      await apiClient.post(`${BASE_ENDPOINT}/logout`);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear token locally regardless of server response
      clearAuthToken();
    }
  },

  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Registration response
   */
  register: (userData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/register`, userData, options);
  },

  /**
   * Request password reset
   * @param {Object} data - Password reset request data
   * @param {string} data.email - User email
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Reset request response
   */
  requestPasswordReset: (data, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/reset-password`, data, options);
  },

  /**
   * Reset password with token
   * @param {Object} data - Password reset data
   * @param {string} data.token - Reset token
   * @param {string} data.password - New password
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Reset response
   */
  resetPassword: (data, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/reset-password/confirm`, data, options);
  },

  /**
   * Get current user profile
   * @param {Object} options - Request options
   * @returns {Promise<Object>} User profile
   */
  getCurrentUser: (options = {}) => {
    return apiClient.get(`${BASE_ENDPOINT}/me`, options);
  },

  /**
   * Update current user profile
   * @param {Object} userData - User data to update
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated user profile
   */
  updateProfile: (userData, options = {}) => {
    return apiClient.put(`${BASE_ENDPOINT}/me`, userData, options);
  },

  /**
   * Change user password
   * @param {Object} passwordData - Password change data
   * @param {string} passwordData.currentPassword - Current password
   * @param {string} passwordData.newPassword - New password
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Password change response
   */
  changePassword: (passwordData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/change-password`, passwordData, options);
  },

  /**
   * Verify auth token validity
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Token validation response
   */
  verifyToken: (options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/verify-token`, {}, options);
  },
};

export default authService;