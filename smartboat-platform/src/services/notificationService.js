/**
 * Notification API Service
 * Handles API requests related to notifications
 */

import apiClient from './apiClient';
import { FEATURES } from '../config/envConfig';
import { mockNotifications, mockNotificationStats } from '../utils/mockData';

const BASE_ENDPOINT = '/api/notification';

const notificationService = {
  /**
   * Get all notifications for the current user
   * @param {Object} options - Request options
   * @returns {Promise<Array>} List of notifications
   */
  getNotifications: async (options = {}) => {
    try {
      const payload = {
        pageLimit: options.pageLimit || 50,
        pageOffset: options.pageOffset || 0,
        sortField: options.sortField || 'timestamp',
        sortOrder: options.sortOrder || 'DESC'
      };

      // Add supported API filters
      if (options.status) payload.status = options.status; // "Sent" or "Read"
      if (options.eventType) payload.eventType = options.eventType; // "Alert", "System", etc.
      if (options.channel) payload.channel = options.channel; // "Email", "In-App", etc.

      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, payload, { 
        ...options, 
        useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK 
      });
      
      // Handle API response format: { data: NotificationDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        const mappedNotifications = response.data.map(notification => {
          
          // API returns content field, split it into title and message if possible
          const content = notification.content || notification.Content || '';
          const lines = content.split('\n');
          const title = lines[0] || 'Notification';
          const message = lines.length > 1 ? lines.slice(1).join('\n') : content;
          
          const mapped = {
            ...notification,
            id: notification.id || notification.Id,
            title: title,
            message: message,
            type: notification.eventType?.toLowerCase() === 'alert' ? 'warning' : 'info',
            category: notification.eventType?.toLowerCase() === 'alert' ? 'sensor' : 'system',
            userId: notification.userId || notification.UserId,
            customerId: notification.customerId || notification.CustomerId,
            vesselId: notification.vesselId || notification.VesselId,
            sensorId: notification.sensorId || notification.SensorId,
            companyId: notification.companyId || notification.CompanyId,
            isRead: notification.status === 'Read',
            priority: notification.eventType?.toLowerCase() === 'alert' ? 'high' : 'medium',
            timestamp: notification.timestamp || notification.Timestamp || notification.created || notification.Created,
            actionUrl: notification.actionUrl || notification.ActionUrl,
            actionText: notification.actionText || notification.ActionText,
          };
          return mapped;
        });
        return mappedNotifications;
      }
      
      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response.map(notification => ({
          ...notification,
          id: notification.id || notification.Id,
          userId: notification.userId || notification.UserId,
          customerId: notification.customerId || notification.CustomerId,
          vesselId: notification.vesselId || notification.VesselId,
          sensorId: notification.sensorId || notification.SensorId,
          companyId: notification.companyId || notification.CompanyId,
          isRead: notification.isRead ?? notification.IsRead ?? false,
          timestamp: notification.timestamp || notification.Timestamp || notification.createdAt || notification.CreatedAt,
        }));
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching notifications:', error);
      // Return mock data as fallback
      if (options.useMockFallback !== false) {
        let filteredNotifications = [...mockNotifications];
        
        // Apply filters if specified (matching API parameters)
        if (options.status === 'Read') {
          filteredNotifications = filteredNotifications.filter(n => n.isRead === true);
        } else if (options.status === 'Sent') {
          filteredNotifications = filteredNotifications.filter(n => n.isRead === false);
        }
        if (options.eventType) {
          // Map eventType to our mock data type field
          const typeMap = { 'Alert': 'warning', 'System': 'info' };
          const mappedType = typeMap[options.eventType] || options.eventType.toLowerCase();
          filteredNotifications = filteredNotifications.filter(n => n.type === mappedType);
        }
        if (options.channel) {
          // Mock data doesn't have channel field, but we can filter by category
          filteredNotifications = filteredNotifications.filter(n => n.category);
        }
        
        // Apply pagination
        const offset = options.pageOffset || 0;
        const limit = options.pageLimit || 50;
        filteredNotifications = filteredNotifications.slice(offset, offset + limit);
        
        return filteredNotifications;
      }
      throw error;
    }
  },

  /**
   * Get notification statistics
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Notification statistics
   */
  getNotificationStats: async (options = {}) => {
    try {
      // Get unread count from the specific endpoint
      const unreadResponse = await apiClient.post(`${BASE_ENDPOINT}/unread-count`, {}, { 
        ...options, 
        useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK 
      });
      
      // For now, we only have unread count. Total could be fetched separately if needed
      const unreadCount = typeof unreadResponse === 'number' ? unreadResponse : unreadResponse?.payload || 0;
      
      return {
        total: 0, // API doesn't provide total count
        unread: unreadCount,
        byType: {}, // API doesn't provide breakdown by type
        byCategory: {} // API doesn't provide breakdown by category
      };
    } catch (error) {
      console.error('Error fetching notification stats:', error);
      // Return mock data as fallback
      if (options.useMockFallback !== false) {
        return mockNotificationStats;
      }
      throw error;
    }
  },

  /**
   * Mark notification as read
   * @param {string} id - Notification ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated notification
   */
  markAsRead: async (id, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/${id}/mark-read`, {}, { 
        ...options, 
        useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK 
      });
      
      return response;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  },

  /**
   * Mark all notifications as read
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response
   */
  markAllAsRead: async (options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/mark-all-read`, {}, { 
        ...options, 
        useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK 
      });
      
      return response;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  },

  /**
   * Delete notification
   * @param {string} id - Notification ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response
   */
  deleteNotification: async (id, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/delete`, {
        Id: id
      }, { ...options, useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK });
      
      return response;
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  },

  /**
   * Get notification preferences
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Notification preferences
   */
  getPreferences: async (options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/preferences`, {}, { 
        ...options, 
        useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK 
      });
      
      return response;
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      throw error;
    }
  },

  /**
   * Update notification preferences
   * @param {Object} preferences - Notification preferences
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated preferences
   */
  updatePreferences: async (preferences, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/preferences/update`, preferences, { 
        ...options, 
        useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK 
      });
      
      return response;
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }
};

export default notificationService;