/**
 * Company API Service
 * Handles API requests related to companies
 */

import apiClient from './apiClient';
import { FEATURES } from '../config/envConfig';

const BASE_ENDPOINT = '/api/company';

const companyService = {
  /**
   * Get all companies
   * @param {Object} options - Request options (pageLimit, pageOffset, sortField, sortOrder, searchTerm, etc.)
   * @returns {Promise<Array>} List of companies (data extracted from API response)
   */
  getAllCompanies: async (options = {}) => {
    const listRequest = {
      pageLimit: options.pageLimit || 20,
      pageOffset: options.pageOffset || 0,
      sortField: options.sortField || 'name',
      sortOrder: options.sortOrder || 'asc',
      searchTerm: options.searchTerm || '',
      name: options.name || null,
      status: options.status || null,
      customerId: options.customerId || null
    };
    
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, listRequest, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
      
      // Handle API response format: { data: CompanyDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      }
      
      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response;
      }
      
      console.warn('Unexpected company list response format:', response);
      return [];
    } catch (error) {
      console.error('Error fetching companies:', error);
      throw error;
    }
  },

  /**
   * Get a company by ID
   * @param {string} id - Company ID (GUID)
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Company data
   */
  getCompanyById: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/get`, { id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Get a company by name
   * @param {string} name - Company name
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Company data
   */
  getCompanyByName: (name, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/get`, { name }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Create a new company
   * @param {Object} companyData - Company data (name, location, industry, status, customerId)
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Created company
   */
  createCompany: (companyData, options = {}) => {
    const createRequest = {
      name: companyData.name,
      location: companyData.location || null,
      industry: companyData.industry || null,
      status: companyData.status || 'Active',
      customerId: companyData.customerId || null
    };
    return apiClient.post(`${BASE_ENDPOINT}/create`, createRequest, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Update a company
   * @param {string} id - Company ID (GUID)
   * @param {Object} companyData - Updated company data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated company
   */
  updateCompany: (id, companyData, options = {}) => {
    const updateRequest = {
      id,
      name: companyData.name,
      location: companyData.location || null,
      industry: companyData.industry || null,
      status: companyData.status || null,
      customerId: companyData.customerId || null
    };
    return apiClient.post(`${BASE_ENDPOINT}/update`, updateRequest, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Delete a company
   * @param {string} id - Company ID (GUID)
   * @param {Object} options - Request options
   * @returns {Promise<boolean>} True if deletion was successful
   */
  deleteCompany: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/delete`, { Id: id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Deactivate a company by name
   * @param {string} name - Company name
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Deactivated company
   */
  deleteCompanyByName: (name, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/deactivate`, { name }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Get vessels for a company
   * @param {string} id - Company ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Company vessels
   */
  getCompanyVessels: async (id, options = {}) => {
    const listRequest = {
      pageLimit: options.pageLimit || 100,
      pageOffset: options.pageOffset || 0,
      sortField: options.sortField || 'name',
      sortOrder: options.sortOrder || 'ASC',
      CompanyId: id  // Filter by company ID (case-sensitive property name)
    };
    
    try {
      const response = await apiClient.post('/api/vessel/list', listRequest, { 
        ...options, 
        useMockFallback: FEATURES.USE_MOCK_FALLBACK 
      });
      
      // Handle API response format: { data: VesselDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        // Process vessels to ensure sensors is a number, not an array
        return response.data.map(vessel => ({
          ...vessel,
          sensors: Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)
        }));
      }
      
      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        // Process vessels to ensure sensors is a number, not an array
        return response.map(vessel => ({
          ...vessel,
          sensors: Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)
        }));
      }
      
      console.warn('Unexpected company vessels response format:', response);
      return [];
    } catch (error) {
      console.error('Error fetching company vessels:', error);
      throw error;
    }
  },

  /**
   * Get customers for a company
   * @param {string} id - Company ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Company customers
   */
  getCompanyCustomers: (id, options = {}) => {
    return apiClient.get(`${BASE_ENDPOINT}/${id}/customers`, options);
  },
  
  /**
   * Get subscriptions for a company
   * @param {string} id - Company ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Company subscriptions
   */
  getCompanySubscriptions: (id, options = {}) => {
    return apiClient.get(`${BASE_ENDPOINT}/${id}/subscriptions`, options);
  },
};

export default companyService;