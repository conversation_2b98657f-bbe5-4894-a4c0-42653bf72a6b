/**
 * Customer API Service
 * Handles API requests related to customers
 */

import apiClient from './apiClient';
import { FEATURES } from '../config/envConfig';

const BASE_ENDPOINT = '/api/customer';

const customerService = {
  /**
   * Get all customers
   * @param {Object} options - Request options
   * @returns {Promise<Array>} List of customers
   */
  getAllCustomers: async (options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, {
        pageLimit: 100,
        pageOffset: 0,
        sortField: 'name',
        sortOrder: 'ASC'
      }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
      
      // Handle API response format: { data: CustomerDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      }
      
      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response;
      }
      
      console.warn('Unexpected customer list response format:', response);
      return [];
    } catch (error) {
      console.error('Failed to fetch customers:', error);
      throw error;
    }
  },

  /**
   * Get a customer by ID
   * @param {string} id - Customer ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Customer data
   */
  getCustomerById: async (id, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/get`, { Id: id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
      
      // Handle direct customer response or envelope format
      if (response && typeof response === 'object') {
        // If it's already a customer object with expected properties, return it
        if (response.name || response.email || response.contactPerson) {
          return response;
        }
        // If it's an envelope with data, extract it
        if (response.data) {
          return response.data;
        }
      }
      
      console.warn('Unexpected customer response format:', response);
      return null;
    } catch (error) {
      console.error('Failed to fetch customer:', error);
      throw error;
    }
  },

  /**
   * Create a new customer
   * @param {Object} customerData - Customer data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Created customer
   */
  createCustomer: (customerData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/create`, customerData, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Update a customer
   * @param {string} id - Customer ID
   * @param {Object} customerData - Updated customer data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated customer
   */
  updateCustomer: (id, customerData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/update`, { ...customerData, id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Delete a customer
   * @param {string} id - Customer ID
   * @param {Object} options - Request options
   * @returns {Promise<void>}
   */
  deleteCustomer: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/deactivate`, { Id: id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Get subscriptions for a customer
   * @param {string} id - Customer ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Customer subscriptions
   */
  getCustomerSubscriptions: (id, options = {}) => {
    return apiClient.get(`${BASE_ENDPOINT}/${id}/subscriptions`, options);
  },

  /**
   * Get vessels associated with a customer
   * @param {string} id - Customer ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Customer vessels
   */
  getCustomerVessels: (id, options = {}) => {
    return apiClient.get(`${BASE_ENDPOINT}/${id}/vessels`, options);
  },
};

export default customerService;