import { mockResponse } from '../mockConfig';
import { Vessel } from '../../types/vessel';

// Mock data for vessels
const mockVessels: Vessel[] = [
  {
    id: '1',
    name: 'Ocean Explorer',
    type: 'Yacht',
    model: 'Luxury 5000',
    manufacturer: 'SeaCraft',
    yearBuilt: 2018,
    length: 25.5,
    beam: 6.2,
    draft: 2.1,
    registrationNumber: 'REG-12345',
    homePort: 'Miami',
    status: 'Active',
    lastUpdated: '2023-10-15',
    sensors: 8,
    image: 'https://placehold.co/600x400?text=Ocean+Explorer',
    location: {
      latitude: 25.7617,
      longitude: -80.1918,
      lastUpdated: '2023-10-15T14:30:00Z'
    },
    owner: {
      id: '101',
      name: '<PERSON>',
      email: '<EMAIL>'
    },
    companyId: '1' // OceanTech Marine
  },
  {
    id: '2',
    name: 'Coastal Voyager',
    type: 'Sailboat',
    model: 'WindRider 42',
    manufacturer: 'SailTech',
    yearBuilt: 2020,
    length: 12.8,
    beam: 4.3,
    draft: 1.8,
    registrationNumber: 'REG-67890',
    homePort: 'San Francisco',
    status: 'Active',
    lastUpdated: '2023-10-14',
    sensors: 5,
    image: 'https://placehold.co/600x400?text=Coastal+Voyager',
    location: {
      latitude: 37.7749,
      longitude: -122.4194,
      lastUpdated: '2023-10-14T18:45:00Z'
    },
    owner: {
      id: '102',
      name: 'Emily Johnson',
      email: '<EMAIL>'
    },
    companyId: '2' // Pacific Sailing Co.
  },
  {
    id: '3',
    name: 'Sea Breeze',
    type: 'Catamaran',
    model: 'DualHull 38',
    manufacturer: 'OceanWave',
    yearBuilt: 2019,
    length: 11.6,
    beam: 6.8,
    draft: 1.2,
    registrationNumber: 'REG-24680',
    homePort: 'Sydney',
    status: 'Maintenance',
    lastUpdated: '2023-10-10',
    sensors: 6,
    image: 'https://placehold.co/600x400?text=Sea+Breeze',
    location: {
      latitude: -33.8688,
      longitude: 151.2093,
      lastUpdated: '2023-10-10T09:15:00Z'
    },
    owner: {
      id: '103',
      name: 'Michael Brown',
      email: '<EMAIL>'
    },
    companyId: '3' // Global Shipping Ltd.
  },
  {
    id: '4',
    name: 'Arctic Pioneer',
    type: 'Fishing Vessel',
    model: 'Trawler 65',
    manufacturer: 'Nordic Marine',
    yearBuilt: 2021,
    length: 19.8,
    beam: 5.4,
    draft: 2.8,
    registrationNumber: 'REG-13579',
    homePort: 'Oslo',
    status: 'Active',
    lastUpdated: '2023-10-13',
    sensors: 12,
    image: 'https://placehold.co/600x400?text=Arctic+Pioneer',
    location: {
      latitude: 59.9139,
      longitude: 10.7522,
      lastUpdated: '2023-10-13T12:00:00Z'
    },
    owner: {
      id: '104',
      name: 'Erik Larsen',
      email: '<EMAIL>'
    },
    companyId: '4' // Nordic Vessels AS
  }
];

// Mock API functions
export const getVessels = (): Promise<Vessel[]> => {
  return mockResponse(mockVessels);
};

export const getVesselById = (id: string): Promise<Vessel | undefined> => {
  const vessel = mockVessels.find(v => v.id === id);
  return mockResponse(vessel);
};

export const createVessel = (vessel: Omit<Vessel, 'id'>): Promise<Vessel> => {
  const newVessel = {
    ...vessel,
    id: `${mockVessels.length + 1}`,
    lastUpdated: new Date().toISOString().split('T')[0]
  };
  mockVessels.push(newVessel as Vessel);
  return mockResponse(newVessel as Vessel);
};

export const updateVessel = (id: string, vessel: Partial<Vessel>): Promise<Vessel | undefined> => {
  const index = mockVessels.findIndex(v => v.id === id);
  if (index === -1) return mockResponse(undefined);
  
  mockVessels[index] = {
    ...mockVessels[index],
    ...vessel,
    lastUpdated: new Date().toISOString().split('T')[0]
  };
  
  return mockResponse(mockVessels[index]);
};

export const deleteVessel = (id: string): Promise<boolean> => {
  const index = mockVessels.findIndex(v => v.id === id);
  if (index === -1) return mockResponse(false);
  
  mockVessels.splice(index, 1);
  return mockResponse(true);
};
