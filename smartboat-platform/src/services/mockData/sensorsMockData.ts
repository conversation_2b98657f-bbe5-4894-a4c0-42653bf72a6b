import { mockResponse } from '../mockConfig';
import { Sensor } from '../../types/sensor';

// Mock data for sensors
const mockSensors: Sensor[] = [
  // Sensors for Ocean Explorer (Vessel 1)
  {
    id: '1',
    name: 'Engine Temperature',
    type: 'Temperature',
    description: 'Main engine temperature monitoring',
    vesselId: '1',
    status: 'Active',
    lastReading: 85.5,
    unit: '°C',
    minThreshold: 40,
    maxThreshold: 100,
    lastUpdated: '2023-10-15T14:30:00Z',
    batteryLevel: 85,
    signalStrength: 92
  },
  {
    id: '2',
    name: 'Fuel Level',
    type: 'Level',
    description: 'Main fuel tank level monitoring',
    vesselId: '1',
    status: 'Active',
    lastReading: 68.2,
    unit: '%',
    minThreshold: 10,
    maxThreshold: 100,
    lastUpdated: '2023-10-15T14:25:00Z',
    batteryLevel: 90,
    signalStrength: 88
  },
  {
    id: '3',
    name: 'GPS Position',
    type: 'Position',
    description: 'Primary GPS location tracking',
    vesselId: '1',
    status: 'Active',
    lastReading: 25.7617,
    unit: 'lat',
    minThreshold: -90,
    maxThreshold: 90,
    lastUpdated: '2023-10-15T14:30:00Z',
    batteryLevel: 95,
    signalStrength: 95
  },

  // Sensors for Coastal Voyager (Vessel 2)
  {
    id: '4',
    name: 'Wind Speed',
    type: 'Speed',
    description: 'Wind speed monitoring for sailing',
    vesselId: '2',
    status: 'Active',
    lastReading: 12.5,
    unit: 'knots',
    minThreshold: 0,
    maxThreshold: 50,
    lastUpdated: '2023-10-14T18:45:00Z',
    batteryLevel: 78,
    signalStrength: 82
  },
  {
    id: '5',
    name: 'Hull Pressure',
    type: 'Pressure',
    description: 'Hull integrity pressure monitoring',
    vesselId: '2',
    status: 'Active',
    lastReading: 1.2,
    unit: 'bar',
    minThreshold: 0.8,
    maxThreshold: 2.0,
    lastUpdated: '2023-10-14T18:40:00Z',
    batteryLevel: 72,
    signalStrength: 79
  },

  // Sensors for Sea Breeze (Vessel 3)
  {
    id: '6',
    name: 'Water Temperature',
    type: 'Temperature',
    description: 'Sea water temperature monitoring',
    vesselId: '3',
    status: 'Warning',
    lastReading: 28.5,
    unit: '°C',
    minThreshold: 15,
    maxThreshold: 35,
    lastUpdated: '2023-10-10T09:15:00Z',
    batteryLevel: 45,
    signalStrength: 65
  },
  {
    id: '7',
    name: 'Depth Sounder',
    type: 'Distance',
    description: 'Water depth measurement',
    vesselId: '3',
    status: 'Active',
    lastReading: 15.8,
    unit: 'm',
    minThreshold: 2,
    maxThreshold: 100,
    lastUpdated: '2023-10-10T09:10:00Z',
    batteryLevel: 88,
    signalStrength: 91
  },

  // Sensors for Arctic Pioneer (Vessel 4)
  {
    id: '8',
    name: 'Fish Finder',
    type: 'Sonar',
    description: 'Fish detection and water column analysis',
    vesselId: '4',
    status: 'Active',
    lastReading: 22.0,
    unit: 'm',
    minThreshold: 5,
    maxThreshold: 200,
    lastUpdated: '2023-10-13T12:00:00Z',
    batteryLevel: 92,
    signalStrength: 87
  },
  {
    id: '9',
    name: 'Net Tension',
    type: 'Force',
    description: 'Fishing net tension monitoring',
    vesselId: '4',
    status: 'Active',
    lastReading: 1250,
    unit: 'N',
    minThreshold: 100,
    maxThreshold: 2000,
    lastUpdated: '2023-10-13T11:55:00Z',
    batteryLevel: 81,
    signalStrength: 84
  },
  {
    id: '10',
    name: 'Ice Detection',
    type: 'Boolean',
    description: 'Ice formation detection on deck',
    vesselId: '4',
    status: 'Active',
    lastReading: 0,
    unit: 'bool',
    minThreshold: 0,
    maxThreshold: 1,
    lastUpdated: '2023-10-13T12:05:00Z',
    batteryLevel: 77,
    signalStrength: 89
  }
];

// Mock API functions
export const getSensors = (): Promise<Sensor[]> => {
  return mockResponse(mockSensors);
};

export const getSensorById = (id: string): Promise<Sensor | undefined> => {
  const sensor = mockSensors.find(s => s.id === id);
  return mockResponse(sensor);
};

export const getSensorsByVessel = (vesselId: string): Promise<Sensor[]> => {
  const vesselSensors = mockSensors.filter(s => s.vesselId === vesselId);
  return mockResponse(vesselSensors);
};

export const createSensor = (sensor: Omit<Sensor, 'id'>): Promise<Sensor> => {
  const newSensor = {
    ...sensor,
    id: `${mockSensors.length + 1}`,
    lastUpdated: new Date().toISOString()
  };
  mockSensors.push(newSensor as Sensor);
  return mockResponse(newSensor as Sensor);
};

export const updateSensor = (id: string, sensor: Partial<Sensor>): Promise<Sensor | undefined> => {
  const index = mockSensors.findIndex(s => s.id === id);
  if (index === -1) return mockResponse(undefined);
  
  mockSensors[index] = {
    ...mockSensors[index],
    ...sensor,
    lastUpdated: new Date().toISOString()
  };
  
  return mockResponse(mockSensors[index]);
};

export const deleteSensor = (id: string): Promise<boolean> => {
  const index = mockSensors.findIndex(s => s.id === id);
  if (index === -1) return mockResponse(false);
  
  mockSensors.splice(index, 1);
  return mockResponse(true);
};