import { mockResponse } from '../mockConfig';
import { User, UserSubscription } from '../../types/user';

// Mock data for current user
const mockCurrentUser: User = {
  id: '1',
  firstName: 'Alex',
  lastName: 'Morgan',
  email: '<EMAIL>',
  role: 'Administrator',
  company: 'SmartBoat Technologies',
  avatar: 'https://placehold.co/200x200?text=AM',
  lastLogin: '2023-10-15T08:30:00Z',
  twoFactorEnabled: true,
  preferences: {
    language: 'en',
    theme: 'light',
    notifications: {
      email: true,
      push: true,
      sms: false
    }
  }
};

// Mock data for user subscriptions
const mockUserSubscriptions: UserSubscription[] = [
  {
    id: '1',
    name: 'Enterprise Plan',
    type: 'Annual Subscription',
    status: 'Active',
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    price: 999.99,
    billingFrequency: 'Annual',
    customerName: 'SmartBoat Technologies',
    features: [
      'Unlimited vessels',
      'Unlimited sensors',
      'Real-time monitoring',
      'Advanced analytics',
      'API access',
      'Priority support',
      'Custom integrations',
      'White-label options'
    ]
  },
  {
    id: '2',
    name: 'Data Storage Add-on',
    type: 'Storage Extension',
    status: 'Active',
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    price: 199.99,
    billingFrequency: 'Annual',
    customerName: 'SmartBoat Technologies',
    features: [
      'Extended data retention (5 years)',
      'Increased storage capacity (10TB)',
      'Data export capabilities',
      'Advanced backup options'
    ]
  }
];

// Mock API functions
export const getCurrentUser = (): Promise<User> => {
  return mockResponse(mockCurrentUser);
};

export const updateUserProfile = (userData: Partial<User>): Promise<User> => {
  Object.assign(mockCurrentUser, userData);
  return mockResponse(mockCurrentUser);
};

export const updateUserPreferences = (preferences: Partial<User['preferences']>): Promise<User['preferences']> => {
  mockCurrentUser.preferences = {
    ...mockCurrentUser.preferences,
    ...preferences
  };
  return mockResponse(mockCurrentUser.preferences);
};

export const getUserSubscriptions = (): Promise<UserSubscription[]> => {
  return mockResponse(mockUserSubscriptions);
};

export const updateUserPassword = (currentPassword: string, newPassword: string): Promise<boolean> => {
  // In a real app, this would validate the current password and update to the new one
  // For mock purposes, we'll just return success
  return mockResponse(true);
};

export const toggleTwoFactorAuth = (enabled: boolean): Promise<boolean> => {
  mockCurrentUser.twoFactorEnabled = enabled;
  return mockResponse(true);
};
