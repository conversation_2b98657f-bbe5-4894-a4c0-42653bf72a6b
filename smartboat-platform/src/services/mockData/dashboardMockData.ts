import { mockResponse } from '../mockConfig';
import { DashboardStats, RecentActivity, VesselSummary, SensorSummary } from '../../types/dashboard';

// Mock data for dashboard statistics
const mockDashboardStats: DashboardStats = {
  totalVessels: 42,
  activeVessels: 38,
  totalSensors: 186,
  activeSensors: 172,
  totalCompanies: 8,
  totalCustomers: 4,
  dataPoints: 1250000,
  alertsToday: 5
};

// Mock data for recent activity
const mockRecentActivity: RecentActivity[] = [
  {
    id: '1',
    type: 'alert',
    title: 'High Engine Temperature',
    description: 'Engine temperature exceeded threshold on Ocean Explorer',
    timestamp: '2023-10-15T14:30:00Z',
    vesselId: '1',
    vesselName: 'Ocean Explorer'
  },
  {
    id: '2',
    type: 'status',
    title: 'Vessel Status Change',
    description: 'Sea Breeze status changed to Maintenance',
    timestamp: '2023-10-14T09:15:00Z',
    vesselId: '3',
    vesselName: 'Sea Breeze'
  },
  {
    id: '3',
    type: 'alert',
    title: 'Low Fuel Level',
    description: 'Fuel level below 20% on Coastal Voyager',
    timestamp: '2023-10-13T16:45:00Z',
    vesselId: '2',
    vesselName: 'Coastal Voyager'
  },
  {
    id: '4',
    type: 'system',
    title: 'System Update',
    description: 'Platform updated to version 2.3.0',
    timestamp: '2023-10-12T08:00:00Z'
  },
  {
    id: '5',
    type: 'status',
    title: 'New Vessel Added',
    description: 'Nordic Explorer added to the fleet',
    timestamp: '2023-10-10T11:30:00Z',
    vesselId: '4',
    vesselName: 'Nordic Explorer'
  }
];

// Mock data for vessel summaries
const mockVesselSummaries: VesselSummary[] = [
  {
    id: '1',
    name: 'Ocean Explorer',
    status: 'Active',
    location: 'Miami Harbor',
    lastUpdated: '2023-10-15T14:30:00Z',
    sensors: 8,
    activeSensors: 8
  },
  {
    id: '2',
    name: 'Coastal Voyager',
    status: 'Active',
    location: 'San Francisco Bay',
    lastUpdated: '2023-10-14T18:45:00Z',
    sensors: 5,
    activeSensors: 5
  },
  {
    id: '3',
    name: 'Sea Breeze',
    status: 'Maintenance',
    location: 'Sydney Harbor',
    lastUpdated: '2023-10-10T09:15:00Z',
    sensors: 6,
    activeSensors: 4
  },
  {
    id: '4',
    name: 'Nordic Explorer',
    status: 'Active',
    location: 'Oslo Fjord',
    lastUpdated: '2023-10-09T12:00:00Z',
    sensors: 7,
    activeSensors: 7
  }
];

// Mock data for sensor summaries
const mockSensorSummaries: SensorSummary[] = [
  {
    id: '1',
    name: 'Engine Temperature',
    type: 'Temperature',
    status: 'Active',
    lastReading: '85°C',
    lastUpdated: '2023-10-15T14:30:00Z',
    vesselId: '1',
    vesselName: 'Ocean Explorer'
  },
  {
    id: '2',
    name: 'Fuel Level',
    type: 'Level',
    status: 'Warning',
    lastReading: '18%',
    lastUpdated: '2023-10-13T16:45:00Z',
    vesselId: '2',
    vesselName: 'Coastal Voyager'
  },
  {
    id: '3',
    name: 'GPS Location',
    type: 'Position',
    status: 'Active',
    lastReading: '37.7749° N, 122.4194° W',
    lastUpdated: '2023-10-14T18:45:00Z',
    vesselId: '2',
    vesselName: 'Coastal Voyager'
  },
  {
    id: '4',
    name: 'Battery Voltage',
    type: 'Voltage',
    status: 'Active',
    lastReading: '12.6V',
    lastUpdated: '2023-10-15T13:15:00Z',
    vesselId: '1',
    vesselName: 'Ocean Explorer'
  },
  {
    id: '5',
    name: 'Bilge Pump',
    type: 'Status',
    status: 'Inactive',
    lastReading: 'Off',
    lastUpdated: '2023-10-10T09:15:00Z',
    vesselId: '3',
    vesselName: 'Sea Breeze'
  }
];

// Mock API functions
export const getDashboardStats = (): Promise<DashboardStats> => {
  return mockResponse(mockDashboardStats);
};

export const getRecentActivity = (): Promise<RecentActivity[]> => {
  return mockResponse(mockRecentActivity);
};

export const getVesselSummaries = (): Promise<VesselSummary[]> => {
  return mockResponse(mockVesselSummaries);
};

export const getSensorSummaries = (): Promise<SensorSummary[]> => {
  return mockResponse(mockSensorSummaries);
};
