import { mockResponse } from '../mockConfig';
import { Company } from '../../types/company';

// Mock data for companies
const mockCompanies: Company[] = [
  {
    id: '1',
    name: 'OceanTech Marine',
    location: 'Miami, FL',
    industry: 'Maritime Technology',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'www.oceantech.com',
    vessels: 12,
    sensors: 48,
    lastUpdated: '2023-10-15',
    status: 'Active',
    subscription: 'Enterprise',
    customerId: '1' // Maritime Solutions Inc.
  },
  {
    id: '2',
    name: 'Pacific Sailing Co.',
    location: 'San Francisco, CA',
    industry: 'Recreational Boating',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'www.pacificsailing.com',
    vessels: 8,
    sensors: 24,
    lastUpdated: '2023-10-12',
    status: 'Active',
    subscription: 'Professional',
    customerId: '2' // Coastal Operations LLC
  },
  {
    id: '3',
    name: 'Global Shipping Ltd.',
    location: 'London, UK',
    industry: 'Commercial Shipping',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+44 20 5555 1234',
    website: 'www.globalshipping.com',
    vessels: 25,
    sensors: 150,
    lastUpdated: '2023-10-10',
    status: 'Active',
    subscription: 'Enterprise',
    customerId: '3' // European Shipping Group
  },
  {
    id: '4',
    name: 'Nordic Vessels AS',
    location: 'Oslo, Norway',
    industry: 'Commercial Fishing',
    contactPerson: 'Erik Larsen',
    email: '<EMAIL>',
    phone: '+47 21 555 6789',
    website: 'www.nordicvessels.com',
    vessels: 15,
    sensors: 60,
    lastUpdated: '2023-10-08',
    status: 'Active',
    subscription: 'Professional',
    customerId: '4' // Pacific Marine Services
  }
];

// Mock API functions
export const getCompanies = (): Promise<Company[]> => {
  return mockResponse(mockCompanies);
};

export const getCompanyById = (id: string): Promise<Company | undefined> => {
  const company = mockCompanies.find(c => c.id === id);
  return mockResponse(company);
};

export const createCompany = (company: Omit<Company, 'id'>): Promise<Company> => {
  const newCompany = {
    ...company,
    id: `${mockCompanies.length + 1}`,
    lastUpdated: new Date().toISOString().split('T')[0]
  };
  mockCompanies.push(newCompany as Company);
  return mockResponse(newCompany as Company);
};

export const updateCompany = (id: string, company: Partial<Company>): Promise<Company | undefined> => {
  const index = mockCompanies.findIndex(c => c.id === id);
  if (index === -1) return mockResponse(undefined);
  
  mockCompanies[index] = {
    ...mockCompanies[index],
    ...company,
    lastUpdated: new Date().toISOString().split('T')[0]
  };
  
  return mockResponse(mockCompanies[index]);
};

export const deleteCompany = (id: string): Promise<boolean> => {
  const index = mockCompanies.findIndex(c => c.id === id);
  if (index === -1) return mockResponse(false);
  
  mockCompanies.splice(index, 1);
  return mockResponse(true);
};
