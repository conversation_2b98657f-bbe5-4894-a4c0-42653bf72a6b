import { mockResponse } from '../mockConfig';
import { Customer } from '../../types/customer';

// Mock data for customers
const mockCustomers: Customer[] = [
  {
    id: '1',
    name: 'Maritime Solutions Inc.',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Harbor Drive, New York, NY 10001',
    companies: 3,
    vessels: 18,
    sensors: 72,
    lastActive: '2023-10-15',
    status: 'Active',
    subscription: 'Enterprise',
    billingInfo: {
      billingAddress: '123 Harbor Drive, New York, NY 10001',
      paymentMethod: 'Credit Card',
      billingContact: 'Finance Department'
    }
  },
  {
    id: '2',
    name: 'Coastal Operations LLC',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Ocean Blvd, Miami, FL 33139',
    companies: 2,
    vessels: 10,
    sensors: 35,
    lastActive: '2023-10-14',
    status: 'Active',
    subscription: 'Professional',
    billingInfo: {
      billingAddress: '456 Ocean Blvd, Miami, FL 33139',
      paymentMethod: 'Bank Transfer',
      billingContact: '<PERSON>'
    }
  },
  {
    id: '3',
    name: 'European Shipping Group',
    contactPerson: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    phone: '+49 30 5555 1234',
    address: 'Hafenstrasse 10, Hamburg, 20457, Germany',
    companies: 5,
    vessels: 32,
    sensors: 180,
    lastActive: '2023-10-12',
    status: 'Active',
    subscription: 'Enterprise',
    billingInfo: {
      billingAddress: 'Hafenstrasse 10, Hamburg, 20457, Germany',
      paymentMethod: 'Credit Card',
      billingContact: 'Accounting Department'
    }
  },
  {
    id: '4',
    name: 'Pacific Marine Services',
    contactPerson: 'Lisa Wong',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Bay Street, San Francisco, CA 94111',
    companies: 1,
    vessels: 6,
    sensors: 24,
    lastActive: '2023-10-10',
    status: 'Active',
    subscription: 'Standard',
    billingInfo: {
      billingAddress: '789 Bay Street, San Francisco, CA 94111',
      paymentMethod: 'Bank Transfer',
      billingContact: 'Lisa Wong'
    }
  }
];

// Mock API functions
export const getCustomers = (): Promise<Customer[]> => {
  return mockResponse(mockCustomers);
};

export const getCustomerById = (id: string): Promise<Customer | undefined> => {
  const customer = mockCustomers.find(c => c.id === id);
  return mockResponse(customer);
};

export const createCustomer = (customer: Omit<Customer, 'id'>): Promise<Customer> => {
  const newCustomer = {
    ...customer,
    id: `${mockCustomers.length + 1}`,
    lastActive: new Date().toISOString().split('T')[0]
  };
  mockCustomers.push(newCustomer as Customer);
  return mockResponse(newCustomer as Customer);
};

export const updateCustomer = (id: string, customer: Partial<Customer>): Promise<Customer | undefined> => {
  const index = mockCustomers.findIndex(c => c.id === id);
  if (index === -1) return mockResponse(undefined);
  
  mockCustomers[index] = {
    ...mockCustomers[index],
    ...customer,
    lastActive: new Date().toISOString().split('T')[0]
  };
  
  return mockResponse(mockCustomers[index]);
};

export const deleteCustomer = (id: string): Promise<boolean> => {
  const index = mockCustomers.findIndex(c => c.id === id);
  if (index === -1) return mockResponse(false);
  
  mockCustomers.splice(index, 1);
  return mockResponse(true);
};
