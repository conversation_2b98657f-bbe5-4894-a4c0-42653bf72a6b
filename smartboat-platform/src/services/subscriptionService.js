/**
 * Subscription API Service
 * Handles API requests related to subscriptions
 */

import apiClient from './apiClient';

const BASE_ENDPOINT = '/api/subscription';

/**
 * Convert date from MM/DD/YYYY format to ISO format for API
 */
const convertDateToISO = (dateString) => {
  if (!dateString) return null;
  
  try {
    // Handle MM/DD/YYYY format
    if (dateString.includes('/')) {
      const [month, day, year] = dateString.split('/');
      const date = new Date(year, month - 1, day);
      return date.toISOString();
    }
    
    // If already in ISO format or other valid format, try to parse
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date.toISOString();
    }
    
    return null;
  } catch (error) {
    console.error('Error converting date:', error);
    return null;
  }
};

/**
 * Prepare subscription data for API by converting dates and handling GUIDs
 */
const prepareSubscriptionData = (subscriptionData) => {
  const prepared = { ...subscriptionData };
  
  // Convert dates to ISO format
  if (prepared.startDate) {
    prepared.startDate = convertDateToISO(prepared.startDate);
  }
  if (prepared.endDate) {
    prepared.endDate = convertDateToISO(prepared.endDate);
  }
  
  // Ensure vesselId is a proper GUID string
  if (prepared.vesselId && typeof prepared.vesselId === 'string') {
    prepared.vesselId = prepared.vesselId;
  }
  
  // Remove frontend-only fields that shouldn't be sent to API
  delete prepared.vesselName;
  delete prepared.lastUpdated;
  
  return prepared;
};

const subscriptionService = {
  /**
   * Get all subscriptions
   * @param {Object} options - Request options
   * @returns {Promise<Array>} List of subscriptions
   */
  getAllSubscriptions: async (options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, {
        pageLimit: 100,
        pageOffset: 0,
        sortField: 'name',
        sortOrder: 'ASC'
      }, { ...options, useMockFallback: false });
      
      // Handle API response format: { data: SubscriptionDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      }
      
      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response;
      }
      
      console.warn('Unexpected subscription list response format:', response);
      return [];
    } catch (error) {
      console.error('Subscription API error:', error);
      throw error;
    }
  },

  /**
   * Get a subscription by ID
   * @param {string} id - Subscription ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Subscription data
   */
  getSubscriptionById: async (id, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/get`, { Id: id }, { 
        ...options, 
        useMockFallback: false 
      });
      
      // Handle direct subscription response or envelope format
      if (response && typeof response === 'object') {
        // If it's already a subscription object with expected properties, return it
        if (response.name || response.type || response.vesselId) {
          return response;
        }
        // If it's an envelope with data, extract it
        if (response.data) {
          return response.data;
        }
      }
      
      console.warn('Unexpected subscription response format:', response);
      return null;
    } catch (error) {
      console.error('Subscription API error:', error);
      throw error;
    }
  },

  /**
   * Get subscriptions by vessel ID
   * @param {string} vesselId - Vessel ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} List of subscriptions for the vessel
   */
  getSubscriptionsByVessel: async (vesselId, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, {
        pageLimit: 100,
        pageOffset: 0,
        sortField: 'name',
        sortOrder: 'ASC',
        vesselId: vesselId
      }, { ...options, useMockFallback: false });
      
      // Handle API response format: { data: SubscriptionDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      }
      
      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response;
      }
      
      console.warn('Unexpected subscription list response format:', response);
      return [];
    } catch (error) {
      console.error('Subscription API error:', error);
      throw error;
    }
  },

  /**
   * Get subscriptions by customer ID
   * @param {string} customerId - Customer ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} List of subscriptions for the customer
   */
  getSubscriptionsByCustomer: async (customerId, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, {
        pageLimit: 100,
        pageOffset: 0,
        sortField: 'name',
        sortOrder: 'ASC',
        customerId: customerId
      }, { ...options, useMockFallback: false });
      
      // Handle API response format: { data: SubscriptionDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      }
      
      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response;
      }
      
      console.warn('Unexpected subscription list response format:', response);
      return [];
    } catch (error) {
      console.error('Subscription API error:', error);
      throw error;
    }
  },

  /**
   * Create a new subscription
   * @param {Object} subscriptionData - Subscription data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Created subscription
   */
  createSubscription: async (subscriptionData, options = {}) => {
    try {
      const preparedData = prepareSubscriptionData(subscriptionData);
      console.log('Creating subscription with prepared data:', preparedData);
      
      const response = await apiClient.post(`${BASE_ENDPOINT}/create`, preparedData, { 
        ...options, 
        useMockFallback: false 
      });
      
      return response;
    } catch (error) {
      console.error('Subscription API error:', error);
      throw error;
    }
  },

  /**
   * Update a subscription
   * @param {string} id - Subscription ID
   * @param {Object} subscriptionData - Updated subscription data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated subscription
   */
  updateSubscription: async (id, subscriptionData, options = {}) => {
    try {
      const preparedData = prepareSubscriptionData({ ...subscriptionData, id });
      console.log('Updating subscription with prepared data:', preparedData);
      
      const response = await apiClient.post(`${BASE_ENDPOINT}/update`, preparedData, { 
        ...options, 
        useMockFallback: false 
      });
      
      return response;
    } catch (error) {
      console.error('Subscription API error:', error);
      throw error;
    }
  },

  /**
   * Delete a subscription
   * @param {string} id - Subscription ID
   * @param {Object} options - Request options
   * @returns {Promise<void>}
   */
  deleteSubscription: async (id, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/deactivate`, { id }, { 
        ...options, 
        useMockFallback: false 
      });
      
      return response;
    } catch (error) {
      console.error('Subscription API error:', error);
      throw error;
    }
  },

  /**
   * Get subscription plans (available plan templates)
   * @param {Object} options - Request options
   * @returns {Promise<Array>} List of available subscription plans
   */
  getSubscriptionPlans: async (options = {}) => {
    try {
      const response = await apiClient.get(`${BASE_ENDPOINT}/plans`, { 
        ...options, 
        useMockFallback: false 
      });
      
      return response;
    } catch (error) {
      console.error('Subscription plans API error:', error);
      throw error;
    }
  },

  /**
   * Update subscription status
   * @param {string} id - Subscription ID
   * @param {string} status - New subscription status
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated subscription
   */
  updateSubscriptionStatus: async (id, status, options = {}) => {
    try {
      const response = await apiClient.patch(`${BASE_ENDPOINT}/${id}/status`, { status }, { 
        ...options, 
        useMockFallback: false 
      });
      
      return response;
    } catch (error) {
      console.error('Subscription status API error:', error);
      throw error;
    }
  },
};

export default subscriptionService;