import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// Create axios instance for image API calls
const imageApi = axios.create({
  baseURL: API_BASE_URL,
});

// Add request interceptor to include auth token
imageApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('smartboat_auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

export const imageService = {
  // Upload image for an entity
  uploadImage: async (entityId, imageFile) => {
    const formData = new FormData();
    formData.append('imageFile', imageFile);

    const response = await imageApi.post(`/api/image/upload/${entityId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Get all images for an entity
  getImagesByEntityId: async (entityId) => {
    const response = await imageApi.get(`/api/image/entity/${entityId}`);
    return response.data;
  },

  // Get image binary data
  getImageUrl: (imageId) => {
    return `${API_BASE_URL}/api/image/${imageId}`;
  },

  // Get image as blob with authentication
  getImageBlob: async (imageId) => {
    const response = await imageApi.get(`/api/image/${imageId}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Get image blob URL with caching
  getImageBlobUrl: async (imageId) => {
    try {
      const blob = await imageService.getImageBlob(imageId);
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('Failed to create blob URL for image:', imageId, error);
      throw error;
    }
  },

  // Delete an image
  deleteImage: async (imageId) => {
    const response = await imageApi.delete(`/api/image/${imageId}`);
    return response.data;
  },
};

export default imageService;