// Configuration for mock data service
import { FEATURES } from '../config/envConfig';

// Use the existing environment configuration, default to true for development
export const MOCK_DATA_ENABLED = FEATURES.USE_MOCK_DATA || FEATURES.USE_MOCK_FALLBACK || import.meta.env.MODE === 'development';

// Delay range for simulating network latency (in ms)
export const MOCK_DELAY_MIN = 800;
export const MOCK_DELAY_MAX = 1500;

// Helper to generate a random delay within the specified range
export const getRandomDelay = (): number => {
  return Math.floor(Math.random() * (MOCK_DELAY_MAX - MOCK_DELAY_MIN + 1)) + MOCK_DELAY_MIN;
};

// Helper to simulate API response with delay
export const mockResponse = <T>(data: T): Promise<T> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(data);
    }, getRandomDelay());
  });
};
