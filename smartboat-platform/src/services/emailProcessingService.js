/**
 * Email Processing API Service
 * Handles API requests related to email processing and Microsoft Graph integration
 */

import apiClient from './apiClient';

const BASE_ENDPOINT = '/api/emailprocessing';

const emailProcessingService = {
  /**
   * Exchange authorization code for tokens via server-side flow
   * @param {Object} exchangeData - Exchange data from Microsoft authentication
   * @param {string} exchangeData.authorizationCode - Authorization code from callback
   * @param {string} exchangeData.codeVerifier - PKCE code verifier
   * @param {string} exchangeData.redirectUri - Redirect URI used in auth flow
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Token data response
   */
  exchangeToken: async (exchangeData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/exchange-token`, exchangeData, options);
  },

  /**
   * Save Microsoft Graph authentication tokens
   * @param {Object} tokenData - Token data from Microsoft authentication
   * @param {string} tokenData.accessToken - Access token
   * @param {string} tokenData.refreshToken - Refresh token (optional)
   * @param {string} tokenData.expiresAt - Expiration timestamp
   * @param {string} tokenData.scope - Token scope
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Save token response
   */
  saveToken: async (tokenData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/save-token`, tokenData, options);
  },

  /**
   * Get current token status and sync information
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Token status with sync times
   */
  getTokenStatus: async (options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/token-status`, {}, options);
  },

  /**
   * Get processing schedule status
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Schedule status and next sync time
   */
  getScheduleStatus: async (options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/schedule-status`, {}, options);
  },

  /**
   * Update schedule configuration
   * @param {Object} scheduleConfig - Schedule configuration
   * @param {boolean} scheduleConfig.isEnabled - Whether schedule is enabled
   * @param {number} scheduleConfig.daysBack - Days back to process (optional)
   * @param {boolean} scheduleConfig.forceReprocess - Force reprocess flag (optional)
   * @param {string} scheduleConfig.description - Schedule description (optional)
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated schedule status
   */
  updateSchedule: async (scheduleConfig, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/update-schedule`, scheduleConfig, options);
  },

  /**
   * Get processing summary with sync history
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Processing summary with last sync info
   */
  getProcessingSummary: async (options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/summary`, {}, options);
  },

  /**
   * Trigger manual email processing and sync
   * @param {Object} processOptions - Processing options
   * @param {number} processOptions.daysBack - Number of days back to process (default: 7)
   * @param {boolean} processOptions.forceReprocess - Force reprocessing of already processed emails (default: false)
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Processing status response
   */
  processEmails: async (processOptions = { daysBack: 7, forceReprocess: false }, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/process-emails`, processOptions, options);
  }
};

export default emailProcessingService;