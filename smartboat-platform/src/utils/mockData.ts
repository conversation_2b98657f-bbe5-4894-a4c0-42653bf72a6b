// Mock data for companies
export const mockCompanies = [
  {
    id: 1,
    name: "Trans-Ocean Logistics",
    location: "Rotterdam, Netherlands",
    industry: "Shipping",
    vessels: 3,
    sensors: 27,
    status: "Active",
    customerId: 1,
    lastUpdated: "1 hour ago"
  },
  {
    id: 2,
    name: "European Shipping Corp",
    location: "Hamburg, Germany",
    industry: "Cargo",
    vessels: 2,
    sensors: 15,
    status: "Active",
    customerId: 1,
    lastUpdated: "30 minutes ago"
  },
  {
    id: 3,
    name: "Mediterranean Marine Services",
    location: "Marseille, France",
    industry: "Shipping",
    vessels: 3,
    sensors: 23,
    status: "Active",
    customerId: 2,
    lastUpdated: "2 hours ago"
  },
  {
    id: 4,
    name: "Gulf Maritime Solutions",
    location: "Dubai, UAE",
    industry: "Oil & Gas",
    vessels: 2,
    sensors: 18,
    status: "Active",
    customerId: 2,
    lastUpdated: "4 hours ago"
  },
  {
    id: 5,
    name: "Atlantic Freight Lines",
    location: "Miami, USA",
    industry: "Logistics",
    vessels: 3,
    sensors: 12,
    status: "Maintenance",
    customerId: 3,
    lastUpdated: "1 day ago"
  },
  {
    id: 6,
    name: "Horizon Container Services",
    location: "Singapore",
    industry: "Container Shipping",
    vessels: 2,
    sensors: 14,
    status: "Active",
    customerId: 1,
    lastUpdated: "3 hours ago"
  }
];

// Mock data for the platform
export const mockVesselData = [
  {
    id: 1,
    name: "HS Newman Bay",
    number: "18",
    type: "Container Ship",
    location: "Tuzla (Turkey)",
    status: "Active",
    startDate: "18/01/2019",
    endDate: "23/01/2019",
    image: "/vessel1.jpg",
    onsigners: 1,
    offsigners: 0,
    sensors: 12,
    lastUpdated: "2 hours ago",
    companyId: 1
  },
  {
    id: 2,
    name: "HS Mortier Bay",
    number: "1",
    type: "Bulk Carrier",
    location: "Hamburg (Germany)",
    status: "Active",
    startDate: "01/02/2019",
    endDate: "07/02/2019",
    image: "/vessel2.jpg",
    onsigners: 1,
    offsigners: 0,
    sensors: 8,
    lastUpdated: "30 minutes ago",
    companyId: 2
  },
  {
    id: 3,
    name: "HS Indian Bay",
    number: "15",
    type: "Container Ship",
    location: "Jebel Ali (UAE)",
    status: "Maintenance",
    startDate: "27/02/2019",
    endDate: "01/03/2019",
    image: "/vessel3.jpg",
    onsigners: 1,
    offsigners: 0,
    sensors: 15,
    lastUpdated: "1 day ago",
    companyId: 4
  },
  {
    id: 4,
    name: "HS Pacific Voyager",
    number: "22",
    type: "Tanker",
    location: "Singapore",
    status: "Active",
    startDate: "05/03/2023",
    endDate: "12/03/2023",
    image: "/vessel1.jpg",
    onsigners: 2,
    offsigners: 1,
    sensors: 10,
    lastUpdated: "5 hours ago",
    companyId: 6
  },
  {
    id: 5,
    name: "HS Atlantic Pioneer",
    number: "33",
    type: "Bulk Carrier",
    location: "Miami (USA)",
    status: "Active",
    startDate: "10/04/2023",
    endDate: "18/04/2023",
    image: "/vessel2.jpg",
    onsigners: 0,
    offsigners: 2,
    sensors: 7,
    lastUpdated: "1 day ago",
    companyId: 5
  },
];

// Mock data for sensors
export const mockSensorData = [
  {
    id: 1,
    name: "Engine Room Temperature",
    type: "Temperature",
    vessel: "HS Mortier Bay",
    vesselId: 2, // HS Mortier Bay
    location: "Engine Room",
    status: "Active",
    lastReading: "38.5°C",
    lastUpdated: "2 minutes ago",
    alertThreshold: "45°C"
  },
  {
    id: 2,
    name: "Hydraulic Pressure",
    type: "Pressure",
    vessel: "HS Indian Bay",
    vesselId: 3, // HS Indian Bay
    location: "Deck",
    status: "Active",
    lastReading: "2.4 MPa",
    lastUpdated: "5 minutes ago",
    alertThreshold: "3.0 MPa"
  },
  {
    id: 3,
    name: "Cargo Hold Humidity",
    type: "Humidity",
    vessel: "HS Newman Bay",
    vesselId: 1, // HS Newman Bay
    location: "Cargo Hold",
    status: "Warning",
    lastReading: "85%",
    lastUpdated: "10 minutes ago",
    alertThreshold: "80%"
  },
  {
    id: 4,
    name: "Main Engine Fuel Flow",
    type: "Flow Rate",
    vessel: "HS Mortier Bay",
    vesselId: 2, // HS Mortier Bay
    location: "Engine Room",
    status: "Active",
    lastReading: "450 L/h",
    lastUpdated: "7 minutes ago",
    alertThreshold: "600 L/h"
  },
  {
    id: 5,
    name: "Bridge Navigation Temperature",
    type: "Temperature",
    vessel: "HS Newman Bay",
    vesselId: 1, // HS Newman Bay
    location: "Bridge",
    status: "Active",
    lastReading: "22.1°C",
    lastUpdated: "1 minute ago",
    alertThreshold: "30°C"
  },
  {
    id: 6,
    name: "Deck Pressure Monitor",
    type: "Pressure",
    vessel: "HS Indian Bay",
    vesselId: 3, // HS Indian Bay
    location: "Main Deck",
    status: "Active",
    lastReading: "1.8 MPa",
    lastUpdated: "3 minutes ago",
    alertThreshold: "2.5 MPa"
  },
];

// Mock data for customers
export const mockCustomers = [
  {
    id: 1,
    name: "Global Shipping Co.",
    contactPerson: "John Smith",
    email: "<EMAIL>",
    phone: "+****************",
    companies: 3,
    vessels: 8,
    sensors: 42,
    status: "Active",
    lastActive: "Today"
  },
  {
    id: 2,
    name: "Maritime Solutions Ltd.",
    contactPerson: "Emma Johnson",
    email: "<EMAIL>",
    phone: "+44 20 1234 5678",
    companies: 2,
    vessels: 5,
    sensors: 18,
    status: "Active",
    lastActive: "Yesterday"
  },
  {
    id: 3,
    name: "Ocean Freight Inc.",
    contactPerson: "Michael Chen",
    email: "<EMAIL>",
    phone: "+****************",
    companies: 1,
    vessels: 3,
    sensors: 12,
    status: "Pending",
    lastActive: "3 days ago"
  },
];

// Mock data for subscriptions
export const mockSubscriptions = [
  {
    id: 1,
    name: "Standard Plan - Global Shipping",
    type: "Standard",
    customerId: 1,
    customerName: "Global Shipping Co.",
    startDate: "01/01/2023",
    endDate: "12/31/2023",
    price: 100,
    billingFrequency: "Monthly",
    status: "Active",
    sensorLimit: 5,
    features: [
      "Up to 5 sensors per vessel",
      "Daily data updates",
      "Email alerts",
      "Basic reporting"
    ],
    lastUpdated: "2 days ago",
    userIds: [1, 2, 5] // Users who have access to this subscription
  },
  {
    id: 2,
    name: "Professional Plan - Maritime Solutions",
    type: "Professional",
    customerId: 2,
    customerName: "Maritime Solutions Ltd.",
    startDate: "03/15/2023",
    endDate: "03/14/2024",
    price: 150,
    billingFrequency: "Quarterly",
    status: "Active",
    sensorLimit: 10,
    features: [
      "Up to 10 sensors per vessel",
      "Hourly data updates",
      "SMS and email alerts",
      "Advanced reporting",
      "30-day data history"
    ],
    lastUpdated: "1 week ago",
    userIds: [1, 3, 6]
  },
  {
    id: 3,
    name: "Enterprise Plan - Ocean Freight",
    type: "Enterprise",
    customerId: 3,
    customerName: "Ocean Freight Inc.",
    startDate: "06/01/2023",
    endDate: "05/31/2024",
    price: 200,
    billingFrequency: "Annually",
    status: "Pending",
    sensorLimit: null, // Unlimited
    features: [
      "Unlimited sensors per vessel",
      "Real-time data updates",
      "Priority support",
      "Advanced analytics",
      "1-year data history"
    ],
    lastUpdated: "3 days ago",
    userIds: [1, 4]
  }
];

// Mock user data
export const mockUsers = [
  {
    id: 1,
    name: "John Doe",
    email: "<EMAIL>",
    role: "Administrator",
    avatar: "",
    joined: "January 15, 2023",
    status: "Active",
    lastLogin: "2 hours ago",
    twoFactorEnabled: true,
    company: "SmartBoat Technologies",
    department: "Administration",
    phone: "+****************",
    timezone: "America/New_York",
    language: "English",
    bio: "System administrator with 10+ years of experience in maritime technology.",
    subscriptionIds: [1, 2, 3], // Admin has access to all subscriptions
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/johndoe/",
      twitter: "https://twitter.com/johndoe",
      github: ""
    },
    notifications: {
      email: true,
      push: true,
      sms: false
    }
  },
  {
    id: 2,
    name: "Emily Chen",
    email: "<EMAIL>",
    role: "Customer",
    avatar: "",
    joined: "February 20, 2023",
    status: "Active",
    lastLogin: "1 day ago",
    twoFactorEnabled: true,
    company: "Global Shipping Co.",
    department: "Operations",
    phone: "+****************",
    timezone: "America/Los_Angeles",
    language: "English",
    bio: "Fleet operations manager responsible for monitoring vessel performance.",
    customerId: 1, // Global Shipping Co.
    subscriptionIds: [1], // Standard Plan
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/emilychen/",
      twitter: "",
      github: ""
    },
    notifications: {
      email: true,
      push: true,
      sms: true
    }
  },
  {
    id: 3,
    name: "Michael Johnson",
    email: "<EMAIL>",
    role: "Customer",
    avatar: "",
    joined: "March 10, 2023",
    status: "Active",
    lastLogin: "3 hours ago",
    twoFactorEnabled: false,
    company: "Maritime Solutions Ltd.",
    department: "Technical",
    phone: "+44 20 1234 5678",
    timezone: "Europe/London",
    language: "English",
    bio: "Technical director overseeing sensor deployment and monitoring.",
    customerId: 2, // Maritime Solutions Ltd.
    subscriptionIds: [2], // Professional Plan
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/michaeljohnson/",
      twitter: "https://twitter.com/mjohnson",
      github: ""
    },
    notifications: {
      email: true,
      push: false,
      sms: false
    }
  },
  {
    id: 4,
    name: "Sarah Kim",
    email: "<EMAIL>",
    role: "Customer",
    avatar: "",
    joined: "April 5, 2023",
    status: "Active",
    lastLogin: "12 hours ago",
    twoFactorEnabled: true,
    company: "Ocean Freight Inc.",
    department: "Technology",
    phone: "+****************",
    timezone: "America/Chicago",
    language: "English",
    bio: "IoT specialist focused on optimizing sensor networks on vessels.",
    customerId: 3, // Ocean Freight Inc.
    subscriptionIds: [3], // Enterprise Plan
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/sarahkim/",
      twitter: "",
      github: "https://github.com/sarahkim"
    },
    notifications: {
      email: true,
      push: true,
      sms: false
    }
  },
  {
    id: 5,
    name: "David Garcia",
    email: "<EMAIL>",
    role: "Technician",
    avatar: "",
    joined: "May 15, 2023",
    status: "Active",
    lastLogin: "4 hours ago",
    twoFactorEnabled: false,
    company: "Global Shipping Co.",
    department: "Maintenance",
    phone: "+****************",
    timezone: "America/New_York",
    language: "English",
    bio: "Marine technician responsible for sensor installation and maintenance.",
    customerId: 1, // Global Shipping Co.
    subscriptionIds: [1], // Standard Plan
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/davidgarcia/",
      twitter: "",
      github: ""
    },
    notifications: {
      email: true,
      push: true,
      sms: true
    }
  },
  {
    id: 6,
    name: "Anna Müller",
    email: "<EMAIL>",
    role: "Manager",
    avatar: "",
    joined: "June 20, 2023",
    status: "Active",
    lastLogin: "6 hours ago",
    twoFactorEnabled: true,
    company: "Maritime Solutions Ltd.",
    department: "Operations",
    phone: "+49 30 123456",
    timezone: "Europe/Berlin",
    language: "German",
    bio: "Operations manager overseeing fleet performance and compliance.",
    customerId: 2, // Maritime Solutions Ltd.
    subscriptionIds: [2], // Professional Plan
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/annamuller/",
      twitter: "https://twitter.com/annamuller",
      github: ""
    },
    notifications: {
      email: true,
      push: false,
      sms: true
    }
  }
];

// Generate vessel path data
export const generateVesselPath = (vesselId: number) => {
  // Different path patterns based on vessel ID
  const paths: Record<number, any[]> = {
    1: [ // HS Newman Bay - Circular route between ports
      { lat: 40.9513, lng: 28.6627, timestamp: "2023-12-01", location: "Tuzla, Turkey" },
      { lat: 41.0082, lng: 28.9784, timestamp: "2023-12-02", location: "Istanbul, Turkey" },
      { lat: 40.4093, lng: 27.9212, timestamp: "2023-12-04", location: "Bandirma, Turkey" },
      { lat: 38.4237, lng: 27.1428, timestamp: "2023-12-06", location: "Izmir, Turkey" },
      { lat: 36.8969, lng: 30.7133, timestamp: "2023-12-08", location: "Antalya, Turkey" },
      { lat: 34.6651, lng: 33.0436, timestamp: "2023-12-10", location: "Limassol, Cyprus" },
      { lat: 31.7683, lng: 35.2137, timestamp: "2023-12-12", location: "Port Said, Egypt" },
      { lat: 29.9771, lng: 32.5499, timestamp: "2023-12-14", location: "Suez, Egypt" },
      { lat: 25.2685, lng: 55.2944, timestamp: "2023-12-20", location: "Dubai, UAE" },
      { lat: 23.1307, lng: 53.4431, timestamp: "2023-12-23", location: "Abu Dhabi, UAE" },
      { lat: 25.3463, lng: 51.1943, timestamp: "2023-12-26", location: "Doha, Qatar" },
      { lat: 24.1302, lng: 57.5850, timestamp: "2023-12-29", location: "Jebel Ali, UAE" },
      { lat: 29.9771, lng: 32.5499, timestamp: "2024-01-05", location: "Suez, Egypt" },
      { lat: 31.7683, lng: 35.2137, timestamp: "2024-01-07", location: "Port Said, Egypt" },
      { lat: 34.6651, lng: 33.0436, timestamp: "2024-01-09", location: "Limassol, Cyprus" },
      { lat: 36.8969, lng: 30.7133, timestamp: "2024-01-11", location: "Antalya, Turkey" },
      { lat: 38.4237, lng: 27.1428, timestamp: "2024-01-13", location: "Izmir, Turkey" },
      { lat: 40.4093, lng: 27.9212, timestamp: "2024-01-15", location: "Bandirma, Turkey" },
      { lat: 41.0082, lng: 28.9784, timestamp: "2024-01-17", location: "Istanbul, Turkey" },
      { lat: 40.9513, lng: 28.6627, timestamp: "2024-01-18", location: "Tuzla, Turkey" }
    ],
    2: [ // HS Mortier Bay - Trans-Atlantic route
      { lat: 53.5511, lng: 9.9937, timestamp: "2023-12-01", location: "Hamburg, Germany" },
      { lat: 51.8098, lng: 4.7625, timestamp: "2023-12-03", location: "Rotterdam, Netherlands" },
      { lat: 50.9427, lng: 1.8329, timestamp: "2023-12-05", location: "Calais, France" },
      { lat: 50.8123, lng: -1.0881, timestamp: "2023-12-06", location: "Southampton, UK" },
      { lat: 49.4944, lng: -1.3653, timestamp: "2023-12-07", location: "Cherbourg, France" },
      { lat: 48.3829, lng: -4.4948, timestamp: "2023-12-08", location: "Brest, France" },
      { lat: 43.3623, lng: -8.4115, timestamp: "2023-12-10", location: "A Coruña, Spain" },
      { lat: 38.7223, lng: -9.1393, timestamp: "2023-12-12", location: "Lisbon, Portugal" },
      { lat: 36.1408, lng: -5.3536, timestamp: "2023-12-14", location: "Gibraltar" },
      { lat: 28.1248, lng: -15.4300, timestamp: "2023-12-17", location: "Las Palmas, Canary Islands" },
      { lat: 18.4655, lng: -66.1057, timestamp: "2023-12-25", location: "San Juan, Puerto Rico" },
      { lat: 25.7617, lng: -80.1918, timestamp: "2023-12-28", location: "Miami, USA" },
      { lat: 29.7604, lng: -95.3698, timestamp: "2024-01-02", location: "Houston, USA" },
      { lat: 25.7617, lng: -80.1918, timestamp: "2024-01-07", location: "Miami, USA" },
      { lat: 18.4655, lng: -66.1057, timestamp: "2024-01-10", location: "San Juan, Puerto Rico" },
      { lat: 28.1248, lng: -15.4300, timestamp: "2024-01-18", location: "Las Palmas, Canary Islands" },
      { lat: 36.1408, lng: -5.3536, timestamp: "2024-01-21", location: "Gibraltar" },
      { lat: 38.7223, lng: -9.1393, timestamp: "2024-01-23", location: "Lisbon, Portugal" },
      { lat: 43.3623, lng: -8.4115, timestamp: "2024-01-25", location: "A Coruña, Spain" },
      { lat: 48.3829, lng: -4.4948, timestamp: "2024-01-27", location: "Brest, France" },
      { lat: 49.4944, lng: -1.3653, timestamp: "2024-01-28", location: "Cherbourg, France" },
      { lat: 50.8123, lng: -1.0881, timestamp: "2024-01-29", location: "Southampton, UK" },
      { lat: 50.9427, lng: 1.8329, timestamp: "2024-01-30", location: "Calais, France" },
      { lat: 51.8098, lng: 4.7625, timestamp: "2024-01-31", location: "Rotterdam, Netherlands" },
      { lat: 53.5511, lng: 9.9937, timestamp: "2024-02-01", location: "Hamburg, Germany" }
    ],
    3: [ // HS Indian Bay - Middle East & South Asia route
      { lat: 25.2685, lng: 55.2944, timestamp: "2023-12-01", location: "Dubai, UAE" },
      { lat: 24.1302, lng: 57.5850, timestamp: "2023-12-03", location: "Jebel Ali, UAE" },
      { lat: 23.6345, lng: 58.5923, timestamp: "2023-12-05", location: "Muscat, Oman" },
      { lat: 25.3463, lng: 51.1943, timestamp: "2023-12-07", location: "Doha, Qatar" },
      { lat: 26.2235, lng: 50.5876, timestamp: "2023-12-09", location: "Manama, Bahrain" },
      { lat: 29.3759, lng: 47.9774, timestamp: "2023-12-11", location: "Kuwait City, Kuwait" },
      { lat: 29.9500, lng: 48.1667, timestamp: "2023-12-12", location: "Basra, Iraq" },
      { lat: 27.1939, lng: 56.2721, timestamp: "2023-12-14", location: "Bandar Abbas, Iran" },
      { lat: 25.0742, lng: 60.6511, timestamp: "2023-12-16", location: "Gwadar, Pakistan" },
      { lat: 24.8607, lng: 66.9909, timestamp: "2023-12-18", location: "Karachi, Pakistan" },
      { lat: 22.2473, lng: 69.1031, timestamp: "2023-12-20", location: "Kandla, India" },
      { lat: 19.2183, lng: 72.9781, timestamp: "2023-12-22", location: "Mumbai, India" },
      { lat: 15.4909, lng: 73.8278, timestamp: "2023-12-24", location: "Goa, India" },
      { lat: 9.9312, lng: 76.2673, timestamp: "2023-12-26", location: "Kochi, India" },
      { lat: 8.0883, lng: 77.5385, timestamp: "2023-12-28", location: "Colombo, Sri Lanka" },
      { lat: 9.9312, lng: 76.2673, timestamp: "2023-12-30", location: "Kochi, India" },
      { lat: 15.4909, lng: 73.8278, timestamp: "2024-01-01", location: "Goa, India" },
      { lat: 19.2183, lng: 72.9781, timestamp: "2024-01-03", location: "Mumbai, India" },
      { lat: 22.2473, lng: 69.1031, timestamp: "2024-01-05", location: "Kandla, India" },
      { lat: 24.8607, lng: 66.9909, timestamp: "2024-01-07", location: "Karachi, Pakistan" },
      { lat: 25.0742, lng: 60.6511, timestamp: "2024-01-09", location: "Gwadar, Pakistan" },
      { lat: 27.1939, lng: 56.2721, timestamp: "2024-01-11", location: "Bandar Abbas, Iran" },
      { lat: 26.2235, lng: 50.5876, timestamp: "2024-01-13", location: "Manama, Bahrain" },
      { lat: 25.3463, lng: 51.1943, timestamp: "2024-01-15", location: "Doha, Qatar" },
      { lat: 24.1302, lng: 57.5850, timestamp: "2024-01-17", location: "Jebel Ali, UAE" }
    ]
  };

  return paths[vesselId] || paths[1]; // Default to first path if not found
};

// Generate sensor chart data
export const generateSensorData = (days = 7, dataPointsPerDay = 24) => {
  const data = [];
  const now = new Date();
  const msPerDay = 24 * 60 * 60 * 1000;
  const msPerDataPoint = msPerDay / dataPointsPerDay;

  for (let d = days - 1; d >= 0; d--) {
    for (let h = 0; h < dataPointsPerDay; h++) {
      const time = new Date(now.getTime() - (d * msPerDay) - (h * msPerDataPoint));

      // Generate temperature with daily pattern
      const hourOfDay = time.getHours();
      let baseTemp = 35;

      // Add daily cycle - warmer in afternoon, cooler at night
      const dailyCycle = Math.sin((hourOfDay - 6) * (Math.PI / 12)) * 3;

      // Add some randomness
      const randomVariation = (Math.random() - 0.5) * 1.5;

      // Add weekly trend (slight increase)
      const weeklyTrend = (days - d) * 0.2;

      const temperature = baseTemp + dailyCycle + randomVariation + weeklyTrend;

      // Generate humidity as somewhat related to temperature
      const baseHumidity = 60;
      const humidityVariation = (Math.random() - 0.5) * 10;
      // Inverse relationship with temperature (higher temp, lower humidity)
      const tempEffect = -(temperature - baseTemp) * 1.5;
      const humidity = Math.min(100, Math.max(20, baseHumidity + tempEffect + humidityVariation));

      // Generate marine sensor measurements for testing
      const battery = parseFloat((95 + (Math.random() - 0.5) * 8).toFixed(2)); // 91-99%
      const powerSupply = parseFloat((12.8 + (Math.random() - 0.5) * 0.8).toFixed(2)); // 12.4-13.2V
      const gsmSignal = parseFloat((70 + (Math.random() - 0.5) * 40).toFixed(1)); // 50-90 dBm
      const rudder = parseFloat(((Math.random() - 0.5) * 30).toFixed(1)); // -15 to +15 degrees
      const totalNauticalMiles = parseFloat((1000 + d * 25 + Math.random() * 50).toFixed(1)); // Increasing with time
      const location = 'Mock Location, Test Harbor';

      // Format the time
      const timeFormatted = `${time.getMonth() + 1}/${time.getDate()} ${time.getHours()}:00`;

      data.push({
        time: timeFormatted,
        timestamp: time.getTime(),
        temperature: parseFloat(temperature.toFixed(1)),
        humidity: parseFloat(humidity.toFixed(1)),
        // Marine sensor measurements
        Battery: battery,
        PowerSupply: powerSupply,
        GSMSignal: gsmSignal,
        Rudder: rudder,
        TotalNauticalMiles: totalNauticalMiles,
        Location: location,
        // Also include lowercase versions for compatibility
        battery: battery,
        powerSupply: powerSupply,
        gsmSignal: gsmSignal,
        rudder: rudder,
        totalNauticalMiles: totalNauticalMiles,
        location: location
      });
    }
  }

  return data;
};

// Mock data for notifications
export const mockNotifications = [
  {
    id: "notif-1",
    title: "Sensor Alert",
    message: "Temperature sensor on Marianna has exceeded threshold",
    type: "warning",
    category: "sensor",
    isRead: false,
    priority: "high",
    timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
    vesselId: "463808fb-2981-45a9-a395-c5e5ff40387b",
    sensorId: "aecc6fca-c7c8-40b7-8c5a-46f140ef8ee7",
    actionUrl: "/sensors/aecc6fca-c7c8-40b7-8c5a-46f140ef8ee7",
    actionText: "View sensor details"
  },
  {
    id: "notif-2",
    title: "Vessel Maintenance Due",
    message: "Marianna is scheduled for maintenance in 3 days",
    type: "info",
    category: "maintenance",
    isRead: false,
    priority: "medium",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    vesselId: "463808fb-2981-45a9-a395-c5e5ff40387b",
    actionUrl: "/vessels/463808fb-2981-45a9-a395-c5e5ff40387b",
    actionText: "Schedule maintenance"
  },
  {
    id: "notif-3",
    title: "Subscription Expiring",
    message: "Your Enterprise subscription expires in 7 days",
    type: "warning",
    category: "subscription",
    isRead: true,
    priority: "high",
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    actionUrl: "/subscriptions",
    actionText: "Renew subscription"
  },
  {
    id: "notif-4",
    title: "System Update",
    message: "SmartBoat platform will undergo maintenance tonight at 2:00 AM",
    type: "info",
    category: "system",
    isRead: true,
    priority: "low",
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    actionText: "View details"
  },
  {
    id: "notif-5",
    title: "Critical Engine Alert",
    message: "Engine temperature critical on Marianna - immediate attention required",
    type: "error",
    category: "sensor",
    isRead: false,
    priority: "urgent",
    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
    vesselId: "463808fb-2981-45a9-a395-c5e5ff40387b",
    actionUrl: "/vessels/463808fb-2981-45a9-a395-c5e5ff40387b",
    actionText: "View vessel status"
  }
];

export const mockNotificationStats = {
  total: 5,
  unread: 3,
  byType: {
    info: 2,
    warning: 2,
    error: 1,
    success: 0,
    alert: 0
  },
  byCategory: {
    sensor: 2,
    maintenance: 1,
    subscription: 1,
    system: 1,
    vessel: 0
  }
};