import { UserRole, RolePermissions, User } from '../types';

// Auth token key for localStorage
const AUTH_TOKEN_KEY = 'smartboat_auth_token';
const USER_DATA_KEY = 'smartboat_user_data';

/**
 * Store the authentication token in localStorage
 * @param token The token to store
 */
export const setAuthToken = (token: string): void => {
  localStorage.setItem(AUTH_TOKEN_KEY, token);
};

/**
 * Get the authentication token from localStorage
 * @returns The stored token or null if not found
 */
export const getAuthToken = (): string | null => {
  return localStorage.getItem(AUTH_TOKEN_KEY);
};

/**
 * Clear the stored authentication token
 */
export const clearAuthToken = (): void => {
  localStorage.removeItem(AUTH_TOKEN_KEY);
  localStorage.removeItem(USER_DATA_KEY);
};

/**
 * Default permissions by role
 * This defines what each role can do in the system
 */
export const rolePermissions: Record<UserRole, RolePermissions> = {
  Administrator: {
    viewAllSubscriptions: true,
    addSubscription: true,
    editSubscription: true,
    viewOwnSubscription: true,
    changeOwnSubscription: true,
    viewAllVessels: true,
    viewOwnVessels: true,
    viewAllSensors: true,
    viewOwnSensors: true,
    editSensors: true,
    viewAllCompanies: true,
    viewOwnCompanies: true,
    manageCompanies: true,
    manageCustomers: true,
    manageUsers: true
  },

  Customer: {
    viewAllSubscriptions: false,
    addSubscription: false,
    editSubscription: false,
    viewOwnSubscription: true,
    changeOwnSubscription: true,
    viewAllVessels: false,
    viewOwnVessels: true,
    viewAllSensors: false,
    viewOwnSensors: true,
    editSensors: false,
    viewAllCompanies: false,
    viewOwnCompanies: true,
    manageCompanies: false,
    manageCustomers: false,
    manageUsers: false
  },
  
  Manager: {
    viewAllSubscriptions: true,
    addSubscription: false,
    editSubscription: false,
    viewOwnSubscription: true,
    changeOwnSubscription: false,
    viewAllVessels: true,
    viewOwnVessels: true,
    viewAllSensors: true,
    viewOwnSensors: true,
    editSensors: true,
    viewAllCompanies: true,
    viewOwnCompanies: true,
    manageCompanies: false,
    manageCustomers: false,
    manageUsers: false
  },

  Technician: {
    viewAllSubscriptions: false,
    addSubscription: false,
    editSubscription: false,
    viewOwnSubscription: true,
    changeOwnSubscription: false,
    viewAllVessels: false,
    viewOwnVessels: true,
    viewAllSensors: true,
    viewOwnSensors: true,
    editSensors: true,
    viewAllCompanies: false,
    viewOwnCompanies: true,
    manageCompanies: false,
    manageCustomers: false,
    manageUsers: false
  },

  Viewer: {
    viewAllSubscriptions: false,
    addSubscription: false,
    editSubscription: false,
    viewOwnSubscription: true,
    changeOwnSubscription: false,
    viewAllVessels: false,
    viewOwnVessels: true,
    viewAllSensors: false,
    viewOwnSensors: true,
    editSensors: false,
    viewAllCompanies: false,
    viewOwnCompanies: false,
    manageCompanies: false,
    manageCustomers: false,
    manageUsers: false
  }
};

/**
 * Check if a user has a specific permission
 * @param user The user to check permissions for
 * @param permission The permission to check
 * @returns True if the user has the permission, false otherwise
 */
export const hasPermission = (
  user: User,
  permission: keyof RolePermissions
): boolean => {
  return rolePermissions[user.role][permission];
};

/**
 * Check if a user can access a specific subscription
 * @param user The user to check
 * @param subscriptionId The subscription ID to check access for
 * @returns True if the user can access the subscription, false otherwise
 */
export const canAccessSubscription = (
  user: User,
  subscriptionId: number
): boolean => {
  // Admins can access all subscriptions
  if (user.role === 'Administrator') {
    return true;
  }
  
  // Other roles can only access their own subscriptions
  return user.subscriptionIds.includes(subscriptionId);
};

/**
 * Get all permissions for a specific user role
 * @param role The user role to get permissions for
 * @returns The permissions for the given role
 */
export const getPermissionsForRole = (role: UserRole): RolePermissions => {
  return rolePermissions[role];
};

/**
 * Create a mock user for development
 * @param role The role to assign to the mock user
 * @returns A mock user with the specified role
 */
export const createMockUser = (role: UserRole = 'Customer'): User => {
  return {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    role: role,
    avatar: '',
    joined: 'January 15, 2023',
    status: 'Active',
    lastLogin: '2 hours ago',
    twoFactorEnabled: true,
    company: 'SmartBoat Technologies',
    department: 'Product Management',
    phone: '+****************',
    timezone: 'America/New_York',
    language: 'English',
    bio: 'Product manager focused on marine telematics and vessel monitoring solutions.',
    customerId: role === 'Customer' ? 1 : undefined,
    subscriptionIds: [1, 2],
    socialLinks: {
      linkedin: 'https://www.linkedin.com/in/johndoe/',
      twitter: 'https://twitter.com/johndoe',
      github: ''
    },
    notifications: {
      email: true,
      push: true,
      sms: false
    }
  };
};