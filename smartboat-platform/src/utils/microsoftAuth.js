/**
 * Microsoft Graph Authentication Utility
 * Handles OAuth2 PKCE flow for Microsoft Graph API integration
 */

// Microsoft Graph configuration
const MICROSOFT_CONFIG = {
  clientId: '4d59239e-ff4d-4bd9-b825-e61d87a1a6e9',
  tenantId: '72d7af18-45b8-4c9a-ac32-d056c6bda0f5',
  redirectUri: `${window.location.origin}/smtp-redirect`,
  scopes: ['User.Read', 'Mail.Read', 'offline_access']
};

// Generate code verifier and challenge for PKCE
const generatePKCE = () => {
  // Generate code verifier (43-128 characters, URL-safe)
  const codeVerifier = btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(32))))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  // Generate code challenge (SHA256 hash of verifier, base64url encoded)
  return crypto.subtle.digest('SHA-256', new TextEncoder().encode(codeVerifier))
    .then(hashBuffer => {
      const hashArray = new Uint8Array(hashBuffer);
      const codeChallenge = btoa(String.fromCharCode(...hashArray))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
      
      return { codeVerifier, codeChallenge };
    });
};

// Generate random state parameter
const generateState = () => {
  return btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(16))))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

// Detect if browser is in incognito/private mode
const isIncognitoMode = () => {
  try {
    // Try to use sessionStorage
    const test = '__incognito_test__';
    sessionStorage.setItem(test, 'test');
    sessionStorage.removeItem(test);
    return false; // SessionStorage works, likely not incognito
  } catch (e) {
    return true; // SessionStorage failed, likely incognito
  }
};

/**
 * Start Microsoft authentication flow
 * Redirects user to Microsoft login page
 * @param {Object} options - Authentication options
 * @param {boolean} options.forceLogin - Force fresh login (useful for account switching)
 * @param {boolean} options.selectAccount - Force account selection dialog
 */
export const startMicrosoftAuth = async (options = {}) => {
  try {
    const { forceLogin = false, selectAccount = true } = options;
    
    const { codeVerifier, codeChallenge } = await generatePKCE();
    const state = generateState();

    // Check if we can use sessionStorage (might fail in some incognito modes)
    try {
      sessionStorage.setItem('msauth_code_verifier', codeVerifier);
      sessionStorage.setItem('msauth_state', state);
      
      // Test if we can retrieve them immediately
      const testVerifier = sessionStorage.getItem('msauth_code_verifier');
      const testState = sessionStorage.getItem('msauth_state');
      
      if (!testVerifier || !testState) {
        throw new Error('SessionStorage test failed');
      }
    } catch (storageError) {
      console.warn('SessionStorage not available (incognito mode?), using fallback storage');
      
      // Fallback: store in memory with a timeout warning
      window._msauth_temp_storage = {
        codeVerifier,
        state,
        timestamp: Date.now()
      };
    }

    // Build authorization URL with tenant-specific endpoint
    const authUrl = new URL(`https://login.microsoftonline.com/${MICROSOFT_CONFIG.tenantId}/oauth2/v2.0/authorize`);
    authUrl.searchParams.set('client_id', MICROSOFT_CONFIG.clientId);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('redirect_uri', MICROSOFT_CONFIG.redirectUri);
    authUrl.searchParams.set('scope', MICROSOFT_CONFIG.scopes.join(' '));
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');
    authUrl.searchParams.set('response_mode', 'query');
    
    // Add prompt parameter for account handling (only one value allowed)
    let prompt = null;
    if (forceLogin) {
      // Force fresh login for account switching - highest priority
      prompt = 'login';
    } else if (selectAccount) {
      // Show account picker for first-time auth
      prompt = 'select_account';
    }
    
    // Only set prompt if we have a value (consent is usually automatic)
    if (prompt) {
      authUrl.searchParams.set('prompt', prompt);
    }
    
    // Add domain hint to focus on organizational accounts
    authUrl.searchParams.set('domain_hint', 'organizations');

    console.log('Starting Microsoft authentication with options:', { forceLogin, selectAccount });
    
    // Redirect to Microsoft login
    window.location.href = authUrl.toString();
  } catch (error) {
    console.error('Failed to start Microsoft authentication:', error);
    
    // Provide specific error messages for common issues
    if (error.message.includes('sessionStorage') || error.message.includes('SessionStorage')) {
      throw new Error('Browser storage not available. Please ensure cookies and local storage are enabled, or try a regular browser window instead of incognito mode.');
    }
    
    throw new Error(`Failed to initialize authentication: ${error.message}`);
  }
};

/**
 * Handle authentication callback
 * Extracts authorization code and PKCE verifier for server-side token exchange
 */
export const handleAuthCallback = async (urlParams) => {
  try {
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');
    const errorDescription = urlParams.get('error_description');

    // Check for OAuth errors
    if (error) {
      throw new Error(`Authentication failed: ${error} - ${errorDescription}`);
    }

    // Verify state parameter (try sessionStorage first, then fallback)
    let storedState = null;
    try {
      storedState = sessionStorage.getItem('msauth_state');
    } catch (e) {
      console.warn('SessionStorage access failed for state verification');
    }
    
    // Try fallback storage if sessionStorage failed
    if (!storedState && window._msauth_temp_storage) {
      const tempStorage = window._msauth_temp_storage;
      if (Date.now() - tempStorage.timestamp < 10 * 60 * 1000) {
        storedState = tempStorage.state;
        console.log('Using fallback storage for state verification');
      }
    }
    
    if (!state || state !== storedState) {
      throw new Error('Invalid state parameter - possible CSRF attack or browser storage unavailable');
    }

    // Get stored PKCE verifier (try sessionStorage first, then fallback)
    let codeVerifier = null;
    try {
      codeVerifier = sessionStorage.getItem('msauth_code_verifier');
    } catch (e) {
      console.warn('SessionStorage access failed, trying fallback storage');
    }
    
    // Try fallback storage if sessionStorage failed
    if (!codeVerifier && window._msauth_temp_storage) {
      const tempStorage = window._msauth_temp_storage;
      
      // Check if storage is still valid (within 10 minutes)
      if (Date.now() - tempStorage.timestamp < 10 * 60 * 1000) {
        codeVerifier = tempStorage.codeVerifier;
        console.log('Using fallback storage for code verifier');
      } else {
        console.warn('Fallback storage expired');
      }
    }
    
    if (!codeVerifier) {
      throw new Error('Missing code verifier - authentication session expired or browser storage unavailable. Please try again in a regular browser window.');
    }

    // Clean up session storage and fallback storage
    try {
      sessionStorage.removeItem('msauth_code_verifier');
      sessionStorage.removeItem('msauth_state');
    } catch (e) {
      console.warn('Could not clean sessionStorage (incognito mode?)');
    }
    
    // Clean up fallback storage
    if (window._msauth_temp_storage) {
      delete window._msauth_temp_storage;
    }

    // Return exchange data for server-side token exchange
    return {
      authorizationCode: code,
      codeVerifier: codeVerifier,
      redirectUri: MICROSOFT_CONFIG.redirectUri
    };
  } catch (error) {
    // Clean up session storage and fallback storage on error
    try {
      sessionStorage.removeItem('msauth_code_verifier');
      sessionStorage.removeItem('msauth_state');
    } catch (e) {
      console.warn('Could not clean sessionStorage on error (incognito mode?)');
    }
    
    // Clean up fallback storage
    if (window._msauth_temp_storage) {
      delete window._msauth_temp_storage;
    }
    
    console.error('Authentication callback failed:', error);
    throw error;
  }
};

/**
 * Check if we're currently in an authentication callback
 */
export const isAuthCallback = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.has('code') || urlParams.has('error');
};

/**
 * Get authentication status from URL
 */
export const getAuthStatus = () => {
  const urlParams = new URLSearchParams(window.location.search);
  
  if (urlParams.has('error')) {
    return {
      success: false,
      error: urlParams.get('error'),
      errorDescription: urlParams.get('error_description')
    };
  }
  
  if (urlParams.has('code')) {
    return {
      success: true,
      code: urlParams.get('code'),
      state: urlParams.get('state')
    };
  }
  
  return null;
};

/**
 * Check if browser is in incognito/private mode
 */
export const isPrivateMode = isIncognitoMode;