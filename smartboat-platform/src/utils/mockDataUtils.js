/**
 * Mock Data Utilities
 * Provides access to mock data for development and testing
 */

import { MOCK_DATA_ENABLED, mockResponse } from '../services/mockConfig';

// Import mock data services
import * as vesselsMock from '../services/mockData/vesselsMockData';
import * as companiesMock from '../services/mockData/companiesMockData';
import * as customersMock from '../services/mockData/customersMockData';
import * as dashboardMock from '../services/mockData/dashboardMockData';
import * as userMock from '../services/mockData/userMockData';
import * as sensorsMock from '../services/mockData/sensorsMockData';

// Mock data for vessels
export const mockVesselData = {
  getAll: () => {
    if (MOCK_DATA_ENABLED) {
      return vesselsMock.getVessels();
    }
    return null;
  },
  getById: (id) => {
    if (MOCK_DATA_ENABLED) {
      return vesselsMock.getVesselById(id);
    }
    return null;
  },
  create: (vessel) => {
    if (MOCK_DATA_ENABLED) {
      return vesselsMock.createVessel(vessel);
    }
    return null;
  },
  update: (id, vessel) => {
    if (MOCK_DATA_ENABLED) {
      return vesselsMock.updateVessel(id, vessel);
    }
    return null;
  },
  delete: (id) => {
    if (MOCK_DATA_ENABLED) {
      return vesselsMock.deleteVessel(id);
    }
    return null;
  }
};

// Mock data for companies
export const mockCompanies = {
  getAll: () => {
    if (MOCK_DATA_ENABLED) {
      return companiesMock.getCompanies();
    }
    return null;
  },
  getById: (id) => {
    if (MOCK_DATA_ENABLED) {
      return companiesMock.getCompanyById(id);
    }
    return null;
  },
  create: (company) => {
    if (MOCK_DATA_ENABLED) {
      return companiesMock.createCompany(company);
    }
    return null;
  },
  update: (id, company) => {
    if (MOCK_DATA_ENABLED) {
      return companiesMock.updateCompany(id, company);
    }
    return null;
  },
  delete: (id) => {
    if (MOCK_DATA_ENABLED) {
      return companiesMock.deleteCompany(id);
    }
    return null;
  }
};

// Mock data for customers
export const mockCustomers = {
  getAll: () => {
    if (MOCK_DATA_ENABLED) {
      return customersMock.getCustomers();
    }
    return null;
  },
  getById: (id) => {
    if (MOCK_DATA_ENABLED) {
      return customersMock.getCustomerById(id);
    }
    return null;
  },
  create: (customer) => {
    if (MOCK_DATA_ENABLED) {
      return customersMock.createCustomer(customer);
    }
    return null;
  },
  update: (id, customer) => {
    if (MOCK_DATA_ENABLED) {
      return customersMock.updateCustomer(id, customer);
    }
    return null;
  },
  delete: (id) => {
    if (MOCK_DATA_ENABLED) {
      return customersMock.deleteCustomer(id);
    }
    return null;
  }
};

// Mock data for dashboard
export const mockDashboard = {
  getStats: () => {
    if (MOCK_DATA_ENABLED) {
      return dashboardMock.getDashboardStats();
    }
    return null;
  },
  getRecentActivity: () => {
    if (MOCK_DATA_ENABLED) {
      return dashboardMock.getRecentActivity();
    }
    return null;
  },
  getVesselSummaries: () => {
    if (MOCK_DATA_ENABLED) {
      return dashboardMock.getVesselSummaries();
    }
    return null;
  },
  getSensorSummaries: () => {
    if (MOCK_DATA_ENABLED) {
      return dashboardMock.getSensorSummaries();
    }
    return null;
  }
};

// Mock data for users
export const mockUsers = {
  getCurrentUser: () => {
    if (MOCK_DATA_ENABLED) {
      return userMock.getCurrentUser();
    }
    return null;
  },
  updateProfile: (userData) => {
    if (MOCK_DATA_ENABLED) {
      return userMock.updateUserProfile(userData);
    }
    return null;
  },
  getSubscriptions: () => {
    if (MOCK_DATA_ENABLED) {
      return userMock.getUserSubscriptions();
    }
    return null;
  }
};

// Mock data for subscriptions
export const mockSubscriptions = {
  getAll: () => {
    if (MOCK_DATA_ENABLED) {
      return userMock.getUserSubscriptions();
    }
    return null;
  }
};

// Mock data for sensors
export const mockSensorData = {
  getAll: () => {
    if (MOCK_DATA_ENABLED) {
      return sensorsMock.getSensors();
    }
    return null;
  },
  getById: (id) => {
    if (MOCK_DATA_ENABLED) {
      return sensorsMock.getSensorById(id);
    }
    return null;
  },
  getByVessel: (vesselId) => {
    if (MOCK_DATA_ENABLED) {
      return sensorsMock.getSensorsByVessel(vesselId);
    }
    return null;
  },
  create: (sensor) => {
    if (MOCK_DATA_ENABLED) {
      return sensorsMock.createSensor(sensor);
    }
    return null;
  },
  update: (id, sensor) => {
    if (MOCK_DATA_ENABLED) {
      return sensorsMock.updateSensor(id, sensor);
    }
    return null;
  },
  delete: (id) => {
    if (MOCK_DATA_ENABLED) {
      return sensorsMock.deleteSensor(id);
    }
    return null;
  }
};
