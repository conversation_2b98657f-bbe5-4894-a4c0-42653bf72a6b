/**
 * Date formatting utilities for human-readable date displays
 */

export interface FormatDateOptions {
  includeTime?: boolean;
  relativeThreshold?: number; // days after which to show absolute date instead of relative
  showSeconds?: boolean;
}

/**
 * Formats a date string or Date object into a human-readable format
 * Supports relative formatting (e.g., "2 hours ago") and absolute formatting
 */
export const formatDate = (
  date: string | Date,
  options: FormatDateOptions = {}
): string => {
  // Handle null, undefined, empty string, or invalid dates
  if (!date || date === '' || date === null || date === undefined) {
    return 'N/A';
  }

  const {
    includeTime = false,
    relativeThreshold = 7,
    showSeconds = false
  } = options;

  const dateObj = new Date(date);
  
  // Check if the date is invalid (including epoch date)
  if (isNaN(dateObj.getTime()) || dateObj.getTime() === 0) {
    return 'N/A';
  }
  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  // Return relative format for recent dates
  if (diffDays < relativeThreshold) {
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
  }

  // Return absolute format for older dates
  const formatOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  };

  if (includeTime) {
    formatOptions.hour = '2-digit';
    formatOptions.minute = '2-digit';
    if (showSeconds) {
      formatOptions.second = '2-digit';
    }
  }

  return dateObj.toLocaleDateString(undefined, formatOptions);
};

/**
 * Formats a date for display in charts and data visualizations
 */
export const formatChartDate = (date: string | Date): string => {
  // Handle null, undefined, empty string, or invalid dates
  if (!date || date === '' || date === null || date === undefined) {
    return 'N/A';
  }

  const dateObj = new Date(date);
  
  // Check if the date is invalid (including epoch date)
  if (isNaN(dateObj.getTime()) || dateObj.getTime() === 0) {
    return 'N/A';
  }
  return dateObj.toLocaleDateString(undefined, {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Formats a date range into a human-readable string
 */
export const formatDateRange = (
  startDate: string | Date,
  endDate: string | Date
): string => {
  // Check if both dates are invalid
  const isStartValid = startDate && startDate !== '' && startDate !== null && startDate !== undefined;
  const isEndValid = endDate && endDate !== '' && endDate !== null && endDate !== undefined;
  
  if (!isStartValid && !isEndValid) {
    return 'N/A';
  }

  let startStr = 'N/A';
  let endStr = 'N/A';

  // Format start date if valid
  if (isStartValid) {
    const start = new Date(startDate);
    if (!isNaN(start.getTime()) && start.getTime() !== 0) {
      startStr = start.toLocaleDateString(undefined, {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    }
  }

  // Format end date if valid
  if (isEndValid) {
    const end = new Date(endDate);
    if (!isNaN(end.getTime()) && end.getTime() !== 0) {
      endStr = end.toLocaleDateString(undefined, {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    }
  }
  
  return `${startStr} - ${endStr}`;
};

/**
 * Formats timestamp for notifications and activity feeds
 */
export const formatTimestamp = (timestamp: string): string => {
  // Handle null, undefined, empty string, or invalid timestamps
  if (!timestamp || timestamp === '' || timestamp === null || timestamp === undefined) {
    return 'N/A';
  }
  return formatDate(timestamp, { relativeThreshold: 7 });
};

/**
 * Formats a date for display in tables and lists
 */
export const formatTableDate = (date: string | Date): string => {
  // Handle null, undefined, empty string, or invalid dates
  if (!date || date === '' || date === null || date === undefined) {
    return 'N/A';
  }
  return formatDate(date, { includeTime: true, relativeThreshold: 1 });
};

/**
 * Formats a date for detailed views
 */
export const formatDetailDate = (date: string | Date): string => {
  // Handle null, undefined, empty string, or invalid dates
  if (!date || date === '' || date === null || date === undefined) {
    return 'N/A';
  }

  const dateObj = new Date(date);
  
  // Check if the date is invalid (including epoch date)
  if (isNaN(dateObj.getTime()) || dateObj.getTime() === 0) {
    return 'N/A';
  }
  return dateObj.toLocaleDateString(undefined, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};