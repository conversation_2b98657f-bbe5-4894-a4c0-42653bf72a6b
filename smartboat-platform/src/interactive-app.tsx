import { useState } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

// Main App Component
const VesselSensorApp = () => {
  const [activePage, setActivePage] = useState('dashboard');

  // Render the active page
  const renderPage = () => {
    switch (activePage) {
      case 'dashboard':
        return <Dashboard />;
      case 'vessels':
        return <Vessels />;
      case 'sensors':
        return <Sensors />;
      case 'customers':
        return <Customers />;
      case 'subscriptions':
        return <Subscriptions />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gray-100 dark:bg-gray-900">
      <Header activePage={activePage} setActivePage={setActivePage} />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar activePage={activePage} setActivePage={setActivePage} />
        <main className="flex-1 overflow-auto p-4">
          <div className="flex-1 overflow-y-auto bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-lg p-4">
            {renderPage()}
          </div>
        </main>
      </div>
    </div>
  );
};

// Header Component
const Header = ({ activePage, setActivePage }) => {
  return (
    <header className="bg-blue-500 text-white">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <button
              onClick={() => setActivePage('dashboard')}
              className="flex items-center"
            >
              <svg className="h-8 w-8 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L4 6V18L12 22L20 18V6L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <circle cx="12" cy="12" r="4" stroke="currentColor" strokeWidth="2" />
              </svg>
              <span className="font-bold text-xl">VesselSense</span>
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 ml-10 hidden md:flex">
            <ul className="flex space-x-2">
              <li>
                <button
                  className={`px-3 py-2 rounded-md ${activePage === 'dashboard' ? 'bg-blue-600' : 'hover:bg-blue-600'}`}
                  onClick={() => setActivePage('dashboard')}
                >
                  Dashboard
                </button>
              </li>
              <li>
                <button
                  className={`px-3 py-2 rounded-md ${activePage === 'customers' ? 'bg-blue-600' : 'hover:bg-blue-600'}`}
                  onClick={() => setActivePage('customers')}
                >
                  Customers
                </button>
              </li>
              <li>
                <button
                  className={`px-3 py-2 rounded-md ${activePage === 'vessels' ? 'bg-blue-600' : 'hover:bg-blue-600'}`}
                  onClick={() => setActivePage('vessels')}
                >
                  Vessels
                </button>
              </li>
              <li>
                <button
                  className={`px-3 py-2 rounded-md ${activePage === 'sensors' ? 'bg-blue-600' : 'hover:bg-blue-600'}`}
                  onClick={() => setActivePage('sensors')}
                >
                  Sensors
                </button>
              </li>
              <li>
                <button
                  className={`px-3 py-2 rounded-md ${activePage === 'subscriptions' ? 'bg-blue-600' : 'hover:bg-blue-600'}`}
                  onClick={() => setActivePage('subscriptions')}
                >
                  Subscriptions
                </button>
              </li>
            </ul>
          </nav>

          {/* Right Side Items */}
          <div className="flex items-center">
            <button className="p-2 rounded-full hover:bg-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
              </svg>
            </button>
            <button className="p-2 rounded-full hover:bg-blue-600 ml-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
            </button>
            <div className="ml-4 relative">
              <button className="flex items-center">
                <span className="rounded-full bg-blue-600 p-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </span>
                <span className="ml-2 hidden md:inline">Admin</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

// Sidebar Component
const Sidebar = ({ activePage, setActivePage }) => {
  // Navigation items
  const navItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
        </svg>
      )
    },
    {
      id: 'customers',
      label: 'Customers',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
        </svg>
      )
    },
    {
      id: 'vessels',
      label: 'Vessels',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M13 7H7v6h6V7z" />
          <path fillRule="evenodd" d="M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2h1a1 1 0 110 2h-1v2h1a1 1 0 110 2h-1v2a2 2 0 01-2 2h-2v1a1 1 0 11-2 0v-1H9v1a1 1 0 11-2 0v-1H5a2 2 0 01-2-2v-2H2a1 1 0 110-2h1V9H2a1 1 0 010-2h1V5a2 2 0 012-2h2V2zM5 5h10v10H5V5z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: 'sensors',
      label: 'Sensors',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: 'subscriptions',
      label: 'Subscriptions',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
        </svg>
      )
    }
  ];

  return (
    <aside className="w-16 md:w-64 bg-white border-r overflow-y-auto">
      <div className="py-4">
        <nav>
          <ul>
            {navItems.map((item) => (
              <li key={item.id}>
                <button
                  className={`flex items-center px-4 py-3 w-full text-left ${
                    activePage === item.id
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                  }`}
                  onClick={() => setActivePage(item.id)}
                >
                  {item.icon}
                  <span className="ml-3 hidden md:inline">{item.label}</span>
                </button>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </aside>
  );
};

// Mock data for the platform
const mockVesselData = [
  {
    id: 1,
    name: "HS Newman Bay",
    number: "18",
    type: "Container Ship",
    location: "Tuzla (Turkey)",
    status: "Active",
    startDate: "18/01/2019",
    endDate: "23/01/2019",
    image: "/vessel1.jpg",
    onsigners: 1,
    offsigners: 0,
    sensors: 12,
    lastUpdated: "2 hours ago"
  },
  {
    id: 2,
    name: "HS Mortier Bay",
    number: "1",
    type: "Bulk Carrier",
    location: "Hamburg (Germany)",
    status: "Active",
    startDate: "01/02/2019",
    endDate: "07/02/2019",
    image: "/vessel2.jpg",
    onsigners: 1,
    offsigners: 0,
    sensors: 8,
    lastUpdated: "30 minutes ago"
  },
  {
    id: 3,
    name: "HS Indian Bay",
    number: "15",
    type: "Container Ship",
    location: "Jebel Ali (UAE)",
    status: "Maintenance",
    startDate: "27/02/2019",
    endDate: "01/03/2019",
    image: "/vessel3.jpg",
    onsigners: 1,
    offsigners: 0,
    sensors: 15,
    lastUpdated: "1 day ago"
  },
];

// Mock data for sensors
const mockSensorData = [
  {
    id: 1,
    name: "Engine Room Temperature",
    type: "Temperature",
    vessel: "HS Mortier Bay",
    location: "Engine Room",
    status: "Active",
    lastReading: "38.5°C",
    lastUpdated: "2 minutes ago",
    alertThreshold: "45°C"
  },
  {
    id: 2,
    name: "Hydraulic Pressure",
    type: "Pressure",
    vessel: "HS Indian Bay",
    location: "Deck",
    status: "Active",
    lastReading: "2.4 MPa",
    lastUpdated: "5 minutes ago",
    alertThreshold: "3.0 MPa"
  },
  {
    id: 3,
    name: "Cargo Hold Humidity",
    type: "Humidity",
    vessel: "HS Newman Bay",
    location: "Cargo Hold",
    status: "Warning",
    lastReading: "85%",
    lastUpdated: "10 minutes ago",
    alertThreshold: "80%"
  },
  {
    id: 4,
    name: "Main Engine Fuel Flow",
    type: "Flow Rate",
    vessel: "HS Mortier Bay",
    location: "Engine Room",
    status: "Active",
    lastReading: "450 L/h",
    lastUpdated: "7 minutes ago",
    alertThreshold: "600 L/h"
  },
];

// Mock data for customers
const mockCustomers = [
  {
    id: 1,
    name: "Global Shipping Co.",
    contactPerson: "John Smith",
    email: "<EMAIL>",
    phone: "+****************",
    companies: 3,
    vessels: 8,
    sensors: 42,
    status: "Active",
    lastActive: "Today"
  },
  {
    id: 2,
    name: "Maritime Solutions Ltd.",
    contactPerson: "Emma Johnson",
    email: "<EMAIL>",
    phone: "+44 20 1234 5678",
    companies: 2,
    vessels: 5,
    sensors: 18,
    status: "Active",
    lastActive: "Yesterday"
  },
  {
    id: 3,
    name: "Ocean Freight Inc.",
    contactPerson: "Michael Chen",
    email: "<EMAIL>",
    phone: "+****************",
    companies: 1,
    vessels: 3,
    sensors: 12,
    status: "Pending",
    lastActive: "3 days ago"
  },
];

// Generate sensor chart data
const generateSensorData = (days = 7, dataPointsPerDay = 24) => {
  const data = [];
  const now = new Date();
  const msPerDay = 24 * 60 * 60 * 1000;
  const msPerDataPoint = msPerDay / dataPointsPerDay;

  for (let d = days - 1; d >= 0; d--) {
    for (let h = 0; h < dataPointsPerDay; h++) {
      const time = new Date(now.getTime() - (d * msPerDay) - (h * msPerDataPoint));

      // Generate temperature with daily pattern
      const hourOfDay = time.getHours();
      let baseTemp = 35;

      // Add daily cycle - warmer in afternoon, cooler at night
      const dailyCycle = Math.sin((hourOfDay - 6) * (Math.PI / 12)) * 3;

      // Add some randomness
      const randomVariation = (Math.random() - 0.5) * 1.5;

      // Add weekly trend (slight increase)
      const weeklyTrend = (days - d) * 0.2;

      const temperature = baseTemp + dailyCycle + randomVariation + weeklyTrend;

      // Generate humidity as somewhat related to temperature
      const baseHumidity = 60;
      const humidityVariation = (Math.random() - 0.5) * 10;
      // Inverse relationship with temperature (higher temp, lower humidity)
      const tempEffect = -(temperature - baseTemp) * 1.5;
      const humidity = Math.min(100, Math.max(20, baseHumidity + tempEffect + humidityVariation));

      // Format the time
      const timeFormatted = `${time.getMonth() + 1}/${time.getDate()} ${time.getHours()}:00`;

      data.push({
        time: timeFormatted,
        timestamp: time.getTime(),
        temperature: parseFloat(temperature.toFixed(1)),
        humidity: parseFloat(humidity.toFixed(1))
      });
    }
  }

  return data;
};

// SensorChart Component
const SensorChart = () => {
  const [data] = useState(generateSensorData(3, 8)); // Simplified for demo
  const [sensorType, setSensorType] = useState('temperature');

  // Calculate statistics
  const calculateStats = () => {
    if (data.length === 0) return { current: 'N/A', avg: 'N/A', min: 'N/A', max: 'N/A' };

    const values = data.map(d => d[sensorType]);
    const current = values[values.length - 1];
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return {
      current: current.toFixed(1),
      avg: avg.toFixed(1),
      min: min.toFixed(1),
      max: max.toFixed(1)
    };
  };

  const stats = calculateStats();
  const getUnit = () => sensorType === 'temperature' ? '°C' : '%';

  return (
    <div className="bg-white rounded-lg p-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold mb-1">Sensor Data</h2>
          <p className="text-gray-500 text-sm">HS Mortier Bay - Engine Room</p>
        </div>

        <div className="flex flex-wrap items-center mt-4 md:mt-0 space-x-2">
          {/* Sensor Type Selector */}
          <div className="bg-gray-100 rounded-lg p-1 flex">
            <button
              className={`px-3 py-1 rounded-md ${sensorType === 'temperature' ? 'bg-white shadow' : ''}`}
              onClick={() => setSensorType('temperature')}
            >
              Temperature
            </button>
            <button
              className={`px-3 py-1 rounded-md ${sensorType === 'humidity' ? 'bg-white shadow' : ''}`}
              onClick={() => setSensorType('humidity')}
            >
              Humidity
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-xs text-gray-500">Current</div>
          <div className="text-xl font-bold text-blue-600">{stats.current}{getUnit()}</div>
        </div>

        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-xs text-gray-500">Average</div>
          <div className="text-xl font-bold text-blue-600">{stats.avg}{getUnit()}</div>
        </div>

        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-xs text-gray-500">Minimum</div>
          <div className="text-xl font-bold text-blue-600">{stats.min}{getUnit()}</div>
        </div>

        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-xs text-gray-500">Maximum</div>
          <div className="text-xl font-bold text-blue-600">{stats.max}{getUnit()}</div>
        </div>
      </div>

      {/* Chart */}
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="time"
              tick={{ fontSize: 12 }}
            />
            <YAxis
              domain={sensorType === 'temperature' ? ['auto', 'auto'] : [0, 100]}
              tick={{ fontSize: 12 }}
            />
            <Tooltip
              formatter={(value) => [`${value}${getUnit()}`, sensorType === 'temperature' ? 'Temperature' : 'Humidity']}
              labelFormatter={(label) => `Time: ${label}`}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey={sensorType}
              name={sensorType === 'temperature' ? 'Temperature' : 'Humidity'}
              stroke={sensorType === 'temperature' ? "#3b82f6" : "#10b981"}
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 5 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

// Dashboard Component
const Dashboard = () => {
  const [expandedVessels, setExpandedVessels] = useState(false);
  const [expandedSensors, setExpandedSensors] = useState(false);

  // Summary stats
  const stats = {
    customers: 12,
    companies: 24,
    vessels: 35,
    activeSensors: 147
  };

  return (
    <div className="container mx-auto">
      <h1 className="text-2xl font-semibold mb-6">Dashboard</h1>

      {/* Stats cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-gray-500">Customers</p>
              <p className="text-2xl font-bold">{stats.customers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0h5m5 0h2m-2-5h-5m-9 0h9" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-gray-500">Companies</p>
              <p className="text-2xl font-bold">{stats.companies}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-gray-500">Vessels</p>
              <p className="text-2xl font-bold">{stats.vessels}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-gray-500">Active Sensors</p>
              <p className="text-2xl font-bold">{stats.activeSensors}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main dashboard content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Vessel Panel */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b flex justify-between items-center">
            <h2 className="text-xl font-medium text-gray-700">Vessels</h2>
            <div className="flex items-center">
              <button
                onClick={() => setExpandedVessels(!expandedVessels)}
                className="text-blue-500 hover:text-blue-700 mr-4 flex items-center"
              >
                Expand all
                {expandedVessels ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            </div>
          </div>

          <div className="overflow-y-auto max-h-96">
            {/* Vessel list */}
            {mockVesselData.map((vessel) => (
              <div key={vessel.id} className="border-b border-l-4 border-l-blue-500">
                <div className="p-4 flex">
                  <div className="w-16 h-16 bg-gray-200 rounded overflow-hidden mr-4">
                    {/* Vessel image placeholder */}
                    <div className="w-full h-full flex items-center justify-center bg-gray-300">
                      <span className="text-gray-600 text-xs">Image</span>
                    </div>
                  </div>

                  <div className="flex-1">
                    <div className="flex justify-between">
                      <h3 className="font-medium text-blue-600">
                        {vessel.number}. {vessel.name}
                      </h3>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{vessel.type} - {vessel.location}</p>
                    <p className="text-sm text-gray-600 mt-1">{vessel.startDate} - {vessel.endDate}</p>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center justify-end mb-1">
                      <span className="text-sm text-gray-600 mr-2">
                        <span className={`px-2 py-0.5 rounded-full text-xs ${
                            vessel.status === 'Active' ? 'bg-green-100 text-green-800' :
                            vessel.status === 'Maintenance' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {vessel.status}
                        </span>
                      </span>
                    </div>
                    <div className="flex items-center justify-end">
                      <span className="text-sm text-gray-600 mr-2">{vessel.sensors} Sensors</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Sensor Panel */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b flex justify-between items-center">
            <h2 className="text-xl font-medium text-gray-700">Sensors</h2>
            <div className="flex items-center">
              <button
                onClick={() => setExpandedSensors(!expandedSensors)}
                className="text-blue-500 hover:text-blue-700 mr-4 flex items-center"
              >
                Expand all
                {expandedSensors ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
              <button className="text-blue-500 hover:text-blue-700 flex items-center">
                Show all
              </button>
            </div>
          </div>

          <div className="overflow-y-auto max-h-96">
            {/* Sensor list */}
            {mockSensorData.map((sensor) => (
              <div key={sensor.id} className="border-b p-4">
                <div className="flex">
                  <div className="w-10 h-10 bg-blue-100 rounded-full overflow-hidden mr-4 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      {sensor.type === 'Temperature' ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2V9a2 2 0 012-2h6a2 2 0 012 2v2a2 2 0 01-2 2v6a4 4 0 11-8 0zm8-6V9a2 2 0 012-2h.093A2 2 0 0121 9v2a2 2 0 01-2 2h-.093a2 2 0 01-2-2z" />
                      ) : sensor.type === 'Pressure' ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      ) : sensor.type === 'Humidity' ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      )}
                    </svg>
                  </div>

                  <div className="flex-1">
                    <div className="flex justify-between">
                      <div>
                        <h3 className="font-medium text-gray-800">{sensor.name}</h3>
                        <p className="text-sm text-gray-600">{sensor.type}</p>
                      </div>
                      <span className="text-sm text-gray-600">{sensor.lastUpdated}</span>
                    </div>

                    <div className="mt-2 flex justify-between items-center">
                      <span className={
                        `py-1 px-3 rounded-full text-sm ${
                          sensor.status === 'Active'
                            ? 'bg-green-100 text-green-800'
                            : sensor.status === 'Warning'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`
                      }>
                        {sensor.status}
                      </span>
                      <span className="text-sm text-gray-600">{sensor.vessel} - {sensor.location}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Chart section */}
          <div className="p-4 border-t">
            <SensorChart />
          </div>
        </div>
      </div>

      {/* Recent Activity Section */}
      <div className="mt-6 bg-white rounded-lg shadow p-4">
        <h2 className="text-xl font-medium text-gray-700 mb-4">Recent Activity</h2>
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="flex-shrink-0 bg-blue-100 rounded-full p-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm text-gray-700">New sensor added to <span className="font-medium">HS Mortier Bay</span></p>
              <p className="text-xs text-gray-500 mt-1">10 minutes ago</p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0 bg-green-100 rounded-full p-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm text-gray-700">Subscription renewed for <span className="font-medium">Global Shipping Co.</span></p>
              <p className="text-xs text-gray-500 mt-1">1 hour ago</p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0 bg-yellow-100 rounded-full p-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm text-gray-700">Alert: Anomaly detected on <span className="font-medium">HS Indian Bay</span> temperature sensor</p>
              <p className="text-xs text-gray-500 mt-1">2 hours ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Generate vessel path data
const generateVesselPath = (vesselId) => {
  // Different path patterns based on vessel ID
  const paths = {
    1: [ // HS Newman Bay - Circular route between ports
      { lat: 40.9513, lng: 28.6627, timestamp: "2023-12-01", location: "Tuzla, Turkey" },
      { lat: 41.0082, lng: 28.9784, timestamp: "2023-12-02", location: "Istanbul, Turkey" },
      { lat: 40.4093, lng: 27.9212, timestamp: "2023-12-04", location: "Bandirma, Turkey" },
      { lat: 38.4237, lng: 27.1428, timestamp: "2023-12-06", location: "Izmir, Turkey" },
      { lat: 36.8969, lng: 30.7133, timestamp: "2023-12-08", location: "Antalya, Turkey" },
      { lat: 34.6651, lng: 33.0436, timestamp: "2023-12-10", location: "Limassol, Cyprus" },
      { lat: 31.7683, lng: 35.2137, timestamp: "2023-12-12", location: "Port Said, Egypt" },
      { lat: 29.9771, lng: 32.5499, timestamp: "2023-12-14", location: "Suez, Egypt" },
      { lat: 25.2685, lng: 55.2944, timestamp: "2023-12-20", location: "Dubai, UAE" },
      { lat: 23.1307, lng: 53.4431, timestamp: "2023-12-23", location: "Abu Dhabi, UAE" },
      { lat: 25.3463, lng: 51.1943, timestamp: "2023-12-26", location: "Doha, Qatar" },
      { lat: 24.1302, lng: 57.5850, timestamp: "2023-12-29", location: "Jebel Ali, UAE" },
      { lat: 29.9771, lng: 32.5499, timestamp: "2024-01-05", location: "Suez, Egypt" },
      { lat: 31.7683, lng: 35.2137, timestamp: "2024-01-07", location: "Port Said, Egypt" },
      { lat: 34.6651, lng: 33.0436, timestamp: "2024-01-09", location: "Limassol, Cyprus" },
      { lat: 36.8969, lng: 30.7133, timestamp: "2024-01-11", location: "Antalya, Turkey" },
      { lat: 38.4237, lng: 27.1428, timestamp: "2024-01-13", location: "Izmir, Turkey" },
      { lat: 40.4093, lng: 27.9212, timestamp: "2024-01-15", location: "Bandirma, Turkey" },
      { lat: 41.0082, lng: 28.9784, timestamp: "2024-01-17", location: "Istanbul, Turkey" },
      { lat: 40.9513, lng: 28.6627, timestamp: "2024-01-18", location: "Tuzla, Turkey" }
    ],
    2: [ // HS Mortier Bay - Trans-Atlantic route
      { lat: 53.5511, lng: 9.9937, timestamp: "2023-12-01", location: "Hamburg, Germany" },
      { lat: 51.8098, lng: 4.7625, timestamp: "2023-12-03", location: "Rotterdam, Netherlands" },
      { lat: 50.9427, lng: 1.8329, timestamp: "2023-12-05", location: "Calais, France" },
      { lat: 50.8123, lng: -1.0881, timestamp: "2023-12-06", location: "Southampton, UK" },
      { lat: 49.4944, lng: -1.3653, timestamp: "2023-12-07", location: "Cherbourg, France" },
      { lat: 48.3829, lng: -4.4948, timestamp: "2023-12-08", location: "Brest, France" },
      { lat: 43.3623, lng: -8.4115, timestamp: "2023-12-10", location: "A Coruña, Spain" },
      { lat: 38.7223, lng: -9.1393, timestamp: "2023-12-12", location: "Lisbon, Portugal" },
      { lat: 36.1408, lng: -5.3536, timestamp: "2023-12-14", location: "Gibraltar" },
      { lat: 28.1248, lng: -15.4300, timestamp: "2023-12-17", location: "Las Palmas, Canary Islands" },
      { lat: 18.4655, lng: -66.1057, timestamp: "2023-12-25", location: "San Juan, Puerto Rico" },
      { lat: 25.7617, lng: -80.1918, timestamp: "2023-12-28", location: "Miami, USA" },
      { lat: 29.7604, lng: -95.3698, timestamp: "2024-01-02", location: "Houston, USA" },
      { lat: 25.7617, lng: -80.1918, timestamp: "2024-01-07", location: "Miami, USA" },
      { lat: 18.4655, lng: -66.1057, timestamp: "2024-01-10", location: "San Juan, Puerto Rico" },
      { lat: 28.1248, lng: -15.4300, timestamp: "2024-01-18", location: "Las Palmas, Canary Islands" },
      { lat: 36.1408, lng: -5.3536, timestamp: "2024-01-21", location: "Gibraltar" },
      { lat: 38.7223, lng: -9.1393, timestamp: "2024-01-23", location: "Lisbon, Portugal" },
      { lat: 43.3623, lng: -8.4115, timestamp: "2024-01-25", location: "A Coruña, Spain" },
      { lat: 48.3829, lng: -4.4948, timestamp: "2024-01-27", location: "Brest, France" },
      { lat: 49.4944, lng: -1.3653, timestamp: "2024-01-28", location: "Cherbourg, France" },
      { lat: 50.8123, lng: -1.0881, timestamp: "2024-01-29", location: "Southampton, UK" },
      { lat: 50.9427, lng: 1.8329, timestamp: "2024-01-30", location: "Calais, France" },
      { lat: 51.8098, lng: 4.7625, timestamp: "2024-01-31", location: "Rotterdam, Netherlands" },
      { lat: 53.5511, lng: 9.9937, timestamp: "2024-02-01", location: "Hamburg, Germany" }
    ],
    3: [ // HS Indian Bay - Middle East & South Asia route
      { lat: 25.2685, lng: 55.2944, timestamp: "2023-12-01", location: "Dubai, UAE" },
      { lat: 24.1302, lng: 57.5850, timestamp: "2023-12-03", location: "Jebel Ali, UAE" },
      { lat: 23.6345, lng: 58.5923, timestamp: "2023-12-05", location: "Muscat, Oman" },
      { lat: 25.3463, lng: 51.1943, timestamp: "2023-12-07", location: "Doha, Qatar" },
      { lat: 26.2235, lng: 50.5876, timestamp: "2023-12-09", location: "Manama, Bahrain" },
      { lat: 29.3759, lng: 47.9774, timestamp: "2023-12-11", location: "Kuwait City, Kuwait" },
      { lat: 29.9500, lng: 48.1667, timestamp: "2023-12-12", location: "Basra, Iraq" },
      { lat: 27.1939, lng: 56.2721, timestamp: "2023-12-14", location: "Bandar Abbas, Iran" },
      { lat: 25.0742, lng: 60.6511, timestamp: "2023-12-16", location: "Gwadar, Pakistan" },
      { lat: 24.8607, lng: 66.9909, timestamp: "2023-12-18", location: "Karachi, Pakistan" },
      { lat: 22.2473, lng: 69.1031, timestamp: "2023-12-20", location: "Kandla, India" },
      { lat: 19.2183, lng: 72.9781, timestamp: "2023-12-22", location: "Mumbai, India" },
      { lat: 15.4909, lng: 73.8278, timestamp: "2023-12-24", location: "Goa, India" },
      { lat: 9.9312, lng: 76.2673, timestamp: "2023-12-26", location: "Kochi, India" },
      { lat: 8.0883, lng: 77.5385, timestamp: "2023-12-28", location: "Colombo, Sri Lanka" },
      { lat: 9.9312, lng: 76.2673, timestamp: "2023-12-30", location: "Kochi, India" },
      { lat: 15.4909, lng: 73.8278, timestamp: "2024-01-01", location: "Goa, India" },
      { lat: 19.2183, lng: 72.9781, timestamp: "2024-01-03", location: "Mumbai, India" },
      { lat: 22.2473, lng: 69.1031, timestamp: "2024-01-05", location: "Kandla, India" },
      { lat: 24.8607, lng: 66.9909, timestamp: "2024-01-07", location: "Karachi, Pakistan" },
      { lat: 25.0742, lng: 60.6511, timestamp: "2024-01-09", location: "Gwadar, Pakistan" },
      { lat: 27.1939, lng: 56.2721, timestamp: "2024-01-11", location: "Bandar Abbas, Iran" },
      { lat: 26.2235, lng: 50.5876, timestamp: "2024-01-13", location: "Manama, Bahrain" },
      { lat: 25.3463, lng: 51.1943, timestamp: "2024-01-15", location: "Doha, Qatar" },
      { lat: 24.1302, lng: 57.5850, timestamp: "2024-01-17", location: "Jebel Ali, UAE" }
    ]
  };

  return paths[vesselId] || paths[1]; // Default to first path if not found
};

// VesselMap Component
const VesselMap = ({ vesselId, timeRange = '3m' }) => {
  const [pathData, setPathData] = useState([]);
  const [dateRange, setDateRange] = useState({ start: null, end: null });
  const [selectedPoint, setSelectedPoint] = useState(null);

  useEffect(() => {
    // Get vessel path data
    const fullPath = generateVesselPath(vesselId);

    // Filter based on time range (1w = 1 week, 1m = 1 month, 3m = 3 months)
    let filteredPath;
    const now = new Date();
    let startDate;

    switch(timeRange) {
      case '1w':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '1m':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '3m':
      default:
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
    }

    // For demo purposes, we'll just use the whole path
    filteredPath = fullPath;

    setPathData(filteredPath);

    if (filteredPath.length > 0) {
      setDateRange({
        start: filteredPath[0].timestamp,
        end: filteredPath[filteredPath.length - 1].timestamp
      });
    }
  }, [vesselId, timeRange]);

  // Render the map
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium">Vessel Route Map</h2>
        <div className="flex space-x-2">
          <button
            className={`px-2 py-1 text-sm rounded ${timeRange === '1w' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
            onClick={() => setTimeRange('1w')}
          >
            1 Week
          </button>
          <button
            className={`px-2 py-1 text-sm rounded ${timeRange === '1m' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
            onClick={() => setTimeRange('1m')}
          >
            1 Month
          </button>
          <button
            className={`px-2 py-1 text-sm rounded ${timeRange === '3m' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
            onClick={() => setTimeRange('3m')}
          >
            3 Months
          </button>
        </div>
      </div>

      {dateRange.start && dateRange.end && (
        <div className="text-sm text-gray-600 mb-2">
          Route from {dateRange.start} to {dateRange.end}
        </div>
      )}

      {/* Simulated map container */}
      <div className="w-full h-80 bg-gray-200 rounded relative overflow-hidden">
        {/* This is a simplified map visualization for demo purposes */}
        <div className="absolute inset-0 bg-blue-50"></div>

        {/* Draw path lines */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 360 180" preserveAspectRatio="none">
          {pathData.length > 1 && pathData.map((point, index) => {
            if (index === 0) return null;
            const prevPoint = pathData[index - 1];

            // Convert lat/lng to x/y coordinates (simplified for demo)
            const x1 = ((prevPoint.lng + 180) / 360) * 360;
            const y1 = ((90 - prevPoint.lat) / 180) * 180;
            const x2 = ((point.lng + 180) / 360) * 360;
            const y2 = ((90 - point.lat) / 180) * 180;

            return (
              <line
                key={`line-${index}`}
                x1={x1}
                y1={y1}
                x2={x2}
                y2={y2}
                stroke="#3b82f6"
                strokeWidth="2"
                strokeDasharray="4,2"
              />
            );
          })}
        </svg>

        {/* Draw path points */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 360 180" preserveAspectRatio="none">
          {pathData.map((point, index) => {
            // Convert lat/lng to x/y coordinates (simplified for demo)
            const x = ((point.lng + 180) / 360) * 360;
            const y = ((90 - point.lat) / 180) * 180;

            return (
              <g key={`point-${index}`}>
                <circle
                  cx={x}
                  cy={y}
                  r="3"
                  fill={index === 0 ? "#10b981" : index === pathData.length - 1 ? "#ef4444" : "#3b82f6"}
                  onClick={() => setSelectedPoint(point)}
                  className="cursor-pointer hover:r-4 transition-all"
                />
                {index === 0 || index === pathData.length - 1 ? (
                  <text
                    x={x}
                    y={y - 8}
                    fontSize="8"
                    textAnchor="middle"
                    fill="#1f2937"
                  >
                    {index === 0 ? 'Start' : 'End'}
                  </text>
                ) : null}
              </g>
            );
          })}
        </svg>

        {/* Selected point info */}
        {selectedPoint && (
          <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-2 border-t border-gray-200">
            <div className="flex justify-between">
              <div>
                <div className="font-medium">{selectedPoint.location}</div>
                <div className="text-xs text-gray-600">Lat: {selectedPoint.lat.toFixed(4)}, Lng: {selectedPoint.lng.toFixed(4)}</div>
              </div>
              <div className="text-sm">{selectedPoint.timestamp}</div>
            </div>
          </div>
        )}

        {/* Map attribution */}
        <div className="absolute bottom-1 right-1 text-xs text-gray-500">
          Map Visualization
        </div>
      </div>

      <div className="mt-4 flex justify-between text-sm text-gray-600">
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-1"></span>
          Start Point
        </div>
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-1"></span>
          Route Point
        </div>
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
          End Point
        </div>
      </div>
    </div>
  );
};

// Now update the VesselDetailModal to include the map
const VesselDetailModal = ({ vessel, onClose }) => {
  const [activeTab, setActiveTab] = useState('info');

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-5xl w-full">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">{vessel.name}</h3>
            <button
              className="text-gray-400 hover:text-gray-500"
              onClick={onClose}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="border-b border-gray-200 mb-4">
            <nav className="-mb-px flex">
              <button
                className={`py-2 px-4 ${
                  activeTab === 'info'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('info')}
              >
                Vessel Info
              </button>
              <button
                className={`py-2 px-4 ${
                  activeTab === 'map'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('map')}
              >
                Route Map
              </button>
              <button
                className={`py-2 px-4 ${
                  activeTab === 'sensors'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('sensors')}
              >
                Sensors
              </button>
            </nav>
          </div>

          {activeTab === 'info' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Vessel Details */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Vessel Information</h4>

                  <div className="space-y-3 text-sm">
                    <div>
                      <span className="text-gray-500">Vessel Number:</span>
                      <span className="ml-2 text-gray-900">{vessel.number}</span>
                    </div>

                    <div>
                      <span className="text-gray-500">Type:</span>
                      <span className="ml-2 text-gray-900">{vessel.type}</span>
                    </div>

                    <div>
                      <span className="text-gray-500">Location:</span>
                      <span className="ml-2 text-gray-900">{vessel.location}</span>
                    </div>

                    <div>
                      <span className="text-gray-500">Status:</span>
                      <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                        vessel.status === 'Active' ? 'bg-green-100 text-green-800' :
                        vessel.status === 'Maintenance' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {vessel.status}
                      </span>
                    </div>

                    <div>
                      <span className="text-gray-500">Total Sensors:</span>
                      <span className="ml-2 text-gray-900 font-medium">{vessel.sensors}</span>
                    </div>

                    <div>
                      <span className="text-gray-500">Last Updated:</span>
                      <span className="ml-2 text-gray-900">{vessel.lastUpdated}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Vessel Sensors */}
              <div className="md:col-span-2">
                <h4 className="font-medium text-gray-900 mb-2">Sensor Overview</h4>
                <SensorChart />
              </div>
            </div>
          )}

          {activeTab === 'map' && (
            <VesselMap vesselId={vessel.id} />
          )}

          {activeTab === 'sensors' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Vessel Sensors</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sensor
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Location
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Reading
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {/* Filter sensors for this vessel */}
                    {mockSensorData
                      .filter(sensor => sensor.vessel === vessel.name)
                      .map((sensor) => (
                        <tr key={sensor.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{sensor.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{sensor.type}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{sensor.location}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              sensor.status === 'Active' ? 'bg-green-100 text-green-800' :
                              sensor.status === 'Warning' ? 'bg-yellow-100 text-yellow-800' :
                              sensor.status === 'Critical' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {sensor.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {sensor.lastReading}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          <div className="mt-4 pt-4 border-t border-gray-200 flex justify-between">
            <button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
              onClick={onClose}
            >
              Close
            </button>
            <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md">
              Manage Sensors
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Update Vessels Component to use the new modal with map
const Vessels = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [selectedVessel, setSelectedVessel] = useState(null);

  // Filter vessels based on search query and status filter
  const filteredVessels = mockVesselData.filter(
    vessel =>
      (vessel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
       vessel.number.toString().includes(searchQuery) ||
       vessel.location.toLowerCase().includes(searchQuery.toLowerCase())) &&
      (filterStatus === "all" || vessel.status.toLowerCase() === filterStatus.toLowerCase())
  );

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Vessels</h1>
        <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Add New Vessel
        </button>
      </div>

      {/* Search and filters */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mb-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
            <input
              type="text"
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-200 text-sm rounded-lg block w-full pl-10 p-2.5"
              placeholder="Search vessels by name, number, or location"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex items-center">
            <select
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-200 text-sm rounded-lg p-2.5"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="maintenance">Maintenance</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Vessels grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredVessels.map(vessel => (
          <div key={vessel.id} className="bg-white rounded-lg shadow overflow-hidden">
            <div className="h-40 bg-gray-200 relative">
              {/* This would be a vessel image in production */}
              <div className="absolute inset-0 flex items-center justify-center bg-gray-300">
                <span className="text-lg font-medium text-gray-600">{vessel.name}</span>
              </div>

              <div className="absolute bottom-2 right-2">
                <span className={`
                  px-2 py-1 rounded-full text-xs font-medium
                  ${vessel.status === 'Active' ? 'bg-green-100 text-green-800' :
                    vessel.status === 'Maintenance' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'}
                `}>
                  {vessel.status}
                </span>
              </div>
            </div>

            <div className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-medium text-blue-600">
                    {vessel.number}. {vessel.name}
                  </h3>
                  <p className="text-sm text-gray-600">{vessel.type}</p>
                </div>
                <div className="text-right">
                  <span className="text-xs text-gray-500">Last update</span>
                  <p className="text-sm text-gray-600">{vessel.lastUpdated}</p>
                </div>
              </div>

              <div className="mt-4 flex justify-between">
                <div>
                  <span className="text-xs text-gray-500">Location</span>
                  <p className="text-sm font-medium">{vessel.location}</p>
                </div>
                <div className="text-right">
                  <span className="text-xs text-gray-500">Sensors</span>
                  <p className="text-sm font-medium">{vessel.sensors}</p>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100 flex justify-end">
                <button
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  onClick={() => setSelectedVessel(vessel)}
                >
                  View Details
                </button>
                <button className="text-blue-600 hover:text-blue-800 text-sm font-medium ml-4">
                  Manage Sensors
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty state */}
      {filteredVessels.length === 0 && (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No vessels found</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your search or filters, or add a new vessel.
          </p>
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center mx-auto">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            Add New Vessel
          </button>
        </div>
      )}

      {/* Vessel Detail Modal with Map */}
      {selectedVessel && (
        <VesselDetailModal
          vessel={selectedVessel}
          onClose={() => setSelectedVessel(null)}
        />
      )}
    </div>
  );
};

// Sensors Component
const Sensors = () => {
  const [selectedSensor, setSelectedSensor] = useState(null);

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Sensors</h1>
        <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Add New Sensor
        </button>
      </div>

      {/* Sensors list */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sensor
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vessel / Location
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Reading
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Updated
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {mockSensorData.map((sensor) => (
                <tr key={sensor.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          {sensor.type === 'Temperature' ? (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2V9a2 2 0 012-2h6a2 2 0 012 2v2a2 2 0 01-2 2v6a4 4 0 11-8 0zm8-6V9a2 2 0 012-2h.093A2 2 0 0121 9v2a2 2 0 01-2 2h-.093a2 2 0 01-2-2z" />
                          ) : sensor.type === 'Pressure' ? (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          ) : sensor.type === 'Humidity' ? (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                          ) : (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          )}
                        </svg>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{sensor.name}</div>
                        <div className="text-sm text-gray-500">{sensor.type}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{sensor.vessel}</div>
                    <div className="text-sm text-gray-500">{sensor.location}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      sensor.status === 'Active' ? 'bg-green-100 text-green-800' :
                      sensor.status === 'Warning' ? 'bg-yellow-100 text-yellow-800' :
                      sensor.status === 'Critical' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {sensor.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {sensor.lastReading}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {sensor.lastUpdated}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      className="text-blue-600 hover:text-blue-900 mr-3"
                      onClick={() => setSelectedSensor(sensor)}
                    >
                      View
                    </button>
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      Configure
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Sensor Detail Modal */}
      {selectedSensor && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">{selectedSensor.name}</h3>
                <button
                  className="text-gray-400 hover:text-gray-500"
                  onClick={() => setSelectedSensor(null)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Sensor Details */}
                <div className="md:col-span-1">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">Sensor Information</h4>

                    <div className="space-y-3 text-sm">
                      <div>
                        <span className="text-gray-500">ID:</span>
                        <span className="ml-2 text-gray-900">SEN-{selectedSensor.id.toString().padStart(4, '0')}</span>
                      </div>

                      <div>
                        <span className="text-gray-500">Type:</span>
                        <span className="ml-2 text-gray-900">{selectedSensor.type}</span>
                      </div>

                      <div>
                        <span className="text-gray-500">Vessel:</span>
                        <span className="ml-2 text-gray-900">{selectedSensor.vessel}</span>
                      </div>

                      <div>
                        <span className="text-gray-500">Location:</span>
                        <span className="ml-2 text-gray-900">{selectedSensor.location}</span>
                      </div>

                      <div>
                        <span className="text-gray-500">Status:</span>
                        <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                          selectedSensor.status === 'Active' ? 'bg-green-100 text-green-800' :
                          selectedSensor.status === 'Warning' ? 'bg-yellow-100 text-yellow-800' :
                          selectedSensor.status === 'Critical' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {selectedSensor.status}
                        </span>
                      </div>

                      <div>
                        <span className="text-gray-500">Last Reading:</span>
                        <span className="ml-2 text-gray-900 font-medium">{selectedSensor.lastReading}</span>
                      </div>

                      <div>
                        <span className="text-gray-500">Last Updated:</span>
                        <span className="ml-2 text-gray-900">{selectedSensor.lastUpdated}</span>
                      </div>

                      <div>
                        <span className="text-gray-500">Alert Threshold:</span>
                        <span className="ml-2 text-gray-900">{selectedSensor.alertThreshold}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Sensor Chart */}
                <div className="md:col-span-2">
                  <SensorChart />
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200 flex justify-end">
                <button
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
                  onClick={() => setSelectedSensor(null)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Customers Component
const Customers = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  // Filter customers based on search
  const filteredCustomers = mockCustomers.filter(customer =>
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.contactPerson.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Customers</h1>
        <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Add New Customer
        </button>
      </div>

      {/* Search bar */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
          </div>
          <input
            type="text"
            className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-200 text-sm rounded-lg block w-full pl-10 p-2.5"
            placeholder="Search customers by name, contact person, or email"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Customer list */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Customer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Contact
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Companies
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Vessels
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Sensors
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredCustomers.map((customer) => (
                <tr key={customer.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold">
                        {customer.name.charAt(0)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                        <div className="text-sm text-gray-500">Last active: {customer.lastActive}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{customer.contactPerson}</div>
                    <div className="text-sm text-gray-500">{customer.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {customer.companies}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {customer.vessels}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {customer.sensors}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      customer.status === 'Active' ? 'bg-green-100 text-green-800' :
                      customer.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {customer.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      className="text-blue-600 hover:text-blue-900 mr-3"
                      onClick={() => setSelectedCustomer(customer)}
                    >
                      View
                    </button>
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      Edit
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Customer Detail Modal */}
      {selectedCustomer && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-800 dark:text-white">Customer Details</h3>
                <button
                  className="text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300"
                  onClick={() => setSelectedCustomer(null)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Company Information</h4>
                    <div className="mt-1">
                      <p className="text-lg font-medium text-gray-800 dark:text-white">{selectedCustomer.name}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        <span className={`inline-block mr-2 px-2 py-1 text-xs rounded-full ${
                          selectedCustomer.status === 'Active' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' :
                          selectedCustomer.status === 'Pending' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                        }`}>
                          {selectedCustomer.status}
                        </span>
                        Last active: {selectedCustomer.lastActive}
                      </p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Primary Contact</h4>
                    <div className="mt-1">
                      <p className="font-medium text-gray-800 dark:text-white">{selectedCustomer.contactPerson}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{selectedCustomer.email}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{selectedCustomer.phone}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Subscription Overview</h4>
                    <div className="mt-1 grid grid-cols-3 gap-2">
                      <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg text-center">
                        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{selectedCustomer.companies}</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Companies</p>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg text-center">
                        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{selectedCustomer.vessels}</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Vessels</p>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg text-center">
                        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{selectedCustomer.sensors}</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Sensors</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex justify-between">
                  <div>
                    <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md mr-3">
                      Edit Customer
                    </button>
                    <button className="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md">
                      View Vessels
                    </button>
                  </div>
                  <button
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    onClick={() => setSelectedCustomer(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Subscriptions Component
const Subscriptions = () => {
  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-800 dark:text-white">Subscriptions</h1>
        <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          New Subscription
        </button>
      </div>

      {/* Subscription Plans */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Basic Plan */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div className="p-6 bg-blue-100 dark:bg-blue-900/20">
            <div className="flex items-center mb-2">
              <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-800 text-blue-500 dark:text-blue-300 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">Standard</h3>
            </div>
            <p className="mt-1 text-gray-600 dark:text-gray-400">Basic vessel monitoring with limited sensors</p>
            <p className="mt-4 text-3xl font-bold text-gray-900 dark:text-white">
              $100
              <span className="text-sm font-normal text-gray-600 dark:text-gray-400">/sensor/month</span>
            </p>
          </div>

          <div className="p-6">
            <ul className="space-y-3">
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Up to 5 sensors per vessel</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Daily data updates</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Email alerts</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Basic reporting</span>
              </li>
            </ul>

            <div className="mt-6">
              <button className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                Select Plan
              </button>
            </div>
          </div>
        </div>

        {/* Professional Plan */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div className="p-6 bg-green-100 dark:bg-green-900/20">
            <div className="flex items-center mb-2">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-800 text-green-500 dark:text-green-300 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0h5m5 0h2m-2-5h-5m-9 0h9" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">Professional</h3>
            </div>
            <p className="mt-1 text-gray-600 dark:text-gray-400">Advanced monitoring with more sensors</p>
            <p className="mt-4 text-3xl font-bold text-gray-900 dark:text-white">
              $150
              <span className="text-sm font-normal text-gray-600 dark:text-gray-400">/sensor/month</span>
            </p>
          </div>

          <div className="p-6">
            <ul className="space-y-3">
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Up to 10 sensors per vessel</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Hourly data updates</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">SMS and email alerts</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Advanced reporting</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">30-day data history</span>
              </li>
            </ul>

            <div className="mt-6">
              <button className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                Select Plan
              </button>
            </div>
          </div>
        </div>

        {/* Enterprise Plan */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div className="p-6 bg-purple-100 dark:bg-purple-900/20">
            <div className="flex items-center mb-2">
              <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-800 text-purple-500 dark:text-purple-300 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">Enterprise</h3>
            </div>
            <p className="mt-1 text-gray-600 dark:text-gray-400">Comprehensive monitoring with unlimited sensors</p>
            <p className="mt-4 text-3xl font-bold text-gray-900 dark:text-white">
              $200
              <span className="text-sm font-normal text-gray-600 dark:text-gray-400">/sensor/month</span>
            </p>
          </div>

          <div className="p-6">
            <ul className="space-y-3">
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Unlimited sensors per vessel</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Real-time data updates</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Priority support</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">Advanced analytics</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-800 dark:text-gray-200">1-year data history</span>
              </li>
            </ul>

            <div className="mt-6">
              <button className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                Contact Sales
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VesselSensorApp;