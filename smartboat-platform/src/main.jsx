import { StrictMode, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { QueryClientProvider } from '@tanstack/react-query'
import './index.css'
import 'react-datepicker/dist/react-datepicker.css'
import App from './App.jsx'
import { ThemeProvider } from './context/ThemeContext'
import { AuthProvider } from './context/AuthContext'
import { LanguageProvider } from './context/LanguageContext'
import { queryClient } from './config/queryClient'
// Import i18n config
import './i18n/i18n'

// React Query DevTools component wrapper
const ReactQueryDevToolsProduction = () => {
  const [isDevtoolsEnabled, setIsDevtoolsEnabled] = useState(false);

  useEffect(() => {
    // Only load DevTools in development mode
    if (import.meta.env.DEV) {
      // Add a slight delay to prevent it from blocking initial render
      const timer = setTimeout(() => {
        setIsDevtoolsEnabled(true);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, []);

  return null; // Don't render DevTools for now to avoid the error
};

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          <LanguageProvider>
            <ThemeProvider>
              <App />
              <ReactQueryDevToolsProduction />
            </ThemeProvider>
          </LanguageProvider>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  </StrictMode>,
)
