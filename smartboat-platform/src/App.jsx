import './app.css';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from './context/AuthContext';
import { useEffect } from 'react';
import LoginPage from './components/pages/LoginPage';
import { Layout } from './components/layout';
import { Vessels } from './components/features/vessels';
import { Sensors } from './components/features/sensors';
import { Customers } from './components/features/customers';
import { Companies } from './components/features/companies';
import { Owners } from './components/features/owners';
import VesselDetailPage from './components/pages/VesselDetailPage';
import SensorDetailPage from './components/pages/SensorDetailPage';
import CustomerDetailPage from './components/pages/CustomerDetailPage';
import CompanyDetailPage from './components/pages/CompanyDetailPage';
import OwnerDetailPage from './components/pages/OwnerDetailPage';
import UserProfilePage from './components/pages/UserProfilePage';
import SubscriptionPage from './components/pages/SubscriptionPage';
import SubscriptionDetailPage from './components/pages/SubscriptionDetailPage';
import AuthCallbackPage from './components/pages/AuthCallbackPage';
import { LoadingScreen, SessionTimeoutHandler, ApiErrorNotification } from './components/common';
import GlobalLoadingOverlay from './components/common/GlobalLoadingOverlay';
import OfflineNotice from './components/common/OfflineNotice';
import { ApiProvider } from './providers/ApiProvider';
import { NotFoundPage, DashboardPage } from './components/pages';

// Protected route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading, currentUser } = useAuth();

  // Show nothing while checking auth status to prevent flash
  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

// Admin only route component
const AdminRoute = ({ children }) => {
  const { isAuthenticated, isLoading, currentUser } = useAuth();

  // Show nothing while checking auth status to prevent flash
  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (currentUser?.role !== 'Administrator' && currentUser?.role !== 'Manager') {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};

function App() {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Handle page refresh and restore last path
  useEffect(() => {
    if (isAuthenticated) {
      // Only restore path if we're on the root path (not if user explicitly navigated to dashboard)
      const isRootPath = location.pathname === '/';
      const lastPath = localStorage.getItem('smartboat_last_path');
      const explicitDashboard = sessionStorage.getItem('dashboard_explicit') === 'true';

      // Do not redirect from dashboard if user explicitly navigated there
      if (location.pathname === '/dashboard') {
        // If user explicitly navigated to dashboard, don't redirect away
        sessionStorage.setItem('dashboard_explicit', 'true');
        return;
      }

      // Only redirect from root path, never from dashboard or other pages
      if (lastPath && isRootPath && lastPath !== '/') {
        console.log('Restoring last path:', lastPath);
        navigate(lastPath, { replace: true });
      }
    }
  }, [isAuthenticated, navigate, location.pathname]);

  // Save the current path whenever it changes (except for login and root)
  useEffect(() => {
    // Don't save login or root paths
    if (location.pathname !== '/login' && location.pathname !== '/') {
      // Save current path with any search params and hash
      localStorage.setItem('smartboat_last_path', location.pathname + location.search + location.hash);
      console.log('Path saved:', location.pathname);

      // If navigating away from dashboard, clear the explicit flag
      if (location.pathname !== '/dashboard') {
        sessionStorage.removeItem('dashboard_explicit');
      }
    }
  }, [location]);

  // If authentication is still being checked, show a loading screen
  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <ApiProvider>
      <SessionTimeoutHandler
        timeout={30 * 60 * 1000} // 30 minutes
        warningDuration={1 * 60 * 1000} // 1 minute warning
      >
        <div className="min-h-screen bg-gray-100 dark:bg-gray-900 overflow-hidden">
          <GlobalLoadingOverlay />
          <ApiErrorNotification />
          <OfflineNotice />
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/smtp-redirect" element={<AuthCallbackPage />} />

            {/* Protected routes */}
            <Route
              path="/"
              element={isAuthenticated ? <Layout /> : <Navigate to="/login" replace />}
            >
          {/* Default route */}
          <Route index element={<Navigate to="/dashboard" />} />

          {/* Dashboard */}
          <Route path="dashboard" element={<DashboardPage />} />

          {/* Vessels routes */}
          <Route path="vessels">
            <Route index element={<Vessels />} />
            <Route path=":id" element={<VesselDetailPage />} />
          </Route>

          {/* Sensors routes */}
          <Route path="sensors">
            <Route index element={<Sensors />} />
            <Route path=":id" element={<SensorDetailPage />} />
          </Route>

          {/* Companies routes */}
          <Route path="companies">
            <Route index element={<Companies />} />
            <Route path=":id" element={<CompanyDetailPage />} />
          </Route>

          {/* Owners routes */}
          <Route path="owners">
            <Route index element={<Owners />} />
            <Route path=":id" element={<OwnerDetailPage />} />
          </Route>

          {/* Customers routes - Admin only */}
          <Route path="customers">
            <Route index element={
              <AdminRoute>
                <Customers />
              </AdminRoute>
            } />
            <Route path=":id" element={
              <AdminRoute>
                <CustomerDetailPage />
              </AdminRoute>
            } />
          </Route>

          {/* Subscriptions routes */}
          <Route path="subscriptions">
            <Route index element={<SubscriptionPage />} />
            <Route path=":id" element={<SubscriptionDetailPage />} />
          </Route>
          
          {/* Profile routes with tab navigation */}
          <Route path="profile">
            <Route index element={<UserProfilePage />} />
            <Route path=":tab" element={<UserProfilePage />} />
          </Route>

          {/* Fallback for unknown paths inside protected area */}
          <Route path="*" element={<NotFoundPage />} />
        </Route>

        {/* Global catch-all */}
        <Route path="*" element={
          isAuthenticated ? <NotFoundPage /> : <Navigate to="/login" />
        } />
      </Routes>
      </div>
    </SessionTimeoutHandler>
    </ApiProvider>
  );
}

export default App;