import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Subscription } from '../../../types';
import { User } from '../../../types';
import { hasPermission } from '../../../utils/authUtils';
import { useTranslation } from 'react-i18next';

interface FeaturedSubscriptionProps {
  subscription: Subscription;
  currentUser: User | null;
  onChangePlan: (subscription: Subscription) => void;
}

const FeaturedSubscription: React.FC<FeaturedSubscriptionProps> = ({
  subscription,
  currentUser,
  onChangePlan
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h3 className="text-lg font-medium text-blue-900">{subscription.name}</h3>
          <p className="text-sm text-blue-700 mt-1">
            {t('subscriptions.validFrom')} {subscription.startDate} {t('subscriptions.validTo')} {subscription.endDate}
          </p>
          <div className="mt-2">
            <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
              ${subscription.status === 'Active' ? 'bg-green-100 text-green-800' : 
              subscription.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 
              subscription.status === 'Expired' ? 'bg-red-100 text-red-800' : 
              'bg-gray-100 text-gray-800'}`}>
              {subscription.status}
            </span>
          </div>
        </div>
        
        <div className="mt-4 md:mt-0">
          <div className="text-2xl font-bold text-blue-800">
            ${subscription.price}
            <span className="text-sm font-normal text-blue-600">
              /{subscription.billingFrequency === 'Monthly' ? 'mo' : 
                subscription.billingFrequency === 'Quarterly' ? 'qtr' : 'yr'}
            </span>
          </div>
          <p className="text-sm text-blue-600 mt-1">{subscription.type} {t('subscriptions.plan')}</p>
        </div>
      </div>
      
      {/* Features */}
      {subscription.features && (
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-2">
          {subscription.features.map((feature, idx) => (
            <div key={idx} className="flex items-center">
              <svg className="h-5 w-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="text-sm text-gray-700">{feature}</span>
            </div>
          ))}
        </div>
      )}
      
      {/* Actions */}
      {hasPermission(currentUser, 'changeOwnSubscription') && (
        <div className="mt-6 flex justify-end">
          <button
            className="ml-3 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm"
            onClick={() => onChangePlan(subscription)}
          >
            {t('customers.subscription.changePlan')}
          </button>
        </div>
      )}
    </div>
  );
};

export default FeaturedSubscription;