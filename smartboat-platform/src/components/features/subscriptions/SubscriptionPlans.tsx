import React from 'react';
import { useTranslation } from 'react-i18next';
import SubscriptionPlan from './SubscriptionPlan';
import { useSubscriptionPlans } from '../../../hooks/queries/useSubscriptionQueries';
import { LoadingOverlay, ErrorNotification } from '../../common';

// Default plan features as fallback
const DEFAULT_PLANS = {
  Standard: {
    features: [
      { text: "Up to 5 sensors per vessel" },
      { text: "Daily data updates" },
      { text: "Email alerts" },
      { text: "Basic reporting" }
    ],
    description: "Basic vessel monitoring with limited sensors",
    price: "$100",
    bgColor: "bg-blue-50"
  },
  Professional: {
    features: [
      { text: "Up to 10 sensors per vessel" },
      { text: "Hourly data updates" },
      { text: "SMS and email alerts" },
      { text: "Advanced reporting" },
      { text: "30-day data history" }
    ],
    description: "Advanced monitoring with more sensors",
    price: "$150",
    bgColor: "bg-blue-100",
    isHighlighted: true
  },
  Enterprise: {
    features: [
      { text: "Unlimited sensors per vessel" },
      { text: "Real-time data updates" },
      { text: "Priority support" },
      { text: "Advanced analytics" },
      { text: "1-year data history" }
    ],
    description: "Comprehensive monitoring with unlimited sensors",
    price: "$200",
    bgColor: "bg-blue-200",
    buttonText: "Contact Sales"
  }
};

const SubscriptionPlans: React.FC = () => {
  const { t } = useTranslation();
  
  // Fetch subscription plans
  const { 
    data: plans = [], 
    isLoading, 
    isError, 
    error, 
    refetch 
  } = useSubscriptionPlans({
    useMockFallback: true
  });
  
  // Map plan features to feature objects expected by SubscriptionPlan component
  const getFeatureObjects = (features: string[]) => {
    return features.map(feature => ({ text: feature }));
  };
  
  return (
    <div className="relative">
      {/* Loading overlay */}
      {isLoading && <LoadingOverlay />}
      
      {/* Error notification */}
      {isError && (
        <ErrorNotification 
          message={t('subscriptions.errorFetchingPlans')} 
          onRetry={refetch}
          error={error}
        />
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {plans.length > 0 ? (
          // If we have plans from API, use them
          plans.map((plan, index) => (
            <SubscriptionPlan 
              key={plan.id || index}
              title={plan.name}
              description={plan.description}
              price={`$${plan.price}`}
              unit={`/${plan.billingFrequency.toLowerCase()}`}
              bgColor={index === 1 ? "bg-blue-100" : index === 2 ? "bg-blue-200" : "bg-blue-50"}
              features={getFeatureObjects(plan.features || [])}
              isHighlighted={index === 1}
              buttonText={plan.name === 'Enterprise' ? "Contact Sales" : undefined}
            />
          ))
        ) : !isLoading ? (
          // If not loading and no plans, use default plans
          <>
            {/* Standard Plan */}
            <SubscriptionPlan 
              title="Standard"
              description={DEFAULT_PLANS.Standard.description}
              price={DEFAULT_PLANS.Standard.price}
              unit="/sensor/month"
              bgColor={DEFAULT_PLANS.Standard.bgColor}
              features={DEFAULT_PLANS.Standard.features}
            />
            
            {/* Professional Plan */}
            <SubscriptionPlan 
              title="Professional"
              description={DEFAULT_PLANS.Professional.description}
              price={DEFAULT_PLANS.Professional.price}
              unit="/sensor/month"
              bgColor={DEFAULT_PLANS.Professional.bgColor}
              features={DEFAULT_PLANS.Professional.features}
              isHighlighted={DEFAULT_PLANS.Professional.isHighlighted}
            />
            
            {/* Enterprise Plan */}
            <SubscriptionPlan 
              title="Enterprise"
              description={DEFAULT_PLANS.Enterprise.description}
              price={DEFAULT_PLANS.Enterprise.price}
              unit="/sensor/month"
              bgColor={DEFAULT_PLANS.Enterprise.bgColor}
              features={DEFAULT_PLANS.Enterprise.features}
              buttonText={DEFAULT_PLANS.Enterprise.buttonText}
            />
          </>
        ) : null}
      </div>
    </div>
  );
};

export default SubscriptionPlans;