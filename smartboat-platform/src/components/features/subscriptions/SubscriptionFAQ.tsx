import React from 'react';

const SubscriptionFAQ: React.FC = () => {
  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-6">
      <h2 className="text-xl font-medium mb-4 dark:text-white">Frequently Asked Questions</h2>

      <div className="space-y-4">
        <div>
          <h3 className="font-medium text-lg dark:text-white">How are subscriptions billed?</h3>
          <p className="text-gray-600 dark:text-gray-300">
            Subscriptions are billed monthly based on the number of active sensors.
            You can upgrade or downgrade your plan at any time.
          </p>
        </div>

        <div>
          <h3 className="font-medium text-lg dark:text-white">Can I change plans later?</h3>
          <p className="text-gray-600 dark:text-gray-300">
            Yes, you can switch between subscription plans at any time.
            Changes will be reflected in your next billing cycle.
          </p>
        </div>

        <div>
          <h3 className="font-medium text-lg dark:text-white">Is there a minimum commitment period?</h3>
          <p className="text-gray-600 dark:text-gray-300">
            No, all subscription plans are month-to-month with no long-term commitment required.
          </p>
        </div>

        <div>
          <h3 className="font-medium text-lg dark:text-white">How do I cancel my subscription?</h3>
          <p className="text-gray-600 dark:text-gray-300">
            You can cancel your subscription at any time through your account settings
            or by contacting our customer support team.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionFAQ;