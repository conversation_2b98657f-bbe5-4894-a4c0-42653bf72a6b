import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Subscription } from '../../../types';
import { useAuth } from '../../../context/AuthContext';
import { hasPermission } from '../../../utils/authUtils';
import { useTranslation } from 'react-i18next';
import {
  useSubscriptions,
  useSubscriptionsByCustomer,
  useCreateSubscription,
  useUpdateSubscription
} from '../../../hooks/queries/useSubscriptionQueries';
import EditSubscriptionForm from './EditSubscriptionForm';
import FeaturedSubscription from './FeaturedSubscription';
import SubscriptionTable from './SubscriptionTable';
import SubscriptionPlans from './SubscriptionPlans';
import CustomPlans from './CustomPlans';
import SubscriptionFAQ from './SubscriptionFAQ';
import EmptySubscriptions from './EmptySubscriptions';
import { LoadingOverlay, ErrorNotification } from '../../common';

const SubscriptionsContent: React.FC = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // State for UI
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null);
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false); // Add state for dragging

  // Query hooks based on user role
  const isAdmin = currentUser?.role === 'Administrator' || hasPermission(currentUser, 'viewAllSubscriptions');

  // For admins, fetch all subscriptions
  const {
    data: allSubscriptions = [],
    isLoading: isLoadingAll,
    isError: isErrorAll,
    error: errorAll,
    refetch: refetchAll
  } = useSubscriptions({
    enabled: !!currentUser && isAdmin,
    useMockFallback: true
  });

  // For customers, fetch only their subscriptions
  const {
    data: customerSubscriptions = [],
    isLoading: isLoadingCustomer,
    isError: isErrorCustomer,
    error: errorCustomer,
    refetch: refetchCustomer
  } = useSubscriptionsByCustomer(currentUser?.customerId, {
    enabled: !!currentUser && !!currentUser.customerId && !isAdmin,
    useMockFallback: true
  });

  // Mutations
  const createSubscription = useCreateSubscription();
  const updateSubscription = useUpdateSubscription();

  // Determine which subscriptions to show
  const subscriptions = isAdmin ? allSubscriptions : customerSubscriptions;
  const isLoading = isLoadingAll || isLoadingCustomer;
  const isError = isErrorAll || isErrorCustomer;
  const error = errorAll || errorCustomer;
  const refetch = isAdmin ? refetchAll : refetchCustomer;

  // Standardize mutation loading states
  const isMutating = createSubscription.isPending || updateSubscription.isPending;

  // Handle adding a new subscription
  const handleAddSubscription = (newSubscription: Subscription) => {
    if (selectedSubscription) {
      // Update existing subscription
      updateSubscription.mutate({
        id: selectedSubscription.id,
        subscriptionData: newSubscription
      }, {
        onSuccess: () => {
          setIsCreateModalOpen(false);
          setSelectedSubscription(null);
        }
      });
    } else {
      // Create new subscription
      createSubscription.mutate(newSubscription, {
        onSuccess: () => {
          setIsCreateModalOpen(false);
          setSelectedSubscription(null);
        }
      });
    }
  };

  // Handle editing an existing subscription
  const handleEditSubscription = (subscription: Subscription) => {
    // Instead of using the detail page, show the edit modal directly
    setSelectedSubscription(subscription);
    setIsCreateModalOpen(true);
  };

  // Handle viewing a subscription
  const handleViewSubscription = (subscription: Subscription) => {
    navigate(`/subscriptions/${subscription.id}`);
  };

  // Handle changing a subscription plan
  const handleChangePlan = (subscription: Subscription) => {
    navigate(`/subscriptions/${subscription.id}`, { state: { editMode: true } });
  };

  // Improved resize handler with isDragging state
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);

    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;

      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);

      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };

    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  return (
    <div className="container mx-auto">
      {/* Loading overlay for content area only */}
      {isLoading && (
        <LoadingOverlay 
          message="Loading subscriptions..."
          subMessage="Fetching subscription data..."
          contentOnly={true}
        />
      )}

      {/* Error notification */}
      {isError && (
        <ErrorNotification
          message={t('common.errorFetchingSubscriptions')}
          onRetry={refetch}
          error={error}
        />
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">
          {currentUser?.role === 'Customer' ? t('subscriptions.yourSubscriptions') : t('common.subscriptions')}
        </h1>
      </div>

      {/* Customer View - Featured Subscription (FIRST for customers) */}
      {currentUser?.role === 'Customer' && subscriptions.length > 0 && (
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-medium mb-4 text-gray-800 dark:text-white">{t('subscriptions.yourCurrentSubscription')}</h2>

          {/* Featured Customer Subscription Card */}
          <FeaturedSubscription
            subscription={subscriptions[0]}
            currentUser={currentUser}
            onChangePlan={handleChangePlan}
          />

          {/* If customer has more than one subscription, show the rest in table format */}
          {subscriptions.length > 1 && (
            <>
              <h3 className="text-lg font-medium mb-4">{t('subscriptions.yourOtherSubscriptions')}</h3>
              <SubscriptionTable
                subscriptions={subscriptions.slice(1)}
                currentUser={currentUser}
                onView={handleViewSubscription}
                onEdit={handleEditSubscription}
              />
            </>
          )}
        </div>
      )}

      {/* Admin/Manager View - Subscription Management (FIRST for admins/managers) */}
      {currentUser?.role !== 'Customer' && (
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-medium mb-4 text-gray-800 dark:text-white">{t('subscriptions.manageSubscriptions')}</h2>

          {subscriptions.length > 0 ? (
            <>
              <SubscriptionTable
                subscriptions={subscriptions}
                currentUser={currentUser}
                onView={handleViewSubscription}
                onEdit={handleEditSubscription}
              />

              {currentUser?.role === 'Administrator' && (
                <div className="mt-4 flex justify-end">
                  <button
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center"
                    onClick={() => {
                      setSelectedSubscription(null);
                      setIsCreateModalOpen(true);
                    }}
                    disabled={isMutating}
                  >
                    {isMutating ? (
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                    )}
                    {t('subscriptions.addNewSubscription')}
                  </button>
                </div>
              )}
            </>
          ) : (
            <EmptySubscriptions
              onCreateNew={() => {
                setSelectedSubscription(null);
                setIsCreateModalOpen(true);
              }}
              isLoading={isMutating}
            />
          )}
        </div>
      )}

      {/* Subscription Plans - Only show for non-admin users */}
      {currentUser?.role !== 'Administrator' && <SubscriptionPlans />}

      {/* Custom Plans - Only show for non-admin users */}
      {currentUser?.role !== 'Administrator' && <CustomPlans />}

      {/* Subscription FAQ - Only show for non-admin users */}
      {currentUser?.role !== 'Administrator' && <SubscriptionFAQ />}

      {/* Create/Edit Subscription Slideover */}
      <div
        className={`fixed inset-y-0 right-0 bg-white dark:bg-gray-900 shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isCreateModalOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {t('common.dragToResize')}
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full">
          {isCreateModalOpen && (
            <EditSubscriptionForm
              subscription={selectedSubscription}
              onSave={handleAddSubscription}
              onCancel={() => {
                setIsCreateModalOpen(false);
                setSelectedSubscription(null);
              }}
              isLoading={isMutating}
            />
          )}
        </div>
      </div>

      {/* Backdrop */}
      {isCreateModalOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={() => {
            setIsCreateModalOpen(false);
            setSelectedSubscription(null);
          }}
        ></div>
      )}
    </div>
  );
};

export default SubscriptionsContent;