import React from "react";

interface PlanFeature {
  text: string;
}

interface SubscriptionPlanProps {
  title: string;
  description: string;
  price: string;
  unit: string;
  bgColor: string;
  features: PlanFeature[];
  isHighlighted?: boolean;
  buttonText?: string;
  onSelect?: () => void;
}

const SubscriptionPlan: React.FC<SubscriptionPlanProps> = ({
  title,
  description,
  price,
  unit,
  bgColor,
  features,
  isHighlighted = false,
  buttonText = "Select Plan",
  onSelect,
}) => {
  return (
    <div
      className={`bg-white dark:bg-gray-900 rounded-lg shadow overflow-hidden ${
        isHighlighted ? "ring-2 ring-blue-500" : ""
      }`}
    >
      <div className={`p-6 ${bgColor} dark:bg-gray-800`}>
        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
          {title}
        </h3>
        <p className="mt-1 text-gray-600 dark:text-gray-400">{description}</p>
        <p className="mt-4 text-3xl font-bold text-gray-900 dark:text-white">
          {price}
          <span className="text-sm font-normal text-gray-600 dark:text-gray-400">
            {unit}
          </span>
        </p>
      </div>

      <div className="p-6">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <svg
                className="h-5 w-5 text-green-500 dark:text-green-400 mr-2"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-gray-800 dark:text-gray-200">
                {feature.text}
              </span>
            </li>
          ))}
        </ul>

        <div className="mt-6">
          <button
            className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
            onClick={onSelect}
          >
            {buttonText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPlan;
