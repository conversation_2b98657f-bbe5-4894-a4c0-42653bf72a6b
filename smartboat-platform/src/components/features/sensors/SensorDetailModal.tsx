import React, { useState } from 'react';
import { Sensor } from '../../../types';
import { StatusBadge, SensorChart } from '../../common';
import { useSensorData } from '../../../hooks/queries/useSensorQueries';
import { useTranslation } from 'react-i18next';

interface SensorDetailModalProps {
  sensor: Sensor;
  onClose: () => void;
}

const SensorDetailModal: React.FC<SensorDetailModalProps> = ({ sensor, onClose }) => {
  const { t } = useTranslation();
  const [timeframe, setTimeframe] = useState('day'); // 'day', 'week', 'month'

  // Fetch sensor data using React Query
  const {
    data: sensorData = [],
    isLoading: dataLoading,
    isError: dataError
  } = useSensorData(sensor.id, {
    timeframe,
    // Default to last 24 hours
    from: timeframe === 'day' ? Date.now() - 24 * 60 * 60 * 1000 :
          timeframe === 'week' ? Date.now() - 7 * 24 * 60 * 60 * 1000 :
          Date.now() - 30 * 24 * 60 * 60 * 1000
  }, {
    // Use mock data fallback
    useMockFallback: true,
    // Update more frequently for sensor data
    refetchInterval: 60 * 1000, // 1 minute
  });

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">{sensor.name}</h3>
            <button
              className="text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300"
              onClick={onClose}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Sensor Details */}
            <div className="md:col-span-1">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Sensor Information</h4>

                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">ID:</span>
                    <span className="ml-2 text-gray-900 dark:text-gray-200">SEN-{sensor.id.toString().padStart(4, '0')}</span>
                  </div>

                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Type:</span>
                    <span className="ml-2 text-gray-900 dark:text-gray-200">{sensor.type}</span>
                  </div>

                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Vessel:</span>
                    <span className="ml-2 text-gray-900 dark:text-gray-200">{sensor.vessel}</span>
                  </div>

                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Location:</span>
                    <span className="ml-2 text-gray-900 dark:text-gray-200">{sensor.location}</span>
                  </div>

                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Status:</span>
                    <span className="ml-2">
                      <StatusBadge status={sensor.status} />
                    </span>
                  </div>

                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Last Reading:</span>
                    <span className="ml-2 text-gray-900 dark:text-gray-200 font-medium">{sensor.lastReading}</span>
                  </div>

                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Last Updated:</span>
                    <span className="ml-2 text-gray-900 dark:text-gray-200">{sensor.lastUpdated}</span>
                  </div>

                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Alert Threshold:</span>
                    <span className="ml-2 text-gray-900 dark:text-gray-200">{sensor.alertThreshold}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Sensor Chart */}
            <div className="md:col-span-2">
              {dataLoading && (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                </div>
              )}

              {dataError && (
                <div className="flex items-center justify-center h-64 bg-red-50 rounded-lg">
                  <div className="text-center p-4">
                    <p className="text-red-500 mb-2">{t('sensors.dataFetchError')}</p>
                    <button
                      className="px-3 py-1 bg-blue-500 text-white rounded-md text-sm"
                      onClick={() => setTimeframe(timeframe)} // This will re-fetch
                    >
                      {t('common.retry')}
                    </button>
                  </div>
                </div>
              )}

              {!dataLoading && !dataError && (
                <>
                  <div className="mb-4 flex justify-between items-center">
                    <h4 className="font-medium text-gray-800 dark:text-white">{t('sensors.dataChart')}</h4>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setTimeframe('day')}
                        className={`px-3 py-1 rounded-md text-sm ${timeframe === 'day'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'}`}
                      >
                        {t('common.day')}
                      </button>
                      <button
                        onClick={() => setTimeframe('week')}
                        className={`px-3 py-1 rounded-md text-sm ${timeframe === 'week'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'}`}
                      >
                        {t('common.week')}
                      </button>
                      <button
                        onClick={() => setTimeframe('month')}
                        className={`px-3 py-1 rounded-md text-sm ${timeframe === 'month'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'}`}
                      >
                        {t('common.month')}
                      </button>
                    </div>
                  </div>
                  <SensorChart
                    sensorId={sensor.id}
                    vesselName={sensor.vessel}
                    location={sensor.location}
                    timeframe={activeTimeframe}
                  />
                </>
              )}
            </div>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
            <button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SensorDetailModal;