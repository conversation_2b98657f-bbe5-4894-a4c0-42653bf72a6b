import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Sensor } from '../../../types';
import { StatusBadge } from '../../common';
import { useAuth } from '../../../context/AuthContext';
import { useTranslation } from 'react-i18next';
import { useDeleteSensor } from '../../../hooks/queries/useSensorQueries';
import { formatDate } from '../../../utils/dateUtils';

interface SensorRowProps {
  sensor: Sensor;
  vessels?: any[];
}

const SensorRow: React.FC<SensorRowProps> = ({ sensor, vessels = [] }) => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth(); // Get the permission checker function
  const { t } = useTranslation();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const deleteSensor = useDeleteSensor();

  const handleViewSensor = () => {
    navigate(`/sensors/${sensor.id}`);
  };

  const handleDeleteSensor = async () => {
    try {
      await deleteSensor.mutateAsync(sensor.id);
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('Failed to delete sensor:', error);
      alert(t('sensors.deleteFailed'));
    }
  };

  return (
    <>
      <tr className="hover:bg-gray-50 dark:hover:bg-gray-800">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {sensor.type === 'Temperature' ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2V9a2 2 0 012-2h6a2 2 0 012 2v2a2 2 0 01-2 2v6a4 4 0 11-8 0zm8-6V9a2 2 0 012-2h.093A2 2 0 0121 9v2a2 2 0 01-2 2h-.093a2 2 0 01-2-2z" />
              ) : sensor.type === 'Pressure' ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              ) : sensor.type === 'Humidity' ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              )}
            </svg>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 dark:text-white">{sensor.name}</div>
            <div className="text-sm text-gray-500 dark:text-gray-400">{sensor.type}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900 dark:text-white">
          {vessels.find(v => 
            v.id.toString() === sensor.vesselId?.toString() || 
            v.id === sensor.vesselId ||
            v.Id?.toString() === sensor.vesselId?.toString() ||
            v.Id === sensor.vesselId
          )?.name || `Unknown Vessel (ID: ${sensor.vesselId})`}
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400">{sensor.location || 'Unknown Location'}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <StatusBadge status={sensor.status} />
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
        {sensor.lastReading || 'No reading'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
        {formatDate(sensor.lastUpdated)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <button
          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
          onClick={handleViewSensor}
        >
          {t('common.view')}
        </button>
        {/* Only show Delete button if user has editSensors permission */}
        {hasPermission('editSensors') && (
          <button
            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
            onClick={() => setShowDeleteConfirm(true)}
          >
            {t('common.delete')}
          </button>
        )}
      </td>
    </tr>

    {/* Delete Confirmation Modal */}
    {showDeleteConfirm && (
      <>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" onClick={() => setShowDeleteConfirm(false)}></div>
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 z-50 max-w-md w-full">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {t('sensors.confirmDelete')}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
            {t('sensors.deleteWarning', { sensorName: sensor.name })}
          </p>
          <div className="flex justify-end space-x-3">
            <button
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
              onClick={() => setShowDeleteConfirm(false)}
              disabled={deleteSensor.isPending}
            >
              {t('common.cancel')}
            </button>
            <button
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
              onClick={handleDeleteSensor}
              disabled={deleteSensor.isPending}
            >
              {deleteSensor.isPending ? t('common.deleting') : t('common.delete')}
            </button>
          </div>
        </div>
      </>
    )}
    </>
  );
};

export default SensorRow;