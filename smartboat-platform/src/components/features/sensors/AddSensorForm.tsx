import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useCreateSensor } from '../../../hooks/queries/useSensorQueries';

interface AddSensorFormProps {
  vesselId?: string;
  vesselName?: string;
  availableVessels?: Array<{ id: string; name: string; }>;
  onSuccess?: (sensor: any) => void;
  onCancel?: () => void;
  isOpen: boolean;
}

const AddSensorForm: React.FC<AddSensorFormProps> = ({
  vesselId,
  vesselName,
  availableVessels = [],
  onSuccess,
  onCancel,
  isOpen
}) => {
  const { t } = useTranslation();
  const createSensor = useCreateSensor();

  const [formData, setFormData] = useState({
    name: '',
    type: '',
    status: 'Active',
    location: '',
    alertThreshold: '',
    vesselId: vesselId || (availableVessels.length > 0 ? availableVessels[0].id : '')
  });

  const [formError, setFormError] = useState<string | null>(null);

  // Reset form when modal opens or vesselId changes
  useEffect(() => {
    if (isOpen) {
      const newVesselId = vesselId || (availableVessels.length > 0 ? availableVessels[0].id : '');
      setFormData({
        name: '',
        type: '',
        status: 'Active',
        location: '',
        alertThreshold: '',
        vesselId: newVesselId
      });
      setFormError(null);
    }
  }, [isOpen, vesselId, availableVessels]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    if (!formData.name.trim()) {
      setFormError(t('sensors.nameRequired'));
      return;
    }

    if (!formData.type) {
      setFormError(t('sensors.typeRequired'));
      return;
    }

    if (!formData.vesselId) {
      console.error('Sensor creation failed - no vessel selected:', {
        formDataVesselId: formData.vesselId,
        vesselIdProp: vesselId,
        availableVessels: availableVessels.map(v => ({ id: v.id, name: v.name })),
        isOpen
      });
      setFormError(t('sensors.vesselRequired'));
      return;
    }

    const sensorData = {
      name: formData.name.trim(),
      type: formData.type,
      vesselId: formData.vesselId,
      location: formData.location.trim() || null,
      status: formData.status || 'Active',
      alertThreshold: formData.alertThreshold.trim() || null
    };

    createSensor.mutate(sensorData, {
      onSuccess: (newSensor) => {
        setFormData({
          name: '',
          type: '',
          status: 'Active',
          location: '',
          alertThreshold: '',
          vesselId: vesselId || (availableVessels.length > 0 ? availableVessels[0].id : '')
        });
        onSuccess?.(newSensor);
      },
      onError: (error) => {
        console.error('Failed to create sensor:', error);
        setFormError(t('sensors.createFailed'));
      }
    });
  };

  if (!isOpen) return null;

  const sensorTypes = [
    'Temperature',
    'Pressure',
    'Humidity',
    'Fuel Level',
    'Engine RPM',
    'Speed',
    'GPS',
    'Depth',
    'Wind Speed',
    'Wind Direction',
    'Battery Voltage',
    'Current',
    'Vibration',
    'Flow Rate',
    'Other'
  ];


  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {t('sensors.addNewSensor')}
              </h3>
              {vesselName && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {t('sensors.addToVessel')}: {vesselName}
                </p>
              )}
            </div>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {formError && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {formError}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4 max-h-96 overflow-y-auto">
            {availableVessels.length > 1 && (
              <div>
                <label htmlFor="vesselId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('sensors.selectVessel')}*
                </label>
                <select
                  id="vesselId"
                  name="vesselId"
                  value={formData.vesselId}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                >
                  {availableVessels.map(vessel => (
                    <option key={vessel.id} value={vessel.id}>
                      {vessel.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('sensors.sensorName')}*
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              />
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('sensors.type')}*
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              >
                <option value="">{t('sensors.selectType')}</option>
                {sensorTypes.map(type => (
                  <option key={type} value={type}>
                    {t(`sensors.types.${type.toLowerCase().replace(/\s+/g, '')}`, type)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('sensors.location')}
              </label>
              <input
                type="text"
                id="location"
                name="location"
                value={formData.location}
                onChange={handleChange}
                placeholder={t('sensors.locationPlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div>
              <label htmlFor="alertThreshold" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('sensors.alertThreshold')}
              </label>
              <input
                type="text"
                id="alertThreshold"
                name="alertThreshold"
                value={formData.alertThreshold}
                onChange={handleChange}
                placeholder="e.g. 45°C, 3.0 MPa, 80%"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>


            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('common.status')}
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="Active">{t('common.active')}</option>
                <option value="Inactive">{t('common.inactive')}</option>
                <option value="Maintenance">{t('sensors.status.maintenance')}</option>
                <option value="Calibrating">{t('sensors.status.calibrating')}</option>
                <option value="Error">{t('sensors.status.error')}</option>
              </select>
            </div>

            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onCancel}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                disabled={createSensor.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 flex items-center justify-center"
                disabled={createSensor.isPending}
              >
                {createSensor.isPending ? (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : null}
                {t('sensors.createSensor')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddSensorForm;