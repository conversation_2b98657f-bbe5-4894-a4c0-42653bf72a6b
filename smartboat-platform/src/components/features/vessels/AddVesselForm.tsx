import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useCreateVessel } from '../../../hooks/queries/useVesselQueries';
import { useOwnersByCompany } from '../../../hooks/queries/useOwnerQueries';

interface AddVesselFormProps {
  companyId?: string;
  companyName?: string;
  availableCompanies?: Array<{ id: string; name: string; }>;
  onSuccess?: (vessel: any) => void;
  onCancel?: () => void;
  isOpen: boolean;
}

const AddVesselForm: React.FC<AddVesselFormProps> = ({
  companyId,
  companyName,
  availableCompanies = [],
  onSuccess,
  onCancel,
  isOpen
}) => {
  const { t } = useTranslation();
  const createVessel = useCreateVessel();

  const [formData, setFormData] = useState({
    name: '',
    number: '',
    type: null as number | null,
    location: '',
    status: 'Active',
    startDate: '',
    endDate: '',
    image: '',
    onsigners: 0,
    offsigners: 0,
    companyId: companyId || (availableCompanies.length > 0 ? availableCompanies[0].id : ''),
    ownerId: ''
  });

  const [formError, setFormError] = useState<string | null>(null);

  // Fetch owners for the selected company
  const { data: owners = [] } = useOwnersByCompany(formData.companyId, {
    enabled: !!formData.companyId
  });

  // Reset form when modal opens or companyId changes
  useEffect(() => {
    if (isOpen) {
      const newCompanyId = companyId || (availableCompanies.length > 0 ? availableCompanies[0].id : '');
      setFormData({
        name: '',
        number: '',
        type: null,
        location: '',
        status: 'Active',
        startDate: '',
        endDate: '',
        image: '',
        onsigners: 0,
        offsigners: 0,
        companyId: newCompanyId,
        ownerId: ''
      });
      setFormError(null);
    }
  }, [isOpen, companyId, availableCompanies]);

  // Reset owner selection when company changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      ownerId: ''
    }));
  }, [formData.companyId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'type' ? (value ? parseInt(value, 10) : null) : value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    if (!formData.name.trim()) {
      setFormError(t('vessels.nameRequired'));
      return;
    }

    if (!formData.number.trim()) {
      setFormError(t('vessels.numberRequired'));
      return;
    }

    if (formData.type === null) {
      setFormError(t('vessels.typeRequired'));
      return;
    }

    if (!formData.location.trim()) {
      setFormError(t('vessels.locationRequired'));
      return;
    }

    if (!formData.companyId.trim()) {
      console.error('Vessel creation failed - no company selected:', {
        formDataCompanyId: formData.companyId,
        companyIdProp: companyId,
        availableCompanies: availableCompanies.map(c => ({ id: c.id, name: c.name })),
        isOpen
      });
      setFormError(t('vessels.companyRequired'));
      return;
    }

    const vesselData = {
      name: formData.name.trim(),
      number: formData.number.trim(),
      type: formData.type,
      location: formData.location.trim(),
      status: formData.status,
      startDate: formData.startDate || null,
      endDate: formData.endDate || null,
      image: formData.image || null,
      onsigners: formData.onsigners || 0,
      offsigners: formData.offsigners || 0,
      companyId: formData.companyId,
      ownerId: formData.ownerId || null
    };

    createVessel.mutate(vesselData, {
      onSuccess: (newVessel) => {
        setFormData({
          name: '',
          number: '',
          type: null,
          location: '',
          status: 'Active',
          startDate: '',
          endDate: '',
          image: '',
          onsigners: 0,
          offsigners: 0,
          companyId: companyId || (availableCompanies.length > 0 ? availableCompanies[0].id : ''),
          ownerId: ''
        });
        onSuccess?.(newVessel);
      },
      onError: (error) => {
        console.error('Failed to create vessel:', error);
        setFormError(t('vessels.createFailed'));
      }
    });
  };

  if (!isOpen) return null;

  const vesselTypes = [
    { value: 1, label: t('vessels.vesselTypes.mechanical', 'Motor-powered') },
    { value: 2, label: t('vessels.vesselTypes.sailing', 'Sailing') }
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {t('vessels.addNewVessel')}
              </h3>
              {companyName && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {t('vessels.addToCompany')}: {companyName}
                </p>
              )}
            </div>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {formError && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {formError}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4 max-h-96 overflow-y-auto">
            {availableCompanies.length > 1 && (
              <div>
                <label htmlFor="companyId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vessels.selectCompany')}*
                </label>
                <select
                  id="companyId"
                  name="companyId"
                  value={formData.companyId}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                >
                  {availableCompanies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Owner Selection */}
            <div>
              <label htmlFor="ownerId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('vessels.selectOwner')}
              </label>
              <select
                id="ownerId"
                name="ownerId"
                value={formData.ownerId}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">{t('vessels.noOwnerSelected')}</option>
                {owners.map(owner => (
                  <option key={owner.id} value={owner.id}>
                    {owner.name} {owner.email ? `(${owner.email})` : ''}
                  </option>
                ))}
              </select>
              {owners.length === 0 && formData.companyId && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {t('vessels.noOwnersForCompany')}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('vessels.vesselName')}*
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              />
            </div>

            <div>
              <label htmlFor="number" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('vessels.vesselNumber')}*
              </label>
              <input
                type="text"
                id="number"
                name="number"
                value={formData.number}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              />
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('vessels.vesselType')}*
              </label>
              <select
                id="type"
                name="type"
                value={formData.type || ''}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              >
                <option value="">{t('vessels.selectType')}</option>
                {vesselTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('vessels.currentLocation')}*
              </label>
              <input
                type="text"
                id="location"
                name="location"
                value={formData.location}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="City, Country"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vessels.startDate')}
                </label>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vessels.endDate')}
                </label>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <label htmlFor="onsigners" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vessels.onsigners')}
                </label>
                <input
                  type="number"
                  id="onsigners"
                  name="onsigners"
                  value={formData.onsigners}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <label htmlFor="offsigners" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vessels.offsigners')}
                </label>
                <input
                  type="number"
                  id="offsigners"
                  name="offsigners"
                  value={formData.offsigners}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('common.status')}
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="Active">{t('common.active')}</option>
                <option value="Inactive">{t('common.inactive')}</option>
                <option value="Maintenance">{t('vessels.status.maintenance')}</option>
                <option value="At Port">{t('vessels.status.atport')}</option>
                <option value="At Sea">{t('vessels.status.atsea')}</option>
              </select>
            </div>

            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onCancel}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                disabled={createVessel.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 flex items-center justify-center"
                disabled={createVessel.isPending}
              >
                {createVessel.isPending ? (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : null}
                {t('vessels.createVessel')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddVesselForm;