import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Vessel } from '../../../types';
import EditVesselForm from './EditVesselForm';
import { useAuth } from '../../../context/AuthContext';
import { useTranslation } from 'react-i18next';
import { useVessels, useCreateVessel, useDeleteVessel } from '../../../hooks';
import { LoadingOverlay, StatusBadge } from '../../common';
import { formatDate } from '../../../utils/dateUtils';

const Vessels: React.FC = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false); // Add state for dragging
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const isAdmin = currentUser?.role === 'Administrator' || currentUser?.role === 'Manager';
  const { hasPermission } = useAuth();
  const deleteVessel = useDeleteVessel();

  // Handle vessel selection
  const handleSelectVessel = (vesselId: string) => {
    navigate(`/vessels/${vesselId}`);
  };

  // Use React Query to fetch vessels
  const queryOptions = {
    // For customers, we'll filter server-side by company
    // This is just an example - actual implementation might vary based on API
    ...(currentUser?.role === 'Customer' && currentUser.customerId && {
      customerId: currentUser.customerId
    })
  };

  // Fetch vessels with React Query
  const {
    data: vessels = [],
    isLoading,
    isError
  } = useVessels({
    // Add query options like staleTime, etc. if needed
    ...queryOptions
  });

  // Mutation hook for creating vessels
  const createVesselMutation = useCreateVessel({
    // Show success message or other side effects on successful creation
    onSuccess: () => {
      setIsCreateModalOpen(false);
    }
  });

  // Filter vessels based on search query and status filter
  const filteredVessels = vessels.filter(
    vessel =>
      (vessel.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
       vessel.number?.toString().includes(searchQuery) ||
       vessel.location?.toLowerCase().includes(searchQuery.toLowerCase())) &&
      (filterStatus === "all" || vessel.status.toLowerCase() === filterStatus.toLowerCase())
  );

  // Handle adding a new vessel
  const handleAddVessel = (newVessel: Vessel) => {
    // Use the mutation to add the vessel to the API
    createVesselMutation.mutate(newVessel);
  };

  // Handle vessel deletion
  const handleDeleteVessel = async (vesselId: string) => {
    try {
      await deleteVessel.mutateAsync(vesselId);
      setShowDeleteConfirm(null);
    } catch (error) {
      console.error('Failed to delete vessel:', error);
      alert(t('vessels.deleteFailed'));
    }
  };

  // Improved resize handler with isDragging state
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);

    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;

      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);

      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };

    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">{t('vessels.vesselsList')}</h1>
        {isAdmin && (
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
            onClick={() => setIsCreateModalOpen(true)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            {t('vessels.addNewVessel')}
          </button>
        )}
      </div>

      {/* Search and filters */}
      <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow mb-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
            <input
              type="text"
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-200 text-sm rounded-lg block w-full pl-10 p-2.5"
              placeholder={t('vessels.searchVessels')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex items-center">
            <select
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-200 text-sm rounded-lg p-2.5"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">{t('vessels.allStatuses')}</option>
              <option value="active">{t('vessels.active')}</option>
              <option value="maintenance">{t('vessels.maintenance')}</option>
              <option value="inactive">{t('vessels.inactive')}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Loading state */}
      <div className="relative">
        {isLoading && <LoadingOverlay />}
      </div>

      {/* Error state */}
      {isError && (
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-8 text-center">
          <div className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 p-4 rounded-lg mb-4">
            <h3 className="text-lg font-medium mb-2">{t('common.error')}</h3>
            <p>{t('vessels.errorLoadingVessels')}</p>
            <p className="mt-2 text-sm">{t('common.usingMockData')}</p>
          </div>
        </div>
      )}

      {/* Vessels table - only show when not loading */}
      {!isLoading && !isError && filteredVessels.length > 0 && (
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.name')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.type')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.status')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.sensors')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.location')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.lastUpdated')}
                  </th>
                  <th scope="col" className="relative px-6 py-3">
                    <span className="sr-only">{t('common.actions')}</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredVessels.map(vessel => (
                  <tr key={vessel.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900 dark:text-white">{vessel.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {vessel.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge status={vessel.status} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {vessel.sensors}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {typeof vessel.location === 'string'
                        ? vessel.location
                        : vessel.location
                          ? `${vessel.location.latitude.toFixed(4)}, ${vessel.location.longitude.toFixed(4)}`
                          : vessel.homePort || t('vessels.locationUnknown')
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(vessel.lastUpdated)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                        onClick={() => handleSelectVessel(vessel.id)}
                      >
                        {t('common.view')}
                      </button>
                      {(hasPermission('manageCompanies') || isAdmin) && (
                        <button
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          onClick={() => setShowDeleteConfirm(vessel.id)}
                        >
                          {t('common.delete')}
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Empty state - only show when not loading and no error */}
      {!isLoading && !isError && filteredVessels.length === 0 && (
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-8 text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('vessels.noVesselsFound')}</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {isAdmin
              ? t('vessels.noVesselsMessage')
              : t('vessels.noVesselsMessageUser')
            }
          </p>
          {isAdmin && (
            <button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center mx-auto"
              onClick={() => setIsCreateModalOpen(true)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              {t('vessels.addNewVessel')}
            </button>
          )}
        </div>
      )}

      {/* Create Vessel Slideover */}
      <div
        className={`fixed inset-y-0 right-0 bg-white dark:bg-gray-900 shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isCreateModalOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {t('common.dragToResize')}
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full">
          {isCreateModalOpen && (
            <EditVesselForm
              vessel={null}
              onSave={handleAddVessel}
              onCancel={() => setIsCreateModalOpen(false)}
            />
          )}
        </div>
      </div>

      {/* Backdrop */}
      {isCreateModalOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={() => setIsCreateModalOpen(false)}
        ></div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" onClick={() => setShowDeleteConfirm(null)}></div>
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 z-50 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t('vessels.confirmDelete')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {t('vessels.deleteWarning', {
                vesselName: filteredVessels.find(v => v.id === showDeleteConfirm)?.name
              })}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
                onClick={() => setShowDeleteConfirm(null)}
                disabled={deleteVessel.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
                onClick={() => handleDeleteVessel(showDeleteConfirm)}
                disabled={deleteVessel.isPending}
              >
                {deleteVessel.isPending ? t('common.deleting') : t('common.delete')}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Vessels;