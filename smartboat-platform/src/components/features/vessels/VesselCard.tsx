import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Vessel } from '../../../types';
import { StatusBadge } from '../../common';
import { useTranslation } from 'react-i18next';
import { formatDate } from '../../../utils/dateUtils';

interface VesselCardProps {
  vessel: Vessel;
}

const VesselCard: React.FC<VesselCardProps> = ({ vessel }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleViewDetails = () => {
    navigate(`/vessels/${vessel.id}`);
  };

  const handleManageSensors = () => {
    navigate(`/vessels/${vessel.id}`, { state: { activeTab: 'sensors' } });
  };

  const handleEditVessel = () => {
    navigate(`/vessels/${vessel.id}`, { state: { editMode: true } });
  };

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow overflow-hidden">
      <div className="h-40 bg-gray-200 dark:bg-gray-700 relative">
        {/* This would be a vessel image in production */}
        <div className="absolute inset-0 flex items-center justify-center bg-gray-300 dark:bg-gray-600">
          <span className="text-lg font-medium text-gray-600 dark:text-gray-300">{vessel.name}</span>
        </div>

        <div className="absolute bottom-2 right-2">
          <StatusBadge status={vessel.status} />
        </div>
      </div>

      <div className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-medium text-blue-600 dark:text-white">
              {vessel.number}. {vessel.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">{vessel.type}</p>
          </div>
          <div className="text-right">
            <span className="text-xs text-gray-500 dark:text-gray-400">{t('vessels.lastUpdate')}</span>
            <p className="text-sm text-gray-600 dark:text-gray-400">{formatDate(vessel.lastUpdated)}</p>
          </div>
        </div>

        <div className="mt-4 flex justify-between">
          <div>
            <span className="text-xs text-gray-500 dark:text-gray-400">{t('vessels.location')}</span>
            <p className="text-sm font-medium text-gray-800 dark:text-white">
              {typeof vessel.location === 'string'
                ? vessel.location
                : vessel.location
                  ? `${vessel.location.latitude.toFixed(4)}, ${vessel.location.longitude.toFixed(4)}`
                  : vessel.homePort || t('vessels.locationUnknown')}
            </p>
          </div>
          <div className="text-right">
            <span className="text-xs text-gray-500 dark:text-gray-400">{t('common.sensors')}</span>
            <p className="text-sm font-medium text-gray-800 dark:text-white">{vessel.sensors}</p>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700 flex justify-end">
          <button
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
            onClick={handleViewDetails}
          >
            {t('vessels.viewDetails')}
          </button>
          <button
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium ml-4"
            onClick={handleManageSensors}
          >
            {t('vessels.manageSensors')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default VesselCard;