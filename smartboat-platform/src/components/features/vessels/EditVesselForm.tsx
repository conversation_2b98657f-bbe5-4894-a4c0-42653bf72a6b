import React, { useState, useEffect } from "react";
import { Vessel } from "../../../types";
import { useTranslation } from "react-i18next";
import {
  useCreateVessel,
  useUpdateVessel,
} from "../../../hooks/queries/useVesselQueries";
import { useCompanies } from "../../../hooks";
import { useOwnersByCompany } from '../../../hooks/queries/useOwnerQueries';

interface EditVesselFormProps {
  vessel: Vessel | null; // The vessel to edit, null if creating a new one
  onSave: (vessel: Vessel) => void;
  onCancel: () => void;
}

const EditVesselForm: React.FC<EditVesselFormProps> = ({
  vessel,
  onSave,
  onCancel,
}) => {
  const { t } = useTranslation();

  // Setup React Query mutations
  const createVessel = useCreateVessel();
  const updateVessel = useUpdateVessel();

  // Fetch companies for the company selector
  const { data: companies = [] } = useCompanies();

  // Track submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Standardize loading state names
  const isLoading =
    isSubmitting || createVessel.isPending || updateVessel.isPending;

  // Form state
  const [formData, setFormData] = useState<Partial<Vessel>>({
    name: "",
    number: "",
    type: "Μηχανοκίνητο",
    location: "",
    status: "Active",
    startDate: "",
    endDate: "",
    image: "",
    companyId: companies.length > 0 ? companies[0].id : "",
    ownerId: "",
    onsigners: 0,
    offsigners: 0,
  });

  // Active section state
  const [activeSection, setActiveSection] = useState("basic");

  // Fetch owners for the selected company
  const { data: owners = [], isLoading: ownersLoading, error: ownersError } = useOwnersByCompany(formData.companyId, {
    enabled: !!formData.companyId
  });

  // Debug logging for owners
  React.useEffect(() => {
    console.log('📝 EditVesselForm: Company ID changed:', formData.companyId);
    console.log('📝 EditVesselForm: Owners loading:', ownersLoading);
    console.log('📝 EditVesselForm: Owners data:', owners);
    console.log('📝 EditVesselForm: Owners error:', ownersError);
  }, [formData.companyId, owners, ownersLoading, ownersError]);

  // Initialize form data when vessel changes
  useEffect(() => {
    if (vessel) {
      setFormData({
        id: vessel.id,
        name: vessel.name,
        number: vessel.number,
        type: vessel.type,
        location: vessel.location,
        status: vessel.status,
        startDate: vessel.startDate,
        endDate: vessel.endDate,
        image: vessel.image,
        // Keep the following fields as they are (not editable directly)
        onsigners: vessel.onsigners,
        offsigners: vessel.offsigners,
        sensors: vessel.sensors,
        lastUpdated: vessel.lastUpdated,
        companyId: vessel.companyId,
        ownerId: vessel.ownerId || "",
      });
    } else {
      // Reset form if not editing (creating new)
      const today = new Date();
      const formattedDate = today.toISOString().split("T")[0]; // YYYY-MM-DD format for API

      setFormData({
        name: "",
        number: "",
        type: "Μηχανοκίνητο",
        location: "",
        status: "Active",
        startDate: formattedDate,
        endDate: "",
        image: "",
        companyId: companies.length > 0 ? companies[0].id : "",
        ownerId: "",
        onsigners: 0,
        offsigners: 0,
      });
    }
  }, [vessel, companies]);

  // Reset owner selection when company changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      ownerId: ""
    }));
  }, [formData.companyId]);

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        name === "onsigners" || name === "offsigners"
          ? parseInt(value) || 0
          : value,
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate form data - CompanyId is required by backend validation
    if (
      !formData.name ||
      !formData.number ||
      !formData.type ||
      !formData.location ||
      !formData.companyId
    ) {
      alert(t("vessels.fillAllRequired"));
      setIsSubmitting(false);
      return;
    }

    // Create API payload to match backend DTOs
    const apiPayload = {
      name: formData.name.trim(),
      number: formData.number.trim(),
      type: formData.type,
      location: formData.location.trim(),
      status: formData.status || "Active",
      startDate: formData.startDate || null,
      endDate: formData.endDate || null,
      image: formData.image || null,
      onsigners: formData.onsigners || 0,
      offsigners: formData.offsigners || 0,
      companyId: formData.companyId, // Required - must not be null
      ownerId: formData.ownerId || null,
    };

    // Create a complete vessel object for local state management
    const completeVessel: Vessel = {
      id: formData.id || Math.floor(Math.random() * 1000), // Will be replaced by API response
      name: formData.name || "",
      number: formData.number || "",
      type: formData.type || "Μηχανοκίνητο",
      location: formData.location || "",
      status: formData.status || "Active",
      startDate: formData.startDate || "",
      endDate: formData.endDate || "",
      image: formData.image || "/vessel1.jpg",
      onsigners: formData.onsigners || 0,
      offsigners: formData.offsigners || 0,
      sensors: formData.sensors || 0,
      lastUpdated: formData.lastUpdated || "Today",
      companyId: formData.companyId,
      ownerId: formData.ownerId || undefined,
    };

    // If we're editing an existing vessel
    if (vessel) {
      updateVessel.mutate(
        {
          id: completeVessel.id,
          vesselData: apiPayload,
        },
        {
          onSuccess: (response) => {
            // Call the parent component's onSave callback with API response or local data
            onSave(response || completeVessel);
            setIsSubmitting(false);
          },
          onError: (error) => {
            console.error("Failed to update vessel:", error);
            setIsSubmitting(false);
            // You could add error handling UI here
          },
        }
      );
    } else {
      // If we're creating a new vessel
      createVessel.mutate(apiPayload, {
        onSuccess: (response) => {
          // Call the parent component's onSave callback with API response or local data
          onSave(response || completeVessel);
          setIsSubmitting(false);
        },
        onError: (error) => {
          console.error("Failed to create vessel:", error);
          setIsSubmitting(false);
          // You could add error handling UI here
        },
      });
    }
  };

  // Block types for the form sections
  const blockTypes = [
    {
      id: "basic",
      label: t("vessels.blocks.basicInformation"),
      icon: "/assets/icons/ship.svg",
      color: "bg-blue-500",
    },
    {
      id: "schedule",
      label: t("vessels.blocks.schedule"),
      icon: "/assets/icons/calendar.svg",
      color: "bg-green-500",
    },
    {
      id: "stats",
      label: t("vessels.blocks.statistics"),
      icon: "/assets/icons/chart.svg",
      color: "bg-purple-500",
    },
  ];

  // Function to render block icon
  const renderBlockIcon = (blockType: string) => {
    switch (blockType) {
      case "basic":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21c0-4.969-4.031-9-9-9s-9 4.031-9 9m18 0H1m3-3h1.5c.828 0 1.5-.672 1.5-1.5v-1c0-.828-.672-1.5-1.5-1.5H4c-.828 0-1.5.672-1.5 1.5v1c0 .828.672 1.5 1.5 1.5zm14 0h1.5c.828 0 1.5-.672 1.5-1.5v-1c0-.828-.672-1.5-1.5-1.5H18c-.828 0-1.5.672-1.5 1.5v1c0 .828.672 1.5 1.5 1.5zM4 13h16c2.209 0 4-1.791 4-4V5c0-1.104-.896-2-2-2H2C.896 3 0 3.896 0 5v4c0 2.209 1.791 4 4 4z"
            />
          </svg>
        );
      case "schedule":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        );
      case "stats":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
        );
      default:
        return null;
    }
  };

  // Function to get block color
  const getBlockColor = (blockType: string) => {
    const block = blockTypes.find((b) => b.id === blockType);
    return block ? block.color : "bg-gray-500";
  };

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden flex flex-col h-full">
      <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
        {/* Left sidebar - Block selection */}
        <div className="w-full md:w-56 bg-gray-100 dark:bg-gray-900 border-r border-gray-300 dark:border-gray-700 p-3 flex flex-col">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
              {vessel ? t("vessels.editVessel") : t("vessels.createNewVessel")}
            </h2>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
              {vessel
                ? t("vessels.updateVesselInfo")
                : t("vessels.addNewVesselInfo")}
            </p>
          </div>

          <div className="mb-4">
            <h3 className="text-xs uppercase tracking-wider text-gray-600 dark:text-gray-400 font-semibold mb-2">
              {t("common.blocks")}
            </h3>
            <div className="space-y-2">
              {blockTypes.map((block) => (
                <button
                  key={block.id}
                  onClick={() => setActiveSection(block.id)}
                  className={`w-full flex items-center p-2 rounded-md transition-colors ${
                    activeSection === block.id
                      ? block.color
                      : "bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700"
                  } text-gray-800 dark:text-white`}
                >
                  <div
                    className={`w-8 h-8 rounded-md flex items-center justify-center ${
                      activeSection === block.id
                        ? "bg-white bg-opacity-20"
                        : block.color
                    } mr-2`}
                  >
                    {renderBlockIcon(block.id)}
                  </div>
                  <span className="text-sm font-medium">{block.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex flex-col bg-gray-100 dark:bg-gray-900 overflow-hidden">
          <div className="flex-1 overflow-y-auto p-6">
            <form
              id="vessel-form"
              onSubmit={handleSubmit}
              className="max-w-3xl mx-auto"
            >
              <div className="mb-4 flex items-center justify-center">
                <h3 className="text-base font-medium text-gray-800 dark:text-white">
                  {activeSection === "basic"
                    ? t("vessels.blocks.basicInformation")
                    : activeSection === "schedule"
                    ? t("vessels.blocks.scheduleDetails")
                    : t("vessels.blocks.statistics")}
                </h3>
                <div
                  className={`ml-2 w-2 h-2 rounded-full ${getBlockColor(
                    activeSection
                  )}`}
                ></div>
              </div>

              {/* Basic Information Section */}
              {activeSection === "basic" && (
                <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-300 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                  <div className="p-4 border-b border-gray-300 dark:border-gray-700 flex items-center">
                    <div
                      className={`w-8 h-8 ${getBlockColor(
                        "basic"
                      )} rounded-md flex items-center justify-center text-white mr-3`}
                    >
                      {renderBlockIcon("basic")}
                    </div>
                    <h4 className="text-md font-medium text-gray-800 dark:text-gray-200">
                      {t("vessels.blocks.basicInformation")}
                    </h4>
                  </div>
                  <div className="p-5">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Vessel Name */}
                      <div>
                        <label
                          htmlFor="name"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("vessels.name")}*
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name || ""}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          required
                        />
                      </div>

                      {/* Vessel Number */}
                      <div>
                        <label
                          htmlFor="number"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("vessels.vesselNumber")}*
                        </label>
                        <input
                          type="text"
                          id="number"
                          name="number"
                          value={formData.number || ""}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          required
                        />
                      </div>

                      {/* Vessel Type */}
                      <div>
                        <label
                          htmlFor="type"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("vessels.type")}*
                        </label>
                        <select
                          id="type"
                          name="type"
                          value={formData.type || "Μηχανοκίνητο"}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                          <option value="Μηχανοκίνητο">
                            {t("vessels.vesselTypes.mechanical")}
                          </option>
                          <option value="Ιστιοπλοϊκό">
                            {t("vessels.vesselTypes.sailing")}
                          </option>
                        </select>
                      </div>

                      {/* Location */}
                      <div>
                        <label
                          htmlFor="location"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("vessels.currentLocation")}*
                        </label>
                        <input
                          type="text"
                          id="location"
                          name="location"
                          value={formData.location || ""}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          required
                          placeholder="City (Country)"
                        />
                      </div>

                      {/* Status */}
                      <div>
                        <label
                          htmlFor="status"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("common.status")}
                        </label>
                        <select
                          id="status"
                          name="status"
                          value={formData.status || "Active"}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                          <option value="Active">{t("vessels.active")}</option>
                          <option value="Maintenance">
                            {t("vessels.maintenance")}
                          </option>
                          <option value="Inactive">
                            {t("vessels.inactive")}
                          </option>
                        </select>
                      </div>

                      {/* Company */}
                      <div>
                        <label
                          htmlFor="companyId"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("common.company")}*
                        </label>
                        <select
                          id="companyId"
                          name="companyId"
                          value={formData.companyId || ""}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          required
                        >
                          <option value="">{t("vessels.selectCompany")}</option>
                          {companies.map((company) => (
                            <option key={company.id} value={company.id}>
                              {company.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Owner */}
                      <div>
                        <label
                          htmlFor="ownerId"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("vessels.selectOwner")}
                        </label>
                        <select
                          id="ownerId"
                          name="ownerId"
                          value={formData.ownerId || ""}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                          <option value="">{t("vessels.noOwnerSelected")}</option>
                          {owners.map((owner) => (
                            <option key={owner.id} value={owner.id}>
                              {owner.name} {owner.email ? `(${owner.email})` : ''}
                            </option>
                          ))}
                        </select>
                        {owners.length === 0 && formData.companyId && (
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            {t('vessels.noOwnersForCompany')}
                          </p>
                        )}
                      </div>

                      {/* Onsigners */}
                      <div>
                        <label
                          htmlFor="onsigners"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("vessels.onsigners")}
                        </label>
                        <input
                          type="number"
                          id="onsigners"
                          name="onsigners"
                          value={formData.onsigners || 0}
                          onChange={handleChange}
                          min="0"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>

                      {/* Offsigners */}
                      <div>
                        <label
                          htmlFor="offsigners"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("vessels.offsigners")}
                        </label>
                        <input
                          type="number"
                          id="offsigners"
                          name="offsigners"
                          value={formData.offsigners || 0}
                          onChange={handleChange}
                          min="0"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Schedule Section */}
              {activeSection === "schedule" && (
                <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-300 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                  <div className="p-4 border-b border-gray-300 dark:border-gray-700 flex items-center">
                    <div
                      className={`w-8 h-8 ${getBlockColor(
                        "schedule"
                      )} rounded-md flex items-center justify-center text-white mr-3`}
                    >
                      {renderBlockIcon("schedule")}
                    </div>
                    <h4 className="text-md font-medium text-gray-800 dark:text-gray-200">
                      {t("vessels.blocks.scheduleDetails")}
                    </h4>
                  </div>
                  <div className="p-5">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Start Date */}
                      <div>
                        <label
                          htmlFor="startDate"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("vessels.startDate")}
                        </label>
                        <input
                          type="date"
                          id="startDate"
                          name="startDate"
                          value={formData.startDate || ""}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>

                      {/* End Date */}
                      <div>
                        <label
                          htmlFor="endDate"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("vessels.endDate")} ({t("common.optional")})
                        </label>
                        <input
                          type="date"
                          id="endDate"
                          name="endDate"
                          value={formData.endDate || ""}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                    </div>

                    {/* Schedule calendar placeholder - in a real app this might be an interactive calendar */}
                    <div className="mt-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-4 border border-gray-300 dark:border-gray-600 h-48 flex items-center justify-center">
                      <div className="text-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-12 w-12 mx-auto text-gray-500 dark:text-gray-400 mb-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={1.5}
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                        <p className="text-gray-600 dark:text-gray-400 text-sm">
                          {t("vessels.scheduleInteractive")}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Statistics Section */}
              {activeSection === "stats" && (
                <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-300 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                  <div className="p-4 border-b border-gray-300 dark:border-gray-700 flex items-center">
                    <div
                      className={`w-8 h-8 ${getBlockColor(
                        "stats"
                      )} rounded-md flex items-center justify-center text-white mr-3`}
                    >
                      {renderBlockIcon("stats")}
                    </div>
                    <h4 className="text-md font-medium text-gray-800 dark:text-gray-200">
                      {t("common.statistics")}
                    </h4>
                  </div>
                  <div className="p-5">
                    {vessel ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("common.sensors")}
                          </label>
                          <input
                            type="text"
                            value={formData.sensors || 0}
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-300 sm:text-sm"
                            disabled
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("vessels.onsigners")}
                          </label>
                          <input
                            type="text"
                            value={formData.onsigners || 0}
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-300 sm:text-sm"
                            disabled
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("vessels.offsigners")}
                          </label>
                          <input
                            type="text"
                            value={formData.offsigners || 0}
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-300 sm:text-sm"
                            disabled
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("common.company")}
                          </label>
                          <input
                            type="text"
                            value={
                              companies.find((c) => c.id === formData.companyId)
                                ?.name || "No company selected"
                            }
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-300 sm:text-sm"
                            disabled
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("vessels.lastUpdated")}
                          </label>
                          <input
                            type="text"
                            value={formData.lastUpdated || "Today"}
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-300 sm:text-sm"
                            disabled
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-12 w-12 mx-auto text-gray-500 dark:text-gray-400 mb-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={1.5}
                            d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                        <p className="text-gray-600 dark:text-gray-400 text-sm">
                          {t("vessels.statUnavailable")}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </form>
          </div>

          {/* Fixed footer with buttons */}
          <div className="border-t border-gray-300 dark:border-gray-700 p-3 bg-gray-100 dark:bg-gray-900">
            <div className="flex flex-row space-x-3 justify-end max-w-3xl mx-auto">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-800 dark:text-white bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700"
              >
                {t("common.cancel")}
              </button>
              <button
                type="submit"
                form="vessel-form"
                disabled={isLoading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed flex items-center"
              >
                {isLoading && (
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                )}
                {vessel ? t("vessels.updateVessel") : t("vessels.createVessel")}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditVesselForm;
