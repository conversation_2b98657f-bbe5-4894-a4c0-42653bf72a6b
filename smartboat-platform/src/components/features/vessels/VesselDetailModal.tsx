import React, { useState } from 'react';
import { Vessel } from '../../../types';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, VesselMap } from '../../common';
import { useTranslation } from 'react-i18next';
import { useSensorsByVessel } from '../../../hooks/queries/useSensorQueries';

interface VesselDetailModalProps {
  vessel: Vessel;
  onClose: () => void;
}

const VesselDetailModal: React.FC<VesselDetailModalProps> = ({ vessel, onClose }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('info');

  // Fetch sensors for this vessel using React Query
  const {
    data: sensors = [],
    isLoading: sensorsLoading,
    isError: sensorsError,
    refetch: refetchSensors
  } = useSensorsByVessel(vessel.id, {
    // Use mock data fallback if API fails
    useMockFallback: true
  });

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-5xl w-full">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">{vessel.name}</h3>
            <button
              className="text-gray-400 hover:text-gray-500"
              onClick={onClose}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="border-b border-gray-200 mb-4">
            <nav className="-mb-px flex">
              <button
                className={`py-2 px-4 ${
                  activeTab === 'info'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('info')}
              >
                {t('vessels.tabs.vesselInfo')}
              </button>
              <button
                className={`py-2 px-4 ${
                  activeTab === 'map'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('map')}
              >
                {t('vessels.tabs.routeMap')}
              </button>
              <button
                className={`py-2 px-4 ${
                  activeTab === 'sensors'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('sensors')}
              >
                {t('vessels.tabs.sensors')}
              </button>
            </nav>
          </div>

          {activeTab === 'info' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Vessel Details */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">{t('vessels.vesselInformation')}</h4>

                  <div className="space-y-3 text-sm">
                    <div>
                      <span className="text-gray-500">{t('vessels.vesselNumber')}:</span>
                      <span className="ml-2 text-gray-900">{vessel.number}</span>
                    </div>

                    <div>
                      <span className="text-gray-500">{t('vessels.type')}:</span>
                      <span className="ml-2 text-gray-900">{vessel.type}</span>
                    </div>

                    <div>
                      <span className="text-gray-500">{t('vessels.location')}:</span>
                      <span className="ml-2 text-gray-900">
                        {typeof vessel.location === 'string'
                          ? vessel.location
                          : vessel.location
                            ? `${vessel.location.latitude.toFixed(4)}, ${vessel.location.longitude.toFixed(4)}`
                            : vessel.homePort || t('vessels.locationUnknown')}
                      </span>
                    </div>

                    <div>
                      <span className="text-gray-500">{t('common.status')}:</span>
                      <span className="ml-2">
                        <StatusBadge status={vessel.status} />
                      </span>
                    </div>

                    <div>
                      <span className="text-gray-500">{t('vessels.totalSensors')}:</span>
                      <span className="ml-2 text-gray-900 font-medium">{vessel.sensors}</span>
                    </div>

                    <div>
                      <span className="text-gray-500">{t('vessels.lastUpdated')}:</span>
                      <span className="ml-2 text-gray-900">{vessel.lastUpdated}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Vessel Sensors */}
              <div className="md:col-span-2">
                <h4 className="font-medium text-gray-900 mb-2">{t('vessels.sensorOverview')}</h4>
                {sensors.length > 0 ? (
                  <SensorChart 
                    sensorId={sensors[0].id} 
                    vesselName={vessel.name} 
                    location={sensors[0].location || 'Unknown Location'} 
                    timeframe="day"
                  />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    {t('vessels.noSensorsFound')}
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'map' && (
            <VesselMap vesselId={vessel.id} />
          )}

          {activeTab === 'sensors' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">{t('vessels.vesselSensors')}</h4>

              {/* Loading state */}
              {sensorsLoading && (
                <div className="text-center py-6">
                  <div className="animate-spin w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full mb-4 mx-auto"></div>
                  <p className="text-gray-600">{t('common.loading')}</p>
                </div>
              )}

              {/* Error state */}
              {sensorsError && (
                <div className="text-center py-6">
                  <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-4">
                    <p>{t('sensors.errorLoadingSensors')}</p>
                    <button
                      onClick={() => refetchSensors()}
                      className="mt-2 px-4 py-2 bg-red-100 text-red-800 rounded-md text-sm font-medium hover:bg-red-200 transition-colors"
                    >
                      {t('common.retry')}
                    </button>
                  </div>
                </div>
              )}

              {/* Data display */}
              {!sensorsLoading && !sensorsError && (
                <>
                  {sensors.length === 0 ? (
                    <div className="text-center py-6">
                      <p className="text-gray-600">{t('sensors.noSensorsForVessel')}</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('sensors.name')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('sensors.type')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('sensors.location')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('common.status')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('sensors.lastReading')}
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {sensors.map((sensor) => (
                            <tr key={sensor.id}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">{sensor.name}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500">{sensor.type}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500">{sensor.location}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <StatusBadge status={sensor.status} />
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {sensor.lastReading}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </>
              )}
            </div>
          )}

          <div className="mt-4 pt-4 border-t border-gray-200 flex justify-between">
            <button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
              onClick={onClose}
            >
              {t('common.close')}
            </button>
            <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md">
              {t('vessels.manageSensors')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VesselDetailModal;