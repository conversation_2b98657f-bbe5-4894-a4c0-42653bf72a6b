import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Customer } from '../../../types';
import { useCustomer, useCustomerSubscriptions, useCustomerVessels } from '../../../hooks/queries/useCustomerQueries';
import { StatusBadge, LoadingOverlay, ErrorNotification } from '../../common';

interface CustomerDetailModalProps {
  customer: Customer;
  onClose: () => void;
  isOpen: boolean;
}

const CustomerDetailModal: React.FC<CustomerDetailModalProps> = ({ customer, onClose, isOpen }) => {
  const { t } = useTranslation();
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false);

  // Fetch customer details for real-time data
  const {
    data: customerDetails,
    isLoading: isLoadingDetails,
    isError: isErrorDetails,
    error: detailsError,
    refetch: refetchDetails
  } = useCustomer(customer.id, {
    initialData: customer, // Use the passed customer as initial data
    useMockFallback: true
  });

  // Fetch customer subscriptions
  const {
    data: subscriptions = [],
    isLoading: isLoadingSubscriptions
  } = useCustomerSubscriptions(customer.id, {
    enabled: isOpen, // Only fetch when modal is open
    useMockFallback: true
  });

  // Fetch customer vessels
  const {
    data: vessels = [],
    isLoading: isLoadingVessels
  } = useCustomerVessels(customer.id, {
    enabled: isOpen, // Only fetch when modal is open
    useMockFallback: true
  });

  // Use the loaded details or the initial customer prop as fallback
  const customerData = customerDetails || customer;

  // Loading state
  const isLoading = isLoadingDetails || isLoadingSubscriptions || isLoadingVessels;

  // Resize handler function
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);

    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;

      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);

      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };

    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }
  return (
    <>
      {/* Customer Detail Slideover */}
      <div
        className={`fixed inset-y-0 right-0 bg-white dark:bg-gray-800 shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {t('common.dragToResize')}
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full overflow-y-auto p-6">
          {/* Loading overlay */}
          {isLoading && <LoadingOverlay />}

          {/* Error notification */}
          {isErrorDetails && (
            <ErrorNotification
              message={t('common.errorFetchingCustomerDetails')}
              onRetry={refetchDetails}
              error={detailsError}
            />
          )}

          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">{t('customers.customerDetails')}</h3>
            <button
              className="text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300"
              onClick={onClose}
              disabled={isLoading}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('customers.companyInformation')}</h4>
                <div className="mt-1">
                  <p className="text-lg font-medium text-gray-800 dark:text-white">{customerData.name}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    <span className="inline-block mr-2">
                      <StatusBadge status={customerData.status} />
                    </span>
                    {t('common.lastActive')}: {customerData.lastActive}
                  </p>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('customers.primaryContact')}</h4>
                <div className="mt-1">
                  <p className="font-medium text-gray-800 dark:text-white">{customerData.contactPerson}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">{customerData.email}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">{customerData.phone}</p>
                </div>
              </div>
            </div>

            <div>
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('customers.subscriptionOverview')}</h4>
                <div className="mt-1 grid grid-cols-3 gap-2">
                  <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg text-center">
                    <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{customerData.companies}</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">{t('common.companies')}</p>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg text-center">
                    <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {isLoadingVessels ? (
                        <span className="text-sm">
                          <svg className="animate-spin h-4 w-4 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </span>
                      ) : (
                        vessels.length || customerData.vessels
                      )}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">{t('common.vessels')}</p>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg text-center">
                    <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{customerData.sensors}</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">{t('common.sensors')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-between">
              <div>
                <button
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md mr-3"
                  disabled={isLoading}
                  onClick={() => window.location.href = `/customers/${customerData.id}/edit`}
                >
                  {t('customers.editCustomer')}
                </button>
                <button
                  className="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md flex items-center"
                  disabled={isLoading || isLoadingVessels}
                  onClick={() => window.location.href = `/customers/${customerData.id}/vessels`}
                >
                  {isLoadingVessels && (
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                  {t('customers.viewVessels')}
                  {vessels.length > 0 && ` (${vessels.length})`}
                </button>
              </div>
              <button
                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                onClick={onClose}
                disabled={isLoading}
              >
                {t('common.close')}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={onClose}
        ></div>
      )}
    </>
  );
};

export default CustomerDetailModal;