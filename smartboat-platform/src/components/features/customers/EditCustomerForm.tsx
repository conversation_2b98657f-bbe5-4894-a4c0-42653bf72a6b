import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Customer } from '../../../types';
import { useCreateCustomer, useUpdateCustomer } from '../../../hooks/queries/useCustomerQueries';
import { ErrorNotification } from '../../common';

interface EditCustomerFormProps {
  customer: Customer | null; // The customer to edit, null if creating a new one
  onSave: (customer: Customer) => void;
  onCancel: () => void;
  isLoading?: boolean; // Optional prop to indicate loading state
}

const EditCustomerForm: React.FC<EditCustomerFormProps> = ({
  customer,
  onSave,
  onCancel,
  isLoading = false
}) => {
  const { t } = useTranslation();

  // API mutations
  const createCustomer = useCreateCustomer();
  const updateCustomer = useUpdateCustomer();

  // Form state
  const [formData, setFormData] = useState<Partial<Customer>>({
    name: '',
    contactPerson: '',
    email: '',
    phone: '',
    status: 'Active',
  });

  // Error state
  const [formError, setFormError] = useState<string | null>(null);

  // Active section state
  const [activeSection, setActiveSection] = useState('basic');

  // Initialize form data when customer changes
  useEffect(() => {
    if (customer) {
      setFormData({
        id: customer.id,
        name: customer.name,
        contactPerson: customer.contactPerson,
        email: customer.email,
        phone: customer.phone,
        status: customer.status,
        // Keep the following fields as they are (not editable directly)
        companies: customer.companies,
        vessels: customer.vessels,
        sensors: customer.sensors,
        lastActive: customer.lastActive
      });
    } else {
      // Reset form if not editing (creating new)
      setFormData({
        name: '',
        contactPerson: '',
        email: '',
        phone: '',
        status: 'Active',
      });
    }
  }, [customer]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Parse backend error messages
  const parseBackendError = (error: any): string => {
    try {
      // Check if it's a known error code from the logs
      const errorMessage = error?.message || error?.toString() || '';
      
      // Check for duplicate email error (DP-409) - specific case for email conflicts
      if (errorMessage.includes('DP-409') && errorMessage.includes('Duplicate email')) {
        return t('customers.errors.duplicateEmail');
      }
      
      // Check for duplicate name error (DP-409)
      if (errorMessage.includes('DP-409') || errorMessage.includes('Duplicate name')) {
        return t('customers.errors.duplicateName');
      }
      
      // Check for technical error (DP-500)
      if (errorMessage.includes('DP-500') || errorMessage.includes('Technical Error')) {
        return t('customers.errors.technicalError');
      }
      
      // Check for conflict error
      if (errorMessage.includes('Conflict') || error?.status === 409) {
        return t('customers.errors.nameAlreadyExists');
      }
      
      // Check for validation errors
      if (error?.status === 400) {
        return t('customers.errors.validationFailed');
      }
      
      // Default error message
      return t('customers.errors.operationFailed');
    } catch {
      return t('customers.errors.operationFailed');
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate form data
    if (!formData.name || !formData.contactPerson || !formData.email || !formData.phone) {
      setFormError(t('customers.fillAllRequired'));
      return;
    }

    // Additional validation for name uniqueness (client-side check)
    if (!formData.name.trim()) {
      setFormError(t('customers.errors.nameRequired'));
      return;
    }

    if (formData.name.trim().length < 2) {
      setFormError(t('customers.errors.nameTooShort'));
      return;
    }

    // Create a complete customer object by merging defaults for any missing fields
    const completeCustomer: Customer = {
      id: formData.id || Math.floor(Math.random() * 1000), // Generate random ID for new customers
      name: formData.name || '',
      contactPerson: formData.contactPerson || '',
      email: formData.email || '',
      phone: formData.phone || '',
      status: formData.status || 'Active',
      companies: formData.companies || 0,
      vessels: formData.vessels || 0,
      sensors: formData.sensors || 0,
      lastActive: formData.lastActive || 'Today',
    };

    // If editing an existing customer
    if (customer) {
      updateCustomer.mutate({ id: completeCustomer.id, customerData: completeCustomer }, {
        onSuccess: (updatedCustomer) => {
          onSave(updatedCustomer);
        },
        onError: (error) => {
          console.error('Failed to update customer:', error);
          const errorMessage = parseBackendError(error);
          setFormError(errorMessage);
        }
      });
    }
    // If creating a new customer
    else {
      createCustomer.mutate(completeCustomer, {
        onSuccess: (newCustomer) => {
          onSave(newCustomer);
        },
        onError: (error) => {
          console.error('Failed to create customer:', error);
          const errorMessage = parseBackendError(error);
          setFormError(errorMessage);
        }
      });
    }
  };

  // Block types for the form sections
  const blockTypes = [
    { id: 'basic', label: t('customers.basicInformation'), icon: '/assets/icons/user.svg', color: 'bg-blue-500' },
    { id: 'contact', label: t('customers.contactDetails'), icon: '/assets/icons/mail.svg', color: 'bg-green-500' },
    { id: 'stats', label: t('common.statistics'), icon: '/assets/icons/chart.svg', color: 'bg-purple-500' },
  ];

  // Function to render block icon
  const renderBlockIcon = (blockType: string) => {
    switch (blockType) {
      case 'basic':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      case 'contact':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      case 'stats':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Function to get block color
  const getBlockColor = (blockType: string) => {
    const block = blockTypes.find(b => b.id === blockType);
    return block ? block.color : 'bg-gray-500';
  };

  // Determine if form is in loading state from props or mutations
  const isFormLoading = isLoading || createCustomer.isPending || updateCustomer.isPending;

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden flex flex-col h-full">
      {/* Display error notification if there's an error */}
      {formError && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{formError}</p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  type="button"
                  onClick={() => setFormError(null)}
                  className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <span className="sr-only">{t('common.dismiss')}</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
        {/* Left sidebar - Block selection */}
        <div className="w-full md:w-56 bg-gray-900 border-r border-gray-700 p-3 flex flex-col">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-white">
              {customer ? t('customers.editCustomer') : t('customers.createNewCustomer')}
            </h2>
            <p className="text-xs text-gray-400 mt-1">
              {customer ? t('customers.updateCustomerInfo') : t('customers.addNewCustomerInfo')}
            </p>
          </div>

          <div className="mb-4">
            <h3 className="text-xs uppercase tracking-wider text-gray-400 font-semibold mb-2">{t('customers.blocks')}</h3>
            <div className="space-y-2">
              {blockTypes.map(block => (
                <button
                  key={block.id}
                  onClick={() => setActiveSection(block.id)}
                  className={`w-full flex items-center p-2 rounded-md transition-colors ${
                    activeSection === block.id
                      ? block.color
                      : 'bg-gray-800 hover:bg-gray-700'
                  } text-white`}
                >
                  <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                    activeSection === block.id ? 'bg-white bg-opacity-20' : block.color
                  } mr-2`}>
                    {renderBlockIcon(block.id)}
                  </div>
                  <span className="text-sm font-medium">{block.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex flex-col bg-gray-900 overflow-hidden">
          <div className="flex-1 overflow-y-auto p-6">
            <form id="customer-form" onSubmit={handleSubmit} className="max-w-3xl mx-auto">
            <div className="mb-4 flex items-center justify-center">
              <h3 className="text-base font-medium text-white">
                {activeSection === 'basic' ? t('customers.basicInformation') :
                 activeSection === 'contact' ? t('customers.contactDetails') : t('common.statistics')}
              </h3>
              <div className={`ml-2 w-2 h-2 rounded-full ${getBlockColor(activeSection)}`}></div>
            </div>

            {/* Basic Information Section */}
            {activeSection === 'basic' && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('basic')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('basic')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('customers.basicInformation')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Customer Name */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('customers.customerName')}*
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>

                    {/* Status */}
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.status')}
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={formData.status || 'Active'}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="Active">{t('common.active')}</option>
                        <option value="Inactive">{t('common.inactive')}</option>
                        <option value="Pending">{t('common.pending')}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Contact Details Section */}
            {activeSection === 'contact' && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('contact')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('contact')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('customers.contactDetails')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 gap-6">
                    {/* Contact Person */}
                    <div>
                      <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.contactPerson')}*
                      </label>
                      <input
                        type="text"
                        id="contactPerson"
                        name="contactPerson"
                        value={formData.contactPerson || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>

                    {/* Email */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.emailAddress')}*
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>

                    {/* Phone */}
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.phoneNumber')}*
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Statistics Section */}
            {activeSection === 'stats' && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('stats')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('stats')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('common.statistics')}</h4>
                </div>
                <div className="p-5">
                  {customer ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          {t('common.companies')}
                        </label>
                        <input
                          type="text"
                          value={formData.companies || 0}
                          className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                          disabled
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          {t('common.vessels')}
                        </label>
                        <input
                          type="text"
                          value={formData.vessels || 0}
                          className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                          disabled
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          {t('common.sensors')}
                        </label>
                        <input
                          type="text"
                          value={formData.sensors || 0}
                          className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                          disabled
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          {t('common.lastActive')}
                        </label>
                        <input
                          type="text"
                          value={formData.lastActive || 'Today'}
                          className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                          disabled
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <p className="text-gray-400 text-sm">{t('customers.statUnavailable')}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </form>
          </div>

          {/* Fixed footer with buttons */}
          <div className="border-t border-gray-700 p-3 bg-gray-900">
            <div className="flex flex-row space-x-3 justify-end max-w-3xl mx-auto">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700"
                disabled={isFormLoading}
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                form="customer-form"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 flex items-center"
                disabled={isFormLoading}
              >
                {isFormLoading && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {customer ? t('customers.updateCustomer') : t('customers.createCustomer')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditCustomerForm;