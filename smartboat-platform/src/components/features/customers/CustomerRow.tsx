import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Customer } from '../../../types';
import { StatusBadge } from '../../common';
import { useAuth } from '../../../context/AuthContext';
import { useDeleteCustomer } from '../../../hooks/queries/useCustomerQueries';
import { formatDate } from '../../../utils/dateUtils';

interface CustomerRowProps {
  customer: Customer;
  refetch?: () => void;
}

const CustomerRow: React.FC<CustomerRowProps> = ({ customer, refetch }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const { currentUser } = useAuth();
  const deleteCustomer = useDeleteCustomer();

  // Check if the user has admin permissions
  const isAdmin = currentUser?.role === 'Administrator';

  const handleViewCustomer = () => {
    navigate(`/customers/${customer.id}`);
  };

  const handleDeleteCustomer = async () => {
    try {
      await deleteCustomer.mutateAsync(customer.id);
      setShowDeleteConfirm(false);
      // Manually trigger refetch to ensure the list refreshes
      if (refetch) {
        refetch();
      }
    } catch (error) {
      console.error('Failed to delete customer:', error);
      alert(t('customers.deleteFailed'));
    }
  };

  return (
    <tr className="hover:bg-gray-50 dark:hover:bg-gray-800">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 font-bold">
            {customer.name.charAt(0)}
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 dark:text-white">{customer.name}</div>
            <div className="text-sm text-gray-500 dark:text-gray-400">{t('common.lastActive')}: {formatDate(customer.lastActive)}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900 dark:text-white">{customer.contactPerson}</div>
        <div className="text-sm text-gray-500 dark:text-gray-400">{customer.email}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <StatusBadge status={customer.status} />
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <button
          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-2"
          onClick={handleViewCustomer}
        >
          {t('common.view')}
        </button>
        {isAdmin && (
          <button
            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
            onClick={() => setShowDeleteConfirm(true)}
          >
            {t('common.delete')}
          </button>
        )}
      </td>
      
      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" onClick={() => setShowDeleteConfirm(false)}></div>
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 z-50 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t('customers.confirmDelete')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {t('customers.deleteWarning', { customerName: customer.name })}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleteCustomer.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
                onClick={handleDeleteCustomer}
                disabled={deleteCustomer.isPending}
              >
                {deleteCustomer.isPending ? t('common.deleting') : t('common.delete')}
              </button>
            </div>
          </div>
        </>
      )}
    </tr>
  );
};

export default CustomerRow;