import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: ReactNode;
  iconBgColor: string;
  iconTextColor: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  iconBgColor,
  iconTextColor
}) => {
  const { t } = useTranslation();

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md p-4 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 transition-colors duration-200">
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${iconBgColor} dark:bg-opacity-20 ${iconTextColor}`}>
          {icon}
        </div>
        <div className="ml-4">
          <p className="text-gray-500 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-gray-800 dark:text-white">{value}</p>
        </div>
      </div>
    </div>
  );
};

export default StatCard;