import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNotifications } from '../../../hooks/queries/useNotificationQueries';
import { formatTimestamp } from '../../../utils/dateUtils';

const RecentActivity: React.FC = () => {
  const { t } = useTranslation();

  // Fetch recent notifications for the current user
  const {
    data: recentNotifications = [],
    isLoading: notificationsLoading
  } = useNotifications({
    pageLimit: 3, // Show only recent 3 notifications for dashboard
    useMockFallback: true
  });

  // Helper function to get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'error':
        return (
          <div className="flex-shrink-0 bg-red-100 dark:bg-red-900 rounded-full p-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 dark:text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'warning':
        return (
          <div className="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-full p-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500 dark:text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'success':
        return (
          <div className="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-full p-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 dark:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
            </svg>
          </div>
        );
    }
  };


  return (
    <div className="mt-6 bg-white dark:bg-gray-900 rounded-lg shadow-md p-4 border-2 border-gray-300 dark:border-gray-600">
      <h2 className="text-xl font-medium text-gray-700 dark:text-white mb-4 pb-2 border-b-2 border-gray-300 dark:border-gray-600">{t('dashboard.recentActivity')}</h2>
      
      {notificationsLoading ? (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-start border-b border-gray-300 dark:border-gray-600 pb-4 animate-pulse">
              <div className="w-9 h-9 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
              <div className="ml-3 flex-1">
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/4"></div>
              </div>
            </div>
          ))}
        </div>
      ) : recentNotifications.length > 0 ? (
        <div className="space-y-4">
          {recentNotifications.map((notification: any, index: number) => (
            <div 
              key={notification.id} 
              className={`flex items-start hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded transition-colors duration-150 ${
                index < recentNotifications.length - 1 ? 'border-b border-gray-300 dark:border-gray-600 pb-4' : ''
              }`}
            >
              {getNotificationIcon(notification.type)}
              <div className="ml-3 flex-1">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {notification.title && (
                    <span className="font-medium">{notification.title}</span>
                  )}
                  {notification.message && notification.title && ' - '}
                  {notification.message}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {formatTimestamp(notification.timestamp)}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="flex-shrink-0 bg-gray-100 dark:bg-gray-700 rounded-full p-3 mx-auto mb-4 w-fit">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
            </svg>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">{t('notifications.noNotifications')}</p>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">{t('notifications.noNotificationsMessage')}</p>
        </div>
      )}
    </div>
  );
};

export default RecentActivity;