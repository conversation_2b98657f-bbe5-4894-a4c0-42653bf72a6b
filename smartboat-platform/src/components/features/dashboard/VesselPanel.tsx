import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { StatusBadge } from '../../common';
import { useAuth } from '../../../context/AuthContext';
import { useVessels, useCompanies } from '../../../hooks';
import { Vessel } from '../../../types';
import { useTranslation } from 'react-i18next';
import { formatDateRange } from '../../../utils/dateUtils';

const VesselPanel: React.FC = () => {
  const { t } = useTranslation();
  const [filteredVessels, setFilteredVessels] = useState<Vessel[]>([]);
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  
  // Fetch vessels and companies from API instead of using mock data
  const { data: vessels = [] } = useVessels();
  const { data: companies = [] } = useCompanies();

  useEffect(() => {
    // Filter vessels based on user role
    if (currentUser && Array.isArray(vessels) && Array.isArray(companies)) {
      if (currentUser.role === 'Administrator' || currentUser.role === 'Manager') {
        // Admins and managers see all vessels
        setFilteredVessels(vessels);
      } else if (currentUser.role === 'Customer' && currentUser.customerId) {
        // Customers only see vessels from their companies
        // First, get the companies that belong to this customer
        const customerCompanies = companies.filter(
          company => company.customerId === currentUser.customerId
        );

        // Then filter vessels that belong to those companies
        const companyIds = customerCompanies.map(company => company.id);
        const customerVessels = vessels.filter(
          vessel => companyIds.includes(vessel.companyId)
        );

        setFilteredVessels(customerVessels);
      } else {
        // Other roles or missing customerId - show nothing
        setFilteredVessels([]);
      }
    } else {
      // No user - show all for development
      setFilteredVessels(vessels);
    }
  }, [currentUser, vessels, companies]);

  const handleVesselClick = (vesselId: number) => {
    navigate(`/vessels/${vesselId}`);
  };

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md border border-gray-300 dark:border-gray-600">
      <div className="p-4 border-b-2 border-gray-300 dark:border-gray-600 flex justify-between items-center">
        <h2 className="text-xl font-medium text-gray-700 dark:text-white">{t('common.vessels')}</h2>
        <div className="flex items-center"></div>
      </div>

      <div className="overflow-y-auto max-h-96">
        {/* Vessel list */}
        {filteredVessels.map((vessel) => (
          <div
            key={vessel.id}
            className="border-b border-gray-300 dark:border-gray-600 border-l-4 border-l-blue-500 cursor-pointer hover:bg-blue-50 dark:hover:bg-gray-700 group transition-colors duration-150"
            onClick={() => handleVesselClick(vessel.id)}
          >
            <div className="p-4 flex">
              <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded overflow-hidden mr-4">
                {/* Vessel image placeholder */}
                <div className="w-full h-full flex items-center justify-center bg-gray-300 dark:bg-gray-600 group-hover:bg-gray-400 dark:group-hover:bg-gray-500">
                  <span className="text-gray-600 dark:text-gray-300 text-xs">{t('common.name')}</span>
                </div>
              </div>

              <div className="flex-1">
                <div className="flex justify-between">
                  <h3 className="font-medium text-blue-600 dark:text-white group-hover:text-blue-700 dark:group-hover:text-blue-300">
                    {vessel.number}. {vessel.name}
                  </h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {vessel.type} - {
                    typeof vessel.location === 'string'
                      ? vessel.location
                      : vessel.location
                        ? `${vessel.location.latitude.toFixed(4)}, ${vessel.location.longitude.toFixed(4)}`
                        : vessel.homePort || t('vessels.locationUnknown')
                  }
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{formatDateRange(vessel.startDate, vessel.endDate)}</p>
              </div>

              <div className="text-right">
                <div className="flex items-center justify-end mb-1">
                  <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">
                    <StatusBadge status={vessel.status} />
                  </span>
                </div>
                <div className="flex items-center justify-end">
                  <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">{vessel.sensors} {t('common.sensors')}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VesselPanel;
