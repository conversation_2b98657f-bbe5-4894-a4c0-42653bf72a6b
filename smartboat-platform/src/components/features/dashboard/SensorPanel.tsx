import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSensors, useVessels, useCompanies } from '../../../hooks';
import { StatusBadge, SensorChart } from '../../common';
import { useAuth } from '../../../context/AuthContext';
import { Sensor } from '../../../types';
import { useTranslation } from 'react-i18next';
import { formatDate } from '../../../utils/dateUtils';

const SensorPanel: React.FC = () => {
  const { t } = useTranslation();
  const [filteredSensors, setFilteredSensors] = useState<Sensor[]>([]);
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  // Fetch sensors, vessels, and companies from API instead of using mock data
  const { data: sensors = [] } = useSensors();
  const { data: vessels = [] } = useVessels();
  const { data: companies = [] } = useCompanies();

  const memoizedSensors = useMemo(() => sensors, [sensors]);
  const memoizedVessels = useMemo(() => vessels, [vessels]);
  const memoizedCompanies = useMemo(() => companies, [companies]);

  useEffect(() => {
    // Debug logging to understand data structure
    if (memoizedSensors.length > 0 && memoizedVessels.length > 0) {
      console.log('Sample sensor data:', memoizedSensors[0]);
      console.log('Sample vessel data:', memoizedVessels[0]);
      console.log('Sensor vesselId type:', typeof memoizedSensors[0].vesselId, 'value:', memoizedSensors[0].vesselId);
      console.log('Vessel id type:', typeof memoizedVessels[0].id, 'value:', memoizedVessels[0].id);
    }

    // Filter sensors based on user role
    if (currentUser && Array.isArray(memoizedSensors) && Array.isArray(memoizedVessels) && Array.isArray(memoizedCompanies)) {
      if (currentUser.role === 'Administrator' || currentUser.role === 'Manager') {
        // Admins and managers see all sensors
        setFilteredSensors(memoizedSensors);
      } else if (currentUser.role === 'Customer' && currentUser.customerId) {
        // Customers only see sensors from their vessels
        // First, get the companies that belong to this customer
        const customerCompanies = memoizedCompanies.filter(
          company => company.customerId === currentUser.customerId
        );

        // Then get vessels that belong to those companies
        const companyIds = customerCompanies.map(company => company.id);
        const customerVessels = memoizedVessels.filter(
          vessel => companyIds.includes(vessel.companyId)
        );

        // Finally, filter sensors that belong to those vessels
        const vesselIds = customerVessels.map(vessel => vessel.id.toString());
        const customerSensors = memoizedSensors.filter(
          sensor => sensor.vesselId && vesselIds.includes(sensor.vesselId)
        );

        setFilteredSensors(customerSensors);
      } else {
        // Other roles or missing customerId - show nothing
        setFilteredSensors([]);
      }
    } else {
      // No user - show all for development
      setFilteredSensors(memoizedSensors);
    }
  }, [currentUser, memoizedSensors, memoizedVessels, memoizedCompanies]);

  const handleSensorClick = (sensorId: number) => {
    navigate(`/sensors/${sensorId}`);
  };

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md border border-gray-300 dark:border-gray-600">
      <div className="p-4 border-b-2 border-gray-300 dark:border-gray-600 flex justify-between items-center">
        <h2 className="text-xl font-medium text-gray-700 dark:text-white">{t('common.sensors')}</h2>
        <div className="flex items-center"></div>
      </div>

      <div className="overflow-y-auto max-h-96">
        {/* Sensor list */}
        {Array.isArray(filteredSensors) && filteredSensors.map((sensor) => (
          <div
            key={sensor.id}
            className="border-b border-gray-300 dark:border-gray-600 p-4 cursor-pointer hover:bg-blue-50 dark:hover:bg-gray-700 group transition-colors duration-150"
            onClick={() => handleSensorClick(sensor.id)}
          >
            <div className="flex">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full overflow-hidden mr-4 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {sensor.type === 'Temperature' ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2V9a2 2 0 012-2h6a2 2 0 012 2v2a2 2 0 01-2 2v6a4 4 0 11-8 0zm8-6V9a2 2 0 012-2h.093A2 2 0 0121 9v2a2 2 0 01-2 2h-.093a2 2 0 01-2-2z" />
                  ) : sensor.type === 'Pressure' ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  ) : sensor.type === 'Humidity' ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  )}
                </svg>
              </div>

              <div className="flex-1">
                <div className="flex justify-between">
                  <div>
                    <h3 className="font-medium text-gray-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-300">{sensor.name}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{sensor.type}</p>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">{formatDate(sensor.lastUpdated)}</span>
                </div>

                <div className="mt-2 flex justify-between items-center">
                  <StatusBadge status={sensor.status} />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {memoizedVessels.find(v =>
                      v.id.toString() === sensor.vesselId?.toString() ||
                      v.id === sensor.vesselId ||
                      v.Id?.toString() === sensor.vesselId?.toString() ||
                      v.Id === sensor.vesselId
                    )?.name || `Unknown Vessel (ID: ${sensor.vesselId})`} - {sensor.location || 'Unknown Location'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

    </div>
  );
};

export default SensorPanel;