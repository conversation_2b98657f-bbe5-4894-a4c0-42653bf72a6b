import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Company } from '../../../types';
import { StatusBadge } from '../../common';
import { useAuth } from '../../../context/AuthContext';
import { useUpdateCompany, useDeleteCompany } from '../../../hooks/queries/useCompanyQueries';
import { formatDate } from '../../../utils/dateUtils';

interface CompanyRowProps {
  company: Company;
  refetch?: () => void;
}

const CompanyRow: React.FC<CompanyRowProps> = ({ company, refetch }) => {
  // Get the current user's role
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const deleteCompany = useDeleteCompany();

  // Extract the first letter of the company name for the avatar
  const firstLetter = company.name.charAt(0);

  // Check if the user has admin permissions
  const isAdmin = currentUser?.role === 'Administrator';

  const handleViewCompany = () => {
    navigate(`/companies/${company.id}`);
  };

  const handleEditCompany = () => {
    navigate(`/companies/${company.id}`, { state: { editMode: true } });
  };

  const handleDeleteCompany = async () => {
    try {
      await deleteCompany.mutateAsync(company.id);
      setShowDeleteConfirm(false);
      // Refresh the list after successful deletion
      if (refetch) {
        refetch();
      }
    } catch (error) {
      console.error('Failed to delete company:', error);
      alert(t('companies.deleteFailed'));
    }
  };

  return (
    <>
      <tr className="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td className="px-6 py-4">
          <div className="flex items-center">
            <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 font-bold">
              {firstLetter}
            </div>
            <div className="ml-4 min-w-0 max-w-[200px]">
              <div className="text-sm font-medium text-gray-900 dark:text-white truncate">{company.name}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{t('vessels.lastUpdated')}: {company.lastUpdated ? formatDate(company.lastUpdated) : '-'}</div>
            </div>
          </div>
        </td>
        <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 truncate">
          {company.location}
        </td>
        <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 truncate">
          {company.industry}
        </td>
        <td className="px-6 py-4">
          <StatusBadge status={company.status} />
        </td>
        <td className="px-6 py-4 text-right text-sm font-medium">
          <button
            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-2"
            onClick={handleViewCompany}
          >
            {t('common.view')}
          </button>
          {isAdmin && (
            <button
              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
              onClick={() => setShowDeleteConfirm(true)}
            >
              {t('common.delete')}
            </button>
          )}
        </td>
      </tr>

      {/* Delete Confirmation Modal - Rendered using Portal to avoid HTML structure issues */}
      {showDeleteConfirm && createPortal(
        <>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" onClick={() => setShowDeleteConfirm(false)}></div>
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 z-50 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t('companies.confirmDelete')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {t('companies.deleteWarning', { companyName: company.name })}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleteCompany.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
                onClick={handleDeleteCompany}
                disabled={deleteCompany.isPending}
              >
                {deleteCompany.isPending ? t('common.deleting') : t('common.delete')}
              </button>
            </div>
          </div>
        </>,
        document.body
      )}
    </>
  );
};

export default CompanyRow;
