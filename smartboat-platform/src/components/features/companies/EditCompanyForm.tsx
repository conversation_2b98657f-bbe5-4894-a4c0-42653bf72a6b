import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Company } from '../../../types';
import { useCreateCompany, useUpdateCompany } from '../../../hooks/queries/useCompanyQueries';
import { useCustomers } from '../../../hooks/queries/useCustomerQueries';

interface EditCompanyFormProps {
  company: Company | null; // The company to edit, null if creating a new one
  onSave: (company: Company) => void;
  onCancel: () => void;
}

const EditCompanyForm: React.FC<EditCompanyFormProps> = ({
  company,
  onSave,
  onCancel
}) => {
  const { t } = useTranslation();
  const createCompany = useCreateCompany();
  const updateCompany = useUpdateCompany();
  
  // Fetch customers for dropdown
  const { data: customers = [], isLoading: customersLoading } = useCustomers();

  // Form state
  const [formData, setFormData] = useState<Partial<Company>>({
    name: '',
    location: '',
    industry: 'Shipping',
    status: 'Active',
    customerId: '',
  });

  // Tracking submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Active section state
  const [activeSection, setActiveSection] = useState('basic');

  // Initialize form data when company changes
  useEffect(() => {
    if (company) {
      setFormData({
        id: company.id,
        name: company.name,
        location: company.location,
        industry: company.industry,
        status: company.status,
        customerId: company.customerId,
        lastUpdated: company.lastUpdated,
        created: company.created,
        changed: company.changed
      });
    } else {
      // Reset form if not editing (creating new)
      setFormData({
        name: '',
        location: '',
        industry: 'Shipping',
        status: 'Active',
        customerId: customers.length > 0 ? customers[0].id : '',
      });
    }
  }, [company, customers]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate form data - name and customerId are required
    if (!formData.name) {
      alert(t('companies.nameRequired'));
      setIsSubmitting(false);
      return;
    }

    if (!formData.customerId) {
      alert(t('companies.customerRequired'));
      setIsSubmitting(false);
      return;
    }

    // Create a complete company object by merging defaults for any missing fields
    const completeCompany: Company = {
      id: formData.id || '', // API will assign real IDs
      name: formData.name || '',
      location: formData.location || '',
      industry: formData.industry || '',
      status: formData.status || 'Active',
      customerId: formData.customerId || '', // Should be provided by parent component
      lastUpdated: formData.lastUpdated,
      created: formData.created,
      changed: formData.changed,
    };

    // If editing an existing company
    if (company) {
      updateCompany.mutate({
        id: completeCompany.id,
        companyData: completeCompany
      }, {
        onSuccess: () => {
          // Call parent handler and close form
          onSave(completeCompany);
          setIsSubmitting(false);
        },
        onError: (error) => {
          console.error('Failed to update company:', error);
          setIsSubmitting(false);
          // You could add error handling here
        }
      });
    } else {
      // Creating a new company
      createCompany.mutate(completeCompany, {
        onSuccess: () => {
          // Call parent handler and close form
          onSave(completeCompany);
          setIsSubmitting(false);
        },
        onError: (error) => {
          console.error('Failed to create company:', error);
          setIsSubmitting(false);
          // You could add error handling here
        }
      });
    }
  };

  // Block types for the form sections
  const blockTypes = [
    { id: 'basic', label: t('customers.basicInformation'), icon: '/assets/icons/building.svg', color: 'bg-blue-500' },
    { id: 'location', label: t('companies.locationDetails'), icon: '/assets/icons/map-pin.svg', color: 'bg-green-500' },
    { id: 'stats', label: t('common.statistics'), icon: '/assets/icons/chart.svg', color: 'bg-purple-500' },
  ];

  // Function to render block icon
  const renderBlockIcon = (blockType: string) => {
    switch (blockType) {
      case 'basic':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        );
      case 'location':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
      case 'stats':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Function to get block color
  const getBlockColor = (blockType: string) => {
    const block = blockTypes.find(b => b.id === blockType);
    return block ? block.color : 'bg-gray-500';
  };

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden flex flex-col h-full">
      <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
        {/* Left sidebar - Block selection */}
        <div className="w-full md:w-56 bg-gray-900 border-r border-gray-700 p-3 flex flex-col">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-white">
              {company ? t('companies.editCompany') : t('companies.createNewCompany')}
            </h2>
            <p className="text-xs text-gray-400 mt-1">
              {company ? t('companies.updateCompanyInfo') : t('companies.addNewCompanyInfo')}
            </p>
          </div>

          <div className="mb-4">
            <h3 className="text-xs uppercase tracking-wider text-gray-400 font-semibold mb-2">{t('customers.blocks')}</h3>
            <div className="space-y-2">
              {blockTypes.map(block => (
                <button
                  key={block.id}
                  onClick={() => setActiveSection(block.id)}
                  className={`w-full flex items-center p-2 rounded-md transition-colors ${
                    activeSection === block.id
                      ? block.color
                      : 'bg-gray-800 hover:bg-gray-700'
                  } text-white`}
                >
                  <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                    activeSection === block.id ? 'bg-white bg-opacity-20' : block.color
                  } mr-2`}>
                    {renderBlockIcon(block.id)}
                  </div>
                  <span className="text-sm font-medium">{block.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex flex-col bg-gray-900 overflow-hidden">
          <div className="flex-1 overflow-y-auto p-6">
            <form id="company-form" onSubmit={handleSubmit} className="max-w-3xl mx-auto">
            <div className="mb-4 flex items-center justify-center">
              <h3 className="text-base font-medium text-white">
                {activeSection === 'basic' ? t('customers.basicInformation') :
                 activeSection === 'location' ? t('companies.locationDetails') : t('common.statistics')}
              </h3>
              <div className={`ml-2 w-2 h-2 rounded-full ${getBlockColor(activeSection)}`}></div>
            </div>

            {/* Basic Information Section */}
            {activeSection === 'basic' && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('basic')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('basic')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('customers.basicInformation')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Company Name */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('companies.companyName')}*
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>

                    {/* Customer Selection */}
                    <div>
                      <label htmlFor="customerId" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('companies.assignToCustomer')}*
                      </label>
                      <select
                        id="customerId"
                        name="customerId"
                        value={formData.customerId || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                        disabled={customersLoading}
                      >
                        <option value="">{customersLoading ? t('common.loading') : t('companies.selectCustomer')}</option>
                        {customers.map((customer) => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name} - {customer.email || customer.companyName || 'No Email'}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Industry */}
                    <div>
                      <label htmlFor="industry" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('companies.industry')}
                      </label>
                      <select
                        id="industry"
                        name="industry"
                        value={formData.industry || 'Shipping'}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="Shipping">Shipping</option>
                        <option value="Cargo">Cargo</option>
                        <option value="Oil & Gas">Oil & Gas</option>
                        <option value="Logistics">Logistics</option>
                        <option value="Container Shipping">Container Shipping</option>
                      </select>
                    </div>

                    {/* Status */}
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.status')}
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={formData.status || 'Active'}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="Active">{t('common.active')}</option>
                        <option value="Inactive">{t('common.inactive')}</option>
                        <option value="Maintenance">Maintenance</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Location Details Section */}
            {activeSection === 'location' && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('location')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('location')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('companies.locationDetails')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 gap-6">
                    {/* Location */}
                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('companies.location')}
                      </label>
                      <input
                        type="text"
                        id="location"
                        name="location"
                        value={formData.location || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="City, Country"
                      />
                    </div>

                    {/* Map placeholder - in a real app this might be an interactive map */}
                    <div className="bg-gray-700 rounded-lg p-4 border border-gray-600 h-40 flex items-center justify-center">
                      <div className="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                        </svg>
                        <p className="text-gray-400 text-sm">Map view will be available in future updates</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Statistics Section */}
            {activeSection === 'stats' && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('stats')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('stats')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('common.statistics')}</h4>
                </div>
                <div className="p-5">
                  {company ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          {t('common.created')}
                        </label>
                        <input
                          type="text"
                          value={formData.created || 'N/A'}
                          className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                          disabled
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          {t('vessels.lastUpdated')}
                        </label>
                        <input
                          type="text"
                          value={formData.lastUpdated || 'N/A'}
                          className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                          disabled
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          {t('companies.assignedCustomer')}
                        </label>
                        <input
                          type="text"
                          value={
                            formData.customerId 
                              ? customers.find(c => c.id === formData.customerId)?.name || 'Unknown Customer'
                              : 'N/A'
                          }
                          className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                          disabled
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <p className="text-gray-400 text-sm">{t('companies.statUnavailable')}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </form>
          </div>

          {/* Fixed footer with buttons */}
          <div className="border-t border-gray-700 p-3 bg-gray-900">
            <div className="flex flex-row space-x-3 justify-end max-w-3xl mx-auto">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700"
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                form="company-form"
                disabled={isSubmitting || createCompany.isPending || updateCompany.isPending}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed flex items-center"
              >
                {(isSubmitting || createCompany.isPending || updateCompany.isPending) && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {company ? t('companies.updateCompany') : t('companies.createCompany')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditCompanyForm;