import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Owner, CreateOwnerRequest, UpdateOwnerRequest } from '../../../types/owner';
import { useCreateOwner, useUpdateOwner } from '../../../hooks/queries/useOwnerQueries';
import { useCompanies } from '../../../hooks/queries/useCompanyQueries';

interface EditOwnerFormProps {
  owner: Owner | null; // The owner to edit, null if creating a new one
  onSave: (owner: Owner) => void;
  onCancel: () => void;
}

const EditOwnerForm: React.FC<EditOwnerFormProps> = ({
  owner,
  onSave,
  onCancel
}) => {
  const { t } = useTranslation();
  const createOwner = useCreateOwner();
  const updateOwner = useUpdateOwner();
  
  // Fetch companies for dropdown
  const { data: companies = [], isLoading: companiesLoading } = useCompanies();

  // Form state
  const [formData, setFormData] = useState<Partial<Owner>>({
    name: '',
    email: '',
    phone: '',
    address: '',
    companyId: '',
    status: 'Active',
  });

  // Tracking submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Active section state
  const [activeSection, setActiveSection] = useState('basic');

  // Initialize form data when owner changes
  useEffect(() => {
    if (owner) {
      setFormData({
        id: owner.id,
        name: owner.name,
        email: owner.email,
        phone: owner.phone,
        address: owner.address,
        companyId: owner.companyId,
        status: owner.status,
        lastUpdated: owner.lastUpdated,
        created: owner.created,
        changed: owner.changed
      });
    } else {
      // Reset form if not editing (creating new)
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        companyId: companies.length > 0 ? companies[0].id : '',
        status: 'Active',
      });
    }
  }, [owner, companies]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate form data - name and companyId are required
    if (!formData.name?.trim()) {
      alert(t('owners.nameRequired'));
      setIsSubmitting(false);
      return;
    }

    if (!formData.companyId) {
      alert(t('owners.companyRequired'));
      setIsSubmitting(false);
      return;
    }

    // If editing an existing owner
    if (owner) {
      const updateRequest: UpdateOwnerRequest = {
        id: owner.id,
        name: formData.name!.trim(),
        email: formData.email?.trim() || undefined,
        phone: formData.phone?.trim() || undefined,
        address: formData.address?.trim() || undefined,
        companyId: formData.companyId!,
      };

      updateOwner.mutate({
        id: owner.id,
        ownerData: updateRequest,
        originalCompanyId: owner.companyId // Pass original company ID for cache invalidation
      }, {
        onSuccess: (updatedOwner) => {
          // Call parent handler and close form
          onSave(updatedOwner);
          setIsSubmitting(false);
        },
        onError: (error) => {
          console.error('Failed to update owner:', error);
          setIsSubmitting(false);
          alert(t('owners.updateFailed'));
        }
      });
    } else {
      // Creating a new owner
      const createRequest: CreateOwnerRequest = {
        name: formData.name!.trim(),
        email: formData.email?.trim() || undefined,
        phone: formData.phone?.trim() || undefined,
        address: formData.address?.trim() || undefined,
        companyId: formData.companyId!,
      };

      createOwner.mutate(createRequest, {
        onSuccess: (createdOwner) => {
          // Call parent handler and close form
          onSave(createdOwner);
          setIsSubmitting(false);
        },
        onError: (error) => {
          console.error('Failed to create owner:', error);
          setIsSubmitting(false);
          alert(t('owners.createFailed'));
        }
      });
    }
  };

  // Block types for the form sections
  const blockTypes = [
    { id: 'basic', label: t('owners.basicInformation'), icon: 'person', color: 'bg-green-500' },
    { id: 'contact', label: t('owners.contactDetails'), icon: 'contact', color: 'bg-blue-500' },
    { id: 'company', label: t('owners.companyAssignment'), icon: 'building', color: 'bg-purple-500' },
    { id: 'stats', label: t('common.statistics'), icon: 'stats', color: 'bg-orange-500' },
  ];

  // Function to render block icon
  const renderBlockIcon = (blockType: string) => {
    switch (blockType) {
      case 'basic':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      case 'contact':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      case 'company':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        );
      case 'stats':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Function to get block color
  const getBlockColor = (blockType: string) => {
    const block = blockTypes.find(b => b.id === blockType);
    return block ? block.color : 'bg-gray-500';
  };

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden flex flex-col h-full">
      <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
        {/* Left sidebar - Block selection */}
        <div className="w-full md:w-56 bg-gray-900 border-r border-gray-700 p-3 flex flex-col">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-white">
              {owner ? t('owners.editOwner') : t('owners.createNewOwner')}
            </h2>
            <p className="text-xs text-gray-400 mt-1">
              {owner ? t('owners.updateOwnerInfo') : t('owners.addNewOwnerInfo')}
            </p>
          </div>

          <div className="mb-4">
            <h3 className="text-xs uppercase tracking-wider text-gray-400 font-semibold mb-2">{t('customers.blocks')}</h3>
            <div className="space-y-2">
              {blockTypes.map(block => {
                // Only show stats section for existing owners
                if (block.id === 'stats' && !owner) return null;
                
                return (
                  <button
                    key={block.id}
                    onClick={() => setActiveSection(block.id)}
                    className={`w-full flex items-center p-2 rounded-md transition-colors ${
                      activeSection === block.id
                        ? block.color
                        : 'bg-gray-800 hover:bg-gray-700'
                    } text-white`}
                  >
                    <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                      activeSection === block.id ? 'bg-white bg-opacity-20' : block.color
                    } mr-2`}>
                      {renderBlockIcon(block.id)}
                    </div>
                    <span className="text-sm font-medium">{block.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex flex-col bg-gray-900 overflow-hidden">
          <div className="flex-1 overflow-y-auto p-6">
            <form id="owner-form" onSubmit={handleSubmit} className="max-w-3xl mx-auto">
            <div className="mb-4 flex items-center justify-center">
              <h3 className="text-base font-medium text-white">
                {activeSection === 'basic' ? t('owners.basicInformation') :
                 activeSection === 'contact' ? t('owners.contactDetails') :
                 activeSection === 'company' ? t('owners.companyAssignment') : t('common.statistics')}
              </h3>
              <div className={`ml-2 w-2 h-2 rounded-full ${getBlockColor(activeSection)}`}></div>
            </div>

            {/* Basic Information Section */}
            {activeSection === 'basic' && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('basic')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('basic')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('owners.basicInformation')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Owner Name */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('owners.ownerName')}*
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                        placeholder={t('owners.enterOwnerName')}
                        required
                      />
                    </div>

                    {/* Status */}
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.status')}
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={formData.status || 'Active'}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                      >
                        <option value="Active">{t('common.active')}</option>
                        <option value="Inactive">{t('common.inactive')}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Contact Details Section */}
            {activeSection === 'contact' && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('contact')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('contact')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('owners.contactDetails')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Email */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('owners.email')}
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder={t('owners.enterEmail')}
                      />
                    </div>

                    {/* Phone */}
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('owners.phone')}
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder={t('owners.enterPhone')}
                      />
                    </div>

                    {/* Address */}
                    <div className="md:col-span-2">
                      <label htmlFor="address" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('owners.address')}
                      </label>
                      <textarea
                        id="address"
                        name="address"
                        rows={3}
                        value={formData.address || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder={t('owners.enterAddress')}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Company Assignment Section */}
            {activeSection === 'company' && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('company')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('company')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('owners.companyAssignment')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 gap-6">
                    {/* Company Selection */}
                    <div>
                      <label htmlFor="companyId" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('owners.assignToCompany')}*
                      </label>
                      <select
                        id="companyId"
                        name="companyId"
                        value={formData.companyId || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                        required
                        disabled={companiesLoading}
                      >
                        <option value="">{companiesLoading ? t('common.loading') : t('owners.selectCompany')}</option>
                        {companies.map((company) => (
                          <option key={company.id} value={company.id}>
                            {company.name} - {company.location || 'No Location'}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Company info display */}
                    {formData.companyId && (
                      <div className="bg-gray-700 rounded-lg p-4 border border-gray-600">
                        <h5 className="text-sm font-medium text-gray-200 mb-2">{t('owners.selectedCompany')}</h5>
                        {(() => {
                          const selectedCompany = companies.find(c => c.id === formData.companyId);
                          return selectedCompany ? (
                            <div className="text-sm text-gray-300">
                              <p><strong>{t('common.name')}:</strong> {selectedCompany.name}</p>
                              <p><strong>{t('companies.location')}:</strong> {selectedCompany.location || 'N/A'}</p>
                              <p><strong>{t('companies.industry')}:</strong> {selectedCompany.industry || 'N/A'}</p>
                              <p><strong>{t('common.status')}:</strong> {selectedCompany.status}</p>
                            </div>
                          ) : null;
                        })()}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Statistics Section */}
            {activeSection === 'stats' && owner && (
              <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('stats')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('stats')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('common.statistics')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.created')}
                      </label>
                      <input
                        type="text"
                        value={formData.created || 'N/A'}
                        className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                        disabled
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        {t('vessels.lastUpdated')}
                      </label>
                      <input
                        type="text"
                        value={formData.lastUpdated || 'N/A'}
                        className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                        disabled
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        {t('owners.assignedCompany')}
                      </label>
                      <input
                        type="text"
                        value={
                          formData.companyId 
                            ? companies.find(c => c.id === formData.companyId)?.name || 'Unknown Company'
                            : 'N/A'
                        }
                        className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                        disabled
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </form>
          </div>

          {/* Fixed footer with buttons */}
          <div className="border-t border-gray-700 p-3 bg-gray-900">
            <div className="flex flex-row space-x-3 justify-end max-w-3xl mx-auto">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700"
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                form="owner-form"
                disabled={isSubmitting || createOwner.isPending || updateOwner.isPending}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-green-400 disabled:cursor-not-allowed flex items-center"
              >
                {(isSubmitting || createOwner.isPending || updateOwner.isPending) && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {owner ? t('owners.updateOwner') : t('owners.createOwner')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditOwnerForm;