import React from 'react';
import { NotificationSettingsProps } from './types';

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ 
  userData, 
  isEditing, 
  handleChange 
}) => {
  // Ensure notifications object exists with default values
  const notifications = userData.notifications || {
    email: false,
    push: false,
    sms: false
  };
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">Email Notifications</h4>
          
          <div className="space-y-3">
            <div className="flex items-start">
              {isEditing ? (
                <div className="flex h-5 items-center">
                  <input
                    id="notifications.email"
                    name="notifications.email"
                    type="checkbox"
                    checked={notifications.email}
                    onChange={(e) => {
                      const checked = e.target.checked;
                      handleChange({
                        target: {
                          name: 'notifications.email',
                          value: checked,
                          type: 'checkbox',
                          checked
                        }
                      } as any);
                    }}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
              ) : (
                <div className="flex-shrink-0 mt-0.5">
                  {notifications.email ? (
                    <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              )}
              <div className="ml-3 text-sm">
                <label htmlFor="notifications.email" className="font-medium text-gray-700">Email Alerts</label>
                <p className="text-gray-500">Receive email notifications for system alerts and updates.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              {isEditing ? (
                <div className="flex h-5 items-center">
                  <input
                    id="notifications.push"
                    name="notifications.push"
                    type="checkbox"
                    checked={notifications.push}
                    onChange={(e) => {
                      const checked = e.target.checked;
                      handleChange({
                        target: {
                          name: 'notifications.push',
                          value: checked,
                          type: 'checkbox',
                          checked
                        }
                      } as any);
                    }}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
              ) : (
                <div className="flex-shrink-0 mt-0.5">
                  {notifications.push ? (
                    <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              )}
              <div className="ml-3 text-sm">
                <label htmlFor="notifications.push" className="font-medium text-gray-700">Push Notifications</label>
                <p className="text-gray-500">Receive push notifications for real-time alerts.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              {isEditing ? (
                <div className="flex h-5 items-center">
                  <input
                    id="notifications.sms"
                    name="notifications.sms"
                    type="checkbox"
                    checked={notifications.sms}
                    onChange={(e) => {
                      const checked = e.target.checked;
                      handleChange({
                        target: {
                          name: 'notifications.sms',
                          value: checked,
                          type: 'checkbox',
                          checked
                        }
                      } as any);
                    }}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
              ) : (
                <div className="flex-shrink-0 mt-0.5">
                  {notifications.sms ? (
                    <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              )}
              <div className="ml-3 text-sm">
                <label htmlFor="notifications.sms" className="font-medium text-gray-700">SMS Notifications</label>
                <p className="text-gray-500">Receive text messages for critical alerts.</p>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">Other Settings</h4>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 mb-1">
                Timezone
              </label>
              {isEditing ? (
                <select
                  id="timezone"
                  name="timezone"
                  value={userData.timezone || 'America/New_York'}
                  onChange={handleChange}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="America/New_York">Eastern Time (US & Canada)</option>
                  <option value="America/Chicago">Central Time (US & Canada)</option>
                  <option value="America/Denver">Mountain Time (US & Canada)</option>
                  <option value="America/Los_Angeles">Pacific Time (US & Canada)</option>
                  <option value="Europe/London">London</option>
                  <option value="Europe/Paris">Paris</option>
                  <option value="Asia/Tokyo">Tokyo</option>
                </select>
              ) : (
                <div className="text-sm text-gray-900">{userData.timezone || 'America/New_York'}</div>
              )}
            </div>
            
            <div>
              <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-1">
                Language
              </label>
              {isEditing ? (
                <select
                  id="language"
                  name="language"
                  value={userData.language || 'English'}
                  onChange={handleChange}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="English">English</option>
                  <option value="Spanish">Spanish</option>
                  <option value="French">French</option>
                  <option value="German">German</option>
                  <option value="Japanese">Japanese</option>
                </select>
              ) : (
                <div className="text-sm text-gray-900">{userData.language || 'English'}</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;