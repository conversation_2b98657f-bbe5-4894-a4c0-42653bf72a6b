import React from 'react';
import { SecuritySettingsProps } from './types';
import { useTranslation } from 'react-i18next';
import { formatDate } from '../../../utils/dateUtils';

const SecuritySettings: React.FC<SecuritySettingsProps> = ({
  userData,
  isEditing,
  setUserData
}) => {
  const { t } = useTranslation();
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">{t('profile.securitySettings')}</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="flex-shrink-0 mt-0.5">
              <svg className="h-5 w-5 text-green-500 dark:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">{t('profile.twoFactorAuth')}</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('profile.twoFactorStatus')} {userData.twoFactorEnabled ? t('profile.enabled') : t('profile.notEnabled')} {t('profile.forYourAccount')}.
              </p>
              {isEditing && (
                <button
                  type="button"
                  className="mt-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  onClick={() => setUserData({
                    ...userData,
                    twoFactorEnabled: !userData.twoFactorEnabled
                  })}
                >
                  {userData.twoFactorEnabled ? t('profile.disable') : t('profile.enable')} {t('profile.twoFactorAuth')}
                </button>
              )}
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">{t('profile.changePassword')}</h4>
            {isEditing ? (
              <div className="space-y-3">
                <div>
                  <label htmlFor="current-password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('profile.currentPassword')}
                  </label>
                  <input
                    type="password"
                    id="current-password"
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="new-password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('profile.newPassword')}
                  </label>
                  <input
                    type="password"
                    id="new-password"
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('profile.confirmPassword')}
                  </label>
                  <input
                    type="password"
                    id="confirm-password"
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <button
                  type="button"
                  className="mt-1 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {t('profile.updatePassword')}
                </button>
              </div>
            ) : (
              <button
                type="button"
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                onClick={() => setUserData(userData)} // This would normally trigger editing mode
              >
                {t('profile.changeYourPassword')}
              </button>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">{t('profile.loginSessions')}</h4>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">{t('profile.currentSession')}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {/* Browser info from user agent */}
                    {(() => {
                      const userAgent = navigator.userAgent;
                      let browser = 'Unknown Browser';
                      let os = 'Unknown OS';
                      
                      // Detect browser
                      if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) browser = 'Chrome';
                      else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) browser = 'Safari';
                      else if (userAgent.includes('Firefox')) browser = 'Firefox';
                      else if (userAgent.includes('Edg')) browser = 'Edge';
                      
                      // Detect OS
                      if (userAgent.includes('Mac')) os = 'macOS';
                      else if (userAgent.includes('Windows')) os = 'Windows';
                      else if (userAgent.includes('Linux')) os = 'Linux';
                      else if (userAgent.includes('Android')) os = 'Android';
                      else if (userAgent.includes('iOS')) os = 'iOS';
                      
                      return `${browser} on ${os}`;
                    })()} • {userData.timezone || 'UTC'}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {t('profile.started')} {formatDate(userData.lastLogin || new Date())}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    IP: {/* In a real app, this would come from the backend */}
                    {window.location.hostname === 'localhost' ? '127.0.0.1' : window.location.hostname}
                  </p>
                </div>
              </div>

              {isEditing && (
                <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <button
                    type="button"
                    className="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                  >
                    {t('profile.signOutAllSessions')}
                  </button>
                </div>
              )}
            </div>
          </div>

          {isEditing && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">{t('profile.accountActions')}</h4>
              <button
                type="button"
                className="text-sm text-red-600 hover:text-red-800"
              >
                {t('profile.deactivateAccount')}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SecuritySettings;