import React from 'react';
import { useTranslation } from 'react-i18next';
import { User } from '../../../types';
import { useLanguage } from '../../../context/LanguageContext';

interface LanguageSettingsProps {
  userData: User;
  isEditing: boolean;
}

const LanguageSettings: React.FC<LanguageSettingsProps> = ({ userData, isEditing }) => {
  const { t } = useTranslation();
  const { language, changeLanguage } = useLanguage();

  const handleLanguageChange = (newLanguage: 'en' | 'el' | 'fr') => {
    changeLanguage(newLanguage);
  };

  return (
    <div className="mb-8">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        {t('profile.language')}
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <div className="relative">
            <div className="flex flex-col md:flex-row">
              <button
                onClick={() => handleLanguageChange('en')}
                className={`py-2 px-4 text-center ${
                  language === 'en'
                    ? 'bg-blue-600 text-white font-medium'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                } rounded-l-md transition-colors`}
              >
                {t('profile.english')}
              </button>
              <button
                onClick={() => handleLanguageChange('el')}
                className={`py-2 px-4 text-center ${
                  language === 'el'
                    ? 'bg-blue-600 text-white font-medium'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                } transition-colors`}
              >
                {t('profile.greek')}
              </button>
              <button
                onClick={() => handleLanguageChange('fr')}
                className={`py-2 px-4 text-center ${
                  language === 'fr'
                    ? 'bg-blue-600 text-white font-medium'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                } rounded-r-md transition-colors`}
              >
                {t('profile.french')}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <p className="text-sm text-gray-500 mt-2">
        {language === 'en'
          ? t('profile.languageSetEnglish')
          : language === 'el'
          ? t('profile.languageSetGreek')
          : t('profile.languageSetFrench')}
      </p>
    </div>
  );
};

export default LanguageSettings;