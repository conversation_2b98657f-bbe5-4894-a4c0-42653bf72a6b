import React from 'react';
import { StatusBadge } from '../../common';
import { ProfileHeaderProps } from './types';
import { useTranslation } from 'react-i18next';
import { formatDate, formatDetailDate } from '../../../utils/dateUtils';

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  userData,
  isEditing,
  setIsEditing,
  getInitials,
  handleSubmit,
  onResetData
}) => {
  const { t } = useTranslation();
  return (
    <>
      {/* User profile header */}
      <div className="relative bg-blue-600 h-32 md:h-48">
        {/* Edit button */}
        {!isEditing && (
          <button 
            onClick={() => setIsEditing(true)}
            className="absolute top-4 right-4 bg-white bg-opacity-90 text-blue-600 px-4 py-2 rounded-md shadow-sm hover:bg-opacity-100 transition-colors duration-150 flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
            {t('profile.editProfile')}
          </button>
        )}
        
        {/* Cancel and Save buttons when editing */}
        {isEditing && (
          <div className="absolute top-4 right-4 flex space-x-2">
            <button 
              onClick={() => {
                setIsEditing(false);
                onResetData();
              }}
              className="bg-white bg-opacity-90 text-gray-600 px-4 py-2 rounded-md shadow-sm hover:bg-opacity-100 transition-colors duration-150"
            >
              {t('common.cancel')}
            </button>
            <button 
              onClick={handleSubmit}
              className="bg-green-500 text-white px-4 py-2 rounded-md shadow-sm hover:bg-green-600 transition-colors duration-150"
            >
              {t('common.saveChanges')}
            </button>
          </div>
        )}
        
        {/* User avatar */}
        <div className="absolute -bottom-12 left-8">
          <div className="relative">
            {userData.avatar ? (
              <img 
                src={userData.avatar} 
                alt={userData.name} 
                className="w-24 h-24 rounded-full border-4 border-white"
              />
            ) : (
              <div className="w-24 h-24 rounded-full border-4 border-white bg-blue-800 flex items-center justify-center text-white text-2xl font-bold">
                {getInitials(userData.name)}
              </div>
            )}
            
            {isEditing && (
              <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                <label htmlFor="avatar-upload" className="cursor-pointer text-white text-xs font-medium p-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  {t('common.change')}
                </label>
                <input id="avatar-upload" type="file" className="hidden" />
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* User basic info */}
      <div className="pt-16 pb-4 px-8 border-b border-gray-200">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{userData.name}</h2>
            <p className="text-gray-600">{userData.email}</p>
            <div className="mt-1 flex items-center">
              <span className="text-sm text-gray-500 mr-2">{t('profile.joined')} {formatDate(userData.joined, { relativeThreshold: 30 })}</span>
              <StatusBadge status={userData.status} />
            </div>
          </div>
          <div className="mt-4 md:mt-0">
            <div className="text-sm text-gray-500">{t('profile.lastLogin')}: {formatDate(userData.lastLogin, { includeTime: true })}</div>
            <div className="text-sm font-medium text-gray-900 mt-1">{userData.role}</div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProfileHeader;