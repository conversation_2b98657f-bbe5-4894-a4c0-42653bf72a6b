import { User, Subscription, UserRole } from '../../../types';

export type ProfileTab = 'subscriptions' | 'security' | 'notifications' | 'language' | 'emailProcessing';

export interface ProfileContextType {
  userData: User | null;
  isEditing: boolean;
  userSubscriptions: Subscription[];
  isLoading: boolean;
  error: any;
  setUserData: (userData: User) => void;
  setIsEditing: (isEditing: boolean) => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  getInitials: (name: string) => string;
  refetchData: () => void;
  refetchNotifications: () => void;
  refetchSubscriptions: () => void;
}

export interface ProfileHeaderProps {
  userData: User;
  isEditing: boolean;
  setIsEditing: (value: boolean) => void;
  getInitials: (name: string) => string;
  handleSubmit: (e: React.FormEvent) => void;
  onResetData: () => void;
}

export interface ProfileNavigationProps {
  activeTab: ProfileTab;
  setActiveTab: (tab: ProfileTab) => void;
  userRole: UserRole;
}

export interface ProfileInfoFormProps {
  userData: User;
  isEditing: boolean;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

export interface UserSubscriptionsProps {
  userSubscriptions: Subscription[];
  userData: User;
}

export interface SecuritySettingsProps {
  userData: User;
  isEditing: boolean;
  setUserData: (userData: User) => void;
}

export interface NotificationSettingsProps {
  userData: User;
  isEditing: boolean;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}