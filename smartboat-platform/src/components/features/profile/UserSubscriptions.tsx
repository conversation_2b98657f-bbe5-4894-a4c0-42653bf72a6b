import React from 'react';
import { UserSubscriptionsProps } from './types';
import { formatDateRange } from '../../../utils/dateUtils';

const UserSubscriptions: React.FC<UserSubscriptionsProps> = ({
  userSubscriptions,
  userData
}) => {
  if (userData.role === 'Administrator') {
    return null; // Don't render for admin users - handled at the parent level
  }

  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Your Subscriptions</h3>

      {userSubscriptions.length === 0 ? (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <p className="text-gray-600 dark:text-gray-400 text-center">No active subscriptions found.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {userSubscriptions.map(subscription => (
            <div key={subscription.id} className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                  <h4 className="text-md font-medium text-gray-900 dark:text-white">{subscription.name}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {subscription.type} - Active {formatDateRange(subscription.startDate, subscription.endDate)}
                  </p>
                  {userData?.role === 'Administrator' && (
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      Customer: {subscription.customerName}
                    </p>
                  )}
                  <div className="mt-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      subscription.status === 'Active' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300' :
                      subscription.status === 'Pending' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300' :
                      'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300'
                    }`}>
                      {subscription.status}
                    </span>
                  </div>
                </div>

                <div className="mt-4 md:mt-0 text-right">
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    ${subscription.price}
                    <span className="text-sm font-normal text-gray-600 dark:text-gray-400">
                      /{subscription.billingFrequency === 'Monthly' ? 'mo' :
                        subscription.billingFrequency === 'Quarterly' ? 'qtr' : 'yr'}
                    </span>
                  </div>
                  <div className="mt-2">
                    <a href="/subscriptions" className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                      Manage subscription
                    </a>
                  </div>
                </div>
              </div>

              {subscription.features && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Plan Features:</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {subscription.features.map((feature, idx) => (
                      <div key={idx} className="flex items-start">
                        <svg className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-xs text-gray-600 dark:text-gray-400">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Subscription Permissions information */}
      <div className="mt-8">
        <h3 className="text-md font-medium text-gray-900 dark:text-white mb-2">Your Subscription Permissions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="flex items-center">
            <div className="w-6 h-6 flex items-center justify-center mr-2">
              {userData.role === 'Administrator' ? (
                <svg className="h-5 w-5 text-green-500 dark:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-5 w-5 text-gray-300 dark:text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <span className="text-gray-700 dark:text-gray-300">View all subscriptions</span>
          </div>

          <div className="flex items-center">
            <div className="w-6 h-6 flex items-center justify-center mr-2">
              {userData.role === 'Administrator' ? (
                <svg className="h-5 w-5 text-green-500 dark:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-5 w-5 text-gray-300 dark:text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <span className="text-gray-700 dark:text-gray-300">Add new subscriptions</span>
          </div>

          <div className="flex items-center">
            <div className="w-6 h-6 flex items-center justify-center mr-2">
              {userData.role === 'Administrator' ? (
                <svg className="h-5 w-5 text-green-500 dark:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-5 w-5 text-gray-300 dark:text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <span className="text-gray-700 dark:text-gray-300">Edit any subscription</span>
          </div>

          <div className="flex items-center">
            <div className="w-6 h-6 flex items-center justify-center mr-2">
              {(['Administrator', 'Customer'].includes(userData.role)) ? (
                <svg className="h-5 w-5 text-green-500 dark:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-5 w-5 text-gray-300 dark:text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <span className="text-gray-700 dark:text-gray-300">Change own subscriptions</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserSubscriptions;