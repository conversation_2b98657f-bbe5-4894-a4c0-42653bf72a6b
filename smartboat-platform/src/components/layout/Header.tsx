import React, { useState } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../context/AuthContext';
import { LanguageSwitcher, ThemeToggle } from '../common';
import NotificationDropdown from '../common/NotificationDropdown';
import { useNotificationStats } from '../../hooks/queries/useNotificationQueries';

interface HeaderProps {
  activePage: string;
  setActivePage: (page: string) => void;
}

const Header: React.FC<HeaderProps> = ({ activePage, setActivePage }) => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { t } = useTranslation();
  const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false);
  
  // Fetch notification stats
  const { data: notificationStats } = useNotificationStats({
    useMockFallback: true
  });

  // Navigate to dashboard using React Router
  const handleGoToDashboard = () => {
    // Set explicit dashboard navigation flag
    sessionStorage.setItem('dashboard_explicit', 'true');
    // Save dashboard path in localStorage to avoid redirection loops
    localStorage.setItem('smartboat_last_path', '/dashboard');
    // Use React Router navigation to avoid page refresh
    navigate('/dashboard');
  };
  return (
    <header className="bg-white dark:bg-gray-800 shadow text-gray-800 dark:text-white px-6 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40 h-[72px] flex items-center">
      <div className="flex items-center justify-between w-full">
        {/* Page Title */}
        <div>
          <h1 className="text-xl font-semibold capitalize group relative" onClick={handleGoToDashboard} style={{ cursor: 'pointer' }}>
            {activePage === 'dashboard' ? t('common.dashboard') : t(`common.${activePage.toLowerCase()}`)}
            {activePage !== 'dashboard' && (
              <span className="ml-2 inline-block">
                <Link
                  to="/dashboard"
                  className="text-blue-500 text-sm hover:text-blue-600 hover:underline"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate('/dashboard');
                  }}
                >
                  {t('common.backToDashboard')}
                </Link>
              </span>
            )}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">{t('common.welcome')}</p>
        </div>

        {/* Search Bar */}
        <div className="flex-1 max-w-xl mx-8 hidden md:block">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder={t('common.search')}
            />
          </div>
        </div>

        {/* Right Side Items */}
        <div className="flex items-center space-x-4">
          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Language Switcher */}
          <LanguageSwitcher />

          <div className="relative">
            <button 
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 relative"
              onClick={() => setNotificationDropdownOpen(!notificationDropdownOpen)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600 dark:text-gray-300" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
              </svg>
              {notificationStats && notificationStats.unread > 0 && (
                <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
              )}
            </button>
            <NotificationDropdown 
              isOpen={notificationDropdownOpen}
              onClose={() => setNotificationDropdownOpen(false)}
            />
          </div>

          <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600 dark:text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
          </button>

          <div className="border-l border-gray-300 dark:border-gray-600 h-8 mx-2"></div>

          <div className="hidden md:flex items-center cursor-pointer" onClick={() => navigate('/profile')}>
            <span className="text-sm font-medium mr-2">{currentUser?.name || 'User'}</span>
            <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center text-white">
              {currentUser?.name ? (
                <span className="text-white font-semibold">
                  {currentUser.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </span>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;