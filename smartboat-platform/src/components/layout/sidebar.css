/* Custom sidebar styles */
.sidebar-menu-item {
  transition: all 0.3s ease;
}

.sidebar-menu-item:hover {
  transform: translateX(5px);
}

.sidebar-menu-item.active {
  position: relative;
}

.sidebar-menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #3b82f6;
  border-radius: 0 4px 4px 0;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

/* User profile section animation */
.user-profile {
  transition: all 0.3s ease;
}

.user-profile:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.dark .user-profile:hover {
  background-color: rgba(59, 130, 246, 0.2);
}

/* Custom scrollbar for sidebar */
.sidebar-scroll::-webkit-scrollbar {
  width: 4px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: #f3f4f6;
}

.dark .sidebar-scroll::-webkit-scrollbar-track {
  background: #1f2937;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 20px;
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 20px;
}

/* Tooltip for collapsed sidebar */
.sidebar-tooltip {
  position: relative;
}

.sidebar-tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: #f3f4f6;
  color: #1f2937;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 100;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 10px;
  opacity: 0;
  transition: opacity 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  pointer-events: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dark .sidebar-tooltip .tooltip-text {
  background-color: #374151;
  color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.sidebar-tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent #f3f4f6 transparent transparent;
}

.dark .sidebar-tooltip .tooltip-text::after {
  border-color: transparent #374151 transparent transparent;
}

.sidebar-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}
