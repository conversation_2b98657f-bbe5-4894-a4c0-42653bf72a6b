import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import Header from './Header';
import Sidebar from './Sidebar';

const Layout: React.FC = () => {
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();

  // Get active page from current path
  const getActivePage = () => {
    const path = location.pathname.split('/')[1] || 'dashboard';
    // If the path is empty or root, explicitly return 'dashboard'
    return path === '' ? 'dashboard' : path;
  };

  // Handle navigation
  const handleNavigate = (page: string) => {
    if (page === 'logout') {
      handleLogout();
      return;
    }
    navigate(`/${page}`);
  };

  // Handle logout
  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Listen for sidebar expansion state changes
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setSidebarExpanded(false);
      } else {
        setSidebarExpanded(true);
      }
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);


  return (
    <div className="flex min-h-screen overflow-hidden bg-white dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar
        activePage={getActivePage()}
        setActivePage={handleNavigate}
        onExpandChange={setSidebarExpanded}
        onLogout={handleLogout}
      />

      {/* Main content area */}
      <div
        className={`flex-1 fixed-header-layout transition-all duration-300 ${
          sidebarExpanded ? 'ml-64' : 'ml-20'
        }`}
      >
        {/* Fixed Header */}
        <Header activePage={getActivePage()} setActivePage={handleNavigate} />

        {/* Scrollable Content Area */}
        <div
          className="flex-1 overflow-y-auto bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white"
          style={{ height: 'calc(100vh - 72px)' }}
        >
          <main className="p-6">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
};

export default Layout;
