import React, { useEffect, useState, useRef } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useTranslation } from 'react-i18next';

interface SessionTimeoutHandlerProps {
  timeout?: number; // Timeout in milliseconds
  warningDuration?: number; // Warning duration in milliseconds
  onWarning?: () => void; // Callback when warning starts
  onTimeout?: () => void; // Callback when timeout occurs
  children?: React.ReactNode;
}

const SessionTimeoutHandler: React.FC<SessionTimeoutHandlerProps> = ({
  timeout = 30 * 60 * 1000, // Default: 30 minutes
  warningDuration = 1 * 60 * 1000, // Default: 1 minute warning
  onWarning,
  onTimeout,
  children,
}) => {
  const { logout, isAuthenticated } = useAuth();
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const warningTimerRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutTimerRef = useRef<NodeJS.Timeout | null>(null);
  const warningIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const activityTimestampRef = useRef(Date.now());

  // Function to reset timers
  const resetTimers = () => {
    if (warningTimerRef.current) clearTimeout(warningTimerRef.current);
    if (timeoutTimerRef.current) clearTimeout(timeoutTimerRef.current);
    if (warningIntervalRef.current) clearInterval(warningIntervalRef.current);
    
    setShowWarning(false);
    
    // Only set timers if the user is authenticated
    if (isAuthenticated) {
      // Set timer for warning
      warningTimerRef.current = setTimeout(() => {
        setShowWarning(true);
        setTimeLeft(warningDuration);
        
        if (onWarning) onWarning();
        
        // Start countdown
        warningIntervalRef.current = setInterval(() => {
          setTimeLeft(prev => Math.max(0, prev - 1000));
        }, 1000);
      }, timeout - warningDuration);
      
      // Set timer for timeout
      timeoutTimerRef.current = setTimeout(() => {
        if (onTimeout) onTimeout();
        logout();
      }, timeout);
    }
  };

  // Track user activity
  const recordActivity = () => {
    activityTimestampRef.current = Date.now();
    resetTimers();
  };

  // Set up event listeners for user activity
  useEffect(() => {
    if (!isAuthenticated) return;
    
    const activityEvents = [
      'mousedown', 'keydown', 'touchstart', 'scroll', 'mousemove'
    ];
    
    // Add throttling for mousemove events to avoid excessive resets
    let lastMove = Date.now();
    const handleMouseMove = () => {
      const now = Date.now();
      if (now - lastMove > 5000) { // Throttle to once every 5 seconds
        lastMove = now;
        recordActivity();
      }
    };
    
    // Add event listeners
    activityEvents.forEach(event => {
      if (event === 'mousemove') {
        window.addEventListener(event, handleMouseMove);
      } else {
        window.addEventListener(event, recordActivity);
      }
    });
    
    // Initial timer setup
    resetTimers();
    
    // Clean up event listeners
    return () => {
      activityEvents.forEach(event => {
        if (event === 'mousemove') {
          window.removeEventListener(event, handleMouseMove);
        } else {
          window.removeEventListener(event, recordActivity);
        }
      });
      
      if (warningTimerRef.current) clearTimeout(warningTimerRef.current);
      if (timeoutTimerRef.current) clearTimeout(timeoutTimerRef.current);
      if (warningIntervalRef.current) clearInterval(warningIntervalRef.current);
    };
  }, [isAuthenticated, timeout, warningDuration, logout, onWarning, onTimeout]);

  // Periodically check for inactivity (every minute)
  useEffect(() => {
    if (!isAuthenticated) return;
    
    const inactivityCheck = setInterval(() => {
      const now = Date.now();
      const inactiveTime = now - activityTimestampRef.current;
      
      // If inactive for longer than our threshold, reset timers
      if (inactiveTime > 60000) {
        resetTimers();
      }
    }, 60000);
    
    return () => clearInterval(inactivityCheck);
  }, [isAuthenticated, timeout]);

  // Component for session timeout warning
  const SessionTimeoutWarning = () => {
    const { t } = useTranslation();
    if (!showWarning) return null;

    const minutes = Math.floor(timeLeft / 60000);
    const seconds = Math.floor((timeLeft % 60000) / 1000);
    
    return (
      <div className="fixed inset-x-0 bottom-0 z-50 p-4 bg-yellow-100 shadow-lg border-t-2 border-yellow-400">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center">
            <svg className="h-6 w-6 text-yellow-600 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-yellow-800">
              <strong>{t('auth.sessionTimeout')}</strong> {t('auth.logoutIn')} {minutes}:{seconds < 10 ? `0${seconds}` : seconds}
            </span>
          </div>
          <button
            onClick={recordActivity}
            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
          >
            {t('auth.stayLoggedIn')}
          </button>
        </div>
      </div>
    );
  };

  return (
    <>
      {children}
      <SessionTimeoutWarning />
    </>
  );
};

export default SessionTimeoutHandler;