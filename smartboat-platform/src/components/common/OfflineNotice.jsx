/**
 * OfflineNotice Component
 * Displays a notice when the application is in offline mode
 */

import { useApi } from '../../providers/ApiProvider';

const OfflineNotice = () => {
  const { isConnected, isMockMode } = useApi();
  
  // Don't show if we're connected to the API
  if (isConnected) return null;
  
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-yellow-100 dark:bg-yellow-900/70 text-yellow-800 dark:text-yellow-200 py-2 px-4 z-50 flex justify-between items-center">
      <div className="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        <span>
          {isMockMode() 
            ? 'Using mock data - API connection unavailable' 
            : 'You are currently offline - Data updates are disabled'}
        </span>
      </div>
      {isMockMode() && (
        <span className="text-xs bg-yellow-200 dark:bg-yellow-800 px-2 py-1 rounded">
          MOCK MODE
        </span>
      )}
    </div>
  );
};

export default OfflineNotice;