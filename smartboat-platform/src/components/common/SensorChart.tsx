import React, { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { SensorDataPoint } from '../../types/sensor';
import { useSensorData } from '../../hooks/queries/useSensorQueries';
import LoadingOverlay from './LoadingOverlay';
import ErrorNotification from './ErrorNotification';
import { useTranslation } from 'react-i18next';
import { formatChartDate } from '../../utils/dateUtils';

interface SensorChartProps {
  sensorId: string;
  vesselName?: string;
  location?: string;
  timeframe?: 'day' | 'week' | 'month';
  sensorType?: 'temperature' | 'humidity' | 'pressure' | 'auto';
  startDate?: Date | null;
  endDate?: Date | null;
}

const SensorChart: React.FC<SensorChartProps> = ({
  sensorId,
  vesselName = "HS Mortier Bay",
  location = "Engine Room",
  timeframe = 'day',
  sensorType: initialSensorType = 'auto',
  startDate,
  endDate
}) => {
  const { t } = useTranslation();
  const [selectedSensorType, setSelectedSensorType] = useState<'temperature' | 'humidity' | 'pressure' | 'auto'>(initialSensorType);
  
  // Fetch sensor data from API
  const {
    data: apiData = [],
    isLoading,
    isError,
    error,
    refetch
  } = useSensorData(sensorId, {
    ...(startDate && endDate ? {
      startTime: startDate.toISOString(),
      endTime: endDate.toISOString()
    } : {
      timeframe,
      interval: timeframe === 'day' ? '1h' : timeframe === 'week' ? '6h' : '1d'
    })
  }, {
    enabled: !!sensorId,
    useMockFallback: false, // Force real API calls
    staleTime: 60 * 1000, // 1 minute
    refetchInterval: 5 * 60 * 1000 // Refetch every 5 minutes
  });
  
  // Transform API data to chart format
  const data = React.useMemo(() => {
    if (!apiData || !Array.isArray(apiData)) return [];
    
    return apiData.map(point => {
      // Handle backend SensorDataPointDto format
      const timestamp = point.Timestamp || point.timestamp;
      const time = timestamp ? formatChartDate(timestamp) : '';
      
      // Parse measurements dictionary if it exists
      let measurements = {};
      if (point.Measurements && typeof point.Measurements === 'object') {
        measurements = point.Measurements;
      } else if (point.measurements && typeof point.measurements === 'object') {
        measurements = point.measurements;
      }
      
      return {
        time,
        timestamp: timestamp || Date.now(),
        temperature: point.Temperature || point.temperature || measurements.temperature || 0,
        humidity: point.Humidity || point.humidity || measurements.humidity || 0,
        pressure: measurements.pressure || 0,
        speed: point.Speed || point.speed || measurements.speed || 0,
        rpm: point.RPM || point.rpm || measurements.rpm || 0,
        value: point.Temperature || point.temperature || measurements.temperature || 0,
        unit: measurements.unit || '°C'
      };
    });
  }, [apiData]);
  
  // Auto-detect sensor type from data if set to 'auto'
  const sensorType = React.useMemo(() => {
    if (selectedSensorType !== 'auto') return selectedSensorType;
    
    if (data.length > 0) {
      const firstPoint = data[0];
      if (firstPoint.temperature > 0) return 'temperature';
      if (firstPoint.humidity > 0) return 'humidity';
      if (firstPoint.pressure > 0) return 'pressure';
    }
    return 'temperature';
  }, [selectedSensorType, data]);

  // Calculate statistics
  const calculateStats = () => {
    if (data.length === 0) return { current: 'N/A', avg: 'N/A', min: 'N/A', max: 'N/A' };

    const values = data.map(d => {
      const value = d[sensorType];
      return typeof value === 'number' ? value : 0;
    }).filter(v => v > 0);
    
    if (values.length === 0) return { current: 'N/A', avg: 'N/A', min: 'N/A', max: 'N/A' };
    
    const current = values[values.length - 1];
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return {
      current: current.toFixed(1),
      avg: avg.toFixed(1),
      min: min.toFixed(1),
      max: max.toFixed(1)
    };
  };

  const stats = calculateStats();
  const getUnit = () => {
    switch (sensorType) {
      case 'temperature': return '°C';
      case 'humidity': return '%';
      case 'pressure': return 'bar';
      default: return data.length > 0 ? data[0].unit || '' : '';
    }
  };
  
  const getSensorDisplayName = () => {
    switch (sensorType) {
      case 'temperature': return t('sensors.temperature');
      case 'humidity': return t('sensors.humidity');
      case 'pressure': return t('sensors.pressure');
      default: return t('sensors.sensor');
    }
  };
  
  // Loading state
  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 relative">
        <LoadingOverlay 
          message={t('sensors.loadingSensorData')}
          subMessage={t('sensors.fetchingSensorReadings')}
          contentOnly={true}
        />
      </div>
    );
  }
  
  // Error state
  if (isError) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
        <ErrorNotification
          message={t('sensors.errorLoadingSensorData')}
          error={error}
          onRetry={refetch}
        />
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold mb-1 text-gray-800 dark:text-white">{t('sensors.sensorData')}</h2>
          <p className="text-gray-500 dark:text-gray-400 text-sm">{vesselName} - {location}</p>
        </div>

      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div className="text-xs text-gray-500 dark:text-gray-400">{t('sensors.current')}</div>
          <div className="text-xl font-bold text-blue-600 dark:text-blue-400">{stats.current}{getUnit()}</div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div className="text-xs text-gray-500 dark:text-gray-400">{t('sensors.average')}</div>
          <div className="text-xl font-bold text-blue-600 dark:text-blue-400">{stats.avg}{getUnit()}</div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div className="text-xs text-gray-500 dark:text-gray-400">{t('sensors.minimum')}</div>
          <div className="text-xl font-bold text-blue-600 dark:text-blue-400">{stats.min}{getUnit()}</div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div className="text-xs text-gray-500 dark:text-gray-400">{t('sensors.maximum')}</div>
          <div className="text-xl font-bold text-blue-600 dark:text-blue-400">{stats.max}{getUnit()}</div>
        </div>
      </div>

      {/* Chart */}
      {data.length > 0 ? (
        <div style={{ width: '100%', height: '100%', minHeight: '280px' }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{ top: 5, right: 5, left: 10, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis
                dataKey="time"
                tick={{ fontSize: 12, fill: '#9CA3AF' }}
                height={50}
                stroke="#4B5563"
              />
              <YAxis
                domain={sensorType === 'humidity' ? [0, 100] : ['auto', 'auto']}
                tick={{ fontSize: 12, fill: '#9CA3AF' }}
                width={50}
                stroke="#4B5563"
              />
              <Tooltip
                formatter={(value: any) => [`${value}${getUnit()}`, getSensorDisplayName()]}
                labelFormatter={(label: string) => `${t('sensors.time')}: ${label}`}
                contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', color: '#F9FAFB' }}
                itemStyle={{ color: '#F9FAFB' }}
                labelStyle={{ color: '#F9FAFB' }}
              />
              <Legend wrapperStyle={{ paddingTop: 10, color: '#9CA3AF' }} />
              <Line
                type="monotone"
                dataKey={sensorType}
                name={getSensorDisplayName()}
                stroke={sensorType === 'temperature' ? "#3b82f6" : sensorType === 'humidity' ? "#10b981" : "#f59e0b"}
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 5 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
          <div className="text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <p>{t('sensors.noDataAvailable')}</p>
          </div>
        </div>
      )}
    </div>
  );
};

// Add default props for backwards compatibility
SensorChart.defaultProps = {
  timeframe: 'day',
  sensorType: 'auto'
};

export default SensorChart;