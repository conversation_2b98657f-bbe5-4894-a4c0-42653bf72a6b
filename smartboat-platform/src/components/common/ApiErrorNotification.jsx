/**
 * ApiErrorNotification Component
 * Displays API error notifications from the API provider
 * 
 * @typedef {Object} ApiError
 * @property {string|number} [code] - Error code
 * @property {string} [message] - Error message
 * @property {number} [status] - HTTP status code
 */

import { useEffect } from 'react';
import { useApi } from '../../providers/ApiProvider';

const ApiErrorNotification = () => {
  const { errors, removeError } = useApi();
  
  // Auto-dismiss errors after 5 seconds
  useEffect(() => {
    const timers = errors.map(error => {
      return setTimeout(() => {
        removeError(error.id);
      }, 5000);
    });
    
    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [errors, removeError]);
  
  if (!errors.length) return null;
  
  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2 max-w-md">
      {errors.map(error => (
        <div 
          key={error.id}
          className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg shadow-lg flex justify-between items-start"
        >
          <div>
            <div className="font-medium">Error {error.code}</div>
            <div className="text-sm mt-1">{error.message}</div>
          </div>
          <button 
            onClick={() => removeError(error.id)} 
            className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 ml-4"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      ))}
    </div>
  );
};

export default ApiErrorNotification;