import React, { useState, useEffect } from 'react';
import { imageService } from '../../services/imageService';

interface AuthenticatedImageProps {
  imageId: string;
  alt?: string;
  className?: string;
  fileName?: string;
}

const AuthenticatedImage: React.FC<AuthenticatedImageProps> = ({
  imageId,
  alt = 'Image',
  className = '',
  fileName,
}) => {
  const [blobUrl, setBlobUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    let isMounted = true;
    let currentBlobUrl: string | null = null;

    const fetchImage = async () => {
      if (!imageId) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      try {
        setIsLoading(true);
        setHasError(false);
        
        const url = await imageService.getImageBlobUrl(imageId);
        
        if (isMounted) {
          currentBlobUrl = url;
          setBlobUrl(url);
        } else {
          // Component unmounted, clean up the blob URL
          URL.revokeObjectURL(url);
        }
      } catch (error) {
        console.error('Failed to load image:', imageId, error);
        if (isMounted) {
          setHasError(true);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchImage();

    return () => {
      isMounted = false;
      if (currentBlobUrl) {
        URL.revokeObjectURL(currentBlobUrl);
      }
    };
  }, [imageId]);

  // Cleanup blob URL when component unmounts
  useEffect(() => {
    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [blobUrl]);

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-gray-200 dark:bg-gray-700 ${className}`}>
        <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (hasError || !blobUrl) {
    return (
      <div className={`flex items-center justify-center bg-gray-200 dark:bg-gray-700 ${className}`}>
        <div className="text-center text-gray-500 dark:text-gray-400">
          <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <p className="text-xs">Failed to load</p>
        </div>
      </div>
    );
  }

  return (
    <img
      src={blobUrl}
      alt={alt || fileName || 'Image'}
      className={className}
      onError={() => {
        console.error('Image failed to display:', imageId);
        setHasError(true);
      }}
    />
  );
};

export default AuthenticatedImage;