import React, { useState, useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { VesselPathPoint } from '../../types';

interface VesselMapProps {
  vesselId: string;
  pathData?: VesselPathPoint[];
  timeRange?: '1w' | '1m' | '3m';
}

const VesselMap: React.FC<VesselMapProps> = ({ vesselId, pathData: externalPathData, timeRange = '3m' }) => {
  const [filteredPathData, setFilteredPathData] = useState<VesselPathPoint[]>([]);
  const [dateRange, setDateRange] = useState<{ start: string | null; end: string | null }>({ 
    start: null, 
    end: null 
  });
  const [selectedPoint, setSelectedPoint] = useState<VesselPathPoint | null>(null);
  const [currentTimeRange, setCurrentTimeRange] = useState<'1w' | '1m' | '3m'>(timeRange);
  const mapRef = useRef<HTMLDivElement>(null);
  const leafletMapRef = useRef<L.Map | null>(null);
  const pathLayerRef = useRef<L.LayerGroup | null>(null);
  
  useEffect(() => {
    // Use the path data from vessel endpoint instead of mock data
    const fullPath = externalPathData || [];
    
    // Filter based on time range (1w = 1 week, 1m = 1 month, 3m = 3 months)
    let filteredPath;
    const now = new Date();
    let startDate;
    
    switch(currentTimeRange) {
      case '1w':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '1m':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '3m':
      default:
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
    }
    
    // Filter path data based on time range
    if (fullPath.length > 0) {
      filteredPath = fullPath.filter(point => {
        const pointDate = new Date(point.timestamp || point.Timestamp);
        return pointDate >= startDate;
      });
    } else {
      filteredPath = [];
    }
    
    setFilteredPathData(filteredPath);
    
    if (filteredPath.length > 0) {
      setDateRange({
        start: filteredPath[0].timestamp,
        end: filteredPath[filteredPath.length - 1].timestamp
      });
    } else {
      setDateRange({ start: null, end: null });
    }
  }, [externalPathData, currentTimeRange]);

  // Initialize Leaflet map
  useEffect(() => {
    if (!mapRef.current) return;

    // Initialize map
    leafletMapRef.current = L.map(mapRef.current).setView([37.9755, 23.7348], 6); // Default to Athens, Greece

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(leafletMapRef.current);

    // Initialize path layer
    pathLayerRef.current = L.layerGroup().addTo(leafletMapRef.current);

    // Cleanup on unmount
    return () => {
      if (leafletMapRef.current) {
        leafletMapRef.current.remove();
        leafletMapRef.current = null;
      }
    };
  }, []);

  // Update path on map when data changes
  useEffect(() => {
    if (!leafletMapRef.current || !pathLayerRef.current) return;

    // Clear existing path
    pathLayerRef.current.clearLayers();

    if (filteredPathData.length === 0) return;

    // Convert path data to Leaflet LatLng format
    const pathPoints = filteredPathData.map(point => [
      point.lat || point.Lat,
      point.lng || point.Lng
    ] as [number, number]);

    // Add path polyline
    if (pathPoints.length > 1) {
      const polyline = L.polyline(pathPoints, {
        color: '#3b82f6',
        weight: 3,
        opacity: 0.7
      }).addTo(pathLayerRef.current);

      // Fit map to path bounds
      leafletMapRef.current.fitBounds(polyline.getBounds(), { padding: [20, 20] });
    }

    // Add markers for start and end points
    if (pathPoints.length > 0) {
      // Start point (green)
      const startMarker = L.circleMarker(pathPoints[0], {
        color: '#10b981',
        fillColor: '#10b981',
        fillOpacity: 0.8,
        radius: 8
      }).addTo(pathLayerRef.current);

      startMarker.bindPopup(`
        <div>
          <strong>Start Point</strong><br>
          Location: ${filteredPathData[0].location || 'Unknown'}<br>
          Time: ${filteredPathData[0].timestamp || filteredPathData[0].Timestamp}
        </div>
      `);

      // End point (red)
      if (pathPoints.length > 1) {
        const endMarker = L.circleMarker(pathPoints[pathPoints.length - 1], {
          color: '#ef4444',
          fillColor: '#ef4444',
          fillOpacity: 0.8,
          radius: 8
        }).addTo(pathLayerRef.current);

        const lastPoint = filteredPathData[filteredPathData.length - 1];
        endMarker.bindPopup(`
          <div>
            <strong>End Point</strong><br>
            Location: ${lastPoint.location || 'Unknown'}<br>
            Time: ${lastPoint.timestamp || lastPoint.Timestamp}
          </div>
        `);
      }

      // Add intermediate points (blue)
      pathPoints.slice(1, -1).forEach((point, index) => {
        const marker = L.circleMarker(point, {
          color: '#3b82f6',
          fillColor: '#3b82f6',
          fillOpacity: 0.6,
          radius: 4
        }).addTo(pathLayerRef.current!);

        const pointData = filteredPathData[index + 1];
        marker.bindPopup(`
          <div>
            <strong>Route Point</strong><br>
            Location: ${pointData.location || 'Unknown'}<br>
            Time: ${pointData.timestamp || pointData.Timestamp}
          </div>
        `);
      });
    }
  }, [filteredPathData]);
  
  // Render the map
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium">Vessel Route Map</h2>
        <div className="flex space-x-2">
          <button 
            className={`px-2 py-1 text-sm rounded ${currentTimeRange === '1w' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
            onClick={() => setCurrentTimeRange('1w')}
          >
            1 Week
          </button>
          <button 
            className={`px-2 py-1 text-sm rounded ${currentTimeRange === '1m' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
            onClick={() => setCurrentTimeRange('1m')}
          >
            1 Month
          </button>
          <button 
            className={`px-2 py-1 text-sm rounded ${currentTimeRange === '3m' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
            onClick={() => setCurrentTimeRange('3m')}
          >
            3 Months
          </button>
        </div>
      </div>
      
      {dateRange.start && dateRange.end && (
        <div className="text-sm text-gray-600 mb-2">
          Route from {dateRange.start} to {dateRange.end}
        </div>
      )}
      
      {/* Leaflet Map Container */}
      <div className="w-full h-80 rounded relative overflow-hidden">
        {filteredPathData.length === 0 ? (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
            <div className="text-center">
              <div className="text-gray-500 mb-2">No path data available</div>
              <div className="text-sm text-gray-400">This vessel has no recorded route information</div>
            </div>
          </div>
        ) : null}
        <div ref={mapRef} className="w-full h-full" />
      </div>
      
      <div className="mt-4 flex justify-between text-sm text-gray-600">
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-1"></span>
          Start Point
        </div>
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-1"></span>
          Route Point
        </div>
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
          End Point
        </div>
      </div>
    </div>
  );
};

export default VesselMap;