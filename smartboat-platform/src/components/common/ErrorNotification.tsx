/**
 * ErrorNotification Component
 * Displays error notifications with retry functionality
 */

import React from 'react';
import { useTranslation } from 'react-i18next';

// Define a type for API errors
export interface ApiError {
  code?: string | number;
  status?: number;
  message?: string;
  [key: string]: any; // Allow for additional properties
}

interface ErrorNotificationProps {
  message: string;
  onRetry?: () => void;
  error?: ApiError | Error | string | unknown;
}

const ErrorNotification: React.FC<ErrorNotificationProps> = ({ 
  message, 
  onRetry,
  error
}) => {
  const { t } = useTranslation();
  
  // Extract error details if available
  let errorMessage = '';
  let errorCode = '';

  // Extract error information based on type
  if (error) {
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'object') {
      // Handle API error object
      errorMessage = (error as ApiError).message || '';
      errorCode = (error as ApiError).code?.toString() || (error as ApiError).status?.toString() || '';
    }
  }
  
  return (
    <div className="w-full bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg mt-4 mb-4">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium">{message}</h3>
          {errorMessage && (
            <div className="mt-2 text-sm">
              {errorCode && <span className="font-medium">Error {errorCode}: </span>}
              {errorMessage}
            </div>
          )}
        </div>
      </div>
      {onRetry && (
        <div className="mt-3">
          <button
            onClick={onRetry}
            className="bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700 text-red-800 dark:text-red-200 px-3 py-1 rounded-md text-sm font-medium transition-colors"
          >
            {t('common.retry')}
          </button>
        </div>
      )}
    </div>
  );
};

export default ErrorNotification;