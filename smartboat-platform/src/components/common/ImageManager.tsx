import React, { useState, useRef } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useImages, useUploadImage, useDeleteImage } from '../../hooks/queries/useImageQueries';
import { imageService } from '../../services/imageService';
import AuthenticatedImage from './AuthenticatedImage';

interface ImageManagerProps {
  entityId: string;
}

interface ImageItem {
  id: string;
  entityId: string;
  fileName: string;
  contentType: string;
  uploadedDate: string;
  uploadedBy: string;
}

const ImageManager: React.FC<ImageManagerProps> = ({ entityId }) => {
  const { currentUser } = useAuth();
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // React Query hooks
  const { data, isLoading, isError } = useImages(entityId);
  const images = data?.payload ?? [];
  const uploadMutation = useUploadImage();
  const deleteMutation = useDeleteImage();

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFiles(files);
  };

  const handleFiles = async (files: File[]) => {
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) {
      alert('Please select image files only.');
      return;
    }

    // Upload files one by one
    for (const file of imageFiles) {
      try {
        await uploadMutation.mutateAsync({
          entityId,
          imageFile: file,
        });
      } catch (error) {
        console.error('Upload failed for file:', file.name, error);
        alert(`Failed to upload ${file.name}`);
      }
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleDeleteImage = async (imageId: string) => {
    if (window.confirm('Are you sure you want to delete this image?')) {
      try {
        await deleteMutation.mutateAsync(imageId);
      } catch (error) {
        console.error('Delete failed:', error);
        alert('Failed to delete image');
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileInput}
          className="hidden"
        />

        {uploadMutation.isPending ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mb-4"></div>
            <p className="text-gray-500">Uploading images...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <svg
              className="w-12 h-12 text-gray-400 mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 48 48"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              />
            </svg>
            <p className="text-lg font-medium text-gray-900 mb-2">Upload Images</p>
            <p className="text-gray-500 mb-4">
              Drag and drop images here, or{' '}
              <button
                onClick={handleUploadClick}
                className="text-blue-600 hover:text-blue-800 underline"
              >
                browse files
              </button>
            </p>
            <p className="text-sm text-gray-400">
              Supports: JPG, PNG, GIF (Max 5MB per file)
            </p>
          </div>
        )}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center py-8">
          <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
        </div>
      )}

      {/* Error State */}
      {isError && (
        <div className="text-center py-8">
          <p className="text-red-500">Failed to load images. Please try again.</p>
        </div>
      )}

      {/* Images Grid */}
      {!isLoading && !isError && images.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                <AuthenticatedImage
                  imageId={image.id}
                  alt={image.fileName}
                  fileName={image.fileName}
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-end justify-center pb-2">
                <button
                  onClick={() => handleDeleteImage(image.id)}
                  className="bg-red-500 hover:bg-red-600 text-white p-2 rounded-full shadow-lg"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-300 truncate">{image.fileName}</p>
            </div>
          ))}
        </div>
      ) : (
        !isLoading && !isError && (
          <div className="text-center py-8">
            <p className="text-gray-500">No images uploaded yet.</p>
          </div>
        )
      )}
    </div>
  );
};

export default ImageManager;