import React from 'react';

interface StatusBadgeProps {
  status?: string;
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className = '' }) => {
  let colorClasses = '';

  // Default to 'unknown' if status is undefined or null
  const statusValue = status ? status.toLowerCase() : 'unknown';

  switch (statusValue) {
    case 'active':
      colorClasses = 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      break;
    case 'warning':
      colorClasses = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      break;
    case 'critical':
    case 'error':
      colorClasses = 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      break;
    case 'maintenance':
      colorClasses = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      break;
    case 'pending':
      colorClasses = 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      break;
    default:
      colorClasses = 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
  }

  return (
    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${colorClasses} ${className}`}>
      {status || 'Unknown'}
    </span>
  );
};

export default StatusBadge;