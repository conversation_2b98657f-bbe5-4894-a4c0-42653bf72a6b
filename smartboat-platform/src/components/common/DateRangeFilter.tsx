import React, { useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useTranslation } from 'react-i18next';

interface DateRangeFilterProps {
  startDate: Date | null;
  endDate: Date | null;
  onDateChange: (start: Date | null, end: Date | null) => void;
  maxDate?: Date;
  minDate?: Date;
}

type PresetOption = 'day' | 'week' | 'month' | 'custom';

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  startDate,
  endDate,
  onDateChange,
  maxDate = new Date(),
  minDate
}) => {
  const { t } = useTranslation();
  const [selectedPreset, setSelectedPreset] = useState<PresetOption>('day');
  const [showCustomRange, setShowCustomRange] = useState(false);

  const handlePresetChange = (preset: PresetOption) => {
    setSelectedPreset(preset);
    
    if (preset === 'custom') {
      setShowCustomRange(true);
      return;
    }
    
    setShowCustomRange(false);
    const now = new Date();
    let start: Date;
    
    switch (preset) {
      case 'day':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
    
    onDateChange(start, now);
  };

  const handleStartDateChange = (date: Date | null) => {
    onDateChange(date, endDate);
  };

  const handleEndDateChange = (date: Date | null) => {
    onDateChange(startDate, date);
  };

  return (
    <div className="flex items-center space-x-4 mb-4">
      <div className="flex space-x-2">
        <button
          onClick={() => handlePresetChange('day')}
          className={`px-3 py-1 text-sm rounded-md transition-colors ${
            selectedPreset === 'day'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          {t('common.last24Hours')}
        </button>
        <button
          onClick={() => handlePresetChange('week')}
          className={`px-3 py-1 text-sm rounded-md transition-colors ${
            selectedPreset === 'week'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          {t('common.last7Days')}
        </button>
        <button
          onClick={() => handlePresetChange('month')}
          className={`px-3 py-1 text-sm rounded-md transition-colors ${
            selectedPreset === 'month'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          {t('common.last30Days')}
        </button>
        <button
          onClick={() => handlePresetChange('custom')}
          className={`px-3 py-1 text-sm rounded-md transition-colors ${
            selectedPreset === 'custom'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          {t('common.customRange')}
        </button>
      </div>

      {showCustomRange && (
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">{t('common.from')}:</span>
          <DatePicker
            selected={startDate}
            onChange={handleStartDateChange}
            selectsStart
            startDate={startDate}
            endDate={endDate}
            maxDate={endDate || maxDate}
            minDate={minDate}
            showTimeSelect
            timeFormat="HH:mm"
            timeIntervals={15}
            dateFormat="dd/MM/yyyy HH:mm"
            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholderText={t('common.selectDate')}
          />
          <span className="text-sm text-gray-600 dark:text-gray-400">{t('common.to')}:</span>
          <DatePicker
            selected={endDate}
            onChange={handleEndDateChange}
            selectsEnd
            startDate={startDate}
            endDate={endDate}
            minDate={startDate}
            maxDate={maxDate}
            showTimeSelect
            timeFormat="HH:mm"
            timeIntervals={15}
            dateFormat="dd/MM/yyyy HH:mm"
            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholderText={t('common.selectDate')}
          />
        </div>
      )}
    </div>
  );
};

export default DateRangeFilter;