/**
 * GlobalLoadingOverlay Component
 * Displays a global loading overlay when API calls are in progress
 */

import { useEffect, useState } from 'react';
import { useApi } from '../../providers/ApiProvider';

const GlobalLoadingOverlay = () => {
  const { isLoading } = useApi();
  const [visible, setVisible] = useState(false);
  
  // Delay showing the loader to prevent flashing for quick operations
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    
    if (isLoading) {
      timeout = setTimeout(() => {
        setVisible(true);
      }, 300);
    } else {
      setVisible(false);
    }
    
    return () => {
      clearTimeout(timeout);
    };
  }, [isLoading]);
  
  if (!visible) return null;
  
  return (
    <div className="fixed inset-0 bg-gray-900/50 dark:bg-black/60 z-50 flex items-center justify-center backdrop-blur-sm transition-opacity">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl flex flex-col items-center">
        <div className="animate-spin w-12 h-12 border-4 border-blue-500 dark:border-blue-400 border-t-transparent rounded-full mb-4"></div>
        <p className="text-gray-700 dark:text-gray-200 font-medium">Loading...</p>
      </div>
    </div>
  );
};

export default GlobalLoadingOverlay;