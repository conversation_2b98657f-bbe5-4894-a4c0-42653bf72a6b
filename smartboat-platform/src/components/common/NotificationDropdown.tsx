import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { 
  useNotifications, 
  useNotificationStats, 
  useMarkNotificationAsRead, 
  useMarkAllNotificationsAsRead 
} from '../../hooks/queries/useNotificationQueries';
import { Notification } from '../../types/notification';
import { formatTimestamp } from '../../utils/dateUtils';

interface NotificationDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // Fetch notifications and stats
  const { data: notifications = [], refetch } = useNotifications({
    pageLimit: 10,
    useMockFallback: true
  });
  
  const { data: stats } = useNotificationStats({
    useMockFallback: true
  });
  
  const markAsRead = useMarkNotificationAsRead();
  const markAllAsRead = useMarkAllNotificationsAsRead();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read if unread
    if (!notification.isRead) {
      await markAsRead.mutateAsync(notification.id);
    }
    
    // Navigate to action URL if available
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
    
    onClose();
  };

  const handleMarkAllAsRead = async () => {
    await markAllAsRead.mutateAsync();
    refetch();
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'error':
        return (
          <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full"></div>
        );
      case 'warning':
        return (
          <div className="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full"></div>
        );
      case 'success':
        return (
          <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full"></div>
        );
      case 'alert':
        return (
          <div className="flex-shrink-0 w-2 h-2 bg-orange-500 rounded-full"></div>
        );
      default:
        return (
          <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"></div>
        );
    }
  };


  if (!isOpen) return null;

  return (
    <div 
      ref={dropdownRef}
      className="absolute right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
    >
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('notifications.title')}
          </h3>
          {stats && stats.unread > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              disabled={markAllAsRead.isPending}
            >
              {markAllAsRead.isPending ? t('notifications.markingAllRead') : t('notifications.markAllRead')}
            </button>
          )}
        </div>
        {stats && (
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {stats.unread > 0 
              ? t('notifications.unreadCountOnly', { count: stats.unread })
              : t('notifications.allRead')
            }
          </p>
        )}
      </div>

      {/* Notifications List */}
      <div className="max-h-96 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="px-4 py-8 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 17h5l-5 5l-5-5h5v-5h0V7a3 3 0 116 0v5z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              {t('notifications.noNotifications')}
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {t('notifications.noNotificationsMessage')}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                  !notification.isRead ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                }`}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start space-x-3">
                  {getNotificationIcon(notification.type)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className={`text-sm font-medium ${
                        !notification.isRead 
                          ? 'text-gray-900 dark:text-white' 
                          : 'text-gray-600 dark:text-gray-300'
                      }`}>
                        {notification.title}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 ml-2">
                        {formatTimestamp(notification.timestamp)}
                      </p>
                    </div>
                    <p className={`text-sm mt-1 ${
                      !notification.isRead 
                        ? 'text-gray-700 dark:text-gray-200' 
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {notification.message}
                    </p>
                    {notification.actionText && (
                      <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                        {notification.actionText}
                      </p>
                    )}
                  </div>
                  {!notification.isRead && (
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-1"></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

    </div>
  );
};

export default NotificationDropdown;