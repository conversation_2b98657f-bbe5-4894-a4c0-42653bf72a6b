import React from 'react';
import { useTranslation } from 'react-i18next';

interface LoadingOverlayProps {
  message?: string;
  subMessage?: string;
  fullScreen?: boolean;
  contentOnly?: boolean; // New option to only cover content area
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ 
  message,
  subMessage,
  fullScreen = false,
  contentOnly = false
}) => {
  const { t } = useTranslation();
  const defaultMessage = t('common.loading');

  // Determine positioning and styling based on props
  const getPositionClass = () => {
    if (contentOnly) {
      return 'fixed top-16 left-64 right-0 bottom-0 z-[60]'; // Position below header and beside sidebar
    }
    if (fullScreen) {
      return 'fixed inset-0 z-[60]';
    }
    return 'absolute inset-0 z-40';
  };

  const getBackgroundClass = () => {
    if (contentOnly || fullScreen) {
      return 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm';
    }
    return 'bg-gray-100';
  };

  return (
    <div className={`${getPositionClass()} ${getBackgroundClass()}`}>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mb-4"></div>
          <p className="text-gray-700 dark:text-gray-200 font-medium text-lg">{message || defaultMessage}</p>
          {subMessage && (
            <p className="text-gray-500 dark:text-gray-400 text-sm mt-2">{subMessage}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoadingOverlay;
