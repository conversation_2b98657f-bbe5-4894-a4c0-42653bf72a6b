# Common Components

This directory contains reusable components that are shared across multiple feature domains in the application.

## Error Notification Components

There are two error notification components:

### 1. ApiErrorNotification

- Automatically displays API errors from the ApiProvider context
- Used for global API error handling
- No props needed - gets errors from context
- Auto-dismisses errors after 5 seconds
- Displayed as a notification in the bottom right corner

Example usage:
```jsx
<ApiErrorNotification />
```

### 2. ErrorNotification

- Standalone component for displaying specific errors in components
- Used for localized error handling within components
- Supports retry functionality
- Displays inline within a component
- Requires explicit error information

Example usage:
```tsx
import { ErrorNotification, ApiError } from '../common';

// In your component
{isError && (
  <ErrorNotification 
    message="Failed to load data" 
    onRetry={refetch}
    error={error}
  />
)}
```

## Loading Components

### 1. LoadingOverlay

- Full-screen or container-based overlay with spinner
- Used for blocking UI during important operations

### 2. LoadingScreen

- Dedicated loading screen for initial app or page load

## Input and Display Components

- **StatusBadge**: For displaying status (Active, Inactive, etc.)
- **SensorChart**: For rendering sensor data visualizations
- **VesselMap**: For displaying vessel geographic data
- **ThemeToggle**: For toggling between light and dark themes
- **LanguageSwitcher**: For changing application language

## Best Practices

1. Prefer using the dedicated error components over custom error UI
2. Use LoadingOverlay for operations that block UI
3. Always ensure proper TypeScript typing when passing errors
4. Dark mode is supported via appropriate className variants