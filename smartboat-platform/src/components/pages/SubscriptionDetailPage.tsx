import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Subscription } from '../../types';
import { useAuth } from '../../context/AuthContext';
import { hasPermission } from '../../utils/authUtils';
import { useTranslation } from 'react-i18next';
import EditSubscriptionForm from '../features/subscriptions/EditSubscriptionForm';
import { formatDateRange } from '../../utils/dateUtils';
import { useSubscription } from '../../hooks/queries/useSubscriptionQueries';
import { LoadingOverlay, ErrorNotification } from '../common';

/**
 * SubscriptionDetailPage displays details of a specific subscription.
 * It provides information about the subscription plan, status, and related data.
 */
const SubscriptionDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  const { t } = useTranslation();

  const [isEditMode, setIsEditMode] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(location.state?.editMode || false);

  // Get subscription ID from URL parameter (as string for UUID)
  const subscriptionId = id;

  // Fetch subscription data using React Query
  const {
    data: subscription,
    isLoading: loading,
    isError,
    error,
    refetch
  } = useSubscription(subscriptionId, {
    useMockFallback: false,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false
  });

  // Check permissions when subscription data is loaded
  useEffect(() => {
    if (subscription && currentUser) {
      // Check if the user has permission to view this subscription
      const canView =
        currentUser?.role === 'Administrator' ||
        // Manager with permission
        (currentUser?.role === 'Manager' && hasPermission(currentUser, 'viewAllSubscriptions')) ||
        // Customer viewing their own subscription
        (currentUser?.role === 'Customer' &&
          (currentUser.customerId === subscription.customerId ||
           subscription.userIds?.includes(currentUser.id)));

      if (!canView) {
        // No permission
        console.error('Permission denied to view this subscription');
        navigate('/subscriptions');
      }
    }
  }, [subscription, currentUser, navigate]);

  const handleBack = () => {
    navigate('/subscriptions');
  };

  const handleEdit = () => {
    setIsEditModalOpen(true);
  };

  const handleSave = (updatedSubscription: Subscription) => {
    // In a real app, this would be an API call
    setSubscription(updatedSubscription);
    setIsEditModalOpen(false);
  };

  const handleCancel = () => {
    setIsEditModalOpen(false);
  };

  if (loading) {
    return <LoadingOverlay />;
  }

  if (isError) {
    return (
      <div className="container mx-auto p-6">
        <ErrorNotification 
          message={t('subscriptions.errorLoadingSubscription')} 
          onRetry={refetch}
          error={error}
        />
        <div className="mt-4">
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            {t('subscriptions.backToSubscriptions')}
          </button>
        </div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="container mx-auto p-6">
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300">{t('subscriptions.subscriptionNotFound')}</h2>
          <p className="mt-2 text-gray-500 dark:text-gray-400">{t('subscriptions.subscriptionNotFoundMessage')}</p>
          <button
            onClick={handleBack}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            {t('subscriptions.backToSubscriptions')}
          </button>
        </div>
      </div>
    );
  }

  // We're not using a separate edit page anymore - using modal instead

  return (
    <div className="container mx-auto p-6">
      {/* Back button */}
      <button
        onClick={handleBack}
        className="mb-4 flex items-center text-sm text-blue-600 hover:text-blue-800"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
        {t('subscriptions.backToSubscriptions')}
      </button>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        {/* Header section */}
        <div className="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
          <h1 className="text-xl font-semibold text-gray-800">{subscription.name}</h1>
          <span className={`px-3 py-1 rounded-full text-xs font-medium
            ${subscription.status === 'Active' ? 'bg-green-100 text-green-800' :
            subscription.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
            subscription.status === 'Expired' ? 'bg-red-100 text-red-800' :
            'bg-gray-100 text-gray-800'}`}>
            {subscription.status}
          </span>
        </div>

        {/* Main content */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left column */}
            <div>
              <h2 className="text-lg font-medium mb-4">{t('subscriptions.subscriptionDetails')}</h2>

              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">{t('common.type')}</p>
                  <p className="font-medium">{subscription.type}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500">{t('subscriptions.billingFrequency')}</p>
                  <p className="font-medium">{subscription.billingFrequency}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500">{t('subscriptions.price')}</p>
                  <p className="font-medium">${subscription.price}/{subscription.billingFrequency === 'Monthly' ? 'mo' : subscription.billingFrequency === 'Quarterly' ? 'qtr' : 'yr'}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500">{t('subscriptions.subscriptionPeriod')}</p>
                  <p className="font-medium">{formatDateRange(subscription.startDate, subscription.endDate)}</p>
                </div>

                {currentUser?.role !== 'Customer' && (
                  <div>
                    <p className="text-sm text-gray-500">{t('common.customers')}</p>
                    <p className="font-medium">{subscription.customerName}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Right column */}
            <div>
              <h2 className="text-lg font-medium mb-4">{t('subscriptions.features')}</h2>

              <ul className="space-y-2">
                {subscription.features?.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>

              {/* Additional features or information can be added here */}
              <div className="mt-8">
                <h3 className="text-md font-medium mb-2">{t('subscriptions.paymentInformation')}</h3>
                <p className="text-sm text-gray-600">{t('subscriptions.nextBillingDate')}: {subscription.nextBillingDate || 'N/A'}</p>
                <p className="text-sm text-gray-600">{t('subscriptions.paymentMethod')}: {subscription.paymentMethod || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Bottom actions */}
          <div className="mt-8 pt-4 border-t border-gray-200 flex justify-end">
            {(currentUser?.role === 'Administrator' ||
              (currentUser?.role === 'Customer' &&
              hasPermission(currentUser, 'changeOwnSubscription') &&
              (subscription.userIds?.includes(currentUser.id) ||
              subscription.customerId === currentUser.customerId))) && (
              <button
                onClick={handleEdit}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                {t('subscriptions.editSubscription')}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Edit Subscription Slideover */}
      {subscription && (
        <>
          <div
            className={`fixed inset-y-0 right-0 bg-white shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
              isEditModalOpen ? 'translate-x-0' : 'translate-x-full'
            }`}
            style={{ width: '30%' }}
          >
            <div className="h-full">
              {isEditModalOpen && (
                <EditSubscriptionForm
                  subscription={subscription}
                  onSave={handleSave}
                  onCancel={handleCancel}
                />
              )}
            </div>
          </div>

          {/* Backdrop */}
          {isEditModalOpen && (
            <div
              className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
              onClick={handleCancel}
            ></div>
          )}
        </>
      )}
    </div>
  );
};

export default SubscriptionDetailPage;