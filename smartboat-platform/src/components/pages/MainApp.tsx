import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

// MainApp is now just a simple redirector since routes are defined in App.jsx
const MainApp: React.FC = () => {
  const { isAuthenticated } = useAuth();

  // If authenticated, redirect to dashboard, otherwise to login
  return isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />;
};

export default MainApp;