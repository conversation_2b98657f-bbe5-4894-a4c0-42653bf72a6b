import React, { useEffect } from 'react';
import { Dashboard } from '../features/dashboard';
import { useLocation } from 'react-router-dom';

/**
 * DashboardPage is a wrapper for the Dashboard component that ensures
 * proper navigation and loading behavior. This component explicitly
 * marks the dashboard as the current page in localStorage to avoid
 * navigation conflicts.
 */
const DashboardPage: React.FC = () => {
  const location = useLocation();

  // When the dashboard page is loaded, explicitly set it as the current path
  // This prevents redirection loops with back navigation
  useEffect(() => {
    // Check if we're already on the dashboard page (not being redirected)
    if (location.pathname === '/dashboard') {
      // Clear any previous paths or explicitly set dashboard as current
      localStorage.setItem('smartboat_last_path', '/dashboard');
      // Set a flag indicating this was an explicit dashboard navigation
      sessionStorage.setItem('dashboard_explicit', 'true');
    }
  }, [location]);

  return <Dashboard />;
};

export default DashboardPage;