import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { StatusBadge, LoadingOverlay, ErrorNotification } from '../common';
import { Owner } from '../../types';
import { useAuth } from '../../context/AuthContext';
import EditOwnerForm from '../features/owners/EditOwnerForm';
import { useOwner, useDeleteOwner } from '../../hooks/queries/useOwnerQueries';
import { useNotifications } from '../../hooks/queries/useNotificationQueries';
import { useVesselsByOwner } from '../../hooks/queries/useVesselQueries';

const OwnerDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  const { t } = useTranslation();
  
  // Keep ownerId as string for UUID support
  const ownerId = id;
  
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false); // Add state for dragging
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const deleteOwner = useDeleteOwner();

  const isAdmin = currentUser?.role === 'Administrator' || currentUser?.role === 'Manager';

  // Fetch owner data using React Query
  const {
    data: owner,
    isLoading: ownerLoading,
    isError: ownerError,
    error,
    refetch
  } = useOwner(ownerId, {
    useMockFallback: true,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false
  });

  // Fetch recent notifications for the current user
  const {
    data: ownerNotifications = [],
    isLoading: notificationsLoading
  } = useNotifications({
    pageLimit: 5, // Show only recent 5 notifications
    useMockFallback: true
  });

  // Fetch vessels for this owner
  const {
    data: ownerVessels = [],
    isLoading: vesselsLoading,
    isError: vesselsError
  } = useVesselsByOwner(ownerId, {
    useMockFallback: true,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false
  });

  // Helper function to get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'error':
        return (
          <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center text-red-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'warning':
        return (
          <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'success':
        return (
          <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  // Helper function to format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return t('notifications.justNow');
    if (diffMins < 60) return t('notifications.minutesAgo', { count: diffMins });
    if (diffHours < 24) return t('notifications.hoursAgo', { count: diffHours });
    if (diffDays < 7) return t('notifications.daysAgo', { count: diffDays });
    
    return date.toLocaleDateString();
  };
  
  // Check if we should open edit modal based on location state
  useEffect(() => {
    if (location.state && location.state.editMode) {
      setIsEditModalOpen(true);
    }
  }, [location.state]);
  
  // Handle owner update
  const handleSaveOwner = (updatedOwner: Owner) => {
    setIsEditModalOpen(false);
    // Refetch owner data to get the latest from the API
    refetch();
    console.log('Owner updated:', updatedOwner);
  };

  // Handle owner deletion
  const handleDeleteOwner = async () => {
    try {
      await deleteOwner.mutateAsync(ownerId);
      setShowDeleteConfirm(false);
      navigate('/owners');
    } catch (error) {
      console.error('Failed to delete owner:', error);
      alert(t('owners.deleteFailed'));
    }
  };

  // Improved resize handler with isDragging state
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);
    
    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;
      
      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);
      
      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };
    
    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };
    
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  // Loading state
  if (ownerLoading) {
    return (
      <div className="container mx-auto relative">
        <LoadingOverlay 
          message="Loading owner data..."
          subMessage="Fetching owner details..."
        />
      </div>
    );
  }

  // Error state
  if (ownerError) {
    return (
      <div className="container mx-auto">
        <ErrorNotification
          message={t('common.errorFetchingData')}
          error={error}
          onRetry={refetch}
        />
      </div>
    );
  }

  // Owner not found
  if (!owner) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">{t('owners.ownerNotFound')}</h3>
        <Link
          to="/owners"
          className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md inline-block"
        >
          {t('owners.backToOwners')}
        </Link>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <Link
            to="/owners"
            className="flex items-center text-blue-600 hover:text-blue-800"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            {t('owners.backToOwners')}
          </Link>
          <h1 className="text-2xl font-semibold mt-2">{owner.name}</h1>
          <div className="flex items-center mt-1">
            <span className="text-gray-600 mr-2">{t('common.status')}:</span>
            <StatusBadge status={owner.status} />
          </div>
        </div>
        {isAdmin && (
          <div className="flex space-x-3">
            <button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
              onClick={() => setIsEditModalOpen(true)}
            >
              {t('owners.editOwner')}
            </button>
            <button
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md"
              onClick={() => setShowDeleteConfirm(true)}
            >
              {t('owners.deleteOwner')}
            </button>
          </div>
        )}
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex">
            <button
              className={`py-4 px-6 ${
                activeTab === 'overview'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              {t('common.overview')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'vessels'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('vessels')}
            >
{t('vessels.vesselsList')} ({ownerVessels.length})
            </button>
          </nav>
        </div>
        
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Owner Details */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-4">{t('owners.ownerInformation')}</h4>

                  <div className="space-y-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.name')}:</span>
                      <span className="text-gray-900 font-medium">{owner.name}</span>
                    </div>

                    {owner.email && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">{t('common.email')}:</span>
                        <span className="text-gray-900 font-medium">{owner.email}</span>
                      </div>
                    )}

                    {owner.phone && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">{t('common.phone')}:</span>
                        <span className="text-gray-900 font-medium">{owner.phone}</span>
                      </div>
                    )}

                    {owner.address && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">{t('owners.address')}:</span>
                        <span className="text-gray-900 font-medium">{owner.address}</span>
                      </div>
                    )}

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.company')}:</span>
                      <span className="text-gray-900 font-medium">
                        {owner.company?.name || t('common.unknown')}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.status')}:</span>
                      <span>
                        <StatusBadge status={owner.status} />
                      </span>
                    </div>

                    {owner.created && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">{t('profile.joined')}:</span>
                        <span className="text-gray-900 font-medium">
                          {new Date(owner.created).toLocaleDateString('el-GR', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric'
                          })}
                        </span>
                      </div>
                    )}

                    {owner.lastUpdated && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">{t('vessels.lastUpdated')}:</span>
                        <span className="text-gray-900 font-medium">
                          {new Date(owner.lastUpdated).toLocaleString('el-GR', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Stats and Activity */}
              <div className="md:col-span-2">
                <h4 className="font-medium text-gray-900 mb-4">{t('customers.recentActivity')}</h4>
                
                {/* Recent Activity */}
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <h5 className="font-medium text-gray-900 dark:text-white mb-4">{t('customers.recentActivity')}</h5>
                  
                  {notificationsLoading ? (
                    <div className="space-y-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex animate-pulse">
                          <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                          <div className="ml-3 flex-1">
                            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4 mb-1"></div>
                            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/4"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : ownerNotifications.length > 0 ? (
                    <div className="space-y-4">
                      {ownerNotifications.map((notification: any) => (
                        <div key={notification.id} className="flex">
                          <div className="flex-shrink-0">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-gray-900 dark:text-gray-300">
                              {notification.title && (
                                <span className="font-medium">{notification.title}</span>
                              )}
                              {notification.message && notification.title && ' - '}
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {formatTimestamp(notification.timestamp)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-sm text-gray-500 dark:text-gray-400">{t('notifications.noNotifications')}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'vessels' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white">{t('vessels.vesselsList')}</h4>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {ownerVessels.length} {t('vessels.vesselsList').toLowerCase()}
                </span>
              </div>

              {vesselsLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="animate-pulse">
                      <div className="flex space-x-4">
                        <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : vesselsError ? (
                <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                        {t('vessels.errorLoadingVessels')}
                      </h3>
                      <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                        <p>{t('common.errorFetchingData')}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : ownerVessels.length > 0 ? (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-100 dark:bg-gray-900">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            {t('common.name')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            {t('common.type')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            {t('common.status')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            {t('common.location')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            {t('common.sensors')}
                          </th>
                          <th scope="col" className="relative px-6 py-3">
                            <span className="sr-only">{t('common.actions')}</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {ownerVessels.map((vessel) => (
                          <tr key={vessel.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10">
                                  <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                    <svg className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                  </div>
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                                    {vessel.name}
                                  </div>
                                  <div className="text-sm text-gray-500 dark:text-gray-400">
                                    #{vessel.number}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {vessel.type}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <StatusBadge status={vessel.status} />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {typeof vessel.location === 'string'
                                ? vessel.location
                                : vessel.location
                                  ? `${vessel.location.latitude.toFixed(4)}, ${vessel.location.longitude.toFixed(4)}`
                                  : vessel.homePort || t('vessels.locationUnknown')
                              }
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                {vessel.sensors} {t('common.sensors').toLowerCase()}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                onClick={() => navigate(`/vessels/${vessel.id}`)}
                              >
                                {t('common.view')}
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <svg className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    {t('vessels.noVesselsFound')}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {t('vessels.ownerHasNoVessels', { ownerName: owner?.name })}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Edit Owner Slideover */}
      <div 
        className={`fixed inset-y-0 right-0 bg-white shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isEditModalOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div 
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Drag to resize
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full">
          {isEditModalOpen && (
            <EditOwnerForm
              owner={owner}
              onSave={handleSaveOwner}
              onCancel={() => setIsEditModalOpen(false)}
            />
          )}
        </div>
      </div>
      
      {/* Backdrop */}
      {isEditModalOpen && (
        <div 
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={() => setIsEditModalOpen(false)}
        ></div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" onClick={() => setShowDeleteConfirm(false)}></div>
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 z-50 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t('owners.confirmDelete')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {t('owners.deleteWarning', { ownerName: owner?.name })}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleteOwner.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
                onClick={handleDeleteOwner}
                disabled={deleteOwner.isPending}
              >
                {deleteOwner.isPending ? t('common.deleting') : t('common.delete')}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default OwnerDetailPage;