import React, { useState } from "react";
import { useAuth } from "../../context/AuthContext";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import ForgotPasswordModal from "./ForgotPasswordModal";
import { LoginLanguageSwitcher, ThemeToggle } from "../common";
import smartboatLogo from "../../assets/smartboat-logo.png";

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  // Initialize rememberMe from stored preference in localStorage or sessionStorage
  const [rememberMe, setRememberMe] = useState(() => {
    // Try to get the preference from localStorage first (for long-term storage)
    const storedPreference =
      localStorage.getItem("smartboat_remember_me") ||
      sessionStorage.getItem("smartboat_remember_me");
    return storedPreference === "true";
  });
  const [isLocalLoading, setIsLocalLoading] = useState(false);
  const [error, setError] = useState("");
  const [isForgotPasswordOpen, setIsForgotPasswordOpen] = useState(false);
  const { login, isAuthenticated, isLoading } = useAuth();

  // If already authenticated, redirect to dashboard
  useEffect(() => {
    // Only redirect if we're authenticated AND not in a loading state
    if (isAuthenticated && !isLoading) {
      navigate("/dashboard", { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent any event bubbling

    if (!email || !password) {
      setError(t("auth.enterEmailPassword"));
      return;
    }

    setIsLocalLoading(true);
    setError(""); // Clear previous errors

    try {
      // Pass the rememberMe parameter to the login function
      const success = await login(email, password, rememberMe);

      if (success) {
        setError("");
        // The navigation will be handled by the useEffect hook above
      } else {
        setError(t("auth.invalidCredentials"));
      }
    } catch (err) {
      // Handle different error types
      if (err?.message && err.message.includes("401")) {
        setError(t("auth.invalidCredentials"));
      } else if (err?.message && err.message.includes("500")) {
        setError("Server error. Please try again later.");
      } else {
        setError(t("auth.loginError"));
      }
      console.error("Login error:", err);
    } finally {
      setIsLocalLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-900 px-4 sm:px-6 lg:px-8">
      {/* Language Switcher and Theme Toggle - absolute positioned at top right */}
      <div className="absolute top-4 right-4 flex items-center space-x-2">
        <ThemeToggle />
        <LoginLanguageSwitcher />
      </div>

      <div className="max-w-md w-full space-y-8">
        <div className="flex flex-col items-center">
          <div className="p-4 rounded-full bg-white dark:bg-white/90 flex items-center justify-center shadow-md">
            <img
              src={smartboatLogo}
              alt="SmartBoat Logo"
              className="h-24 w-auto"
            />
          </div>
          <p className="mt-4 text-center text-sm text-gray-600 dark:text-gray-400">
            {t("auth.signIn")}
          </p>
        </div>

        {error && (
          <div className="rounded-md bg-red-50 dark:bg-red-900/30 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
                  {error}
                </h3>
              </div>
            </div>
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email-address" className="sr-only">
                {t("auth.emailAddress")}
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder={t("auth.emailAddress")}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                {t("auth.password")}
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder={t("auth.password")}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-sm text-gray-900 dark:text-gray-300"
              >
                {t("auth.rememberMe")}
              </label>
            </div>

            <div className="text-sm">
              <button
                type="button"
                onClick={() => setIsForgotPasswordOpen(true)}
                className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
              >
                {t("auth.forgotPassword")}
              </button>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLocalLoading || isLoading}
              className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white ${
                isLocalLoading || isLoading
                  ? "bg-blue-400"
                  : "bg-blue-600 hover:bg-blue-700"
              } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
            >
              {isLocalLoading || isLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {t("auth.signingIn")}
                </>
              ) : (
                t("auth.signIn")
              )}
            </button>
          </div>

          {/* Demo user credentials */}
          <div className="mt-6">
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center mb-2">
              {t("auth.demoAccounts")}
            </p>
            <div className="grid grid-cols-2 gap-2">
              <button
                type="button"
                onClick={() => {
                  setEmail("<EMAIL>");
                  setPassword("admin123");
                }}
                className="text-xs py-1 px-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                {t("auth.adminUser")}
              </button>
              <button
                type="button"
                onClick={() => {
                  setEmail("<EMAIL>");
                  setPassword("admin123");
                }}
                className="text-xs py-1 px-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                {t("auth.customerUser")}
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Forgot Password Modal */}
      <ForgotPasswordModal
        isOpen={isForgotPasswordOpen}
        onClose={() => setIsForgotPasswordOpen(false)}
      />
    </div>
  );
};

export default LoginPage;
