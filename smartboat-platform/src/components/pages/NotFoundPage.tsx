import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const NotFoundPage: React.FC = () => {
  const location = useLocation();
  const { t } = useTranslation();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 px-4">
      <div className="text-center max-w-md">
        <h2 className="text-6xl font-bold text-blue-600 mb-4">{t('notFound.title')}</h2>
        <h3 className="text-2xl font-semibold text-gray-800 mb-4">{t('notFound.subtitle')}</h3>
        <p className="text-gray-600 mb-6">
          {t('notFound.message')}
        </p>
        <div className="space-x-4">
          <Link
            to="/dashboard"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            {t('notFound.backToDashboard')}
          </Link>
          <button
            onClick={() => window.history.back()}
            className="inline-block px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
          >
            {t('notFound.goBack')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;