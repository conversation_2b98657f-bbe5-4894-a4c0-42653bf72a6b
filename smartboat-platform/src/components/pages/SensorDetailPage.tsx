import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link, useLocation } from 'react-router-dom';
import { StatusBadge, <PERSON>sor<PERSON><PERSON>, DateRangeFilter } from '../common';
import { Sensor } from '../../types';
import { useAuth } from '../../context/AuthContext';
import { useTranslation } from 'react-i18next';
import EditSensorForm from '../features/sensors/EditSensorForm';
import { useSensor, useDeleteSensor, useSensorData } from '../../hooks/queries/useSensorQueries';

const SensorDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false); // Add state for dragging
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [historyTimeframe, setHistoryTimeframe] = useState('day');
  const [historyStartDate, setHistoryStartDate] = useState<Date | null>(new Date(Date.now() - 24 * 60 * 60 * 1000));
  const [historyEndDate, setHistoryEndDate] = useState<Date | null>(new Date());
  const deleteSensor = useDeleteSensor();
  
  // Use the sensor query hook to fetch sensor data by ID
  const { data: sensor, isLoading, isError, error } = useSensor(id, { useMockFallback: false });
  
  // Fetch sensor data for overview tab
  const {
    data: overviewData = [],
    isLoading: overviewLoading,
    isError: overviewError
  } = useSensorData(id, {
    timeframe: 'day',
    limit: 1 // Get most recent data point
  }, {
    enabled: !!id && activeTab === 'overview', // Only fetch when on overview tab
    useMockFallback: false
  });

  // Fetch sensor history data for the history tab
  const {
    data: historyData = [],
    isLoading: historyLoading,
    isError: historyError,
    refetch: refetchHistory
  } = useSensorData(id, {
    startTime: historyStartDate?.toISOString(),
    endTime: historyEndDate?.toISOString(),
    limit: 50 // Get more data for history table
  }, {
    enabled: !!id && activeTab === 'history', // Only fetch when on history tab
    useMockFallback: false
  });
  
  // Debug logging
  useEffect(() => {
    console.log('SensorDetailPage - ID from params:', id);
    console.log('SensorDetailPage - isLoading:', isLoading);
    console.log('SensorDetailPage - isError:', isError);
    console.log('SensorDetailPage - error:', error);
    console.log('SensorDetailPage - sensor data:', sensor);
  }, [id, isLoading, isError, error, sensor]);
  
  // Check if we should open edit modal based on location state
  useEffect(() => {
    if (location.state && location.state.editMode) {
      setIsEditModalOpen(true);
    }
  }, [location.state]);

  useEffect(() => {
    // If no ID provided, redirect to sensors list
    if (!id) {
      console.error('No sensor ID provided in URL parameters');
      navigate('/sensors');
    }
  }, [id, navigate]);

  // Check if the user can edit sensors via permissions
  const canEditSensors = useAuth().hasPermission('editSensors');

  // Handle sensor update
  const handleSaveSensor = (updatedSensor: Sensor) => {
    setIsEditModalOpen(false);
    // The query will automatically refetch after the mutation succeeds
    console.log('Sensor updated:', updatedSensor);
  };

  // Handle sensor deletion
  const handleDeleteSensor = async () => {
    try {
      await deleteSensor.mutateAsync(sensor.id);
      setShowDeleteConfirm(false);
      navigate('/sensors');
    } catch (error) {
      console.error('Failed to delete sensor:', error);
      alert(t('sensors.deleteFailed'));
    }
  };

  // Handle date range change
  const handleDateRangeChange = (start: Date | null, end: Date | null) => {
    setHistoryStartDate(start);
    setHistoryEndDate(end);
  };

  // Improved resize handler with isDragging state
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);

    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;

      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);

      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };

    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">{t('common.loading')}</h3>
      </div>
    );
  }

  if (isError || (!isLoading && !sensor)) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {isError ? t('sensors.errorLoadingSensor') : t('sensors.sensorNotFound')}
        </h3>
        {isError && (
          <div className="mb-4">
            <p className="text-gray-600 mb-2">
              {error?.message || 'Unknown error occurred'}
            </p>
            <details className="text-left bg-gray-100 p-3 rounded text-sm">
              <summary className="cursor-pointer font-medium">Debug Information</summary>
              <div className="mt-2">
                <p><strong>Sensor ID:</strong> {id || 'undefined'}</p>
                <p><strong>Error:</strong> {JSON.stringify(error, null, 2)}</p>
              </div>
            </details>
          </div>
        )}
        <Link
          to="/sensors"
          className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md inline-block"
        >
          {t('sensors.backToSensors')}
        </Link>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <Link
            to="/sensors"
            className="flex items-center text-blue-600 hover:text-blue-800"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            {t('sensors.backToSensors')}
          </Link>
          <h1 className="text-2xl font-semibold mt-2">{sensor.name}</h1>
          <div className="flex items-center mt-1">
            <span className="text-gray-600 mr-2">{t('common.status')}:</span>
            <StatusBadge status={sensor.status} />
          </div>
        </div>
        {canEditSensors && (
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
            onClick={() => setIsEditModalOpen(true)}
          >
            {t('sensors.editSensor')}
          </button>
        )}
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex">
            <button
              className={`py-4 px-6 ${
                activeTab === 'overview'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              {t('common.overview')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'history'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('history')}
            >
              {t('sensors.history')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'settings'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('settings')}
            >
              {t('sensors.settings')}
            </button>
          </nav>
        </div>
        
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Sensor Details */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-4">{t('sensors.sensorInformation')}</h4>
                  
                  <div className="space-y-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.id')}:</span>
                      <span className="text-gray-900 font-medium">SEN-{sensor.id.toString().padStart(4, '0')}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.type')}:</span>
                      <span className="text-gray-900 font-medium">{sensor.type}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.vessel')}:</span>
                      <span className="text-gray-900 font-medium">{sensor.vessel?.name || sensor.vesselId}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.location')}:</span>
                      <span className="text-gray-900 font-medium">{sensor.location}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('sensors.lastReading')}:</span>
                      <span className="text-gray-900 font-medium">
                        {overviewData?.length > 0 ? (
                          overviewData[0]?.Temperature ? `${overviewData[0].Temperature.toFixed(1)}°C` :
                          overviewData[0]?.temperature ? `${overviewData[0].temperature.toFixed(1)}°C` :
                          overviewData[0]?.Battery ? `${overviewData[0].Battery.toFixed(1)}%` :
                          overviewData[0]?.battery ? `${overviewData[0].battery.toFixed(1)}%` :
                          overviewData[0]?.PowerSupply ? `${overviewData[0].PowerSupply.toFixed(1)}V` :
                          overviewData[0]?.powerSupply ? `${overviewData[0].powerSupply.toFixed(1)}V` :
                          overviewData[0]?.GSMSignal ? `${overviewData[0].GSMSignal.toFixed(0)} dBm` :
                          overviewData[0]?.gsmSignal ? `${overviewData[0].gsmSignal.toFixed(0)} dBm` :
                          overviewData[0]?.pressure ? `${overviewData[0].pressure.toFixed(1)} MPa` :
                          overviewData[0]?.humidity ? `${Math.round(overviewData[0].humidity)}%` :
                          overviewData[0]?.Humidity ? `${Math.round(overviewData[0].Humidity)}%` :
                          overviewData[0]?.flowRate ? `${overviewData[0].flowRate} L/h` :
                          overviewData[0]?.value || 'N/A'
                        ) : (
                          overviewLoading ? t('common.loading') : sensor.lastReading
                        )}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('sensors.lastUpdated')}:</span>
                      <span className="text-gray-900 font-medium">
                        {overviewData?.length > 0 && overviewData[0]?.timestamp ? (
                          new Date(overviewData[0].timestamp).toLocaleString('el-GR', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })
                        ) : (
                          overviewLoading ? t('common.loading') : sensor.lastUpdated
                        )}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('sensors.alertThreshold')}:</span>
                      <span className="text-gray-900 font-medium">{sensor.alertThreshold}</span>
                    </div>
                  </div>
                </div>
                
              </div>
              
              {/* Sensor Data Chart */}
              <div className="md:col-span-2">
                <h4 className="font-medium text-gray-900 mb-2">{t('sensors.sensorReadings')}</h4>
                <SensorChart 
                  sensorId={sensor.id} 
                  vesselName={sensor.vessel?.name || 'Unknown Vessel'} 
                  location={sensor.location || 'Unknown Location'} 
                  timeframe="day"
                />
              </div>
            </div>
          )}
          
          {activeTab === 'history' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-4">{t('sensors.readingHistory')}</h4>
              
              {/* Date Range Filter */}
              <DateRangeFilter
                startDate={historyStartDate}
                endDate={historyEndDate}
                onDateChange={handleDateRangeChange}
              />
              
              {/* Sensor Data Chart */}
              <div className="bg-white border border-gray-200 rounded-md mb-6">
                <div className="p-4 pb-8">
                  <div className="w-full" style={{ height: '340px' }}>
                    <SensorChart 
                      sensorId={sensor.id} 
                      vesselName={sensor.vessel?.name || 'Unknown Vessel'} 
                      location={sensor.location || 'Unknown Location'} 
                      startDate={historyStartDate}
                      endDate={historyEndDate}
                    />
                  </div>
                </div>
              </div>
              
              {/* Readings Table */}
              <div className="bg-white border border-gray-200 rounded-md">
                <div className="p-4 border-b border-gray-200">
                  <h5 className="font-medium text-gray-900">{t('sensors.readings')}</h5>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('sensors.timestamp')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('sensors.value')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.status')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {historyLoading ? (
                        <tr>
                          <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                            {t('common.loading')}...
                          </td>
                        </tr>
                      ) : historyError ? (
                        <tr>
                          <td colSpan={3} className="px-6 py-4 text-center text-sm text-red-500">
                            {t('sensors.errorLoadingHistory')}
                          </td>
                        </tr>
                      ) : historyData?.length > 0 ? (
                        historyData.map((dataPoint, index) => (
                          <tr key={dataPoint.id || index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {dataPoint.timestamp ? new Date(dataPoint.timestamp).toLocaleString() : 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {dataPoint.Temperature ? `${dataPoint.Temperature.toFixed(1)}°C` :
                               dataPoint.temperature ? `${dataPoint.temperature.toFixed(1)}°C` :
                               dataPoint.Battery ? `${dataPoint.Battery.toFixed(1)}%` :
                               dataPoint.battery ? `${dataPoint.battery.toFixed(1)}%` :
                               dataPoint.PowerSupply ? `${dataPoint.PowerSupply.toFixed(1)}V` :
                               dataPoint.powerSupply ? `${dataPoint.powerSupply.toFixed(1)}V` :
                               dataPoint.GSMSignal ? `${dataPoint.GSMSignal.toFixed(0)} dBm` :
                               dataPoint.gsmSignal ? `${dataPoint.gsmSignal.toFixed(0)} dBm` :
                               dataPoint.pressure ? `${dataPoint.pressure.toFixed(1)} MPa` :
                               dataPoint.humidity ? `${Math.round(dataPoint.humidity)}%` :
                               dataPoint.Humidity ? `${Math.round(dataPoint.Humidity)}%` :
                               dataPoint.flowRate ? `${dataPoint.flowRate} L/h` :
                               dataPoint.value || 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <StatusBadge 
                                status={dataPoint.status || 'Active'} 
                              />
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                            {t('sensors.noHistoryData')}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
                <div className="p-4 border-t border-gray-200 flex justify-between items-center">
                  <div className="text-sm text-gray-700">
                    {historyData?.length > 0 ? (
                      <>
                        {t('common.showing')} <span className="font-medium">1</span> {t('common.to')} <span className="font-medium">{historyData.length}</span> {t('common.of')} <span className="font-medium">{historyData.length}</span> {t('sensors.readings')}
                      </>
                    ) : (
                      t('sensors.noHistoryData')
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                      {t('common.previous')}
                    </button>
                    <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                      {t('common.next')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'settings' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-4">{t('sensors.sensorSettings')}</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-4">{t('sensors.generalSettings')}</h5>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('sensors.sensorName')}
                        </label>
                        <input
                          type="text"
                          className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          defaultValue={sensor.name}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('sensors.type')}
                        </label>
                        <select className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                          <option selected={sensor.type === 'Temperature'}>Temperature</option>
                          <option selected={sensor.type === 'Pressure'}>Pressure</option>
                          <option selected={sensor.type === 'Humidity'}>Humidity</option>
                          <option selected={sensor.type === 'Flow Rate'}>Flow Rate</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('common.location')}
                        </label>
                        <input
                          type="text"
                          className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          defaultValue={sensor.location}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-4">{t('sensors.alertConfiguration')}</h5>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('sensors.alertThreshold')}
                        </label>
                        <input
                          type="text"
                          className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          defaultValue={sensor.alertThreshold}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('sensors.samplingRate')}
                        </label>
                        <select className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                          <option>{t('sensors.every5Minutes')}</option>
                          <option>{t('sensors.every15Minutes')}</option>
                          <option>{t('sensors.every30Minutes')}</option>
                          <option>{t('sensors.hourly')}</option>
                        </select>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          id="email-alerts"
                          name="email-alerts"
                          type="checkbox"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          defaultChecked
                        />
                        <label htmlFor="email-alerts" className="ml-2 block text-sm text-gray-900">
                          {t('sensors.enableEmailAlerts')}
                        </label>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          id="sms-alerts"
                          name="sms-alerts"
                          type="checkbox"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="sms-alerts" className="ml-2 block text-sm text-gray-900">
                          {t('sensors.enableSmsAlerts')}
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {canEditSensors && (
                <div className="mt-6 flex justify-between">
                  <button 
                    className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md"
                    onClick={() => setShowDeleteConfirm(true)}
                  >
                    {t('sensors.deleteSensor')}
                  </button>
                  <div className="flex space-x-3">
                    <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md">
                      {t('common.cancel')}
                    </button>
                    <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                      {t('common.saveChanges')}
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Edit Sensor Slideover */}
      <div
        className={`fixed inset-y-0 right-0 bg-white shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isEditModalOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {t('common.dragToResize')}
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full">
          {isEditModalOpen && (
            <EditSensorForm
              sensor={sensor}
              onSave={handleSaveSensor}
              onCancel={() => setIsEditModalOpen(false)}
            />
          )}
        </div>
      </div>

      {/* Backdrop */}
      {isEditModalOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={() => setIsEditModalOpen(false)}
        ></div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" onClick={() => setShowDeleteConfirm(false)}></div>
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 z-50 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t('sensors.confirmDelete')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {t('sensors.deleteWarning', { sensorName: sensor?.name })}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleteSensor.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
                onClick={handleDeleteSensor}
                disabled={deleteSensor.isPending}
              >
                {deleteSensor.isPending ? t('common.deleting') : t('common.delete')}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SensorDetailPage;