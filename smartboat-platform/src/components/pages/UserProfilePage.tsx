import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ProfileProvider,
  useProfile,
  ProfileHeader,
  ProfileNavigation,
  UserSubscriptions,
  SecuritySettings,
  NotificationSettings,
  LanguageSettings
} from '../features/profile';
import EmailProcessingSettings from '../features/profile/EmailProcessingSettings';
import { ProfileTab } from '../features/profile/types';

// This component manages tab state based on URL, not context
const ProfileContent: React.FC = () => {
  const profile = useProfile();
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Get tab from URL params
  const { tab } = useParams<{ tab?: string }>();

  // Local state for tab management, completely separate from context
  const [activeTab, setActiveTab] = useState<ProfileTab>('security');

  // Extract needed data from profile context
  const {
    userData,
    isEditing,
    userSubscriptions,
    setIsEditing,
    setUserData,
    handleChange,
    handleSubmit,
    getInitials,
    refetchNotifications,
    refetchSubscriptions
  } = profile;

  // Update local tab state when URL changes
  useEffect(() => {
    if (tab && ['subscriptions', 'security', 'notifications', 'language', 'emailProcessing'].includes(tab)) {
      setActiveTab(tab as ProfileTab);

      // Fetch data for the tab if needed
      if (tab === 'notifications' && userData?.id) {
        refetchNotifications();
      } else if (tab === 'subscriptions' && userData?.role !== 'Administrator') {
        refetchSubscriptions();
      }
    } else if (!tab) {
      // If no tab specified, redirect to security
      navigate('/profile/security', { replace: true });
    }
  }, [tab, navigate, userData, refetchNotifications, refetchSubscriptions]);

  // Tab change handler - only updates URL, not context
  const handleTabChange = (newTab: ProfileTab) => {
    if (newTab === activeTab) return;
    navigate(`/profile/${newTab}`);
  };

  // If userData is null, show loading state
  if (!userData) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{t('profile.userProfile')}</h1>
          <p className="text-gray-600 dark:text-gray-400">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  // Get current user from auth context
  const { currentUser } = useAuth();

  // Reset function for the cancel button
  const handleResetData = () => {
    if (currentUser) {
      setUserData(currentUser);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{t('profile.userProfile')}</h1>
        <p className="text-gray-600 dark:text-gray-400">{t('profile.personalInformation')}</p>
      </div>

      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
        {/* User profile header */}
        <ProfileHeader
          userData={userData}
          isEditing={isEditing}
          setIsEditing={setIsEditing}
          getInitials={getInitials}
          handleSubmit={handleSubmit}
          onResetData={handleResetData}
        />

        {/* Profile tabs */}
        <ProfileNavigation
          activeTab={activeTab}
          setActiveTab={handleTabChange}
          userRole={userData.role}
        />

        <div className="p-8">
          {/* Subscriptions tab content - only for non-admin users */}
          {activeTab === 'subscriptions' && userData?.role !== 'Administrator' && (
            <UserSubscriptions
              userSubscriptions={userSubscriptions}
              userData={userData}
            />
          )}

          {/* Security tab content */}
          {activeTab === 'security' && (
            <SecuritySettings
              userData={userData}
              isEditing={isEditing}
              setUserData={setUserData}
            />
          )}

          {/* Notifications tab content */}
          {activeTab === 'notifications' && (
            <NotificationSettings
              userData={userData}
              isEditing={isEditing}
              handleChange={handleChange}
            />
          )}

          {/* Language tab content */}
          {activeTab === 'language' && (
            <LanguageSettings
              userData={userData}
              isEditing={isEditing}
            />
          )}

          {/* Email Processing tab content */}
          {activeTab === 'emailProcessing' && (
            <EmailProcessingSettings
              userData={userData}
              isEditing={isEditing}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// Simple wrapper component that prevents premature navigation
const UserProfilePage: React.FC = () => {
  return (
    <ProfileProvider>
      <ProfileContent />
    </ProfileProvider>
  );
};

export default UserProfilePage;