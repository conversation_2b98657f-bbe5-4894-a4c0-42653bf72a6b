/**
 * API Provider Component
 * Sets up React Query and provides context for API interactions
 */

import { createContext, useContext, useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { FEATURES } from '../config/envConfig';

// Context for API state and utility functions
const ApiContext = createContext(null);

/**
 * API Provider component
 * Wraps application to provide API-related state and utilities
 */
export const ApiProvider = ({ children, initialIsConnected = true }) => {
  const queryClient = useQueryClient();
  const [isConnected, setIsConnected] = useState(initialIsConnected);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState([]);

  /**
   * Add an error to the error stack
   */
  const addError = (error) => {
    setErrors((prev) => [...prev, {
      id: Date.now(),
      message: error.message || 'An unknown error occurred',
      code: error.status || 500,
      timestamp: new Date().toISOString(),
    }]);
  };

  /**
   * Remove an error from the error stack by ID
   */
  const removeError = (errorId) => {
    setErrors((prev) => prev.filter((error) => error.id !== errorId));
  };

  /**
   * Clear all errors
   */
  const clearErrors = () => {
    setErrors([]);
  };

  /**
   * Reset the query cache and fetch fresh data
   */
  const refreshData = async () => {
    setIsLoading(true);
    
    try {
      // Clear the entire cache and refetch active queries
      await queryClient.invalidateQueries();
      clearErrors();
    } catch (error) {
      addError(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Reset loading state on mount to prevent stuck loading states
  useEffect(() => {
    setIsLoading(false);
  }, []);

  /**
   * Check if we're in mock data mode
   */
  const isMockMode = () => {
    return FEATURES.USE_MOCK_FALLBACK;
  };

  // Context value with state and functions
  const value = {
    isConnected,
    setIsConnected,
    isLoading,
    setIsLoading,
    errors,
    addError,
    removeError,
    clearErrors,
    refreshData,
    isMockMode,
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
};

/**
 * Hook to access the API context
 */
export const useApi = () => {
  const context = useContext(ApiContext);
  
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  
  return context;
};