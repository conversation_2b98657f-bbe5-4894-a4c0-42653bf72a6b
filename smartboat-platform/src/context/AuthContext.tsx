import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, UserRole } from '../types';
import { hasPermission, setAuthToken, clearAuthToken, getAuthToken } from '../utils/authUtils';
import authService from '../services/authService';

interface AuthContextProps {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean; // Added isLoading property
  hasPermission: (permission: string) => boolean;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<boolean>;
  logout: () => Promise<void>;
  updateUserRole: (role: UserRole) => void; // For development/testing
}


const AuthContext = createContext<AuthContextProps>({
  currentUser: null,
  isAuthenticated: false,
  isLoading: true, // Default to loading when context is created
  hasPermission: () => false,
  login: async () => false,
  logout: async () => {},
  updateUserRole: () => {}
});

export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: ReactNode;
}

// Note: Demo credentials are now handled by the real backend API
// <EMAIL> / admin123
// <EMAIL> / admin123

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true); // Add loading state

  // Check for saved authentication on mount
  useEffect(() => {
    const checkAuthentication = async () => {
      setIsLoading(true); // Set loading to true

      // First check localStorage (for "remember me" sessions)
      let savedUser = localStorage.getItem('smartboat_user');

      // If not in localStorage, check sessionStorage (for regular sessions)
      if (!savedUser) {
        savedUser = sessionStorage.getItem('smartboat_user');
      }

      if (savedUser) {
        try {
          setCurrentUser(JSON.parse(savedUser));
        } catch (error) {
          console.error('Failed to parse saved user:', error);
          // Clear from both storage types to be safe
          localStorage.removeItem('smartboat_user');
          sessionStorage.removeItem('smartboat_user');
          localStorage.removeItem('smartboat_remember_me');
          sessionStorage.removeItem('smartboat_remember_me');
        }
      }

      setIsLoading(false); // Set loading to false when done
    };

    checkAuthentication();
  }, []);

  // Listen for logout events from API client
  useEffect(() => {
    const handleLogoutEvent = () => {
      setCurrentUser(null);
      setIsLoading(false);
    };

    window.addEventListener('auth:logout', handleLogoutEvent);
    
    return () => {
      window.removeEventListener('auth:logout', handleLogoutEvent);
    };
  }, []);

  // Login function - calls the real API
  const login = async (email: string, password: string, rememberMe = false): Promise<boolean> => {
    try {
      setIsLoading(true); // Set loading state while logging in

      // Try to login with the real API first
      try {
        const response = await authService.login({ email, password });

        if (response && response.user && response.token) {
          // Convert API user response to our User type
          const user: User = {
            id: response.user.id,
            name: `${response.user.firstName || ''} ${response.user.lastName || ''}`.trim() || response.user.username || 'User',
            email: response.user.email,
            role: response.user.role === 'Administrator' || response.user.role === 'Super Admin' ? 'Administrator' : 'Customer', // Map Super Admin to Administrator
            avatar: response.user.avatar || '',
            joined: response.user.created || new Date().toISOString(),
            status: response.user.status || 'Active',
            lastLogin: response.user.lastLogin || new Date().toISOString(),
            twoFactorEnabled: response.user.twoFactorEnabled || false,
            notificationPreferences: {
              emailAlerts: true,
              smsAlerts: false,
              systemNotifications: true
            },
            subscriptionIds: [], // This would come from a separate API call
            socialLinks: {
              linkedin: '',
              twitter: '',
              github: ''
            },
            company: response.user.company || '',
            department: response.user.department || '',
            phone: response.user.phone || response.user.phoneNumber || '',
            timezone: response.user.timezone || 'UTC',
            language: response.user.language || 'en',
            bio: response.user.bio || ''
          };

          setCurrentUser(user);

          // Storage method - use localStorage for remember me, sessionStorage otherwise
          const storageMethod = rememberMe ? localStorage : sessionStorage;

          // Store user data
          storageMethod.setItem('smartboat_user', JSON.stringify(user));
          storageMethod.setItem('smartboat_remember_me', String(rememberMe));

          setIsLoading(false);
          return true;
        }
      } catch (apiError) {
        console.error('API login failed:', apiError);
        // No more fallback to mock credentials - API should work now
      }

      // If no match found, fail authentication
      setIsLoading(false);
      return false;
    } catch (error) {
      console.error('Login failed:', error);
      setIsLoading(false);
      return false;
    }
  };

  // Logout function
  const logout = async () => {
    setIsLoading(true); // Set loading state while logging out

    try {
      // Call the API logout endpoint
      await authService.logout();
    } catch (error) {
      console.warn('API logout failed:', error);
      // Continue with local logout even if API call fails
    }

    setCurrentUser(null);

    // Clear from both storage types to ensure complete logout
    localStorage.removeItem('smartboat_user');
    sessionStorage.removeItem('smartboat_user');
    localStorage.removeItem('smartboat_remember_me');
    sessionStorage.removeItem('smartboat_remember_me');

    setIsLoading(false);
  };

  // Check if user has a specific permission
  const checkPermission = (permission: string): boolean => {
    if (!currentUser) return false;

    return hasPermission(currentUser, permission as any);
  };

  // Update user role (for development/testing)
  const updateUserRole = (role: UserRole) => {
    if (!currentUser) return;

    const updatedUser = {
      ...currentUser,
      role
    };

    setCurrentUser(updatedUser);
    localStorage.setItem('smartboat_user', JSON.stringify(updatedUser));
  };

  return (
    <AuthContext.Provider
      value={{
        currentUser,
        isAuthenticated: !!currentUser,
        isLoading,
        hasPermission: checkPermission,
        login,
        logout,
        updateUserRole
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
