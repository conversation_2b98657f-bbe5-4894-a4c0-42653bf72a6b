/**
 * Data Fetching Custom Hook
 * Combines React Query with API context for simplified data fetching
 */

import { useCallback } from 'react';
import { useApi } from '../providers/ApiProvider';

/**
 * Custom hook to simplify data fetching with error handling
 */
const useDataFetching = () => {
  const { addError, setIsLoading } = useApi();

  /**
   * Executes a data fetching operation with automatic error handling
   * @param {Promise} fetchingPromise - The promise to execute
   * @param {Object} options - Options object
   * @param {boolean} options.showLoading - Whether to show the global loading indicator
   * @param {boolean} options.handleError - Whether to handle errors using the global error system
   * @param {Function} options.onError - Custom error handler
   * @param {Function} options.onSuccess - Success callback
   * @param {Function} options.onFinally - Callback to run when operation completes
   * @returns {Promise} - The result of the fetching operation
   */
  const fetchData = useCallback(
    async (fetchingPromise, options = {}) => {
      const {
        showLoading = true,
        handleError = true,
        onError,
        onSuccess,
        onFinally,
      } = options;

      try {
        // Show loading indicator if requested
        if (showLoading) {
          setIsLoading(true);
        }

        // Execute the fetch operation
        const result = await fetchingPromise;

        // Call success callback if provided
        if (onSuccess) {
          onSuccess(result);
        }

        return result;
      } catch (error) {
        // Handle error globally if requested
        if (handleError) {
          addError(error);
        }

        // Call custom error handler if provided
        if (onError) {
          onError(error);
        }

        throw error;
      } finally {
        // Hide loading indicator if it was shown
        if (showLoading) {
          setIsLoading(false);
        }

        // Call finally callback if provided
        if (onFinally) {
          onFinally();
        }
      }
    },
    [addError, setIsLoading]
  );

  return { fetchData };
};

export default useDataFetching;