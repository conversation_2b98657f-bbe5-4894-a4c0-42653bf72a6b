import { useQuery, useMutation, useQueryClient, QueryKey } from 'react-query';
import apiService from '../services/api';

// Vessels hooks
export const useVessels = () => {
  return useQuery('vessels', () => apiService.vessels.getAll());
};

export const useVessel = (id: string) => {
  return useQuery(['vessel', id], () => apiService.vessels.getById(id), {
    enabled: !!id
  });
};

export const useCreateVessel = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (vessel: any) => apiService.vessels.create(vessel),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('vessels');
      }
    }
  );
};

export const useUpdateVessel = () => {
  const queryClient = useQueryClient();
  return useMutation(
    ({ id, vessel }: { id: string; vessel: any }) => apiService.vessels.update(id, vessel),
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries('vessels');
        queryClient.invalidateQueries(['vessel', variables.id]);
      }
    }
  );
};

export const useDeleteVessel = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (id: string) => apiService.vessels.delete(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('vessels');
      }
    }
  );
};

// Companies hooks
export const useCompanies = () => {
  return useQuery('companies', () => apiService.companies.getAll());
};

export const useCompany = (id: string) => {
  return useQuery(['company', id], () => apiService.companies.getById(id), {
    enabled: !!id
  });
};

export const useCreateCompany = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (company: any) => apiService.companies.create(company),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('companies');
      }
    }
  );
};

export const useUpdateCompany = () => {
  const queryClient = useQueryClient();
  return useMutation(
    ({ id, company }: { id: string; company: any }) => apiService.companies.update(id, company),
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries('companies');
        queryClient.invalidateQueries(['company', variables.id]);
      }
    }
  );
};

export const useDeleteCompany = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (id: string) => apiService.companies.delete(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('companies');
      }
    }
  );
};

// Customers hooks
export const useCustomers = () => {
  return useQuery('customers', () => apiService.customers.getAll());
};

export const useCustomer = (id: string) => {
  return useQuery(['customer', id], () => apiService.customers.getById(id), {
    enabled: !!id
  });
};

export const useCreateCustomer = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (customer: any) => apiService.customers.create(customer),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('customers');
      }
    }
  );
};

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();
  return useMutation(
    ({ id, customer }: { id: string; customer: any }) => apiService.customers.update(id, customer),
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries('customers');
        queryClient.invalidateQueries(['customer', variables.id]);
      }
    }
  );
};

export const useDeleteCustomer = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (id: string) => apiService.customers.delete(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('customers');
      }
    }
  );
};

// Dashboard hooks
export const useDashboardStats = () => {
  return useQuery('dashboardStats', () => apiService.dashboard.getStats());
};

export const useRecentActivity = () => {
  return useQuery('recentActivity', () => apiService.dashboard.getRecentActivity());
};

export const useVesselSummaries = () => {
  return useQuery('vesselSummaries', () => apiService.dashboard.getVesselSummaries());
};

export const useSensorSummaries = () => {
  return useQuery('sensorSummaries', () => apiService.dashboard.getSensorSummaries());
};

// User hooks
export const useCurrentUser = () => {
  return useQuery('currentUser', () => apiService.user.getCurrentUser());
};

export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (userData: any) => apiService.user.updateProfile(userData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('currentUser');
      }
    }
  );
};

export const useUpdateUserPreferences = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (preferences: any) => apiService.user.updatePreferences(preferences),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('currentUser');
      }
    }
  );
};

export const useUserSubscriptions = () => {
  return useQuery('userSubscriptions', () => apiService.user.getSubscriptions());
};

export const useUpdatePassword = () => {
  return useMutation(
    ({ currentPassword, newPassword }: { currentPassword: string; newPassword: string }) => 
      apiService.user.updatePassword(currentPassword, newPassword)
  );
};

export const useToggleTwoFactorAuth = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (enabled: boolean) => apiService.user.toggleTwoFactorAuth(enabled),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('currentUser');
      }
    }
  );
};
