/**
 * User Query Hooks
 * Custom React Query hooks for user data
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userService } from '../../services';
import { queryKeys } from '../../config/queryClient';

/**
 * Hook to fetch all users (admin only)
 */
export const useUsers = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.users.lists(),
    queryFn: () => userService.getAllUsers(),
    ...options,
  });
};

/**
 * Hook to fetch a user by ID
 */
export const useUser = (id, options = {}) => {
  return useQuery({
    queryKey: queryKeys.users.detail(id),
    queryFn: () => userService.getUserById(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to create a new user (admin only)
 */
export const useCreateUser = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (userData) => userService.createUser(userData),
    onSuccess: () => {
      // Invalidate users list
      queryClient.invalidateQueries(queryKeys.users.lists());
    },
    ...options,
  });
};

/**
 * Hook to update a user
 */
export const useUpdateUser = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, userData }) => userService.updateUser(id, userData),
    onSuccess: (_, variables) => {
      // Invalidate specific user and list
      queryClient.invalidateQueries(queryKeys.users.detail(variables.id));
      queryClient.invalidateQueries(queryKeys.users.lists());
      
      // If updating current user, also update current user query
      const currentUser = queryClient.getQueryData(queryKeys.auth.currentUser());
      if (currentUser && currentUser.id === variables.id) {
        queryClient.invalidateQueries(queryKeys.auth.currentUser());
      }
    },
    ...options,
  });
};

/**
 * Hook to delete a user (admin only)
 */
export const useDeleteUser = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => userService.deleteUser(id),
    onSuccess: (_, id) => {
      // Invalidate users list
      queryClient.invalidateQueries(queryKeys.users.lists());
      
      // Remove user from cache
      queryClient.removeQueries(queryKeys.users.detail(id));
    },
    ...options,
  });
};

/**
 * Hook to update user role (admin only)
 */
export const useUpdateUserRole = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, role }) => userService.updateUserRole(id, role),
    onSuccess: (_, variables) => {
      // Invalidate specific user and list
      queryClient.invalidateQueries(queryKeys.users.detail(variables.id));
      queryClient.invalidateQueries(queryKeys.users.lists());
      
      // If updating current user, also update current user query
      const currentUser = queryClient.getQueryData(queryKeys.auth.currentUser());
      if (currentUser && currentUser.id === variables.id) {
        queryClient.invalidateQueries(queryKeys.auth.currentUser());
      }
    },
    ...options,
  });
};

/**
 * Hook to fetch user notification settings
 */
export const useUserNotificationSettings = (id, options = {}) => {
  return useQuery({
    queryKey: queryKeys.users.notifications(id),
    queryFn: () => userService.getUserNotificationSettings(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to update user notification settings
 */
export const useUpdateUserNotificationSettings = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, notificationSettings }) => userService.updateUserNotificationSettings(id, notificationSettings),
    onSuccess: (_, variables) => {
      // Invalidate notification settings
      queryClient.invalidateQueries(queryKeys.users.notifications(variables.id));
      
      // If updating current user, also update current user query
      const currentUser = queryClient.getQueryData(queryKeys.auth.currentUser());
      if (currentUser && currentUser.id === variables.id) {
        queryClient.invalidateQueries(queryKeys.auth.currentUser());
      }
    },
    ...options,
  });
};