/**
 * Notification Query Hooks
 * Custom React Query hooks for notification data
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import notificationService from '../../services/notificationService';
import { queryKeys } from '../../config/queryClient';

/**
 * Hook to fetch all notifications
 */
export const useNotifications = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.notifications.lists(options),
    queryFn: () => notificationService.getNotifications(options),
    staleTime: 1 * 60 * 1000, // 1 minute
    ...options,
  });
};

/**
 * Hook to fetch notification statistics
 */
export const useNotificationStats = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.notifications.stats(),
    queryFn: () => notificationService.getNotificationStats(options),
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

/**
 * Hook to fetch notification preferences
 */
export const useNotificationPreferences = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.notifications.preferences(),
    queryFn: () => notificationService.getPreferences(options),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook to mark notification as read
 */
export const useMarkNotificationAsRead = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => notificationService.markAsRead(id),
    onSuccess: () => {
      // Invalidate notifications list and stats
      queryClient.invalidateQueries(queryKeys.notifications.lists());
      queryClient.invalidateQueries(queryKeys.notifications.stats());
    },
    ...options,
  });
};

/**
 * Hook to mark all notifications as read
 */
export const useMarkAllNotificationsAsRead = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => notificationService.markAllAsRead(),
    onSuccess: () => {
      // Invalidate notifications list and stats
      queryClient.invalidateQueries(queryKeys.notifications.lists());
      queryClient.invalidateQueries(queryKeys.notifications.stats());
    },
    ...options,
  });
};

/**
 * Hook to delete notification
 */
export const useDeleteNotification = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => notificationService.deleteNotification(id),
    onSuccess: () => {
      // Invalidate notifications list and stats
      queryClient.invalidateQueries(queryKeys.notifications.lists());
      queryClient.invalidateQueries(queryKeys.notifications.stats());
    },
    ...options,
  });
};

/**
 * Hook to update notification preferences
 */
export const useUpdateNotificationPreferences = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (preferences) => notificationService.updatePreferences(preferences),
    onSuccess: () => {
      // Invalidate preferences
      queryClient.invalidateQueries(queryKeys.notifications.preferences());
    },
    ...options,
  });
};