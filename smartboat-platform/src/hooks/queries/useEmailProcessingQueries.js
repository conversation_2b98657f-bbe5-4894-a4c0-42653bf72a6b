/**
 * React Query hooks for Email Processing functionality
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import emailProcessingService from '../../services/emailProcessingService';

// Query key factory for email processing
export const emailProcessingKeys = {
  all: ['email-processing'],
  tokenStatus: () => [...emailProcessingKeys.all, 'token-status'],
  scheduleStatus: () => [...emailProcessingKeys.all, 'schedule-status'],
  summary: () => [...emailProcessingKeys.all, 'summary'],
};

/**
 * Hook to get Microsoft Graph token status and sync information
 */
export const useTokenStatus = (options = {}) => {
  return useQuery({
    queryKey: emailProcessingKeys.tokenStatus(),
    queryFn: () => emailProcessingService.getTokenStatus(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 60 * 1000, // 1 minute
    retry: 2,
    ...options,
  });
};

/**
 * Hook to exchange authorization code for tokens via server-side flow
 */
export const useExchangeToken = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (exchangeData) => emailProcessingService.exchangeToken(exchangeData),
    onSuccess: () => {
      // Invalidate and refetch token status after exchange
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.tokenStatus() });
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.summary() });
    },
    onError: (error) => {
      console.error('Failed to exchange Microsoft Graph token:', error);
    },
  });
};

/**
 * Hook to save Microsoft Graph authentication tokens
 */
export const useSaveToken = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (tokenData) => emailProcessingService.saveToken(tokenData),
    onSuccess: () => {
      // Invalidate and refetch token status after saving
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.tokenStatus() });
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.summary() });
    },
    onError: (error) => {
      console.error('Failed to save Microsoft Graph token:', error);
    },
  });
};

/**
 * Hook to get schedule status and next sync time
 */
export const useScheduleStatus = (options = {}) => {
  return useQuery({
    queryKey: emailProcessingKeys.scheduleStatus(),
    queryFn: () => emailProcessingService.getScheduleStatus(),
    staleTime: 30 * 1000, // 30 seconds - shorter stale time for more responsive UI updates
    retry: (failureCount, error) => {
      // Don't retry if it's a database setup issue
      if (error?.message?.includes('Technical Error') ||
          error?.message?.includes('Schedule configuration is not available')) {
        return false;
      }
      return failureCount < 2;
    },
    ...options,
  });
};

/**
 * Hook to get processing summary with sync history
 */
export const useProcessingSummary = (options = {}) => {
  return useQuery({
    queryKey: emailProcessingKeys.summary(),
    queryFn: () => emailProcessingService.getProcessingSummary(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry if it's a database setup issue
      if (error?.message?.includes('Technical Error') ||
          error?.message?.includes('Unable to retrieve processing summary')) {
        return false;
      }
      return failureCount < 2;
    },
    ...options,
  });
};

/**
 * Hook to trigger manual email processing and sync
 */
export const useProcessEmails = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (processOptions = { daysBack: 7, forceReprocess: false }) =>
      emailProcessingService.processEmails(processOptions),
    onSuccess: () => {
      // Invalidate and refetch relevant queries after processing
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.summary() });
      queryClient.invalidateQueries({ queryKey: emailProcessingKeys.scheduleStatus() });
    },
    onError: (error) => {
      console.error('Failed to process emails:', error);
    },
  });
};

/**
 * Hook to update schedule configuration
 */
export const useUpdateSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (scheduleConfig) => emailProcessingService.updateSchedule(scheduleConfig),
    onSuccess: (data) => {
      console.log('🎯 Mutation hook onSuccess - Schedule update successful:', data);
      console.log('🎯 Mutation hook - NOT auto-invalidating cache (letting component handle it)');
      // Temporarily disabled automatic cache invalidation to prevent conflicts
      // queryClient.invalidateQueries({ queryKey: emailProcessingKeys.scheduleStatus() });
      // queryClient.refetchQueries({ queryKey: emailProcessingKeys.scheduleStatus() });
    },
    onError: (error) => {
      console.error('Failed to update schedule:', error);
    },
  });
};