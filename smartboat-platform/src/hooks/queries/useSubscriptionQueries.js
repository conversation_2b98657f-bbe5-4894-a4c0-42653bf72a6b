/**
 * Subscription Query Hooks
 * Custom React Query hooks for subscription data
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { subscriptionService } from '../../services';
import { queryKeys } from '../../config/queryClient';

/**
 * Hook to fetch all subscriptions
 */
export const useSubscriptions = (options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback, ...queryOptions } = options;
  
  return useQuery({
    queryKey: queryKeys.subscriptions.lists(),
    queryFn: () => subscriptionService.getAllSubscriptions({ useMockFallback, skipGlobalLoading: true }),
    ...queryOptions,
  });
};

/**
 * Hook to fetch a subscription by ID
 */
export const useSubscription = (id, options = {}) => {
  return useQuery({
    queryKey: queryKeys.subscriptions.detail(id),
    queryFn: () => subscriptionService.getSubscriptionById(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to fetch subscriptions by vessel ID
 */
export const useSubscriptionsByVessel = (vesselId, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback, ...queryOptions } = options;
  
  return useQuery({
    queryKey: queryKeys.subscriptions.byVessel(vesselId),
    queryFn: () => subscriptionService.getSubscriptionsByVessel(vesselId, { useMockFallback, skipGlobalLoading: true }),
    enabled: !!vesselId,
    ...queryOptions,
  });
};

/**
 * Hook to fetch subscriptions by customer ID
 */
export const useSubscriptionsByCustomer = (customerId, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback, ...queryOptions } = options;
  
  return useQuery({
    queryKey: queryKeys.subscriptions.byCustomer(customerId),
    queryFn: () => subscriptionService.getSubscriptionsByCustomer(customerId, { useMockFallback, skipGlobalLoading: true }),
    enabled: !!customerId,
    ...queryOptions,
  });
};

/**
 * Hook to fetch available subscription plans
 */
export const useSubscriptionPlans = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.subscriptions.plans(),
    queryFn: () => subscriptionService.getSubscriptionPlans(),
    ...options,
  });
};

/**
 * Hook to create a new subscription
 */
export const useCreateSubscription = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (subscriptionData) => subscriptionService.createSubscription(subscriptionData),
    onSuccess: (data) => {
      // Invalidate subscriptions list
      queryClient.invalidateQueries(queryKeys.subscriptions.lists());
      
      // If subscription has a customer, invalidate customer subscriptions
      if (data.customerId) {
        queryClient.invalidateQueries(queryKeys.subscriptions.byCustomer(data.customerId));
        queryClient.invalidateQueries(queryKeys.customers.subscriptions(data.customerId));
      }
    },
    ...options,
  });
};

/**
 * Hook to update a subscription
 */
export const useUpdateSubscription = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, subscriptionData }) => subscriptionService.updateSubscription(id, subscriptionData),
    onSuccess: (data, variables) => {
      // Invalidate specific subscription and list
      queryClient.invalidateQueries(queryKeys.subscriptions.detail(variables.id));
      queryClient.invalidateQueries(queryKeys.subscriptions.lists());
      
      // If subscription has a customer, invalidate customer subscriptions
      if (data.customerId) {
        queryClient.invalidateQueries(queryKeys.subscriptions.byCustomer(data.customerId));
        queryClient.invalidateQueries(queryKeys.customers.subscriptions(data.customerId));
      }
    },
    ...options,
  });
};

/**
 * Hook to update subscription status
 */
export const useUpdateSubscriptionStatus = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, status }) => subscriptionService.updateSubscriptionStatus(id, status),
    onSuccess: (data, variables) => {
      // Invalidate specific subscription
      queryClient.invalidateQueries(queryKeys.subscriptions.detail(variables.id));
      
      // If subscription has a customer, invalidate customer subscriptions
      if (data.customerId) {
        queryClient.invalidateQueries(queryKeys.subscriptions.byCustomer(data.customerId));
        queryClient.invalidateQueries(queryKeys.customers.subscriptions(data.customerId));
      }
    },
    ...options,
  });
};

/**
 * Hook to delete a subscription
 */
export const useDeleteSubscription = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => subscriptionService.deleteSubscription(id),
    onSuccess: (_, id) => {
      // Get customer ID from cache if possible
      const cachedSubscription = queryClient.getQueryData(queryKeys.subscriptions.detail(id));
      const customerId = cachedSubscription?.customerId;
      
      // Invalidate subscription lists
      queryClient.invalidateQueries(queryKeys.subscriptions.lists());
      if (customerId) {
        queryClient.invalidateQueries(queryKeys.subscriptions.byCustomer(customerId));
        queryClient.invalidateQueries(queryKeys.customers.subscriptions(customerId));
      }
      
      // Remove subscription from cache
      queryClient.removeQueries(queryKeys.subscriptions.detail(id));
    },
    ...options,
  });
};