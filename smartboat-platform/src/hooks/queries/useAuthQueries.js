/**
 * Auth Query Hooks
 * Custom React Query hooks for authentication
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authService } from '../../services';
import { queryKeys } from '../../config/queryClient';
import { clearAuthToken } from '../../utils/authUtils';

/**
 * Hook to get the current authenticated user
 */
export const useCurrentUser = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.auth.currentUser(),
    queryFn: () => authService.getCurrentUser(),
    // Handle 401 errors without global error popup
    onError: (error) => {
      if (error.status === 401) {
        clearAuthToken();
      }
    },
    // Don't refetch on focus for auth status to prevent login screen flashes
    refetchOnWindowFocus: false,
    // Cache user data for longer
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

/**
 * Hook to handle user login
 */
export const useLogin = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (credentials) => authService.login(credentials),
    onSuccess: (data) => {
      // Set current user data in cache
      queryClient.setQueryData(queryKeys.auth.currentUser(), data.user);
    },
    ...options,
  });
};

/**
 * Hook to handle user logout
 */
export const useLogout = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => authService.logout(),
    onSuccess: () => {
      // Clear current user from cache
      queryClient.setQueryData(queryKeys.auth.currentUser(), null);
      // Clear other personal data from cache
      queryClient.invalidateQueries(queryKeys.auth.all);
    },
    ...options,
  });
};

/**
 * Hook to register a new user
 */
export const useRegister = (options = {}) => {
  return useMutation({
    mutationFn: (userData) => authService.register(userData),
    ...options,
  });
};

/**
 * Hook to request a password reset
 */
export const useRequestPasswordReset = (options = {}) => {
  return useMutation({
    mutationFn: (data) => authService.requestPasswordReset(data),
    ...options,
  });
};

/**
 * Hook to reset a password with a token
 */
export const useResetPassword = (options = {}) => {
  return useMutation({
    mutationFn: (data) => authService.resetPassword(data),
    ...options,
  });
};

/**
 * Hook to update the user's profile
 */
export const useUpdateProfile = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (userData) => authService.updateProfile(userData),
    onSuccess: (data) => {
      // Update current user in cache
      queryClient.setQueryData(queryKeys.auth.currentUser(), data);
    },
    ...options,
  });
};

/**
 * Hook to change the user's password
 */
export const useChangePassword = (options = {}) => {
  return useMutation({
    mutationFn: (passwordData) => authService.changePassword(passwordData),
    ...options,
  });
};

/**
 * Hook to verify token validity
 */
export const useVerifyToken = (options = {}) => {
  return useQuery({
    queryKey: ['tokenVerification'],
    queryFn: () => authService.verifyToken(),
    // Run only once at app initialization
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false,
    // Short stale time since token validity can change
    staleTime: 60 * 1000, // 1 minute
    ...options,
  });
};