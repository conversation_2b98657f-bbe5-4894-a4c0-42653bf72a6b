/**
 * Customer Query Hooks
 * Custom React Query hooks for customer data
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { customerService } from '../../services';
import { queryKeys } from '../../config/queryClient';

/**
 * Hook to fetch all customers
 */
export const useCustomers = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.customers.lists(),
    queryFn: () => customerService.getAllCustomers(),
    ...options,
  });
};

/**
 * Hook to fetch a customer by ID
 */
export const useCustomer = (id, options = {}) => {
  return useQuery({
    queryKey: queryKeys.customers.detail(id),
    queryFn: () => customerService.getCustomerById(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to fetch subscriptions for a customer
 */
export const useCustomerSubscriptions = (id, options = {}) => {
  return useQuery({
    queryKey: queryKeys.customers.subscriptions(id),
    queryFn: () => customerService.getCustomerSubscriptions(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to fetch vessels for a customer
 */
export const useCustomerVessels = (id, options = {}) => {
  return useQuery({
    queryKey: queryKeys.customers.vessels(id),
    queryFn: () => customerService.getCustomerVessels(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to create a new customer
 */
export const useCreateCustomer = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (customerData) => customerService.createCustomer(customerData),
    onSuccess: (data) => {
      // Invalidate customers list
      queryClient.invalidateQueries(queryKeys.customers.lists());
      
      // If customer has a company, invalidate company customers
      if (data.companyId) {
        queryClient.invalidateQueries(queryKeys.companies.customers(data.companyId));
      }
    },
    ...options,
  });
};

/**
 * Hook to update a customer
 */
export const useUpdateCustomer = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, customerData }) => customerService.updateCustomer(id, customerData),
    onSuccess: (data, variables) => {
      // Invalidate specific customer and list
      queryClient.invalidateQueries(queryKeys.customers.detail(variables.id));
      queryClient.invalidateQueries(queryKeys.customers.lists());
      
      // If customer has a company, invalidate company customers
      if (data.companyId) {
        queryClient.invalidateQueries(queryKeys.companies.customers(data.companyId));
      }
    },
    ...options,
  });
};

/**
 * Hook to delete a customer
 */
export const useDeleteCustomer = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => customerService.deleteCustomer(id),
    onSuccess: (_, id) => {
      // Get company ID from cache if possible
      const cachedCustomer = queryClient.getQueryData(queryKeys.customers.detail(id));
      const companyId = cachedCustomer?.companyId;
      
      // Invalidate customer lists
      queryClient.invalidateQueries(queryKeys.customers.lists());
      if (companyId) {
        queryClient.invalidateQueries(queryKeys.companies.customers(companyId));
      }
      
      // Remove customer from cache
      queryClient.removeQueries(queryKeys.customers.detail(id));
      queryClient.removeQueries(queryKeys.customers.subscriptions(id));
      queryClient.removeQueries(queryKeys.customers.vessels(id));
    },
    ...options,
  });
};