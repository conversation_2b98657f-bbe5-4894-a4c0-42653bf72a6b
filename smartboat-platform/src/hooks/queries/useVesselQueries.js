/**
 * Vessel Query Hooks
 * Custom React Query hooks for vessel data
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { vesselService } from '../../services';
import { queryKeys } from '../../config/queryClient';

/**
 * Hook to fetch all vessels
 */
export const useVessels = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.vessels.lists(),
    queryFn: () => vesselService.getAllVessels(),
    ...options,
  });
};

/**
 * Hook to fetch a vessel by ID
 */
export const useVessel = (id, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback, ...queryOptions } = options;
  
  return useQuery({
    queryKey: queryKeys.vessels.detail(id),
    queryFn: () => vesselService.getVesselById(id, { useMockFallback }),
    enabled: !!id,
    ...queryOptions,
  });
};

/**
 * Hook to fetch vessels by company ID
 */
export const useVesselsByCompany = (companyId, options = {}) => {
  return useQuery({
    queryKey: queryKeys.vessels.byCompany(companyId),
    queryFn: () => vesselService.getVesselsByCompany(companyId),
    enabled: !!companyId,
    ...options,
  });
};

/**
 * Hook to fetch vessels by owner ID
 */
export const useVesselsByOwner = (ownerId, options = {}) => {
  return useQuery({
    queryKey: queryKeys.vessels.byOwner(ownerId),
    queryFn: () => vesselService.getVesselsByOwner(ownerId),
    enabled: !!ownerId,
    ...options,
  });
};

/**
 * Hook to fetch vessel path history
 */
export const useVesselPath = (id, params = {}, options = {}) => {
  return useQuery({
    queryKey: queryKeys.vessels.path(id, params),
    queryFn: () => vesselService.getVesselPath(id, params),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to create a new vessel
 */
export const useCreateVessel = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (vesselData) => vesselService.createVessel(vesselData),
    onSuccess: (data) => {
      // Invalidate vessels list and company vessels
      queryClient.invalidateQueries(queryKeys.vessels.lists());
      if (data.companyId) {
        queryClient.invalidateQueries(queryKeys.vessels.byCompany(data.companyId));
        queryClient.invalidateQueries(queryKeys.companies.vessels(data.companyId));
      }
    },
    ...options,
  });
};

/**
 * Hook to update a vessel
 */
export const useUpdateVessel = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, vesselData }) => vesselService.updateVessel(id, vesselData),
    onSuccess: (data, variables) => {
      // Invalidate specific vessel and lists
      queryClient.invalidateQueries(queryKeys.vessels.detail(variables.id));
      queryClient.invalidateQueries(queryKeys.vessels.lists());
      if (data.companyId) {
        queryClient.invalidateQueries(queryKeys.vessels.byCompany(data.companyId));
        queryClient.invalidateQueries(queryKeys.companies.vessels(data.companyId));
      }
    },
    ...options,
  });
};

/**
 * Hook to delete a vessel
 */
export const useDeleteVessel = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => vesselService.deleteVessel(id),
    onSuccess: async (_, id) => {
      // Get company ID from cache if possible
      const cachedVessel = queryClient.getQueryData(queryKeys.vessels.detail(id));
      const companyId = cachedVessel?.companyId;
      
      // Invalidate vessel lists with immediate refetch
      await queryClient.invalidateQueries({
        queryKey: queryKeys.vessels.lists(),
        refetchType: 'all'
      });
      
      if (companyId) {
        await queryClient.invalidateQueries({
          queryKey: queryKeys.vessels.byCompany(companyId),
          refetchType: 'all'
        });
        await queryClient.invalidateQueries({
          queryKey: queryKeys.companies.vessels(companyId),
          refetchType: 'all'
        });
      }
      
      // Remove vessel from cache
      queryClient.removeQueries(queryKeys.vessels.detail(id));
    },
    ...options,
  });
};