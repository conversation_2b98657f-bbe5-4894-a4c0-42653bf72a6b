import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { imageService } from '../../services/imageService';

// Query keys
export const imageQueryKeys = {
  all: ['images'],
  byEntity: (entityId) => ['images', 'entity', entityId],
  blob: (imageId) => ['images', 'blob', imageId],
};

// Hook to get images for an entity
export const useImages = (entityId, options = {}) => {
  return useQuery({
    queryKey: imageQueryKeys.byEntity(entityId),
    queryFn: () => imageService.getImagesByEntityId(entityId),
    enabled: !!entityId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    ...options,
  });
};

// Hook to upload an image
export const useUploadImage = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ entityId, imageFile }) => 
      imageService.uploadImage(entityId, imageFile),
    onSuccess: (data, variables) => {
      // Invalidate and refetch images for this entity
      queryClient.invalidateQueries({
        queryKey: imageQueryKeys.byEntity(variables.entityId),
      });
    },
    onError: (error) => {
      console.error('Upload failed:', error);
    },
  });
};

// Hook to get image blob data
export const useImageBlob = (imageId, options = {}) => {
  return useQuery({
    queryKey: imageQueryKeys.blob(imageId),
    queryFn: () => imageService.getImageBlobUrl(imageId),
    enabled: !!imageId,
    staleTime: 1000 * 60 * 10, // 10 minutes
    gcTime: 1000 * 60 * 15, // 15 minutes (formerly cacheTime)
    ...options,
  });
};

// Hook to delete an image
export const useDeleteImage = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (imageId) => imageService.deleteImage(imageId),
    onSuccess: () => {
      // Invalidate all image queries to refresh the lists
      queryClient.invalidateQueries({
        queryKey: imageQueryKeys.all,
      });
    },
    onError: (error) => {
      console.error('Delete failed:', error);
    },
  });
};