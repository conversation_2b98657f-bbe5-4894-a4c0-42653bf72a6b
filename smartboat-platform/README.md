# SmartBoat Platform

The SmartBoat Platform is a comprehensive web application for monitoring and managing vessels, sensors, companies, and customers in a maritime environment. Built with React, TypeScript, and Tailwind CSS, it provides a professional user interface for fleet management and IoT data visualization.

## Features

- **Dashboard View**: Get an overview of your fleet with key metrics and recent activity
- **Vessels Management**: Track and monitor vessels with detailed information and mapping
- **Sensor Monitoring**: Real-time sensor data visualization and alerting
- **Customer Management**: Manage customer details and their associated companies
- **Company Organization**: Organize vessels by their owning companies
- **Subscription Management**: Handle customer subscriptions with different plans and features
- **User Authentication**: Role-based access control with different permission levels
- **User Profiles**: Personalized user profiles with role-specific features
- **Responsive Design**: Works across desktop and mobile devices
- **Light/Dark Theme**: Toggle between light and dark mode with system preference detection
- **Fixed Header Navigation**: Easy access to all sections from any page
- **Multilingual Support**: Full internationalization with English, Greek, and French languages

## Project Structure

The project follows a professional, modular component structure:

```
src/
├── components/
│   ├── common/           # Reusable components (StatusBadge, SensorChart, etc.)
│   ├── features/         # Feature-specific components
│   │   ├── customers/    # Customer management components
│   │   ├── companies/    # Company management components
│   │   ├── dashboard/    # Dashboard components
│   │   ├── sensors/      # Sensor monitoring components
│   │   ├── vessels/      # Vessel management components
│   │   └── subscriptions/# Subscription management components
│   ├── layout/           # Layout components (Header, Sidebar, Layout)
│   └── pages/            # Page components including user profile
├── config/
│   ├── envConfig.js      # Environment configuration variables
│   └── queryClient.js    # React Query client configuration and query keys
├── context/              # React context providers (ThemeContext, AuthContext, LanguageContext)
├── hooks/
│   ├── queries/          # React Query custom hooks for data fetching
│   │   ├── useAuthQueries.js     # Authentication queries
│   │   ├── useVesselQueries.js   # Vessel data queries
│   │   ├── useSensorQueries.js   # Sensor data queries
│   │   └── ... other entity queries
│   └── useDataFetching.js # General data fetching hook
├── i18n/                 # Internationalization files
│   ├── locales/          # Translation JSON files (en.json, el.json, fr.json)
│   └── i18n.ts           # i18n configuration
├── services/             # API service modules
│   ├── apiClient.js      # Core API client with request/response handling
│   ├── authService.js    # Authentication API operations
│   ├── vesselService.js  # Vessel API operations
│   ├── sensorService.js  # Sensor API operations
│   └── ... other entity services
├── types/                # TypeScript interfaces and types (User, Vessel, Sensor, etc.)
├── utils/                # Utility functions, mock data, and authorization helpers
├── assets/               # Static assets like icons and images
├── App.jsx               # Main application component
└── main.jsx              # Application entry point
```

## Technologies Used

- **React**: Frontend library for building user interfaces
- **TypeScript**: For type-safe code with interfaces and types
- **Tailwind CSS**: For styling and responsive design
- **Vite**: Fast, modern build tool
- **TanStack Query (React Query)**: For data fetching, caching, and state management
- **React Context API**: For state management (auth, theme, language)
- **React Hooks**: For component state and lifecycle management
- **react-i18next**: For internationalization and localization
- **ESLint**: For code quality and consistency
- **PostCSS**: For CSS processing and optimizations

## Getting Started

### Prerequisites

- Node.js (v16.x or later)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/smartboat-platform.git
   cd smartboat-platform
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn
   ```

3. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

### Demo Accounts

The platform includes two demo accounts for testing:

1. **Administrator Account**
   - Email: <EMAIL>
   - Password: admin123
   - Full access to all features and subscriptions

2. **Customer Account**
   - Email: <EMAIL>
   - Password: customer123
   - Access limited to their own subscriptions and vessels

## Development

### Commands

- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run lint`: Run ESLint
- `npm run preview`: Preview production build locally

### Key Features Implementation

#### Authentication System

The platform implements a role-based authentication system:

- Multiple user roles (Administrator, Customer, Manager, Technician, Viewer)
- Role-specific permissions defined in `authUtils.ts`
- UI adapts based on user role and permissions
- User data persisted in localStorage for demonstration

#### Responsive User Interface

- Sidebar collapses into a mobile-friendly menu on smaller screens
- UI components adapt to different screen sizes
- Tailwind CSS utility classes for responsive design
- Theme toggle with light/dark modes and system preference detection

#### Data Management

The platform implements a robust data management system using React Query and a service layer:

**API Client and Services:**
- Centralized API client with standardized request/response handling
- Entity-specific service modules for data operations
- Consistent error handling and authentication management
- Support for mock data fallback during development

**React Query Integration:**
- Custom hooks for data fetching with automatic caching
- Optimized refetching strategies with configurable stale times
- Consistent query key management for cache invalidation
- Loading and error states handled automatically
- Mutations for data updates with automatic cache updates
- Background data refreshing when the window regains focus

**Data Relationships:**
- Users and subscriptions
- Companies and vessels
- Customers and companies
- Vessels and sensors

During development, the system can fall back to mock data (in `utils/mockData.ts`) while maintaining the same API structure.

#### Theme System

The platform features a flexible light/dark theme system:

- Context-based theme management (`ThemeContext.tsx`)
- User preferences stored in localStorage for persistence
- System preference detection using `prefers-color-scheme` media query
- Tailwind CSS dark mode via class strategy
- Dedicated theme toggle component with contextual icons (sun/moon)
- Custom color palette optimized for both light and dark modes
- All UI components support both themes with appropriate contrast
- Added visual cues and appropriate colors for charts and data visualization
- Custom gray-750 color for improved dark mode hover states

#### Internationalization

The platform features comprehensive multilingual support:

- Three supported languages (English, Greek, and French)
- Language switcher available in both the main application and login screen
- Translation files organized in a hierarchical structure by feature
- Language preference stored in localStorage
- Component-based approach using the `useTranslation` hook
- Easily extensible for additional languages

## Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- React team for the excellent frontend library
- Tailwind CSS team for the utility-first CSS framework
- Vite team for the blazing fast build tool
