# SmartBoat Platform API Endpoints

This document outlines all API endpoints required by the SmartBoat Platform application, including request/response properties.

## Authentication Endpoints

### POST /auth/login
**Description:** Authenticate user and get token
**Request Body:**
```json
{
  "email": "string",
  "password": "string"
}
```
**Response:**
```json
{
  "token": "string",
  "user": {
    "id": "number",
    "name": "string",
    "email": "string",
    "role": "string",
    "avatar": "string",
    "joined": "string",
    "status": "string",
    "lastLogin": "string",
    "twoFactorEnabled": "boolean",
    "company": "string",
    "department": "string",
    "phone": "string",
    "timezone": "string",
    "language": "string",
    "bio": "string",
    "customerId": "number",
    "subscriptionIds": ["number"],
    "socialLinks": {
      "linkedin": "string",
      "twitter": "string",
      "github": "string"
    },
    "notifications": {
      "email": "boolean",
      "push": "boolean",
      "sms": "boolean"
    }
  }
}
```

### POST /auth/logout
**Description:** Invalidate current user token
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:** HTTP 200 OK

### POST /auth/register
**Description:** Register a new user
**Request Body:**
```json
{
  "name": "string",
  "email": "string",
  "password": "string",
  "role": "string",
  "company": "string",
  "department": "string",
  "phone": "string",
  "timezone": "string",
  "language": "string"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "email": "string",
  "role": "string",
  "joined": "string",
  "status": "string"
}
```

### POST /auth/reset-password
**Description:** Request password reset
**Request Body:**
```json
{
  "email": "string"
}
```
**Response:** HTTP 200 OK

### POST /auth/reset-password/confirm
**Description:** Reset password with token
**Request Body:**
```json
{
  "token": "string",
  "password": "string"
}
```
**Response:** HTTP 200 OK

### GET /auth/me
**Description:** Get current user profile
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "email": "string",
  "role": "string",
  "avatar": "string",
  "joined": "string",
  "status": "string",
  "lastLogin": "string",
  "twoFactorEnabled": "boolean",
  "company": "string",
  "department": "string",
  "phone": "string",
  "timezone": "string",
  "language": "string",
  "bio": "string",
  "customerId": "number",
  "subscriptionIds": ["number"],
  "socialLinks": {
    "linkedin": "string",
    "twitter": "string",
    "github": "string"
  },
  "notifications": {
    "email": "boolean",
    "push": "boolean",
    "sms": "boolean"
  }
}
```

### PUT /auth/me
**Description:** Update current user profile
**Request Headers:**
```
Authorization: Bearer {token}
```
**Request Body:**
```json
{
  "name": "string",
  "email": "string",
  "avatar": "string",
  "company": "string",
  "department": "string",
  "phone": "string",
  "timezone": "string",
  "language": "string",
  "bio": "string",
  "socialLinks": {
    "linkedin": "string",
    "twitter": "string",
    "github": "string"
  },
  "notifications": {
    "email": "boolean",
    "push": "boolean",
    "sms": "boolean"
  }
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "email": "string",
  "role": "string",
  "avatar": "string",
  "joined": "string",
  "status": "string",
  "lastLogin": "string",
  "twoFactorEnabled": "boolean",
  "company": "string",
  "department": "string",
  "phone": "string",
  "timezone": "string",
  "language": "string",
  "bio": "string",
  "customerId": "number",
  "subscriptionIds": ["number"],
  "socialLinks": {
    "linkedin": "string",
    "twitter": "string",
    "github": "string"
  },
  "notifications": {
    "email": "boolean",
    "push": "boolean",
    "sms": "boolean"
  }
}
```

### POST /auth/change-password
**Description:** Change user password
**Request Headers:**
```
Authorization: Bearer {token}
```
**Request Body:**
```json
{
  "currentPassword": "string",
  "newPassword": "string"
}
```
**Response:** HTTP 200 OK

### POST /auth/verify-token
**Description:** Verify auth token validity
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:**
```json
{
  "valid": "boolean",
  "expiresAt": "string"
}
```

## Vessel Endpoints

### GET /vessels
**Description:** Get all vessels
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "number": "string",
    "type": "string",
    "location": "string",
    "status": "string",
    "startDate": "string",
    "endDate": "string",
    "image": "string",
    "onsigners": "number",
    "offsigners": "number",
    "sensors": "number",
    "lastUpdated": "string",
    "companyId": "number"
  }
]
```

### GET /vessels/{id}
**Description:** Get vessel by ID
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Vessel ID
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "number": "string",
  "type": "string",
  "location": "string",
  "status": "string",
  "startDate": "string",
  "endDate": "string",
  "image": "string",
  "onsigners": "number",
  "offsigners": "number",
  "sensors": "number",
  "lastUpdated": "string",
  "companyId": "number"
}
```

### GET /vessels?companyId={companyId}
**Description:** Get vessels by company
**Request Headers:**
```
Authorization: Bearer {token}
```
**Query Parameters:**
- companyId: Company ID
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "number": "string",
    "type": "string",
    "location": "string",
    "status": "string",
    "startDate": "string",
    "endDate": "string",
    "image": "string",
    "onsigners": "number",
    "offsigners": "number",
    "sensors": "number",
    "lastUpdated": "string",
    "companyId": "number"
  }
]
```

### POST /vessels
**Description:** Create new vessel
**Request Headers:**
```
Authorization: Bearer {token}
```
**Request Body:**
```json
{
  "name": "string",
  "number": "string",
  "type": "string",
  "location": "string",
  "status": "string",
  "startDate": "string",
  "endDate": "string",
  "image": "string",
  "onsigners": "number",
  "offsigners": "number",
  "companyId": "number"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "number": "string",
  "type": "string",
  "location": "string",
  "status": "string",
  "startDate": "string",
  "endDate": "string",
  "image": "string",
  "onsigners": "number",
  "offsigners": "number",
  "sensors": "number",
  "lastUpdated": "string",
  "companyId": "number"
}
```

### PUT /vessels/{id}
**Description:** Update vessel
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Vessel ID
**Request Body:**
```json
{
  "name": "string",
  "number": "string",
  "type": "string",
  "location": "string",
  "status": "string",
  "startDate": "string",
  "endDate": "string",
  "image": "string",
  "onsigners": "number",
  "offsigners": "number",
  "companyId": "number"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "number": "string",
  "type": "string",
  "location": "string",
  "status": "string",
  "startDate": "string",
  "endDate": "string",
  "image": "string",
  "onsigners": "number",
  "offsigners": "number",
  "sensors": "number",
  "lastUpdated": "string",
  "companyId": "number"
}
```

### DELETE /vessels/{id}
**Description:** Delete vessel
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Vessel ID
**Response:** HTTP 200 OK

### GET /vessels/{id}/path
**Description:** Get vessel path history
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Vessel ID
**Query Parameters:**
- startDate: (Optional) Start date filter
- endDate: (Optional) End date filter
- limit: (Optional) Number of records to return
**Response:**
```json
[
  {
    "lat": "number",
    "lng": "number",
    "timestamp": "string",
    "location": "string"
  }
]
```

## Sensor Endpoints

### GET /sensors
**Description:** Get all sensors
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "type": "string",
    "vessel": "string",
    "location": "string",
    "status": "string",
    "lastReading": "string",
    "lastUpdated": "string",
    "alertThreshold": "string"
  }
]
```

### GET /sensors/{id}
**Description:** Get sensor by ID
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Sensor ID
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "type": "string",
  "vessel": "string",
  "location": "string",
  "status": "string",
  "lastReading": "string",
  "lastUpdated": "string",
  "alertThreshold": "string"
}
```

### GET /sensors?vesselId={vesselId}
**Description:** Get sensors by vessel
**Request Headers:**
```
Authorization: Bearer {token}
```
**Query Parameters:**
- vesselId: Vessel ID
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "type": "string",
    "vessel": "string",
    "location": "string",
    "status": "string",
    "lastReading": "string",
    "lastUpdated": "string",
    "alertThreshold": "string"
  }
]
```

### POST /sensors
**Description:** Create new sensor
**Request Headers:**
```
Authorization: Bearer {token}
```
**Request Body:**
```json
{
  "name": "string",
  "type": "string",
  "vessel": "string",
  "location": "string",
  "status": "string",
  "alertThreshold": "string"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "type": "string",
  "vessel": "string",
  "location": "string",
  "status": "string",
  "lastReading": "string",
  "lastUpdated": "string",
  "alertThreshold": "string"
}
```

### PUT /sensors/{id}
**Description:** Update sensor
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Sensor ID
**Request Body:**
```json
{
  "name": "string",
  "type": "string",
  "vessel": "string",
  "location": "string",
  "status": "string",
  "alertThreshold": "string"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "type": "string",
  "vessel": "string",
  "location": "string",
  "status": "string",
  "lastReading": "string",
  "lastUpdated": "string",
  "alertThreshold": "string"
}
```

### DELETE /sensors/{id}
**Description:** Delete sensor
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Sensor ID
**Response:** HTTP 200 OK

### GET /sensors/{id}/data
**Description:** Get sensor readings
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Sensor ID
**Query Parameters:**
- timeframe: (Optional) Time range (e.g., "day", "week", "month")
- startTime: (Optional) Start timestamp
- endTime: (Optional) End timestamp
- interval: (Optional) Data aggregation interval
**Response:**
```json
[
  {
    "time": "string",
    "timestamp": "number",
    "temperature": "number",
    "humidity": "number"
  }
]
```

### GET /sensors/{id}/alerts
**Description:** Get sensor alerts
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Sensor ID
**Query Parameters:**
- startDate: (Optional) Start date filter
- endDate: (Optional) End date filter
- severity: (Optional) Alert severity level
**Response:**
```json
[
  {
    "id": "number",
    "sensorId": "number",
    "timestamp": "string",
    "type": "string",
    "severity": "string",
    "message": "string",
    "reading": "number",
    "threshold": "number",
    "acknowledged": "boolean"
  }
]
```

### PATCH /sensors/{id}/thresholds
**Description:** Update sensor thresholds
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Sensor ID
**Request Body:**
```json
{
  "tempMin": "number",
  "tempMax": "number",
  "humidityMin": "number",
  "humidityMax": "number",
  "alertSeverity": "string"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "type": "string",
  "vessel": "string",
  "location": "string",
  "status": "string",
  "lastReading": "string",
  "lastUpdated": "string",
  "alertThreshold": "string"
}
```

## Company Endpoints

### GET /companies
**Description:** Get all companies
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "location": "string",
    "industry": "string",
    "vessels": "number",
    "sensors": "number",
    "status": "string",
    "customerId": "number",
    "lastUpdated": "string"
  }
]
```

### GET /companies/{id}
**Description:** Get company by ID
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Company ID
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "location": "string",
  "industry": "string",
  "vessels": "number",
  "sensors": "number",
  "status": "string",
  "customerId": "number",
  "lastUpdated": "string"
}
```

### POST /companies
**Description:** Create new company
**Request Headers:**
```
Authorization: Bearer {token}
```
**Request Body:**
```json
{
  "name": "string",
  "location": "string",
  "industry": "string",
  "status": "string",
  "customerId": "number"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "location": "string",
  "industry": "string",
  "vessels": "number",
  "sensors": "number",
  "status": "string",
  "customerId": "number",
  "lastUpdated": "string"
}
```

### PUT /companies/{id}
**Description:** Update company
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Company ID
**Request Body:**
```json
{
  "name": "string",
  "location": "string",
  "industry": "string",
  "status": "string",
  "customerId": "number"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "location": "string",
  "industry": "string",
  "vessels": "number",
  "sensors": "number",
  "status": "string",
  "customerId": "number",
  "lastUpdated": "string"
}
```

### DELETE /companies/{id}
**Description:** Delete company
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Company ID
**Response:** HTTP 200 OK

### GET /companies/{id}/vessels
**Description:** Get company vessels
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Company ID
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "number": "string",
    "type": "string",
    "location": "string",
    "status": "string",
    "startDate": "string",
    "endDate": "string",
    "image": "string",
    "onsigners": "number",
    "offsigners": "number",
    "sensors": "number",
    "lastUpdated": "string",
    "companyId": "number"
  }
]
```

### GET /companies/{id}/customers
**Description:** Get company customers
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Company ID
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "contactPerson": "string",
    "email": "string",
    "phone": "string",
    "companies": "number",
    "vessels": "number",
    "sensors": "number",
    "status": "string",
    "lastActive": "string"
  }
]
```

### GET /companies/{id}/subscriptions
**Description:** Get company subscriptions
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Company ID
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "type": "string",
    "customerId": "number",
    "customerName": "string",
    "startDate": "string",
    "endDate": "string",
    "price": "number",
    "billingFrequency": "string",
    "status": "string",
    "sensorLimit": "number",
    "features": ["string"],
    "lastUpdated": "string",
    "userIds": ["number"]
  }
]
```

## Customer Endpoints

### GET /customers
**Description:** Get all customers
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "contactPerson": "string",
    "email": "string",
    "phone": "string",
    "companies": "number",
    "vessels": "number",
    "sensors": "number",
    "status": "string",
    "lastActive": "string"
  }
]
```

### GET /customers/{id}
**Description:** Get customer by ID
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Customer ID
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "contactPerson": "string",
  "email": "string",
  "phone": "string",
  "companies": "number",
  "vessels": "number",
  "sensors": "number",
  "status": "string",
  "lastActive": "string"
}
```

### POST /customers
**Description:** Create new customer
**Request Headers:**
```
Authorization: Bearer {token}
```
**Request Body:**
```json
{
  "name": "string",
  "contactPerson": "string",
  "email": "string",
  "phone": "string",
  "status": "string"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "contactPerson": "string",
  "email": "string",
  "phone": "string",
  "companies": "number",
  "vessels": "number",
  "sensors": "number",
  "status": "string",
  "lastActive": "string"
}
```

### PUT /customers/{id}
**Description:** Update customer
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Customer ID
**Request Body:**
```json
{
  "name": "string",
  "contactPerson": "string",
  "email": "string",
  "phone": "string",
  "status": "string"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "contactPerson": "string",
  "email": "string",
  "phone": "string",
  "companies": "number",
  "vessels": "number",
  "sensors": "number",
  "status": "string",
  "lastActive": "string"
}
```

### DELETE /customers/{id}
**Description:** Delete customer
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Customer ID
**Response:** HTTP 200 OK

### GET /customers/{id}/subscriptions
**Description:** Get customer subscriptions
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Customer ID
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "type": "string",
    "customerId": "number",
    "customerName": "string",
    "startDate": "string",
    "endDate": "string",
    "price": "number",
    "billingFrequency": "string",
    "status": "string",
    "sensorLimit": "number",
    "features": ["string"],
    "lastUpdated": "string",
    "userIds": ["number"]
  }
]
```

### GET /customers/{id}/vessels
**Description:** Get customer vessels
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Customer ID
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "number": "string",
    "type": "string",
    "location": "string",
    "status": "string",
    "startDate": "string",
    "endDate": "string",
    "image": "string",
    "onsigners": "number",
    "offsigners": "number",
    "sensors": "number",
    "lastUpdated": "string",
    "companyId": "number"
  }
]
```

## Subscription Endpoints

### GET /subscriptions
**Description:** Get all subscriptions
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "type": "string",
    "customerId": "number",
    "customerName": "string",
    "startDate": "string",
    "endDate": "string",
    "price": "number",
    "billingFrequency": "string",
    "status": "string",
    "sensorLimit": "number",
    "features": ["string"],
    "lastUpdated": "string",
    "userIds": ["number"]
  }
]
```

### GET /subscriptions/{id}
**Description:** Get subscription by ID
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Subscription ID
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "type": "string",
  "customerId": "number",
  "customerName": "string",
  "startDate": "string",
  "endDate": "string",
  "price": "number",
  "billingFrequency": "string",
  "status": "string",
  "sensorLimit": "number",
  "features": ["string"],
  "lastUpdated": "string",
  "userIds": ["number"]
}
```

### GET /subscriptions?customerId={customerId}
**Description:** Get subscriptions by customer
**Request Headers:**
```
Authorization: Bearer {token}
```
**Query Parameters:**
- customerId: Customer ID
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "type": "string",
    "customerId": "number",
    "customerName": "string",
    "startDate": "string",
    "endDate": "string",
    "price": "number",
    "billingFrequency": "string",
    "status": "string",
    "sensorLimit": "number",
    "features": ["string"],
    "lastUpdated": "string",
    "userIds": ["number"]
  }
]
```

### POST /subscriptions
**Description:** Create new subscription
**Request Headers:**
```
Authorization: Bearer {token}
```
**Request Body:**
```json
{
  "name": "string",
  "type": "string",
  "customerId": "number",
  "startDate": "string",
  "endDate": "string",
  "price": "number",
  "billingFrequency": "string",
  "status": "string",
  "sensorLimit": "number",
  "features": ["string"],
  "userIds": ["number"]
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "type": "string",
  "customerId": "number",
  "customerName": "string",
  "startDate": "string",
  "endDate": "string",
  "price": "number",
  "billingFrequency": "string",
  "status": "string",
  "sensorLimit": "number",
  "features": ["string"],
  "lastUpdated": "string",
  "userIds": ["number"]
}
```

### PUT /subscriptions/{id}
**Description:** Update subscription
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Subscription ID
**Request Body:**
```json
{
  "name": "string",
  "type": "string",
  "customerId": "number",
  "startDate": "string",
  "endDate": "string",
  "price": "number",
  "billingFrequency": "string",
  "status": "string",
  "sensorLimit": "number",
  "features": ["string"],
  "userIds": ["number"]
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "type": "string",
  "customerId": "number",
  "customerName": "string",
  "startDate": "string",
  "endDate": "string",
  "price": "number",
  "billingFrequency": "string",
  "status": "string",
  "sensorLimit": "number",
  "features": ["string"],
  "lastUpdated": "string",
  "userIds": ["number"]
}
```

### DELETE /subscriptions/{id}
**Description:** Delete subscription
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Subscription ID
**Response:** HTTP 200 OK

### GET /subscriptions/plans
**Description:** Get available subscription plans
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "type": "string",
    "price": "number",
    "billingFrequency": "string",
    "sensorLimit": "number",
    "features": ["string"],
    "isPopular": "boolean",
    "description": "string"
  }
]
```

### PATCH /subscriptions/{id}/status
**Description:** Update subscription status
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: Subscription ID
**Request Body:**
```json
{
  "status": "string"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "type": "string",
  "customerId": "number",
  "customerName": "string",
  "startDate": "string",
  "endDate": "string",
  "price": "number",
  "billingFrequency": "string",
  "status": "string",
  "sensorLimit": "number",
  "features": ["string"],
  "lastUpdated": "string",
  "userIds": ["number"]
}
```

## User Endpoints

### GET /users
**Description:** Get all users (admin only)
**Request Headers:**
```
Authorization: Bearer {token}
```
**Response:**
```json
[
  {
    "id": "number",
    "name": "string",
    "email": "string",
    "role": "string",
    "avatar": "string",
    "joined": "string",
    "status": "string",
    "lastLogin": "string",
    "twoFactorEnabled": "boolean",
    "company": "string",
    "department": "string",
    "customerId": "number",
    "subscriptionIds": ["number"]
  }
]
```

### GET /users/{id}
**Description:** Get user by ID
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: User ID
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "email": "string",
  "role": "string",
  "avatar": "string",
  "joined": "string",
  "status": "string",
  "lastLogin": "string",
  "twoFactorEnabled": "boolean",
  "company": "string",
  "department": "string",
  "phone": "string",
  "timezone": "string",
  "language": "string",
  "bio": "string",
  "customerId": "number",
  "subscriptionIds": ["number"],
  "socialLinks": {
    "linkedin": "string",
    "twitter": "string",
    "github": "string"
  },
  "notifications": {
    "email": "boolean",
    "push": "boolean",
    "sms": "boolean"
  }
}
```

### POST /users
**Description:** Create new user (admin only)
**Request Headers:**
```
Authorization: Bearer {token}
```
**Request Body:**
```json
{
  "name": "string",
  "email": "string",
  "password": "string",
  "role": "string",
  "company": "string",
  "department": "string",
  "phone": "string",
  "timezone": "string",
  "language": "string",
  "customerId": "number",
  "subscriptionIds": ["number"]
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "email": "string",
  "role": "string",
  "joined": "string",
  "status": "string",
  "customerId": "number",
  "subscriptionIds": ["number"]
}
```

### PUT /users/{id}
**Description:** Update user
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: User ID
**Request Body:**
```json
{
  "name": "string",
  "email": "string",
  "company": "string",
  "department": "string",
  "phone": "string",
  "timezone": "string",
  "language": "string",
  "bio": "string",
  "customerId": "number",
  "subscriptionIds": ["number"],
  "socialLinks": {
    "linkedin": "string",
    "twitter": "string",
    "github": "string"
  }
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "email": "string",
  "role": "string",
  "avatar": "string",
  "joined": "string",
  "status": "string",
  "lastLogin": "string",
  "twoFactorEnabled": "boolean",
  "company": "string",
  "department": "string",
  "phone": "string",
  "timezone": "string",
  "language": "string",
  "bio": "string",
  "customerId": "number",
  "subscriptionIds": ["number"],
  "socialLinks": {
    "linkedin": "string",
    "twitter": "string",
    "github": "string"
  }
}
```

### DELETE /users/{id}
**Description:** Delete user (admin only)
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: User ID
**Response:** HTTP 200 OK

### PATCH /users/{id}/role
**Description:** Update user role (admin only)
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: User ID
**Request Body:**
```json
{
  "role": "string"
}
```
**Response:**
```json
{
  "id": "number",
  "name": "string",
  "email": "string",
  "role": "string",
  "status": "string"
}
```

### GET /users/{id}/notifications
**Description:** Get user notification settings
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: User ID
**Response:**
```json
{
  "email": "boolean",
  "push": "boolean",
  "sms": "boolean"
}
```

### PUT /users/{id}/notifications
**Description:** Update notification settings
**Request Headers:**
```
Authorization: Bearer {token}
```
**Path Parameters:**
- id: User ID
**Request Body:**
```json
{
  "email": "boolean",
  "push": "boolean",
  "sms": "boolean"
}
```
**Response:**
```json
{
  "email": "boolean",
  "push": "boolean",
  "sms": "boolean"
}
```