-- Check if Super Admin user exists and is active
USE SmartBoat;
GO

PRINT 'Checking Super Admin users...';

-- Check all users with Super Admin role
SELECT 
    Id,
    <PERSON>rna<PERSON>,
    Email,
    FirstName,
    LastName,
    Role,
    Status,
    Created,
    LastLogin,
    IsDeleted
FROM Users 
WHERE Role = 'Super Admin' 
ORDER BY Created;

-- Check if there are any active Super Admin users
DECLARE @ActiveSuperAdminCount int = (
    SELECT COUNT(*) 
    FROM Users 
    WHERE Role = 'Super Admin' 
    AND Status = 'Active' 
    AND IsDeleted = 0
);

PRINT 'Active Super Admin users found: ' + CAST(@ActiveSuperAdminCount AS nvarchar(10));

-- Show the first active Super Admin user (what the scheduled job should use)
IF @ActiveSuperAdminCount > 0
BEGIN
    DECLARE @SuperAdminId uniqueidentifier = (
        SELECT TOP 1 Id 
        FROM Users 
        WHERE Role = 'Super Admin' 
        AND Status = 'Active' 
        AND IsDeleted = 0
        ORDER BY Created
    );
    
    DECLARE @SuperAdminUsername nvarchar(255) = (
        SELECT Username 
        FROM Users 
        WHERE Id = @SuperAdminId
    );
    
    PRINT 'First Super Admin ID: ' + CAST(@SuperAdminId AS nvarchar(50));
    PRINT 'First Super Admin Username: ' + @SuperAdminUsername;
END
ELSE
BEGIN
    PRINT 'ERROR: No active Super Admin users found!';
    PRINT 'The scheduled job will fail because it cannot find a valid system user.';
    PRINT '';
    PRINT 'To fix this, run the SeedUsersAndRoles.sql script to create the Super Admin user.';
END
