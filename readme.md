# MCP Server: LLMBridge

This project contains an MCP server named LLMBridge. The server is designed to facilitate communication and operations using the Model Context Protocol (MCP).


## Prerequisites

- Python 3.13 or higher
- [uv](https://github.com/astral-sh/uv) package manager



## To install uv on Windows with pip

   - pip install uv

## To install uv on macOS with brew

   - brew install uv

## To verify installation

   - pip --version 


## Create and activate virtual environment

    # Create new venv
    uv venv

    # Activate venv
    .venv\Scripts\activate


## Package Management

    # Install from pyproject.toml
    uv pip install .


## Configuration

Below is the configuration for the LLMBridge MCP server:

```json
{
  "mcpServers": {
    "LLMBridge": {
      "command": "uv",
      "args": [
        "--directory",
        "/{your_path}",
        "run",
        "llmbridge_mcp_server.py"
      ],
      "timeout": 1800,
      "disabled": false
    }
  }
}
```

