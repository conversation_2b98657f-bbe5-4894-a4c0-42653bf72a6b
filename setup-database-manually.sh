#!/bin/bash

# Manual Database Setup Script for Azure SQL
# Run this after deployment if automatic setup didn't work

echo "=== SmartBoat Database Setup for Azure SQL ==="
echo "This script will set up the database schema for your SmartBoat application."
echo ""

# Get database connection details
read -p "Enter SQL Server name (e.g., smartboat-prod-sql): " SQL_SERVER_NAME
read -p "Enter database name (e.g., smartboat): " SQL_DATABASE_NAME  
read -p "Enter admin username: " SQL_ADMIN_USER
read -s -p "Enter admin password: " SQL_ADMIN_PASSWORD
echo ""

SERVER_FQDN="${SQL_SERVER_NAME}.database.windows.net"

echo "Connecting to: $SERVER_FQDN"
echo "Database: $SQL_DATABASE_NAME"
echo ""

# Check if sqlcmd is available
if ! command -v sqlcmd &> /dev/null; then
    echo "❌ sqlcmd not found. Please install SQL Server command line tools:"
    echo ""
    echo "On macOS:"
    echo "brew install mssql-tools"
    echo ""
    echo "On Linux:"
    echo "curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -"
    echo "curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list"
    echo "sudo apt-get update && sudo apt-get install -y mssql-tools"
    echo ""
    exit 1
fi

echo "✅ sqlcmd found, proceeding with database setup..."

# Change to API directory
cd SmartBoat.API

# Function to execute SQL script
execute_sql_script() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    echo "📝 Executing $script_name..."
    
    sqlcmd -S "$SERVER_FQDN" -d "$SQL_DATABASE_NAME" -U "$SQL_ADMIN_USER" -P "$SQL_ADMIN_PASSWORD" -i "$script_file" -b
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to execute $script_name"
        echo "Please check the error above and fix any issues."
        return 1
    else
        echo "✅ $script_name executed successfully"
        return 0
    fi
}

echo ""
echo "🚀 Setting up database schema..."
echo ""

# 1. Execute Authorization module setup FIRST
if [ -f "Authorization/Database/setup-authorization.sql" ]; then
    echo "🔐 Setting up Authorization module..."
    if ! execute_sql_script "Authorization/Database/setup-authorization.sql"; then
        exit 1
    fi
else
    echo "⚠️ Authorization module setup script not found, skipping..."
fi

# 2. Execute core database tables
echo ""
echo "🏗️ Setting up core database tables..."
for sql_file in Database/*.sql; do
    filename=$(basename "$sql_file")
    # Skip seed files and specific files that need special order
    if [ -f "$sql_file" ] && [[ ! "$filename" =~ ^Seed.*\.sql$ ]] && [ "$filename" != "EntityPermissions.sql" ] && [ "$filename" != "RolePermission.sql" ] && [ "$filename" != "VesselTypeMigration.sql" ]; then
        if ! execute_sql_script "$sql_file"; then
            echo "❌ Setup failed. Please check the error and try again."
            exit 1
        fi
    fi
done

# 2.5. Execute VesselType migration (handles both new and existing deployments)
echo ""
echo "🔄 Executing VesselType migration..."
if [ -f "Database/VesselTypeMigration.sql" ]; then
    if ! execute_sql_script "Database/VesselTypeMigration.sql"; then
        echo "⚠️ VesselType migration failed, but continuing..."
    fi
fi

# 3. Execute EntityPermissions.sql
if [ -f "Database/EntityPermissions.sql" ]; then
    echo ""
    echo "🔐 Setting up entity permissions..."
    if ! execute_sql_script "Database/EntityPermissions.sql"; then
        exit 1
    fi
fi

# 4. Execute seed scripts in order
echo ""
echo "🌱 Populating with seed data..."

SEED_FILES=(
    "Database/SeedUsersAndRoles.sql"
    "Database/SeedVessels.sql" 
    "Database/SeedSensors.sql"
    "Database/SeedVesselPathPoints.sql"
    "Database/SeedRolePermissions.sql"
)

for seed_file in "${SEED_FILES[@]}"; do
    if [ -f "$seed_file" ]; then
        if ! execute_sql_script "$seed_file"; then
            echo "⚠️ Seed script failed: $(basename "$seed_file")"
            echo "You can continue and run this manually later if needed."
            read -p "Continue with remaining setup? (y/n): " CONTINUE
            if [[ ! $CONTINUE =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    fi
done

echo ""
echo "🎉 Database setup completed successfully!"
echo ""
echo "Your SmartBoat application should now be ready to use."
echo "You can access it at: https://${SQL_SERVER_NAME//-sql/}.azurewebsites.net"
echo ""